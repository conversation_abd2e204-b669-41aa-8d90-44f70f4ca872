# 合同审查任务启动按钮后端处理流程图

## 🎯 整体流程概览

```mermaid
graph TD
    A[用户点击启动按钮] --> B[ContractReviewTaskController.startReview]
    B --> C{验证任务状态}
    C -->|任务不存在| D[返回错误]
    C -->|任务已启动| E[返回已启动状态]
    C -->|可以启动| F[检查合同文件]
    F --> G{文件检查}
    G -->|无文件| H[返回错误：无合同文件]
    G -->|有文件| I[更新任务状态为进行中]
    I --> J[异步启动审查流程]
    J --> K[调用DifyService.runWorkflowStream]
    K --> L[返回启动成功响应]
    
    %% 异步处理分支
    J --> M[异步处理开始]
    M --> N[文件处理与解析]
    N --> O[AI工作流执行]
    O --> P[结果处理与存储]
```

## 📋 详细处理步骤

### 1. Controller层处理 (ContractReviewTaskController)

```mermaid
sequenceDiagram
    participant User as 用户界面
    participant Controller as ContractReviewTaskController
    participant TaskService as ContractReviewTaskService
    participant FileService as ContractFileService
    participant DifyService as DifyService
    
    User->>Controller: POST /contract/review-task/start/{taskId}
    Controller->>Controller: 权限验证 @PreAuthorize
    Controller->>TaskService: selectContractReviewTaskById(taskId)
    
    alt 任务不存在
        TaskService-->>Controller: null
        Controller-->>User: error("任务不存在")
    else 任务已启动
        TaskService-->>Controller: task.status = "1"
        Controller-->>User: success("任务已在进行中")
    else 可以启动
        TaskService-->>Controller: task.status = "0"
        Controller->>FileService: 查询关联的合同文件
        
        alt 无合同文件
            FileService-->>Controller: empty list
            Controller-->>User: error("请先上传合同文件")
        else 有合同文件
            FileService-->>Controller: file list
            Controller->>TaskService: 更新任务状态为"1"(进行中)
            Controller->>DifyService: runWorkflowStream(异步调用)
            Controller-->>User: success("审查任务启动成功")
        end
    end
```

### 2. 文件处理流程 (ContractFileProcessService)

```mermaid
graph TD
    A[文件上传处理] --> B[ContractFileProcessServiceImpl.processUploadedFiles]
    B --> C[文件校验]
    C --> D{校验结果}
    D -->|失败| E[返回错误信息]
    D -->|成功| F[MD5去重检查]
    F --> G{是否重复}
    G -->|重复| H[返回已存在文件]
    G -->|不重复| I[保存到MinIO存储]
    I --> J[保存文件记录到数据库]
    J --> K[提交异步解析任务]
    K --> L[AsyncDocumentParseService.submitParseTask]
    
    subgraph "异步解析处理"
        L --> M[创建解析任务]
        M --> N[线程池执行解析]
        N --> O{文件类型}
        O -->|PDF| P[PdfParseService.parsePdfSync]
        O -->|Word| Q[WordParseService.parseWordSync]
        P --> R[解析完成回调]
        Q --> R
        R --> S[更新文件解析状态]
        S --> T[保存解析结果]
    end
```

### 3. AI工作流调用 (DifyService)

```mermaid
sequenceDiagram
    participant Controller as Controller
    participant DifyService as DifyServiceImpl
    participant HttpClient as HTTP客户端
    participant DifyAPI as Dify工作流API
    participant Cache as 消息缓存
    
    Controller->>DifyService: runWorkflowStream(inputs, userId)
    DifyService->>DifyService: 构建请求参数
    DifyService->>HttpClient: 发送POST请求到Dify API
    HttpClient->>DifyAPI: /workflows/run (流式调用)
    
    loop 流式响应处理
        DifyAPI-->>HttpClient: SSE数据流
        HttpClient-->>DifyService: 解析响应数据
        DifyService->>Cache: 缓存消息片段
        DifyService->>DifyService: 处理不同事件类型
        
        alt workflow_started
            DifyService->>DifyService: 记录工作流开始
        else node_started
            DifyService->>DifyService: 记录节点开始执行
        else node_finished
            DifyService->>DifyService: 记录节点执行完成
        else text_chunk
            DifyService->>Cache: 累积文本内容
        else workflow_finished
            DifyService->>DifyService: 工作流完成处理
            DifyService->>Controller: 返回最终结果
        else error
            DifyService->>DifyService: 错误处理
            DifyService->>Controller: 返回错误信息
        end
    end
```

### 4. 文档渲染服务 (ContractDocumentRenderService)

```mermaid
graph TD
    A[文档预览请求] --> B[ContractDocumentRenderServiceImpl]
    B --> C{文件类型检查}
    C -->|PDF| D[直接返回PDF URL]
    C -->|Word文档| E[检查PDF缓存]
    E --> F{缓存存在?}
    F -->|存在| G[返回缓存PDF URL]
    F -->|不存在| H[Word转PDF处理]
    
    subgraph "Word转PDF流程"
        H --> I{转换方式选择}
        I -->|LibreOffice| J[convertWithLibreOffice]
        I -->|Apache POI| K[convertWithApachePOI]
        
        J --> L[创建临时目录]
        L --> M[复制源文件到临时目录]
        M --> N[执行LibreOffice命令行转换]
        N --> O[检查转换结果]
        O --> P{转换成功?}
        P -->|成功| Q[移动PDF到缓存目录]
        P -->|失败| R[尝试Apache POI转换]
        
        K --> S[加载Word文档]
        S --> T[处理中文字体]
        T --> U[创建PDF文档]
        U --> V[逐页转换内容]
        V --> W[保存PDF文件]
        
        Q --> X[返回PDF访问URL]
        W --> X
        R --> K
    end
```

### 5. 异步任务状态管理

```mermaid
stateDiagram-v2
    [*] --> 待解析: 文件上传完成
    待解析 --> 解析中: 提交解析任务
    解析中 --> 解析成功: 解析完成
    解析中 --> 解析失败: 解析出错
    解析中 --> 已取消: 用户取消
    解析成功 --> [*]: 任务完成
    解析失败 --> [*]: 任务结束
    已取消 --> [*]: 任务终止
    
    note right of 解析中
        - 更新进度状态
        - 执行文档解析
        - 调用回调函数
    end note
    
    note right of 解析成功
        - 保存解析结果
        - 更新文件状态
        - 触发后续流程
    end note
```

## 🔧 核心服务组件说明

### ContractReviewTaskController
- **职责**: 处理合同审查任务的HTTP请求
- **关键方法**: `startReview(Long taskId)`
- **权限控制**: `@PreAuthorize("@ss.hasPermi('contract:review:start')")`

### ContractFileProcessServiceImpl
- **职责**: 统一的文件处理服务
- **核心功能**: 文件上传、校验、MD5去重、异步解析
- **事务管理**: `@Transactional(rollbackFor = Exception.class)`

### AsyncDocumentParseService
- **职责**: 异步文档解析服务
- **线程池**: `Executors.newFixedThreadPool(10)`
- **支持格式**: PDF、DOC、DOCX
- **回调机制**: `ParseCompletionCallback`

### DifyServiceImpl
- **职责**: AI工作流调用服务
- **调用方式**: HTTP流式请求
- **消息处理**: SSE事件流解析
- **缓存机制**: 消息片段缓存

### ContractDocumentRenderServiceImpl
- **职责**: 文档渲染和格式转换
- **转换引擎**: LibreOffice + Apache POI
- **缓存策略**: PDF文件缓存
- **中文支持**: 字体加载优化

## 📊 性能优化要点

1. **异步处理**: 文件解析和AI工作流调用都采用异步方式
2. **线程池管理**: 合理配置线程池大小避免资源竞争
3. **缓存策略**: PDF转换结果缓存，避免重复转换
4. **流式处理**: AI工作流采用SSE流式响应，提升用户体验
5. **事务控制**: 关键操作使用事务确保数据一致性

## 🚨 异常处理机制

1. **文件校验失败**: 返回具体错误信息
2. **解析任务失败**: 更新状态并记录错误日志
3. **AI工作流异常**: 捕获并处理各种API异常
4. **转换失败**: 多引擎备选方案
5. **超时处理**: 设置合理的超时时间和重试机制

---

**总结**: 整个流程采用了分层架构设计，通过异步处理、缓存优化、多引擎支持等技术手段，确保了系统的高性能和高可用性。每个环节都有完善的异常处理和状态管理机制。