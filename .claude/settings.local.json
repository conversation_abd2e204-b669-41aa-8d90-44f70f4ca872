{"permissions": {"allow": ["<PERSON><PERSON>", "Read", "Write", "Edit", "MultiEdit", "Glob", "Grep", "WebFetch", "WebSearch", "NotebookEdit", "Task", "TodoWrite", "BashOutput", "KillBash", "ExitPlanMode", "mcp__ide__getDiagnostics", "mcp__ide__executeCode", "mcp__promptx__promptx_init", "mcp__promptx__promptx_action", "mcp__filesystem__directory_tree", "mcp__filesystem__create_directory", "mcp__filesystem__write_file"], "deny": [], "ask": []}, "defaultMode": "bypassPermissions"}