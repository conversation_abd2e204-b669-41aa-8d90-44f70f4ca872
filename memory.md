# 操作与笔记

## 2025-09-16
- 初始化：确认使用 `/bin/bash --noprofile --norc` 执行命令以规避 `brew shellenv` 对 `/bin/ps` 的权限限制，成功列出仓库结构。
- 文档：查阅 `README.md`、`AGENTS.md`、`CLAUDE.md` 了解项目背景、技术栈与协作规范。
- 工具：使用 `date +%F` 获取当前日期，保证记录时间准确。
- 需求：阅读 `PRD/合同审查系统PRDV2.0.md`，掌握核心业务流程与角色定义。
- 目录：查看 `TunnyContract_BACK` 顶层内容，确认 ruoyi 模块划分与自定义扩展目录。
- 目录：列出仓库根目录条目，识别前后端子项目、文档与测试资源位置。
- 配置：阅读 `TunnyContract_BACK/pom.xml`，梳理 Spring Boot 2.5.15 + RuoYi 3.9.0 的依赖与 LangChain4j 引入情况。
- 资源：确认 `ruoyi-admin/src/main/resources` 结构，包括多环境配置、MyBatis 映射与日志设置。
- 代码：检查 `TunnyContract_BACK/src/main/java`，发现自定义 `com.ruoyi.contract` 目录目前仅含空 `utils` 子目录。
- 检索：使用 `rg` 定位 `contract` 相关控制器 `ContractRiskResultController`，确认后端存在合同风险结果模块接口。
- 检索：通过 `rg --files -g '*Contract*'` 盘点合同模块的 controller/domain/service/mapper，掌握后端自定义业务包结构。
- 代码：研读 `ruoyi-system/.../ContractReviewTaskServiceImpl.java`，了解任务编号生成、文件状态聚合等业务逻辑。
- 代码：查阅 `ContractDocumentRenderServiceImpl`，掌握文档预览转换流程（MinIO 缓存、Word→PDF 处理）。
- 配置：阅读 `ContractConversionConfig`，了解 LibreOffice 路径、临时目录、缓存目录的配置校验。 
- 工具：查看 `ContractFileUploadService`，掌握文件上传校验、MinIO存储及批量处理逻辑。
- 数据库：审阅 `sql/contract_system.sql`，获取合同分类、策略、条款、风险点、任务等表结构与索引设计。
- 配置：查看 `MinioConfig`，确认对象存储依赖及Bean初始化校验逻辑。
- 目录：列出 `TunnyContract_UI` 顶层结构，掌握前端构建配置与脚本目录分布。
- 配置：阅读前端 `package.json`，确认 Vue3 + Vite6 + Pinia + ElementPlus 及 lint/commit 工具链。
- 目录：检查 `TunnyContract_UI/src`，了解 api/components/views/store 等分层。
- 路由：阅读 `src/router/index.ts`，确认 hash 路由策略与守卫初始化流程。
- 状态：分析 `src/store/modules/menu.ts`，了解菜单状态管理及动态路由卸载逻辑。
- 接口：阅读 `src/api/contract/task.ts`，确认合同审查任务 API 封装与批量操作支持。
- 类型：查看 `src/types/contract/task.ts`，记录任务实体字段及枚举映射。
- 页面：阅读 `src/views/contract/task/index.vue`，掌握任务管理界面结构、文件进度展示与对话框交互。
- 构建：查看 `vite.config.ts`，确认多别名配置、自动导入、gzip 压缩与 dev proxy 设置。
- 网络：分析 `src/utils/http/index.ts`，了解 axios 封装、token 注入、参数序列化与 401 重登处理。
- 用户：阅读 `src/store/modules/user.ts`，掌握登录状态、权限缓存与登出流程。
- 评估：查阅 `TunnyTest/todo.md`，了解现有对文档转换流程的风险分析与优化建议。
