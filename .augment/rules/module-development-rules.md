---
type: "manual"
description: "功能模块开发规范 - TunnyContract 基于用户管理和角色管理模块提取的前后端开发规范，用于指导AI进行标准化的功能模块开发。"
---

# 功能模块开发规范 - TunnyContract

基于用户管理和角色管理模块提取的前后端开发规范，用于指导AI进行标准化的功能模块开发。

## 前端开发规范

### 1. 页面结构规范

#### 标准页面布局
```vue
<template>
  <div class="art-full-height">
    <!-- 左侧树形结构(可选) -->
    <div class="left-sidebar" v-if="hasTreeStructure">
      <ElCard class="art-table-card" shadow="never">
        <template #header>
          <span>{{ treeTitle }}</span>
        </template>
        <el-input
          v-model="treeSearchText"
          placeholder="请输入搜索关键词"
          clearable
          prefix-icon="Search"
          style="margin-bottom: 20px"
        />
        <el-tree
          ref="treeRef"
          :data="treeData"
          :props="{ label: 'label', children: 'children' }"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          node-key="id"
          highlight-current
          default-expand-all
          @node-click="handleNodeClick"
        />
      </ElCard>
    </div>

    <!-- 主内容区域 -->
    <div class="right-content art-full-height">
      <!-- 搜索条件区域 -->
      <SearchComponent v-model="searchForm" @search="handleSearch" @reset="resetSearchParams" />

      <!-- 表格区域 -->
      <ElCard class="art-table-card" shadow="never">
        <!-- 表格头部操作栏 -->
        <ArtTableHeader :columns="columnChecks" @refresh="refreshData">
          <template #left>
            <ElButton
              v-auth="'system:module:add'"
              type="primary"
              @click="showDialog('add')"
              v-ripple
            >
              新增{{ moduleName }}
            </ElButton>
            <ElButton
              v-auth="'system:module:edit'"
              type="success"
              :disabled="single"
              @click="() => handleUpdate()"
              v-ripple
            >
              修改
            </ElButton>
            <ElButton
              v-auth="'system:module:remove'"
              type="danger"
              :disabled="multiple"
              @click="() => handleDelete()"
              v-ripple
            >
              删除
            </ElButton>
            <ElButton v-auth="'system:module:export'" type="warning" @click="handleExport" v-ripple>
              导出
            </ElButton>
          </template>
        </ArtTableHeader>

        <!-- 数据表格 -->
        <ArtTable
          :loading="loading"
          :columns="columns"
          :data="dataList"
          :pagination="pagination"
          @pagination:size-change="handleSizeChange"
          @pagination:current-change="handleCurrentChange"
          @selection-change="handleSelectionChange"
        >
          <!-- 状态列插槽 -->
          <template #status="{ row }">
            <ElTag :type="row.status === '0' ? 'success' : 'danger'">
              {{ row.status === '0' ? '正常' : '停用' }}
            </ElTag>
          </template>

          <!-- 时间列插槽 -->
          <template #createTime="{ row }">
            <span>{{ formatTime(row.createTime) || '--' }}</span>
          </template>

          <!-- 操作列插槽 -->
          <template #action="{ row }">
            <ElButton
              v-auth="'system:module:edit'"
              type="primary"
              link
              @click="handleUpdate(row)"
            >
              修改
            </ElButton>
            <ElButton
              v-auth="'system:module:remove'"
              type="danger"
              link
              @click="handleDelete(row)"
            >
              删除
            </ElButton>
          </template>
        </ArtTable>
      </ElCard>
    </div>

    <!-- 表单对话框 -->
    <FormDialog
      v-model:visible="dialogVisible"
      :type="dialogType"
      :form-data="formData"
      @submit="handleSubmit"
    />
  </div>
</template>
```

### 2. useTable组合式函数规范

#### 标准useTable配置
```typescript
const {
  data: dataList,
  columns,
  columnChecks,
  loading,
  pagination,
  refreshData,
  handleSizeChange,
  handleCurrentChange,
  searchParams
} = useTable<EntityType>({
  core: {
    apiFn: ModuleApi.getModuleList,
    apiParams: {
      pageNum: 1,        // 若依分页参数
      pageSize: 10,      // 若依分页参数
      moduleName: '',    // 业务查询参数
      status: '',        // 状态查询参数
      beginTime: '',     // 开始时间
      endTime: ''        // 结束时间
    },
    paginationKey: {
      current: 'pageNum',  // 若依分页键名
      size: 'pageSize'     // 若依分页键名
    },
    columnsFactory: () => [
      {
        prop: 'moduleId',
        label: 'ID',
        width: 80
      },
      {
        prop: 'moduleName',
        label: '模块名称',
        sortable: true
      },
      {
        prop: 'status',
        label: '状态',
        width: 100,
        slot: 'status'
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 180,
        slot: 'createTime'
      },
      {
        prop: 'action',
        label: '操作',
        width: 150,
        slot: 'action',
        fixed: 'right'
      }
    ]
  }
})
```

### 3. 表格组件使用规范

#### ArtTable组件标准配置
```vue
<ArtTable
  :loading="loading"
  :columns="columns"
  :data="dataList"
  :pagination="pagination"
  row-key="id"
  @pagination:size-change="handleSizeChange"
  @pagination:current-change="handleCurrentChange"
  @selection-change="handleSelectionChange"
  :row-selection="{
    selectable: checkSelectable,
    preserveSelection: true
  }"
>
  <!-- 插槽内容 -->
</ArtTable>
```

#### 表格列配置规范
```typescript
interface TableColumn {
  prop: string          // 字段名
  label: string         // 列标题
  width?: number        // 列宽度
  minWidth?: number     // 最小宽度
  sortable?: boolean    // 是否可排序
  fixed?: 'left' | 'right'  // 固定列
  slot?: string         // 插槽名称
  formatter?: (row: any) => string  // 格式化函数
  align?: 'left' | 'center' | 'right'  // 对齐方式
}
```

### 4. 搜索组件规范

#### ArtSearchBar组件使用
```vue
<ArtSearchBar
  v-model="formFilters"
  :items="formItems"
  @reset="handleReset"
  @search="handleSearch"
/>
```

#### 搜索项配置
```typescript
const formItems = ref([
  {
    prop: 'moduleName',
    label: '模块名称',
    type: 'input',
    placeholder: '请输入模块名称'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '正常', value: '0' },
      { label: '停用', value: '1' }
    ]
  },
  {
    prop: 'dateRange',
    label: '创建时间',
    type: 'daterange',
    placeholder: ['开始日期', '结束日期']
  }
])
```

### 5. 树形组件规范

#### ElTree组件标准配置
```vue
<el-tree
  ref="treeRef"
  :data="treeData"
  :props="{ label: 'label', children: 'children' }"
  :expand-on-click-node="false"
  :filter-node-method="filterNode"
  node-key="id"
  highlight-current
  default-expand-all
  @node-click="handleNodeClick"
/>
```

#### 树形数据结构
```typescript
interface TreeNode {
  id: number | string
  label: string
  children?: TreeNode[]
  [key: string]: any
}
```

### 6. TypeScript类型定义规范

#### 实体类型定义
```typescript
// 基础实体接口
interface BaseEntity {
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  delFlag?: string
  attr1?: string
  attr2?: string
  attr3?: string
}

// 业务实体接口
interface ModuleEntity extends BaseEntity {
  moduleId?: number
  moduleName: string
  moduleKey: string
  status?: string
  remark?: string
}

// 查询参数接口
interface ModuleQueryParams extends RuoyiQueryParams {
  moduleName?: string
  moduleKey?: string
  status?: string
  beginTime?: string
  endTime?: string
}

// 表单数据接口
interface ModuleFormData {
  moduleId?: number
  moduleName: string
  moduleKey: string
  status: string
  remark?: string
}
```

### 7. API调用规范

#### API类定义
```typescript
export class ModuleApi {
  /**
   * 查询模块列表
   */
  static async getModuleList(params: ModuleQueryParams): Promise<RuoyiResponse<ModuleEntity>> {
    return http.get('/system/module/list', { params })
  }

  /**
   * 查询模块详情
   */
  static async getModule(moduleId: number): Promise<RuoyiResponse<ModuleEntity>> {
    return http.get(`/system/module/${moduleId}`)
  }

  /**
   * 新增模块
   */
  static async addModule(data: Partial<ModuleEntity>): Promise<RuoyiResponse> {
    return http.post('/system/module', data)
  }

  /**
   * 修改模块
   */
  static async updateModule(data: ModuleEntity): Promise<RuoyiResponse> {
    return http.put('/system/module', data)
  }

  /**
   * 删除模块
   */
  static async deleteModule(moduleIds: number[]): Promise<RuoyiResponse> {
    return http.delete(`/system/module/${moduleIds.join(',')}`)
  }

  /**
   * 导出模块
   */
  static async exportModule(params: ModuleQueryParams): Promise<Blob> {
    return http.download('/system/module/export', { params })
  }
}
```

## 后端开发规范

### 1. Controller层规范

#### 标准Controller结构
```java
@RestController
@RequestMapping("/system/module")
public class SysModuleController extends BaseController {

    @Autowired
    private ISysModuleService moduleService;

    /**
     * 查询模块列表
     */
    @PreAuthorize("@ss.hasPermi('system:module:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysModule module) {
        startPage();
        List<SysModule> list = moduleService.selectModuleList(module);
        return getDataTable(list);
    }

    /**
     * 导出模块列表
     */
    @PreAuthorize("@ss.hasPermi('system:module:export')")
    @Log(title = "模块管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysModule module) {
        List<SysModule> list = moduleService.selectModuleList(module);
        ExcelUtil<SysModule> util = new ExcelUtil<SysModule>(SysModule.class);
        util.exportExcel(response, list, "模块数据");
    }

    /**
     * 根据模块编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:module:query')")
    @GetMapping(value = "/{moduleId}")
    public AjaxResult getInfo(@PathVariable("moduleId") Long moduleId) {
        return success(moduleService.selectModuleByModuleId(moduleId));
    }

    /**
     * 新增模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:add')")
    @Log(title = "模块管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysModule module) {
        if (!moduleService.checkModuleNameUnique(module)) {
            return error("新增模块'" + module.getModuleName() + "'失败，模块名称已存在");
        }
        module.setCreateBy(getUsername());
        return toAjax(moduleService.insertModule(module));
    }

    /**
     * 修改模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:edit')")
    @Log(title = "模块管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysModule module) {
        if (!moduleService.checkModuleNameUnique(module)) {
            return error("修改模块'" + module.getModuleName() + "'失败，模块名称已存在");
        }
        module.setUpdateBy(getUsername());
        return toAjax(moduleService.updateModule(module));
    }

    /**
     * 删除模块
     */
    @PreAuthorize("@ss.hasPermi('system:module:remove')")
    @Log(title = "模块管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{moduleIds}")
    public AjaxResult remove(@PathVariable Long[] moduleIds) {
        return toAjax(moduleService.deleteModuleByModuleIds(moduleIds));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:module:edit')")
    @Log(title = "模块管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysModule module) {
        module.setUpdateBy(getUsername());
        return toAjax(moduleService.updateModuleStatus(module));
    }
}
```

#### 权限注解规范
```java
// 权限注解格式：@PreAuthorize("@ss.hasPermi('模块:功能:操作')")
@PreAuthorize("@ss.hasPermi('system:module:list')")    // 查询权限
@PreAuthorize("@ss.hasPermi('system:module:query')")   // 详情权限
@PreAuthorize("@ss.hasPermi('system:module:add')")     // 新增权限
@PreAuthorize("@ss.hasPermi('system:module:edit')")    // 修改权限
@PreAuthorize("@ss.hasPermi('system:module:remove')")  // 删除权限
@PreAuthorize("@ss.hasPermi('system:module:export')")  // 导出权限
@PreAuthorize("@ss.hasPermi('system:module:import')")  // 导入权限
```

#### 操作日志注解规范
```java
// 日志注解格式
@Log(title = "模块名称", businessType = BusinessType.操作类型)

// 操作类型枚举
BusinessType.INSERT    // 新增
BusinessType.UPDATE    // 修改
BusinessType.DELETE    // 删除
BusinessType.GRANT     // 授权
BusinessType.EXPORT    // 导出
BusinessType.IMPORT    // 导入
BusinessType.FORCE     // 强退
BusinessType.GENCODE   // 生成代码
BusinessType.CLEAN     // 清空数据
```

#### 分页处理规范
```java
/**
 * 分页查询标准流程
 */
@GetMapping("/list")
public TableDataInfo list(SysModule module) {
    // 1. 开启分页（从PageHelper获取分页参数）
    startPage();

    // 2. 执行查询
    List<SysModule> list = moduleService.selectModuleList(module);

    // 3. 返回分页结果
    return getDataTable(list);
}

// BaseController中的分页方法
protected void startPage() {
    PageUtils.startPage();
}

protected TableDataInfo getDataTable(List<?> list) {
    TableDataInfo rspData = new TableDataInfo();
    rspData.setCode(HttpStatus.SUCCESS);
    rspData.setMsg("查询成功");
    rspData.setRows(list);
    rspData.setTotal(new PageInfo(list).getTotal());
    return rspData;
}
```

#### 返回值规范
```java
// 成功返回
return success();                           // 操作成功，无数据
return success(data);                       // 操作成功，返回数据
return success("操作成功");                  // 操作成功，自定义消息

// 失败返回
return error();                             // 操作失败，默认消息
return error("操作失败的具体原因");           // 操作失败，自定义消息

// 条件返回
return toAjax(rows);                        // 根据影响行数返回成功/失败

// 分页返回
return getDataTable(list);                  // 返回分页数据

// 标准返回格式
{
    "code": 200,        // 状态码：200成功，500失败
    "msg": "操作成功",   // 消息
    "data": {},         // 数据（可选）
    "rows": [],         // 列表数据（分页时使用）
    "total": 0          // 总数（分页时使用）
}
```

### 2. Service层规范

#### Service接口定义
```java
public interface ISysModuleService {
    /**
     * 查询模块
     */
    public SysModule selectModuleByModuleId(Long moduleId);

    /**
     * 查询模块列表
     */
    public List<SysModule> selectModuleList(SysModule module);

    /**
     * 新增模块
     */
    public int insertModule(SysModule module);

    /**
     * 修改模块
     */
    public int updateModule(SysModule module);

    /**
     * 批量删除模块
     */
    public int deleteModuleByModuleIds(Long[] moduleIds);

    /**
     * 删除模块信息
     */
    public int deleteModuleByModuleId(Long moduleId);

    /**
     * 校验模块名称是否唯一
     */
    public boolean checkModuleNameUnique(SysModule module);

    /**
     * 修改模块状态
     */
    public int updateModuleStatus(SysModule module);
}
```

#### Service实现类规范
```java
@Service
public class SysModuleServiceImpl implements ISysModuleService {

    @Autowired
    private SysModuleMapper moduleMapper;

    /**
     * 查询模块
     */
    @Override
    public SysModule selectModuleByModuleId(Long moduleId) {
        return moduleMapper.selectModuleByModuleId(moduleId);
    }

    /**
     * 查询模块列表
     */
    @Override
    public List<SysModule> selectModuleList(SysModule module) {
        return moduleMapper.selectModuleList(module);
    }

    /**
     * 新增模块
     */
    @Override
    @Transactional
    public int insertModule(SysModule module) {
        module.setCreateTime(DateUtils.getNowDate());
        return moduleMapper.insertModule(module);
    }

    /**
     * 修改模块
     */
    @Override
    @Transactional
    public int updateModule(SysModule module) {
        module.setUpdateTime(DateUtils.getNowDate());
        return moduleMapper.updateModule(module);
    }

    /**
     * 批量删除模块
     */
    @Override
    @Transactional
    public int deleteModuleByModuleIds(Long[] moduleIds) {
        return moduleMapper.deleteModuleByModuleIds(moduleIds);
    }

    /**
     * 删除模块信息
     */
    @Override
    @Transactional
    public int deleteModuleByModuleId(Long moduleId) {
        return moduleMapper.deleteModuleByModuleId(moduleId);
    }

    /**
     * 校验模块名称是否唯一
     */
    @Override
    public boolean checkModuleNameUnique(SysModule module) {
        Long moduleId = StringUtils.isNull(module.getModuleId()) ? -1L : module.getModuleId();
        SysModule info = moduleMapper.checkModuleNameUnique(module.getModuleName());
        if (StringUtils.isNotNull(info) && info.getModuleId().longValue() != moduleId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 修改模块状态
     */
    @Override
    public int updateModuleStatus(SysModule module) {
        return moduleMapper.updateModule(module);
    }
}
```

### 3. 实体类规范

#### 标准实体类定义
```java
/**
 * 模块对象 sys_module
 */
public class SysModule extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 模块ID */
    private Long moduleId;

    /** 模块名称 */
    @Excel(name = "模块名称")
    private String moduleName;

    /** 模块标识 */
    @Excel(name = "模块标识")
    private String moduleKey;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer moduleSort;

    /** 模块状态（0正常 1停用） */
    @Excel(name = "模块状态", readConverterExp = "0=正常,1=停用")
    private String status;

    // getter/setter方法
    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }

    @NotBlank(message = "模块名称不能为空")
    @Size(min = 0, max = 30, message = "模块名称长度不能超过30个字符")
    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    @NotBlank(message = "模块标识不能为空")
    @Size(min = 0, max = 100, message = "模块标识长度不能超过100个字符")
    public String getModuleKey() {
        return moduleKey;
    }

    public void setModuleKey(String moduleKey) {
        this.moduleKey = moduleKey;
    }

    // 其他getter/setter方法...

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("moduleId", getModuleId())
            .append("moduleName", getModuleName())
            .append("moduleKey", getModuleKey())
            .append("moduleSort", getModuleSort())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
```

#### 实体类注解规范
```java
// 验证注解
@NotBlank(message = "字段不能为空")
@NotNull(message = "字段不能为null")
@Size(min = 0, max = 50, message = "字段长度不能超过50个字符")
@Pattern(regexp = "^[a-zA-Z0-9_]*$", message = "字段只能包含字母、数字和下划线")

// Excel导出注解
@Excel(name = "列名")
@Excel(name = "状态", readConverterExp = "0=正常,1=停用")
@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")

// 数据库字段映射注解（MyBatis-Plus）
@TableId(value = "module_id", type = IdType.AUTO)
@TableField("module_name")
```

### 4. Mapper层规范

#### Mapper接口定义
```java
public interface SysModuleMapper {
    /**
     * 查询模块
     */
    public SysModule selectModuleByModuleId(Long moduleId);

    /**
     * 查询模块列表
     */
    public List<SysModule> selectModuleList(SysModule module);

    /**
     * 新增模块
     */
    public int insertModule(SysModule module);

    /**
     * 修改模块
     */
    public int updateModule(SysModule module);

    /**
     * 删除模块
     */
    public int deleteModuleByModuleId(Long moduleId);

    /**
     * 批量删除模块
     */
    public int deleteModuleByModuleIds(Long[] moduleIds);

    /**
     * 校验模块名称
     */
    public SysModule checkModuleNameUnique(String moduleName);
}
```

#### Mapper XML规范
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysModuleMapper">

    <resultMap type="SysModule" id="SysModuleResult">
        <id     property="moduleId"       column="module_id"      />
        <result property="moduleName"     column="module_name"    />
        <result property="moduleKey"      column="module_key"     />
        <result property="moduleSort"     column="module_sort"    />
        <result property="status"         column="status"         />
        <result property="createBy"       column="create_by"      />
        <result property="createTime"     column="create_time"    />
        <result property="updateBy"       column="update_by"      />
        <result property="updateTime"     column="update_time"    />
        <result property="remark"         column="remark"         />
    </resultMap>

    <sql id="selectModuleVo">
        select module_id, module_name, module_key, module_sort, status,
               create_by, create_time, update_by, update_time, remark
        from sys_module
    </sql>

    <select id="selectModuleList" parameterType="SysModule" resultMap="SysModuleResult">
        <include refid="selectModuleVo"/>
        <where>
            <if test="moduleName != null and moduleName != ''">
                AND module_name like concat('%', #{moduleName}, '%')
            </if>
            <if test="moduleKey != null and moduleKey != ''">
                AND module_key like concat('%', #{moduleKey}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by module_sort, module_id
    </select>

    <select id="selectModuleByModuleId" parameterType="Long" resultMap="SysModuleResult">
        <include refid="selectModuleVo"/>
        where module_id = #{moduleId}
    </select>

    <select id="checkModuleNameUnique" parameterType="String" resultMap="SysModuleResult">
        <include refid="selectModuleVo"/>
        where module_name = #{moduleName} limit 1
    </select>

    <insert id="insertModule" parameterType="SysModule" useGeneratedKeys="true" keyProperty="moduleId">
        insert into sys_module
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">module_name,</if>
            <if test="moduleKey != null and moduleKey != ''">module_key,</if>
            <if test="moduleSort != null">module_sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">#{moduleName},</if>
            <if test="moduleKey != null and moduleKey != ''">#{moduleKey},</if>
            <if test="moduleSort != null">#{moduleSort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateModule" parameterType="SysModule">
        update sys_module
        <trim prefix="SET" suffixOverrides=",">
            <if test="moduleName != null and moduleName != ''">module_name = #{moduleName},</if>
            <if test="moduleKey != null and moduleKey != ''">module_key = #{moduleKey},</if>
            <if test="moduleSort != null">module_sort = #{moduleSort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where module_id = #{moduleId}
    </update>

    <delete id="deleteModuleByModuleId" parameterType="Long">
        delete from sys_module where module_id = #{moduleId}
    </delete>

    <delete id="deleteModuleByModuleIds" parameterType="String">
        delete from sys_module where module_id in
        <foreach item="moduleId" collection="array" open="(" separator="," close=")">
            #{moduleId}
        </foreach>
    </delete>
</mapper>
```

### 5. 数据库表设计规范

#### 标准表结构
```sql
CREATE TABLE `sys_module` (
  `module_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模块ID',
  `module_name` varchar(30) NOT NULL COMMENT '模块名称',
  `module_key` varchar(100) NOT NULL COMMENT '模块标识',
  `module_sort` int(4) DEFAULT '0' COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '模块状态（0正常 1停用）',

  -- 标准审计字段
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',

  -- 扩展字段
  `attr1` varchar(255) DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) DEFAULT NULL COMMENT '保留字段3',

  PRIMARY KEY (`module_id`),
  UNIQUE KEY `uk_module_name` (`module_name`),
  UNIQUE KEY `uk_module_key` (`module_key`),
  KEY `idx_module_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='模块信息表';
```

### 6. 开发规范总结

#### 前端开发检查清单
- [ ] 使用useTable组合式函数管理表格数据
- [ ] 配置ArtTable组件的标准属性
- [ ] 实现ArtSearchBar搜索组件
- [ ] 定义完整的TypeScript类型
- [ ] 使用v-auth指令控制按钮权限
- [ ] 配置表格列的插槽和格式化
- [ ] 实现标准的CRUD操作方法
- [ ] 添加适当的加载状态和错误处理

#### 后端开发检查清单
- [ ] Controller继承BaseController
- [ ] 使用@PreAuthorize注解控制权限
- [ ] 使用@Log注解记录操作日志
- [ ] 实现标准的分页查询方法
- [ ] Service层使用@Transactional注解
- [ ] 实体类继承BaseEntity
- [ ] 添加数据验证注解
- [ ] Mapper XML使用标准的SQL模板
- [ ] 数据库表包含标准审计字段
- [ ] 实现唯一性校验方法

#### 命名规范
- **Controller**: `Sys[Module]Controller`
- **Service**: `ISys[Module]Service` / `Sys[Module]ServiceImpl`
- **Mapper**: `Sys[Module]Mapper`
- **Entity**: `Sys[Module]`
- **表名**: `sys_[module]`
- **权限标识**: `system:[module]:[operation]`
