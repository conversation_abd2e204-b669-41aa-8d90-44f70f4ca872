---
type: "agent_requested"
description: "后端开发规则 - TunnyContract Backend"
---

# 后端开发规则 - TunnyContract Backend

## 技术栈

- **框架**: Spring Boot 2.5.15
- **安全**: Spring Security 5.7.12
- **ORM**: MyBatis + PageHelper
- **数据库**: MySQL 5.7+ + Redis 6.0+
- **文件存储**: MinIO 8.2.1
- **文档处理**: Apache PDFBox 2.0.30 + Apache POI 5.2.5
- **智能分析**: Dify Java Client 1.1.7
- **构建工具**: Maven 3.6+
- **Java版本**: **严格使用Java 8语法**

## 项目结构

```
TunnyContract_BACK/
├── ruoyi-admin/              # 主应用模块
├── ruoyi-common/             # 通用工具和常量
├── ruoyi-framework/          # 核心框架组件
├── ruoyi-system/             # 系统管理模块
├── ruoyi-generator/          # 代码生成工具
├── ruoyi-quartz/             # 定时任务管理
├── sql/                      # 数据库脚本
└── pom.xml                   # Maven主配置
```

## 开发规范

### 1. Java 8语法约束

**严格禁止使用Java 8以上版本特性**：
- ❌ `var` 关键字 (Java 10+)
- ❌ `switch` 表达式 (Java 14+)
- ❌ 文本块 (Java 15+)
- ❌ `record` 类型 (Java 14+)
- ❌ 模式匹配 (Java 16+)

**推荐使用Java 8特性**：
- ✅ Lambda表达式
- ✅ Stream API
- ✅ Optional类
- ✅ 新的日期时间API
- ✅ 接口默认方法

### 2. 包结构规范

```
com.ruoyi
├── common/                   # 通用模块
│   ├── annotation/          # 自定义注解
│   ├── config/              # 配置类
│   ├── constant/            # 常量定义
│   ├── core/                # 核心组件
│   ├── enums/               # 枚举类
│   ├── exception/           # 异常处理
│   ├── filter/              # 过滤器
│   ├── utils/               # 工具类
│   └── xss/                 # XSS防护
├── framework/               # 框架模块
│   ├── aspectj/             # AOP切面
│   ├── config/              # 框架配置
│   ├── datasource/          # 数据源配置
│   ├── interceptor/         # 拦截器
│   ├── manager/             # 异步任务管理
│   ├── security/            # 安全配置
│   └── web/                 # Web配置
├── system/                  # 系统模块
│   ├── controller/          # 控制器
│   ├── domain/              # 实体类
│   ├── mapper/              # Mapper接口
│   ├── service/             # 服务层
│   └── service/impl/        # 服务实现
└── [业务模块]/              # 业务模块
    ├── controller/
    ├── domain/
    ├── mapper/
    ├── service/
    └── service/impl/
```

### 3. 命名规范

#### 类命名
- **Controller**: `[Module]Controller`
- **Service**: `I[Module]Service` (接口) / `[Module]ServiceImpl` (实现)
- **Mapper**: `[Module]Mapper`
- **Entity**: `[Module]` (如 `User`, `ContractInfo`)
- **DTO**: `[Module]DTO`
- **VO**: `[Module]VO`

#### 方法命名
- **查询**: `select*`, `get*`, `find*`, `list*`
- **新增**: `insert*`, `add*`, `save*`
- **修改**: `update*`, `edit*`, `modify*`
- **删除**: `delete*`, `remove*`

### 4. 实体类规范

#### BaseEntity基类
```java
public class BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 备用字段1 */
    private String attr1;

    /** 备用字段2 */
    private String attr2;

    /** 备用字段3 */
    private String attr3;

    // getter/setter方法
}
```

#### 实体类示例
```java
@TableName("contract_info")
public class ContractInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 合同ID */
    @TableId(value = "contract_id", type = IdType.AUTO)
    private Long contractId;

    /** 合同名称 */
    @Excel(name = "合同名称")
    private String contractName;

    /** 合同类型 */
    @Excel(name = "合同类型", readConverterExp = "0=采购合同,1=销售合同,2=服务合同")
    private String contractType;

    /** 合同状态 */
    @Excel(name = "合同状态", readConverterExp = "0=草稿,1=审核中,2=已生效,3=已终止")
    private String status;

    // getter/setter方法
}
```

### 5. Controller层规范

```java
@RestController
@RequestMapping("/contract/info")
public class ContractInfoController extends BaseController {
    
    @Autowired
    private IContractInfoService contractInfoService;

    /**
     * 查询合同信息列表
     */
    @PreAuthorize("@ss.hasPermi('contract:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractInfo contractInfo) {
        startPage();
        List<ContractInfo> list = contractInfoService.selectContractInfoList(contractInfo);
        return getDataTable(list);
    }

    /**
     * 获取合同信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:query')")
    @GetMapping(value = "/{contractId}")
    public AjaxResult getInfo(@PathVariable("contractId") Long contractId) {
        return AjaxResult.success(contractInfoService.selectContractInfoByContractId(contractId));
    }

    /**
     * 新增合同信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:add')")
    @Log(title = "合同信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractInfo contractInfo) {
        return toAjax(contractInfoService.insertContractInfo(contractInfo));
    }

    /**
     * 修改合同信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:edit')")
    @Log(title = "合同信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractInfo contractInfo) {
        return toAjax(contractInfoService.updateContractInfo(contractInfo));
    }

    /**
     * 删除合同信息
     */
    @PreAuthorize("@ss.hasPermi('contract:info:remove')")
    @Log(title = "合同信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{contractIds}")
    public AjaxResult remove(@PathVariable Long[] contractIds) {
        return toAjax(contractInfoService.deleteContractInfoByContractIds(contractIds));
    }
}
```

### 6. Service层规范

#### 接口定义
```java
public interface IContractInfoService {
    /**
     * 查询合同信息
     */
    public ContractInfo selectContractInfoByContractId(Long contractId);

    /**
     * 查询合同信息列表
     */
    public List<ContractInfo> selectContractInfoList(ContractInfo contractInfo);

    /**
     * 新增合同信息
     */
    public int insertContractInfo(ContractInfo contractInfo);

    /**
     * 修改合同信息
     */
    public int updateContractInfo(ContractInfo contractInfo);

    /**
     * 批量删除合同信息
     */
    public int deleteContractInfoByContractIds(Long[] contractIds);

    /**
     * 删除合同信息信息
     */
    public int deleteContractInfoByContractId(Long contractId);
}
```

#### 实现类
```java
@Service
public class ContractInfoServiceImpl implements IContractInfoService {
    
    @Autowired
    private ContractInfoMapper contractInfoMapper;

    @Override
    public ContractInfo selectContractInfoByContractId(Long contractId) {
        return contractInfoMapper.selectContractInfoByContractId(contractId);
    }

    @Override
    public List<ContractInfo> selectContractInfoList(ContractInfo contractInfo) {
        return contractInfoMapper.selectContractInfoList(contractInfo);
    }

    @Override
    public int insertContractInfo(ContractInfo contractInfo) {
        contractInfo.setCreateTime(DateUtils.getNowDate());
        return contractInfoMapper.insertContractInfo(contractInfo);
    }

    @Override
    public int updateContractInfo(ContractInfo contractInfo) {
        contractInfo.setUpdateTime(DateUtils.getNowDate());
        return contractInfoMapper.updateContractInfo(contractInfo);
    }

    @Override
    public int deleteContractInfoByContractIds(Long[] contractIds) {
        return contractInfoMapper.deleteContractInfoByContractIds(contractIds);
    }

    @Override
    public int deleteContractInfoByContractId(Long contractId) {
        return contractInfoMapper.deleteContractInfoByContractId(contractId);
    }
}
```

### 7. Mapper层规范

#### Mapper接口
```java
public interface ContractInfoMapper {
    /**
     * 查询合同信息
     */
    public ContractInfo selectContractInfoByContractId(Long contractId);

    /**
     * 查询合同信息列表
     */
    public List<ContractInfo> selectContractInfoList(ContractInfo contractInfo);

    /**
     * 新增合同信息
     */
    public int insertContractInfo(ContractInfo contractInfo);

    /**
     * 修改合同信息
     */
    public int updateContractInfo(ContractInfo contractInfo);

    /**
     * 删除合同信息
     */
    public int deleteContractInfoByContractId(Long contractId);

    /**
     * 批量删除合同信息
     */
    public int deleteContractInfoByContractIds(Long[] contractIds);
}
```

### 8. 异常处理

#### 自定义异常
```java
public class ContractException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    protected final String message;

    public ContractException(String message) {
        this.message = message;
    }

    public ContractException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
```

### 9. 配置管理

#### 应用配置
```yaml
# 合同处理配置
contract:
  # 文件上传配置
  upload:
    max-file-size: 50MB
    allowed-types: pdf,doc,docx
    storage-path: /contract/files
  
  # 解析配置
  parse:
    thread-pool-size: 10
    timeout: 300000
    batch-size: 100
```

### 10. 日志规范

#### 日志级别
- **ERROR**: 系统错误、异常
- **WARN**: 警告信息
- **INFO**: 重要业务信息
- **DEBUG**: 调试信息

#### 日志格式
```java
private static final Logger log = LoggerFactory.getLogger(ContractInfoServiceImpl.class);

public int insertContractInfo(ContractInfo contractInfo) {
    log.info("新增合同信息：{}", contractInfo.getContractName());
    try {
        contractInfo.setCreateTime(DateUtils.getNowDate());
        int result = contractInfoMapper.insertContractInfo(contractInfo);
        log.info("合同信息新增成功，ID：{}", contractInfo.getContractId());
        return result;
    } catch (Exception e) {
        log.error("新增合同信息失败：{}", e.getMessage(), e);
        throw new ContractException("新增合同信息失败");
    }
}
```

### 11. 测试规范

#### 单元测试
```java
@SpringBootTest
@RunWith(SpringRunner.class)
public class ContractInfoServiceTest {
    
    @Autowired
    private IContractInfoService contractInfoService;

    @Test
    public void testInsertContractInfo() {
        ContractInfo contractInfo = new ContractInfo();
        contractInfo.setContractName("测试合同");
        contractInfo.setContractType("0");
        contractInfo.setStatus("0");
        
        int result = contractInfoService.insertContractInfo(contractInfo);
        Assert.assertTrue(result > 0);
    }
}
```

### 12. 安全规范

#### 权限控制
- 使用`@PreAuthorize`注解进行权限控制
- 遵循最小权限原则
- 敏感操作记录日志

#### 数据校验
- 使用`@Valid`注解进行参数校验
- 自定义校验注解
- SQL注入防护

### 13. 性能优化

#### 数据库优化
- 合理使用索引
- 避免N+1查询
- 分页查询优化
- 连接池配置

#### 缓存策略
- Redis缓存热点数据
- 本地缓存配置
- 缓存更新策略
