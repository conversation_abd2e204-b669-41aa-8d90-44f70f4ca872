---
type: "manual"
description: "数据库设计规范 - TunnyContract"
---

# 数据库设计规范 - TunnyContract

## 数据库基础配置

### 数据库设置
- **数据库编码**: UTF8MB4_BIN
- **表引擎**: InnoDB
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_bin
- **主键策略**: 自增ID + 业务唯一索引

## 标准字段规范

### 必选审计字段
所有业务表**必须**包含以下标准字段：

```sql
-- 主键ID
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',

-- 时间审计字段
`create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',

-- 用户审计字段  
`create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
`update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',

-- 软删除标识
`del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
```

### 推荐扩展字段
```sql
-- 预留扩展字段，便于后续业务扩展
`attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
`attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2', 
`attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
```

### 合同业务必需字段
合同相关表还必须包含：

```sql
-- 文件处理相关
`file_md5` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件MD5校验',
`parse_status` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '解析状态：0-未解析，1-解析中，2-解析完成，3-解析失败',
`upload_status` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '上传状态：0-未上传，1-上传成功，2-上传失败',
`task_id` bigint(20) DEFAULT NULL COMMENT '关联审查任务ID',
```

## 完整表结构模板

```sql
CREATE TABLE `业务表名` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 业务字段
  `业务字段名` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '业务字段说明',
  
  -- 标准审计字段（必选）
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段（推荐）
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='业务表说明';
```

## 命名规范

### 表命名
- **格式**: `模块_业务表名`
- **示例**: `sys_user`、`contract_info`、`order_detail`
- **规则**: 全小写，单词间用下划线分隔

### 字段命名
- **格式**: 全小写，单词间用下划线分隔
- **布尔字段**: 以 `is_`、`has_`、`can_` 开头
- **状态字段**: 以 `status`、`state`、`flag` 结尾
- **时间字段**: 以 `_time` 结尾
- **ID字段**: 以 `_id` 结尾

### 索引命名
- **主键**: `PRIMARY KEY`
- **唯一索引**: `uk_字段名`
- **普通索引**: `idx_字段名`
- **组合索引**: `idx_字段1_字段2`

## 数据类型规范

### 数值类型
- **主键ID**: `bigint(20)` - 支持大数据量
- **状态标识**: `char(1)` - 固定长度状态
- **金额字段**: `decimal(10,2)` - 精确计算
- **数量字段**: `int(11)` - 整数数量

### 字符类型
- **短文本**: `varchar(50/100/255)` - 根据实际需要
- **长文本**: `text` - 大段文本内容
- **JSON数据**: `json` - 结构化数据存储

### 时间类型
- **时间戳**: `timestamp` - 自动维护时间
- **日期**: `date` - 仅日期不含时间
- **日期时间**: `datetime` - 完整日期时间

## 业务规范

### 软删除
- 使用 `del_flag` 字段实现软删除
- `0` 表示正常，`2` 表示已删除
- 查询时必须添加 `del_flag = '0'` 条件

### 审计追踪
- `create_by`/`update_by` 存储用户ID或用户名
- `create_time`/`update_time` 自动维护时间戳
- 重要业务操作应记录操作日志

### 扩展设计
- 预留 `attr1`、`attr2`、`attr3` 字段用于业务扩展
- 复杂扩展需求可使用JSON字段存储
- 考虑未来数据迁移和版本兼容性

## MyBatis 集成

### BaseEntity 基类
```java
public class BaseEntity implements Serializable {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    private String createBy;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")  
    private Date updateTime;
    
    private String updateBy;
    
    private String delFlag = "0";
    
    private String attr1;
    private String attr2;
    private String attr3;
}
```

### Mapper XML 模板
```xml
<!-- 标准查询条件 -->
<select id="selectList" resultMap="BaseResultMap">
    SELECT * FROM 表名 
    WHERE del_flag = '0'
    <if test="createBy != null and createBy != ''">
        AND create_by = #{createBy}
    </if>
    ORDER BY create_time DESC
</select>

<!-- 软删除更新 -->
<update id="delete">
    UPDATE 表名 SET 
        del_flag = '2',
        update_time = NOW(),
        update_by = #{updateBy}
    WHERE id = #{id}
</update>
```

## 索引设计

### 基础索引
- 主键索引：自动创建
- 创建时间索引：`idx_create_time`
- 删除标志索引：`idx_del_flag`

### 业务索引
- 根据查询频率创建
- 避免过多索引影响写入性能
- 定期分析索引使用情况

### 组合索引
- 遵循最左前缀原则
- 区分度高的字段在前
- 考虑覆盖索引优化

## 性能优化

### 查询优化
- 避免SELECT *
- 合理使用LIMIT
- 优化WHERE条件
- 使用EXPLAIN分析执行计划

### 表设计优化
- 合理的字段长度
- 适当的数据类型
- 避免NULL值
- 考虑分区表

### 连接优化
- 合理使用JOIN
- 避免笛卡尔积
- 优化子查询

## 数据安全

### 敏感数据
- 密码字段加密存储
- 个人信息脱敏处理
- 重要数据备份策略

### 权限控制
- 数据库用户权限最小化
- 敏感表访问控制
- 操作日志记录

## 开发检查清单

在创建新表或修改表结构时，请确认：

- [ ] 包含所有必需的审计字段
- [ ] 遵循命名规范
- [ ] 设置适当的索引
- [ ] 添加字段注释
- [ ] 考虑软删除需求  
- [ ] 预留扩展字段
- [ ] 对应Entity类继承BaseEntity
- [ ] Mapper支持软删除条件
- [ ] 考虑数据迁移方案
- [ ] 性能测试验证

## 常见问题

### Q: 为什么使用软删除？
A: 保留数据完整性，支持数据恢复，满足审计要求。

### Q: 扩展字段如何使用？
A: 用于存储临时业务需求，避免频繁修改表结构。

### Q: 如何处理大表？
A: 考虑分区、分表、归档等策略。

### Q: 索引过多的影响？
A: 影响写入性能，增加存储空间，需要平衡。
