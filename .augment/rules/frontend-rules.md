---
type: "manual"
description: "前端开发规则 - TunnyContract UI"
---

# 前端开发规则 - TunnyContract UI

## 技术栈

- **框架**: Vue 3.5.12 + Composition API
- **语言**: TypeScript 5.6.3 (严格模式)
- **构建工具**: Vite 6.1.0
- **UI框架**: Element Plus 2.10.2
- **状态管理**: Pinia 3.0.2 + 持久化插件
- **路由**: Vue Router 4.4.2
- **HTTP客户端**: Axios 1.7.5

## 项目结构

```
TunnyContract_UI/
├── src/
│   ├── api/                   # API接口定义
│   ├── assets/                # 静态资源
│   ├── components/            # 组件库
│   │   ├── core/             # 可复用UI组件
│   │   └── custom/           # 项目特定组件
│   ├── locales/              # 国际化文件
│   ├── router/               # 路由配置
│   ├── store/                # Pinia状态管理
│   ├── styles/               # 样式文件
│   ├── types/                # TypeScript类型定义
│   ├── utils/                # 工具函数
│   └── views/                # 页面组件
├── public/                   # 公共静态资源
└── scripts/                  # 构建脚本
```

## 开发规范

### 1. 命名约定

#### 文件命名
- **组件**: PascalCase (如 `UserCard.vue`)
- **组合式函数**: camelCase，以 `use` 开头 (如 `useTable.ts`)
- **类型定义**: PascalCase (如 `UserInfo.ts`)
- **API模块**: PascalCase 类 (如 `UserApi`)
- **页面组件**: PascalCase (如 `UserManagement.vue`)

#### 变量命名
- **变量/函数**: camelCase
- **常量**: UPPER_SNAKE_CASE
- **接口**: PascalCase，以 `I` 开头
- **类型**: PascalCase

### 2. 代码风格

#### ESLint配置
- 强制使用单引号
- 禁用分号
- 2空格缩进
- 最大行长度120字符

#### TypeScript规范
- 启用严格类型检查
- 所有API响应类型扩展 `RuoyiResponse<T>`
- 避免使用 `any` 类型
- 优先使用接口而非类型别名

### 3. 组件开发

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from './types'

// 接口定义
interface Props {
  title: string
  data?: any[]
}

// Props和Emits
const props = withDefaults(defineProps<Props>(), {
  data: () => []
})

const emit = defineEmits<{
  change: [value: string]
}>()

// 响应式数据
const loading = ref(false)

// 计算属性
const computedValue = computed(() => {
  return props.data.length
})

// 方法
const handleClick = () => {
  emit('change', 'new-value')
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
// 样式
</style>
```

#### 组件设计原则
- 单一职责原则
- 可复用性优先
- Props类型严格定义
- 事件命名语义化
- 支持插槽扩展

### 4. API集成

#### API类模式
```typescript
export class UserApi {
  static async getUserList(params?: QueryParams) {
    return http.get<RuoyiResponse<UserInfo[]>>('/system/user/list', { params })
  }
  
  static async addUser(data: Partial<UserInfo>) {
    return http.put<RuoyiResponse<void>>('/system/user', data)
  }
  
  static async updateUser(data: UserInfo) {
    return http.post<RuoyiResponse<void>>('/system/user', data)
  }
  
  static async deleteUser(userIds: number[]) {
    return http.delete<RuoyiResponse<void>>(`/system/user/${userIds.join(',')}`)
  }
}
```

#### 错误处理
- 统一错误拦截器
- 用户友好的错误提示
- 网络异常重试机制
- 业务错误码处理

### 5. 状态管理

#### Pinia Store结构
```typescript
export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const permissions = ref<string[]>([])
  
  // 计算属性
  const isLoggedIn = computed(() => !!userInfo.value)
  
  // 方法
  const login = async (credentials: LoginForm) => {
    try {
      const response = await AuthApi.login(credentials)
      userInfo.value = response.data
      return response
    } catch (error) {
      throw error
    }
  }
  
  const logout = () => {
    userInfo.value = null
    permissions.value = []
  }
  
  return {
    userInfo,
    permissions,
    isLoggedIn,
    login,
    logout
  }
}, {
  persist: {
    key: 'user-store',
    storage: localStorage
  }
})
```

### 6. 路由管理

#### 路由架构设计

**路由模式**: Hash模式 (`createWebHashHistory`)
**路由类型**:
- **静态路由**: 无需权限的基础路由(登录、注册、异常页面等)
- **动态路由**: 基于用户权限动态生成的业务路由

#### 路由配置结构

```typescript
// 路由元数据接口
interface RouteMeta {
  title: string              // 路由标题(支持i18n)
  icon?: string             // 菜单图标
  isHide?: boolean          // 是否在菜单中隐藏
  isHideTab?: boolean       // 是否在标签页中隐藏
  keepAlive?: boolean       // 是否缓存组件
  roles?: string[]          // 角色权限
  isFirstLevel?: boolean    // 是否为一级菜单
  fixedTab?: boolean        // 是否固定标签页
  activePath?: string       // 激活菜单路径
  isIframe?: boolean        // 是否为iframe页面
  link?: string             // 外部链接
  authList?: Array<{        // 操作权限列表
    title: string
    authMark: string
  }>
}
```

#### 动态路由生成

**权限模式**:
1. **前端权限模式**: 基于角色过滤静态路由配置
2. **后端权限模式**: 从后端API获取菜单数据生成路由

```typescript
// 动态路由生成流程
async function generateAccessRoutes(router: Router): Promise<void> {
  if (useCommon().isFrontendMode.value) {
    // 前端权限模式：根据角色过滤路由
    await processFrontendMenu(router)
  } else {
    // 后端权限模式：从后端获取菜单
    await processBackendMenu(router)
  }

  // 注册路由到Vue Router实例
  registerDynamicRoutes(router, menuList)
}
```

#### 路由守卫系统

**前置守卫功能**:
- 白名单路由检查
- 登录状态验证
- 用户信息获取
- 动态路由生成
- 权限验证
- 页面标题设置

```typescript
// 路由守卫配置
router.beforeEach(async (to, from, next) => {
  // 1. 进度条开始
  if (settingStore.showNprogress) {
    NProgress.start()
  }

  // 2. 设置页面标题
  if (to.meta?.title) {
    setPageTitle(to)
  }

  // 3. 白名单检查
  if (isInWhiteList(to.path)) {
    next()
    return
  }

  // 4. 登录状态检查
  if (!userStore.isLogin || !userStore.token) {
    next(`/auth/login?redirect=${encodeURIComponent(to.fullPath)}`)
    return
  }

  // 5. 用户信息和权限验证
  if (!userStore.userInfo.userId || !userStore.getRoles.length) {
    await getUserInfoAndPermissions(userStore)
    await generateAccessRoutes(router)
    next({ ...to, replace: true })
    return
  }

  next()
})
```

#### 路由组件懒加载

**组件动态导入**:
```typescript
// 自动导入views目录下所有组件
const modules: Record<string, () => Promise<any>> = import.meta.glob('../../views/**/*.vue')

// 组件加载函数
function loadComponent(componentPath: string, routeName: string): () => Promise<any> {
  const fullPath = `../../views${componentPath}.vue`
  const fullPathWithIndex = `../../views${componentPath}/index.vue`

  const module = modules[fullPath] || modules[fullPathWithIndex]

  if (!module) {
    console.error(`组件未找到: ${routeName}，路径: ${fullPath}`)
    return () => Promise.resolve({
      render() {
        return h('div', `组件未找到: ${routeName}`)
      }
    })
  }

  return module
}
```

#### 面包屑导航

**自动生成机制**:
```typescript
// 面包屑组件实现
const breadcrumbItems = computed<BreadcrumbItem[]>(() => {
  const { matched } = route

  // 处理首页情况
  if (!matched.length || isHomeRoute(matched[0])) {
    return []
  }

  // 处理一级菜单和普通路由
  const isFirstLevel = matched[0].meta?.isFirstLevel
  const currentRoute = matched[matched.length - 1]

  return isFirstLevel
    ? [createBreadcrumbItem(currentRoute)]
    : matched.map(createBreadcrumbItem)
})

// 面包屑点击导航
async function handleBreadcrumbClick(item: BreadcrumbItem, index: number): Promise<void> {
  if (isLastItem(index) || item.path === '/outside') {
    return
  }

  const targetRoute = router.getRoutes().find(route => route.path === item.path)

  if (!targetRoute?.children?.length) {
    await router.push(item.path)
    return
  }

  const firstValidChild = findFirstValidChild(targetRoute)
  const navigationPath = firstValidChild ? buildFullPath(firstValidChild.path) : item.path

  await router.push(navigationPath)
}
```

#### 路由别名管理

**集中式路由别名**:
```typescript
// routesAlias.ts - 统一管理路由路径
export enum RoutesAlias {
  Layout = '/index/index',           // 布局容器
  Login = '/auth/login',             // 登录页
  Register = '/auth/register',       // 注册页
  Dashboard = '/dashboard/console',   // 工作台
  Exception403 = '/exception/403',   // 403页面
  Exception404 = '/exception/404',   // 404页面
  Exception500 = '/exception/500',   // 500页面
  UserCenter = '/system/user-center', // 用户中心
  // ... 更多路由别名
}

// 使用示例
router.push(RoutesAlias.Dashboard)
```

#### 路由权限控制

**角色权限过滤**:
```typescript
// 根据角色过滤菜单路由
const filterMenuByRoles = (menu: AppRouteRecord[], roles: string[]): AppRouteRecord[] => {
  return menu.reduce((acc: AppRouteRecord[], item) => {
    const itemRoles = item.meta?.roles
    const hasPermission = !itemRoles || itemRoles.some(role => roles?.includes(role))

    if (hasPermission) {
      const filteredItem = { ...item }
      if (filteredItem.children?.length) {
        filteredItem.children = filterMenuByRoles(filteredItem.children, roles)
      }
      acc.push(filteredItem)
    }

    return acc
  }, [])
}
```

**操作权限验证**:
```typescript
// 在组件中使用权限验证
import { usePermission } from '@/composables/usePermission'

const { hasPermission } = usePermission()

// 检查是否有特定权限
const canAdd = hasPermission('system:user:add')
const canEdit = hasPermission('system:user:edit')
const canDelete = hasPermission('system:user:remove')
```

#### 路由缓存策略

**组件缓存配置**:
```typescript
// 路由元信息中配置缓存
{
  path: '/user/list',
  name: 'UserList',
  component: () => import('@views/system/user/index.vue'),
  meta: {
    title: '用户列表',
    keepAlive: true,  // 启用组件缓存
    roles: ['admin', 'user']
  }
}

// 在布局组件中使用KeepAlive
<template>
  <router-view v-slot="{ Component, route }">
    <keep-alive :include="cachedViews">
      <component :is="Component" :key="route.fullPath" />
    </keep-alive>
  </router-view>
</template>
```

#### 路由跳转最佳实践

**编程式导航**:
```typescript
// 基础跳转
router.push('/user/list')

// 带参数跳转
router.push({
  path: '/user/detail',
  query: { id: '123' }
})

// 命名路由跳转
router.push({
  name: 'UserDetail',
  params: { id: '123' }
})

// 替换当前路由
router.replace('/dashboard')

// 历史记录导航
router.go(-1)  // 后退
router.back()  // 后退
router.forward()  // 前进
```

**声明式导航**:
```vue
<template>
  <!-- 基础链接 -->
  <router-link to="/user/list">用户列表</router-link>

  <!-- 命名路由 -->
  <router-link :to="{ name: 'UserList' }">用户列表</router-link>

  <!-- 带参数 -->
  <router-link :to="{ path: '/user/detail', query: { id: userId } }">
    用户详情
  </router-link>

  <!-- 替换历史记录 -->
  <router-link to="/dashboard" replace>工作台</router-link>
</template>
```

#### 路由错误处理

**404路由配置**:
```typescript
// 动态添加404通配符路由(在所有动态路由注册完成后)
const catch404Route = router.addRoute({
  path: '/:pathMatch(.*)*',
  name: 'Exception404',
  component: () => import('@/views/exception/404/index.vue'),
  meta: { title: '404' }
})
```

**路由错误边界**:
```typescript
// 路由守卫中的错误处理
router.beforeEach(async (to, from, next) => {
  try {
    await handleRouteGuard(to, from, next, router)
  } catch (error) {
    console.error('路由守卫处理失败:', error)
    next(RoutesAlias.Exception500)
  }
})
```

#### 路由性能优化

**路由懒加载分组**:
```typescript
// 按功能模块分组懒加载
const UserManagement = () => import(
  /* webpackChunkName: "user-management" */
  '@views/system/user/index.vue'
)

const RoleManagement = () => import(
  /* webpackChunkName: "role-management" */
  '@views/system/role/index.vue'
)

// 预加载关键路由
const Dashboard = () => import(
  /* webpackChunkName: "dashboard" */
  /* webpackPreload: true */
  '@views/dashboard/console/index.vue'
)
```

**路由预加载策略**:
```typescript
// 在路由守卫中预加载下一个可能访问的路由
router.afterEach((to) => {
  // 预加载相关路由组件
  if (to.path === '/dashboard') {
    import('@views/system/user/index.vue')
    import('@views/system/role/index.vue')
  }
})
```

#### 路由调试和监控

**路由变化监听**:
```typescript
// 在组合式函数中监听路由变化
import { watch } from 'vue'
import { useRoute } from 'vue-router'

export function useRouteMonitor() {
  const route = useRoute()

  watch(
    () => route.fullPath,
    (newPath, oldPath) => {
      console.log(`路由变化: ${oldPath} -> ${newPath}`)
      // 路由埋点统计
      trackRouteChange(newPath)
    },
    { immediate: true }
  )
}
```

**路由重复检测**:
```typescript
// 检测菜单列表中是否有重复路由
function checkDuplicateRoutes(menuList: AppRouteRecord[]): void {
  const routeNameMap = new Map<string, string>()
  const componentPathMap = new Map<string, string>()

  const checkRoutes = (routes: AppRouteRecord[], parentPath = '') => {
    routes.forEach((route) => {
      const fullPath = resolvePath(parentPath, route.path || '')

      // 名称重复检测
      if (route.name) {
        if (routeNameMap.has(String(route.name))) {
          console.warn(`[路由警告] 名称重复: "${String(route.name)}"`)
        } else {
          routeNameMap.set(String(route.name), fullPath)
        }
      }

      // 组件路径重复检测
      if (route.component) {
        const componentPath = getComponentPathString(route.component)
        if (componentPath && componentPath !== RoutesAlias.Layout) {
          const componentKey = `${parentPath}:${componentPath}`
          if (componentPathMap.has(componentKey)) {
            console.warn(`[路由警告] 路径重复: "${componentPath}"`)
          } else {
            componentPathMap.set(componentKey, fullPath)
          }
        }
      }

      // 递归检查子路由
      if (route.children?.length) {
        checkRoutes(route.children, fullPath)
      }
    })
  }

  checkRoutes(menuList)
}
```

#### 路由状态管理

**路由状态重置**:
```typescript
// 重置路由相关状态(用户登出时)
export function resetRouterState(): void {
  isRouteRegistered.value = false
  isGettingUserInfo.value = false

  const menuStore = useMenuStore()
  menuStore.removeAllDynamicRoutes()  // 移除动态路由
  menuStore.setMenuList([])           // 清空菜单列表
}
```

**路由状态持久化**:
```typescript
// 在Pinia store中持久化路由相关状态
export const useRouterStore = defineStore('router', () => {
  const currentRoute = ref<string>('')
  const visitedRoutes = ref<string[]>([])

  const addVisitedRoute = (path: string) => {
    if (!visitedRoutes.value.includes(path)) {
      visitedRoutes.value.push(path)
    }
  }

  return {
    currentRoute,
    visitedRoutes,
    addVisitedRoute
  }
}, {
  persist: {
    key: 'router-state',
    storage: sessionStorage
  }
})
```

#### 前台演示路由配置

**添加演示路由的完整流程**:

##### 1. 在静态路由中添加演示路由
```typescript
// src/router/routes/staticRoutes.ts
export const staticRoutes: AppRouteRecordRaw[] = [
  // 现有路由...

  // 前台演示路由
  {
    path: '/demo',
    name: 'Demo',
    component: () => import('@views/demo/index.vue'),
    meta: {
      title: '产品演示',
      noLogin: true,        // 标记为无需登录
      isHideTab: true,      // 隐藏标签页
      setTheme: true        // 设置主题
    }
  },
  {
    path: '/demo/contract-analysis',
    name: 'DemoContractAnalysis',
    component: () => import('@views/demo/contract-analysis/index.vue'),
    meta: {
      title: '合同分析演示',
      noLogin: true,
      isHideTab: true
    }
  },
  {
    path: '/demo/document-upload',
    name: 'DemoDocumentUpload',
    component: () => import('@views/demo/document-upload/index.vue'),
    meta: {
      title: '文档上传演示',
      noLogin: true,
      isHideTab: true
    }
  }
]
```

##### 2. 添加路由到白名单
```typescript
// src/router/guards/beforeEach.ts
const whiteList: string[] = [
  RoutesAlias.Login,
  RoutesAlias.Register,
  RoutesAlias.ForgetPassword,
  RoutesAlias.Exception403,
  RoutesAlias.Exception404,
  RoutesAlias.Exception500,

  // 新增演示路由白名单
  '/demo',                    // 演示首页
  '/demo/contract-analysis',  // 合同分析演示
  '/demo/document-upload',    // 文档上传演示
  '/demo/ai-review',          // AI审查演示
  '/demo/report-generation'   // 报告生成演示
]
```

##### 3. 在路由别名中定义
```typescript
// src/router/routesAlias.ts
export enum RoutesAlias {
  // 现有别名...

  // 演示路由别名
  Demo = '/demo',
  DemoContractAnalysis = '/demo/contract-analysis',
  DemoDocumentUpload = '/demo/document-upload',
  DemoAiReview = '/demo/ai-review',
  DemoReportGeneration = '/demo/report-generation'
}
```

##### 4. 创建演示页面组件
```vue
<!-- src/views/demo/index.vue -->
<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>TunnyContract 智能合同审查系统演示</h1>
      <p>体验AI驱动的合同分析和风险识别功能</p>
    </div>

    <div class="demo-features">
      <div class="feature-card" @click="navigateToDemo('contract-analysis')">
        <h3>合同分析演示</h3>
        <p>上传合同文档，体验AI智能分析</p>
      </div>

      <div class="feature-card" @click="navigateToDemo('document-upload')">
        <h3>文档处理演示</h3>
        <p>演示PDF/Word文档解析功能</p>
      </div>

      <div class="feature-card" @click="navigateToDemo('ai-review')">
        <h3>AI审查演示</h3>
        <p>体验智能风险识别和条款检查</p>
      </div>
    </div>

    <div class="demo-actions">
      <el-button type="primary" @click="goToLogin">
        登录体验完整功能
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { RoutesAlias } from '@/router/routesAlias'

defineOptions({ name: 'Demo' })

const router = useRouter()

const navigateToDemo = (demoType: string) => {
  router.push(`/demo/${demoType}`)
}

const goToLogin = () => {
  router.push(RoutesAlias.Login)
}
</script>
```

##### 5. 演示路由的特殊配置

**无需登录验证**:
```typescript
// 在路由守卫中检查noLogin标记
if (to.meta?.noLogin || isInWhiteList(to.path)) {
  next()
  return
}
```

**演示数据模拟**:
```typescript
// src/api/demoApi.ts
export class DemoApi {
  // 演示用的模拟数据
  static async getDemoContractData() {
    return {
      success: true,
      data: {
        contractName: '演示合同.pdf',
        analysisResult: {
          riskLevel: 'medium',
          issues: ['条款不明确', '缺少违约责任'],
          suggestions: ['建议明确付款条款', '增加争议解决机制']
        }
      }
    }
  }

  static async uploadDemoFile(file: File) {
    // 模拟文件上传
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          success: true,
          data: { fileId: 'demo-123', fileName: file.name }
        })
      }, 2000)
    })
  }
}
```

#### 白名单路由管理

**白名单配置原则**:
1. **公开访问页面**: 无需登录即可访问
2. **演示功能页面**: 展示产品功能的页面
3. **错误页面**: 系统异常页面
4. **认证相关页面**: 登录、注册、找回密码

**白名单路由类型**:
```typescript
// 完整的白名单配置示例
const whiteList: string[] = [
  // 认证相关
  RoutesAlias.Login,           // 登录页
  RoutesAlias.Register,        // 注册页
  RoutesAlias.ForgetPassword,  // 忘记密码

  // 错误页面
  RoutesAlias.Exception403,    // 403无权限
  RoutesAlias.Exception404,    // 404未找到
  RoutesAlias.Exception500,    // 500服务器错误

  // 演示页面
  '/demo',                     // 演示首页
  '/demo/contract-analysis',   // 合同分析演示
  '/demo/document-upload',     // 文档上传演示
  '/demo/ai-review',          // AI审查演示
  '/demo/report-generation',   // 报告生成演示

  // 公开页面
  '/about',                    // 关于我们
  '/contact',                  // 联系我们
  '/privacy',                  // 隐私政策
  '/terms',                    // 服务条款
  '/help',                     // 帮助中心

  // API文档(如果需要公开)
  '/api-docs',                 // API文档
  '/swagger-ui'                // Swagger UI
]
```

**动态白名单管理**:
```typescript
// 支持动态添加白名单路由
export class WhiteListManager {
  private static whiteList = new Set<string>([
    RoutesAlias.Login,
    RoutesAlias.Register,
    // ... 其他默认白名单
  ])

  // 添加白名单路由
  static addWhiteListRoute(path: string): void {
    this.whiteList.add(path)
  }

  // 移除白名单路由
  static removeWhiteListRoute(path: string): void {
    this.whiteList.delete(path)
  }

  // 检查是否在白名单中
  static isInWhiteList(path: string): boolean {
    return this.whiteList.has(path) ||
           Array.from(this.whiteList).some(whitePath =>
             path.startsWith(whitePath)
           )
  }

  // 批量添加白名单路由
  static addBatchWhiteListRoutes(paths: string[]): void {
    paths.forEach(path => this.whiteList.add(path))
  }
}

// 在路由守卫中使用
function isInWhiteList(path: string): boolean {
  return WhiteListManager.isInWhiteList(path)
}
```

#### 演示路由最佳实践

**1. 数据隔离**:
```typescript
// 演示环境使用独立的数据源
const isDemoMode = computed(() => route.path.startsWith('/demo'))

const apiClient = isDemoMode.value ? DemoApi : ProductionApi
```

**2. 功能限制**:
```typescript
// 演示模式下限制某些功能
const canDelete = computed(() =>
  !isDemoMode.value && hasPermission('system:user:remove')
)
```

**3. 演示数据标识**:
```vue
<template>
  <div class="demo-banner" v-if="isDemoMode">
    <el-alert
      title="演示模式"
      type="info"
      description="当前为演示环境，数据仅供展示"
      show-icon
      :closable="false"
    />
  </div>
</template>
```

#### 路由开发规范总结

1. **路由命名**: 使用PascalCase，语义化命名
2. **路径设计**: RESTful风格，层级清晰
3. **元信息**: 完整配置title、权限、缓存等
4. **组件懒加载**: 所有路由组件必须懒加载
5. **权限控制**: 基于角色和操作权限双重验证
6. **错误处理**: 完善的404、403、500错误页面
7. **性能优化**: 合理的预加载和缓存策略
8. **调试友好**: 详细的错误日志和警告信息
9. **白名单管理**: 合理配置公开访问路由
10. **演示路由**: 独立的演示环境和数据隔离

### 7. 样式管理

#### SCSS规范
- 使用SCSS变量和混合器
- 支持深色/浅色主题
- 组件样式作用域隔离
- 全局样式统一管理

#### 主题配置
```scss
// 主题变量
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
}

// 深色主题
[data-theme='dark'] {
  --primary-color: #337ecc;
  // 其他深色主题变量
}
```

### 8. 工具函数

#### 常用工具
- 日期格式化
- 数据验证
- 文件处理
- 权限检查
- 缓存管理

### 9. 测试策略

#### 组件测试
- 使用Vue Test Utils
- 测试组件渲染
- 测试用户交互
- 测试Props和Events

#### E2E测试
- 关键业务流程测试
- 跨浏览器兼容性测试

### 10. 性能优化

#### 构建优化
- 代码分割
- 树摇优化
- 资源压缩
- 缓存策略

#### 运行时优化
- 组件懒加载
- 虚拟滚动
- 防抖节流
- 内存泄漏防护

### 11. 开发工具

#### 推荐插件
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Vue DevTools

#### 调试技巧
- Vue DevTools使用
- 浏览器调试工具
- 网络请求监控
- 性能分析

### 12. 常见问题与解决方案

#### 状态开关组件渲染时序问题

**问题描述**：
- 数据未加载完成时状态开关就开始渲染
- 用户操作时触发"数据异常，无法操作"错误
- 组件在数据不完整时就可以交互

**解决方案**：
```vue
<!-- ❌ 错误：没有条件渲染保护 -->
<template #status="{ row }">
  <el-switch
    v-model="row.status"
    active-value="0"
    inactive-value="1"
    @change="handleStatusChange(row)"
  />
</template>

<!-- ✅ 正确：使用条件渲染保护 -->
<template #status="{ row }">
  <el-switch
    v-if="row && row.id && row.status !== undefined"
    v-model="row.status"
    active-value="0"
    inactive-value="1"
    @change="handleStatusChange(row)"
  />
  <span v-else class="text-gray-400">加载中...</span>
</template>
```

**状态处理函数优化**：
```typescript
// ❌ 错误：复杂的防御性代码
function handleStatusChange(row: Entity) {
  if (!row || !row.id || !row.name || row.status === undefined) {
    console.warn('数据不完整，跳过状态切换:', row)
    return
  }
  // 复杂的验证逻辑...
}

// ✅ 正确：简化的状态处理（因为模板已有保护）
function handleStatusChange(row: Entity) {
  // 由于模板已有条件渲染，这里的数据一定是完整的
  const newStatus = row.status
  const originalStatus = newStatus === '0' ? '1' : '0'

  // 立即恢复到原始状态
  row.status = originalStatus

  // 确认后处理...
}
```

#### 编辑表单数据加载问题

**问题描述**：
- 点击编辑按钮后表单显示为空
- 数据加载有明显的卡顿和闪烁
- 后端返回嵌套数据结构解析错误

**数据结构解析**：
```typescript
// ❌ 错误：数据层级解析错误
const response = await Api.getDetail(id)
const data = response.data  // 错误：这里拿到的是整个响应体

// ✅ 正确：正确解析数据层级
const response = await Api.getDetail(id)
const data = response.data.data  // 正确：这里才是真正的数据

// 后端返回结构：
// {
//   "msg": "操作成功",
//   "code": 200,
//   "data": {  ← 真正的数据在这里
//     "id": 1,
//     "name": "示例"
//   }
// }
```

**预加载数据方案**：
```typescript
// ❌ 错误：先打开对话框再加载数据
async function handleUpdate(row?: Entity) {
  resetForm()
  open.value = true  // 用户看到空表单

  const response = await Api.getDetail(id)
  form.value = response.data.data  // 数据突然出现
}

// ✅ 正确：预加载数据方案
async function handleUpdate(row?: Entity) {
  try {
    editLoading.value = true

    // 先加载数据
    const response = await Api.getDetail(id)

    // 数据加载完成后再打开对话框
    resetForm()
    form.value = response.data.data
    open.value = true  // 用户看到完整数据
  } finally {
    editLoading.value = false
  }
}
```

#### 表格操作列模板问题

**问题描述**：
- 操作列按钮不显示
- 列配置与模板插槽名不匹配
- 组件命名不一致

**操作列配置规范**：
```typescript
// ❌ 错误：使用 operation
{
  prop: 'operation',  // 错误的列名
  label: '操作',
  useSlot: true
}

// ✅ 正确：使用 action
{
  prop: 'action',     // 正确的列名
  label: '操作',
  useSlot: true
}
```

**模板插槽规范**：
```vue
<!-- ❌ 错误：插槽名不匹配 -->
<template #operation="{ row }">
  <ElButton @click="handleEdit(row)">编辑</ElButton>
</template>

<!-- ✅ 正确：插槽名匹配列配置 -->
<template #action="{ row }">
  <el-button @click="handleEdit(row)">编辑</el-button>
</template>
```

**组件命名一致性**：
```vue
<!-- ✅ 推荐：使用小写组件名保持项目一致性 -->
<template #action="{ row }">
  <el-tooltip content="修改" placement="top">
    <el-button
      v-ripple
      v-auth="'system:user:edit'"
      link
      type="primary"
      @click="handleUpdate(row)"
    >
      编辑
    </el-button>
  </el-tooltip>
</template>
```

#### 最佳实践总结

1. **条件渲染优先**：对于依赖数据的交互组件，始终使用条件渲染
2. **数据结构验证**：确认后端返回的数据结构，正确解析嵌套数据
3. **预加载策略**：编辑功能采用预加载数据方案，提升用户体验
4. **命名一致性**：保持组件命名、插槽名、列配置的一致性
5. **错误边界**：在关键交互点添加适当的错误处理和用户提示

### 13. 部署配置

#### 环境变量
```typescript
// .env.development
VITE_APP_BASE_API = '/dev-api'
VITE_API_PROXY_URL = 'http://localhost:8080'

// .env.production
VITE_APP_BASE_API = '/prod-api'
```

#### 构建配置
- 生产环境优化
- 静态资源CDN
- Gzip压缩
- 缓存策略
