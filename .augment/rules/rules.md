---
type: "always_apply"
description: "Augment主配置文件 - TunnyContract"
---

# Augment Rules - TunnyContract 智能合同审查系统

## 项目概述

**TunnyContract** 是一个基于若依框架的全栈智能合同审查系统，采用现代化技术栈：

- **前端**: Vue 3.5.12 + TypeScript 5.6.3 + Vite 6.1.0 (onePiece Pro 主题)
- **后端**: Spring Boot 2.5.15 + Spring Security + MyBatis (若依 v3.9.0)
- **数据库**: MySQL + Redis
- **文件存储**: MinIO
- **智能分析**: Dify AI集成

## 核心架构原则

### 1. 模块化设计
- 前后端完全分离
- 业务模块独立开发
- 组件化复用设计

### 2. 技术栈约束
- **Java版本**: 严格使用Java 8语法，不得使用高版本特性
- **包管理**: 前端使用pnpm，不使用npm
- **框架版本**: 遵循CLAUDE.md中指定的版本

### 3. 开发规范
- 遵循若依框架约定
- 统一的代码风格和命名规范
- 完整的错误处理和日志记录

## 目录结构

```
TunnyContract/
├── TunnyContract_UI/          # Vue 3前端应用
├── TunnyContract_BACK/        # Spring Boot后端应用
├── TunnyTest/                 # 测试相关
├── PRD/                       # 产品需求文档
├── logs/                      # 日志文件
├── CLAUDE.md                  # 开发指导文档
└── .augment/                  # Augment配置
```

## 开发工作流

### 前端开发
```bash
cd TunnyContract_UI
pnpm install
pnpm dev                       # 开发服务器
pnpm build                     # 生产构建
pnpm lint                      # 代码检查
```

### 后端开发
```bash
cd TunnyContract_BACK
mvn clean install             # 构建项目
./ry.sh                       # 启动应用(Linux/macOS)
ry.bat                        # 启动应用(Windows)
```

## 核心业务模块

### 1. 文档处理引擎
- **位置**: `TunnyContract_BACK/ruoyi-common/src/main/java/com/ruoyi/common/utils/contract/`
- **功能**: PDF/Word文档解析、异步处理、文件校验、进度跟踪
- **关键类**: PdfParseService, WordParseService, AsyncDocumentParseService

### 2. 智能审查集成
- **依赖**: `io.github.imfangs:dify-java-client:1.1.7`
- **功能**: 合同内容分析、风险识别、合规性检查、合同对比

### 3. 文件存储管理
- **存储**: MinIO集成
- **功能**: 文件上传、MD5去重、安全校验、分片上传

## 开发约束和要求

### 代码质量
- 所有业务表必须包含标准审计字段
- 使用软删除机制(del_flag)
- 完整的异常处理和日志记录
- 遵循RESTful API设计规范

### 安全要求
- 文件类型严格校验
- 路径安全检查
- 权限控制(@PreAuthorize)
- 操作日志记录(@Log)

### 性能要求
- 大文件异步处理
- Redis缓存优化
- 数据库查询优化
- 前端组件懒加载

## 测试策略

- 单元测试覆盖核心业务逻辑
- 集成测试验证API接口
- 前端组件测试
- 文档解析功能测试

## 部署和运维

### 环境要求
- Node.js ≥18.12.0
- Java 8+
- Maven 3.6+
- MySQL 5.7+
- Redis 6.0+

### 配置管理
- 环境变量配置
- 配置文件分离
- 敏感信息加密

## 扩展指导

详细的技术栈特定规则请参考：
- [前端开发规则](./frontend-rules.md)
- [后端开发规则](./backend-rules.md)
- [数据库设计规范](./database-rules.md)
- [功能模块开发规范](./module-development-rules.md)

## 智能合同审查系统特色

### 文档处理引擎
- **核心位置**: `TunnyContract_BACK/ruoyi-common/src/main/java/com/ruoyi/common/utils/contract/`
- **支持格式**: PDF、DOC、DOCX
- **处理能力**: 同步/异步解析、批量处理、进度跟踪
- **安全特性**: 文件类型检测、MD5去重、路径安全校验

### Dify AI集成
- **智能分析**: 合同内容分析、风险识别、条款合规性检查、合同对比
- **配置管理**: 多智能体配置、动态启用/禁用
- **API集成**: 统一的Dify客户端封装

### MinIO文件存储
- **分布式存储**: 支持大文件上传和管理
- **安全机制**: 访问控制、文件校验
- **性能优化**: 分片上传、断点续传

## API设计规范

### RESTful API模式
```
GET    /contract/[module]/list          # 查询列表
GET    /contract/[module]/{id}          # 查询详情
POST   /contract/[module]               # 新增
PUT    /contract/[module]               # 修改
DELETE /contract/[module]/{ids}         # 删除
```

### 异步任务API
```
POST   /contract/async-parse/submit     # 提交解析任务
GET    /contract/async-parse/status/{taskId}  # 查询任务状态
GET    /contract/async-parse/progress/{taskId} # 获取任务进度
DELETE /contract/async-parse/cancel/{taskId}  # 取消任务
```

## 关键依赖版本

### 后端核心依赖
```xml
<!-- 文档处理 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.30</version>
</dependency>

<!-- 智能审查 -->
<dependency>
    <groupId>io.github.imfangs</groupId>
    <artifactId>dify-java-client</artifactId>
    <version>1.1.7</version>
</dependency>

<!-- 文件存储 -->
<dependency>
    <groupId>io.minio</groupId>
    <artifactId>minio</artifactId>
    <version>8.2.1</version>
</dependency>
```

### 前端核心依赖
- Vue 3.5.12 + TypeScript 5.6.3
- Element Plus 2.10.2
- Vite 6.1.0
- Pinia 3.0.2

## 重要提醒

1. **严格遵循Java 8语法**，避免使用高版本特性
2. **使用pnpm而非npm**进行前端包管理
3. **遵循若依框架约定**，保持代码一致性
4. **所有业务表必须包含标准审计字段**
5. **使用软删除机制**，避免物理删除数据
6. **文件上传必须进行安全校验**
7. **异步任务需要进度跟踪和状态管理**
