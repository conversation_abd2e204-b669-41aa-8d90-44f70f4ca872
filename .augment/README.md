# Augment Rules Configuration

这是TunnyContract智能合同审查系统的Augment AI助手配置文件目录。

## 配置文件说明

### 📋 [rules.md](./rules.md)
**主配置文件** - 项目概述和通用开发规范
- 项目架构概述
- 技术栈说明
- 开发工作流
- 核心业务模块介绍
- 通用开发约束

### 🎨 [frontend-rules.md](./frontend-rules.md)
**前端开发规则** - Vue 3 + TypeScript 开发规范
- Vue 3 Composition API 使用规范
- TypeScript 严格模式配置
- 组件开发最佳实践
- API集成模式
- 状态管理(Pinia)规范
- 路由和样式管理

### ⚙️ [backend-rules.md](./backend-rules.md)
**后端开发规则** - Spring Boot + 若依框架规范
- Java 8语法严格约束
- 若依框架开发模式
- Controller/Service/Mapper层规范
- 异常处理和日志规范
- 安全和性能优化
- 测试策略

### 🗄️ [database-rules.md](./database-rules.md)
**数据库设计规范** - MySQL数据库设计和操作规范
- 标准字段规范(审计字段、软删除)
- 表结构设计模板
- 命名和索引规范
- MyBatis集成规范
- 性能优化策略

### 🧩 [module-development-rules.md](./module-development-rules.md)
**功能模块开发规范** - 基于用户管理和角色管理模块提取的标准化开发规范
- 前端页面结构和组件使用规范
- useTable组合式函数标准配置
- 表格、搜索、树形组件使用规范
- TypeScript类型定义和API调用规范
- 后端Controller、Service、Mapper层开发规范
- 权限注解、分页处理、返回值规范
- 实体类和数据库表设计规范

## 使用指南

### 对于AI助手
1. 首先阅读 `rules.md` 了解项目整体架构
2. 根据开发任务类型，参考对应的专门规则文件
3. 严格遵循配置中的约束和规范
4. 参考CLAUDE.md获取更详细的技术指导

### 对于开发者
1. 这些配置文件定义了项目的开发标准
2. 新团队成员应该熟读所有配置文件
3. 代码审查时应参考这些规范
4. 有疑问时可以查阅对应的规则文件

## 重要约束提醒

⚠️ **Java 8语法约束**: 后端开发严格使用Java 8语法，不得使用高版本特性
⚠️ **包管理工具**: 前端使用pnpm，不使用npm
⚠️ **框架约定**: 严格遵循若依框架的开发模式和约定
⚠️ **数据库规范**: 所有业务表必须包含标准审计字段和软删除标识

## 配置更新

当项目架构或开发规范发生变化时，应及时更新对应的配置文件，确保AI助手能够提供准确的开发指导。

## 技术支持

如有疑问，请参考：
- [CLAUDE.md](../CLAUDE.md) - 详细的开发指导文档
- [PRD文档](../PRD/) - 产品需求和业务规范
- 项目代码示例和最佳实践
