---
alwaysApply: true
description: "TunnyContract项目概览和架构指导"
---

# TunnyContract 项目概览

## 项目简介
**TunnyContract** 是一个基于若依框架的全栈智能合同审查系统，采用现代化技术栈：

- **前端**: Vue 3.5.12 + TypeScript 5.6.3 + Vite 6.1.0 (onePiece Pro 主题)
- **后端**: Spring Boot 2.5.15 + Spring Security + MyBatis (若依 v3.9.0)
- **数据库**: MySQL + Redis
- **文件存储**: MinIO
- **智能分析**: Dify AI集成 + LangChain4j

## 核心架构原则

### 1. 模块化设计
- 前后端完全分离
- 业务模块独立开发
- 组件化复用设计

### 2. 技术栈约束
- **Java版本**: 严格使用Java 8语法，不得使用高版本特性
- **包管理**: 前端使用pnpm，不使用npm
- **框架版本**: 遵循CLAUDE.md中指定的版本

### 3. 开发规范
- 遵循若依框架约定
- 统一的代码风格和命名规范
- 完整的错误处理和日志记录

## 目录结构

```
TunnyContract/
├── TunnyContract_UI/          # Vue 3前端应用
│   ├── src/
│   │   ├── api/               # API接口层
│   │   ├── components/        # 可复用组件
│   │   ├── views/             # 页面组件
│   │   ├── store/             # 状态管理
│   │   ├── router/            # 路由配置
│   │   ├── utils/             # 工具函数
│   │   └── types/             # TypeScript类型定义
│   ├── package.json           # 项目依赖配置
│   ├── vite.config.ts         # Vite构建配置
│   └── tsconfig.json          # TypeScript配置
├── TunnyContract_BACK/        # Spring Boot后端应用
│   ├── ruoyi-admin/           # 主应用模块
│   ├── ruoyi-common/          # 通用工具模块
│   ├── ruoyi-framework/       # 核心框架模块
│   ├── ruoyi-system/          # 系统管理模块
│   ├── ruoyi-generator/       # 代码生成工具
│   ├── ruoyi-quartz/          # 定时任务管理
│   └── sql/                   # 数据库脚本
├── TunnyTest/                 # 测试相关
├── PRD/                       # 产品需求文档
├── logs/                      # 日志文件
└── CLAUDE.md                  # 开发指导文档
```

## 开发工作流

### 前端开发
```bash
cd TunnyContract_UI
pnpm install
pnpm dev                       # 开发服务器
pnpm build                     # 生产构建
pnpm lint                      # 代码检查
```

### 后端开发
```bash
cd TunnyContract_BACK
mvn clean install             # 构建项目
./ry.sh                       # 启动应用(Linux/macOS)
ry.bat                        # 启动应用(Windows)
```

## 核心业务模块

### 1. 文档处理引擎
- **位置**: `TunnyContract_BACK/ruoyi-common/src/main/java/com/ruoyi/common/utils/contract/`
- **功能**: PDF/Word文档解析、异步处理、文件校验、进度跟踪

### 2. 智能审查引擎
- **位置**: `TunnyContract_BACK/ruoyi-common/src/main/java/com/ruoyi/common/utils/ai/`
- **功能**: Dify AI集成、合同条款分析、风险识别、智能建议

### 3. 合同管理模块
- **位置**: `TunnyContract_UI/src/views/contract/`
- **功能**: 合同CRUD、状态管理、审批流程、版本控制

### 4. 用户权限系统
- **位置**: `TunnyContract_BACK/ruoyi-system/`
- **功能**: RBAC权限控制、用户管理、角色分配、菜单权限

## 重要配置文件

- [package.json](mdc:TunnyContract_UI/package.json) - 前端依赖和脚本配置
- [vite.config.ts](mdc:TunnyContract_UI/vite.config.ts) - Vite构建配置
- [tsconfig.json](mdc:TunnyContract_UI/tsconfig.json) - TypeScript配置
- [pom.xml](mdc:TunnyContract_BACK/pom.xml) - Maven项目配置
- [CLAUDE.md](mdc:CLAUDE.md) - 开发指导文档
- [README.md](mdc:README.md) - 项目说明文档