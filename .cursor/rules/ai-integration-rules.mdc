---
description: "AI集成规则 - Dify平台和LangChain4j集成规范"
---

# AI集成规则 - TunnyContract 智能合同审查系统

## AI集成架构

### 1. 技术栈
- **Dify平台**: 主要AI服务平台
- **LangChain4j**: 本地AI集成框架
- **OpenAI API**: 备用AI服务
- **Azure OpenAI**: 企业级AI服务

### 2. 集成层次
```
┌─────────────────────────────────────┐
│           业务应用层                │
├─────────────────────────────────────┤
│         AI服务抽象层                │
├─────────────────────────────────────┤
│    Dify平台    │    LangChain4j     │
├─────────────────────────────────────┤
│   OpenAI API   │   Azure OpenAI     │
└─────────────────────────────────────┘
```

## Dify平台集成

### 1. 配置管理
```java
@Configuration
@ConfigurationProperties(prefix = "dify")
@Data
public class DifyConfig {
    
    /**
     * Dify API基础URL
     */
    private String baseUrl = "https://api.dify.ai/v1";
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 请求超时时间(秒)
     */
    private Integer timeout = 30;
    
    /**
     * 重试次数
     */
    private Integer retryCount = 3;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
```

### 2. Dify客户端
```java
@Component
@Slf4j
public class DifyClient {
    
    @Autowired
    private DifyConfig difyConfig;
    
    @Autowired
    private RestTemplate restTemplate;
    
    /**
     * 发送聊天消息到Dify
     * @param message 消息内容
     * @param userId 用户ID
     * @return 响应结果
     */
    public DifyChatResponse sendChatMessage(String message, String userId) {
        if (!difyConfig.getEnabled()) {
            throw new ServiceException("Dify服务未启用");
        }
        
        DifyChatRequest request = DifyChatRequest.builder()
            .inputs(Collections.emptyMap())
            .query(message)
            .responseMode("blocking")
            .conversationId(generateConversationId(userId))
            .user(userId)
            .build();
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + difyConfig.getApiKey());
            
            HttpEntity<DifyChatRequest> entity = new HttpEntity<>(request, headers);
            
            String url = difyConfig.getBaseUrl() + "/chat-messages";
            ResponseEntity<DifyChatResponse> response = restTemplate.postForEntity(
                url, entity, DifyChatResponse.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            } else {
                throw new ServiceException("Dify API调用失败: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Dify API调用异常", e);
            throw new ServiceException("Dify服务调用失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行工作流
     * @param workflowId 工作流ID
     * @param inputs 输入参数
     * @param userId 用户ID
     * @return 工作流执行结果
     */
    public DifyWorkflowResponse executeWorkflow(String workflowId, Map<String, Object> inputs, String userId) {
        DifyWorkflowRequest request = DifyWorkflowRequest.builder()
            .inputs(inputs)
            .responseMode("blocking")
            .user(userId)
            .build();
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + difyConfig.getApiKey());
            
            HttpEntity<DifyWorkflowRequest> entity = new HttpEntity<>(request, headers);
            
            String url = difyConfig.getBaseUrl() + "/workflows/" + workflowId + "/run";
            ResponseEntity<DifyWorkflowResponse> response = restTemplate.postForEntity(
                url, entity, DifyWorkflowResponse.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody();
            } else {
                throw new ServiceException("Dify工作流执行失败: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Dify工作流执行异常", e);
            throw new ServiceException("Dify工作流执行失败: " + e.getMessage());
        }
    }
}
```

### 3. 合同审查AI服务
```java
@Service
@Slf4j
public class ContractReviewAIService {
    
    @Autowired
    private DifyClient difyClient;
    
    @Autowired
    private LangChain4jService langChain4jService;
    
    /**
     * 审查合同条款
     * @param contractContent 合同内容
     * @param reviewType 审查类型
     * @return 审查结果
     */
    public ContractReviewResult reviewContract(String contractContent, ReviewType reviewType) {
        try {
            // 优先使用Dify平台
            if (isDifyAvailable()) {
                return reviewWithDify(contractContent, reviewType);
            } else {
                // 降级到LangChain4j
                return reviewWithLangChain4j(contractContent, reviewType);
            }
        } catch (Exception e) {
            log.error("合同审查失败", e);
            throw new ServiceException("合同审查失败: " + e.getMessage());
        }
    }
    
    /**
     * 使用Dify平台审查
     */
    private ContractReviewResult reviewWithDify(String contractContent, ReviewType reviewType) {
        String prompt = buildReviewPrompt(contractContent, reviewType);
        DifyChatResponse response = difyClient.sendChatMessage(prompt, "system");
        
        return parseDifyResponse(response);
    }
    
    /**
     * 使用LangChain4j审查
     */
    private ContractReviewResult reviewWithLangChain4j(String contractContent, ReviewType reviewType) {
        return langChain4jService.reviewContract(contractContent, reviewType);
    }
    
    /**
     * 构建审查提示词
     */
    private String buildReviewPrompt(String contractContent, ReviewType reviewType) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下合同内容进行").append(reviewType.getDescription()).append("审查：\n\n");
        prompt.append("合同内容：\n").append(contractContent).append("\n\n");
        prompt.append("请从以下方面进行分析：\n");
        prompt.append("1. 法律风险识别\n");
        prompt.append("2. 条款合规性检查\n");
        prompt.append("3. 风险等级评估\n");
        prompt.append("4. 改进建议\n");
        prompt.append("请以JSON格式返回结果。");
        
        return prompt.toString();
    }
}
```

## LangChain4j集成

### 1. 配置管理
```java
@Configuration
@ConfigurationProperties(prefix = "langchain4j")
@Data
public class LangChain4jConfig {
    
    /**
     * OpenAI API密钥
     */
    private String openaiApiKey;
    
    /**
     * OpenAI API基础URL
     */
    private String openaiBaseUrl = "https://api.openai.com/v1";
    
    /**
     * 模型名称
     */
    private String modelName = "gpt-3.5-turbo";
    
    /**
     * 最大令牌数
     */
    private Integer maxTokens = 4000;
    
    /**
     * 温度参数
     */
    private Double temperature = 0.7;
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
```

### 2. LangChain4j服务
```java
@Service
@Slf4j
public class LangChain4jService {
    
    @Autowired
    private LangChain4jConfig config;
    
    private ChatLanguageModel chatLanguageModel;
    
    @PostConstruct
    public void init() {
        if (config.getEnabled()) {
            this.chatLanguageModel = OpenAiChatModel.builder()
                .apiKey(config.getOpenaiApiKey())
                .baseUrl(config.getOpenaiBaseUrl())
                .modelName(config.getModelName())
                .maxTokens(config.getMaxTokens())
                .temperature(config.getTemperature())
                .build();
        }
    }
    
    /**
     * 审查合同
     * @param contractContent 合同内容
     * @param reviewType 审查类型
     * @return 审查结果
     */
    public ContractReviewResult reviewContract(String contractContent, ReviewType reviewType) {
        if (!config.getEnabled() || chatLanguageModel == null) {
            throw new ServiceException("LangChain4j服务未启用");
        }
        
        try {
            String prompt = buildReviewPrompt(contractContent, reviewType);
            String response = chatLanguageModel.generate(prompt);
            return parseResponse(response);
        } catch (Exception e) {
            log.error("LangChain4j审查失败", e);
            throw new ServiceException("AI审查失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析合同风险
     * @param contractContent 合同内容
     * @return 风险分析结果
     */
    public RiskAnalysisResult analyzeRisk(String contractContent) {
        try {
            String prompt = buildRiskAnalysisPrompt(contractContent);
            String response = chatLanguageModel.generate(prompt);
            return parseRiskAnalysisResponse(response);
        } catch (Exception e) {
            log.error("风险分析失败", e);
            throw new ServiceException("风险分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成合同建议
     * @param contractContent 合同内容
     * @param issues 发现的问题
     * @return 改进建议
     */
    public ImprovementSuggestion generateSuggestion(String contractContent, List<String> issues) {
        try {
            String prompt = buildSuggestionPrompt(contractContent, issues);
            String response = chatLanguageModel.generate(prompt);
            return parseSuggestionResponse(response);
        } catch (Exception e) {
            log.error("建议生成失败", e);
            throw new ServiceException("建议生成失败: " + e.getMessage());
        }
    }
}
```

### 3. 文档解析集成
```java
@Service
@Slf4j
public class DocumentParseAIService {
    
    @Autowired
    private LangChain4jService langChain4jService;
    
    /**
     * 使用AI解析文档结构
     * @param documentContent 文档内容
     * @return 文档结构
     */
    public DocumentStructure parseDocumentStructure(String documentContent) {
        try {
            String prompt = buildStructureParsePrompt(documentContent);
            String response = langChain4jService.getChatLanguageModel().generate(prompt);
            return parseDocumentStructureResponse(response);
        } catch (Exception e) {
            log.error("文档结构解析失败", e);
            throw new ServiceException("文档结构解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 提取合同关键信息
     * @param documentContent 文档内容
     * @return 关键信息
     */
    public ContractKeyInfo extractKeyInfo(String documentContent) {
        try {
            String prompt = buildKeyInfoExtractionPrompt(documentContent);
            String response = langChain4jService.getChatLanguageModel().generate(prompt);
            return parseKeyInfoResponse(response);
        } catch (Exception e) {
            log.error("关键信息提取失败", e);
            throw new ServiceException("关键信息提取失败: " + e.getMessage());
        }
    }
}
```

## 数据模型

### 1. Dify请求响应模型
```java
@Data
@Builder
public class DifyChatRequest {
    private Map<String, Object> inputs;
    private String query;
    private String responseMode;
    private String conversationId;
    private String user;
}

@Data
public class DifyChatResponse {
    private String event;
    private String taskId;
    private String id;
    private String message;
    private String conversationId;
    private String mode;
    private Map<String, Object> metadata;
}

@Data
@Builder
public class DifyWorkflowRequest {
    private Map<String, Object> inputs;
    private String responseMode;
    private String user;
}

@Data
public class DifyWorkflowResponse {
    private String taskId;
    private String workflowRunId;
    private String data;
    private Map<String, Object> metadata;
}
```

### 2. 审查结果模型
```java
@Data
public class ContractReviewResult {
    private Long id;
    private Long contractId;
    private String reviewType;
    private String status;
    private String riskLevel;
    private Double riskScore;
    private String reviewResult;
    private List<RiskPoint> riskPoints;
    private List<ImprovementSuggestion> suggestions;
    private Date reviewTime;
    private String aiProvider;
}

@Data
public class RiskPoint {
    private String type;
    private String description;
    private String severity;
    private String location;
    private String suggestion;
}

@Data
public class ImprovementSuggestion {
    private String category;
    private String description;
    private String priority;
    private String implementation;
}
```

## 错误处理和重试

### 1. 重试策略
```java
@Component
public class AIRetryService {
    
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public ContractReviewResult reviewWithRetry(String content, ReviewType type) {
        return contractReviewAIService.reviewContract(content, type);
    }
    
    @Recover
    public ContractReviewResult recover(Exception ex, String content, ReviewType type) {
        log.error("AI服务重试失败，使用降级方案", ex);
        return getDefaultReviewResult(content, type);
    }
}
```

### 2. 降级策略
```java
@Service
public class AIServiceFallback {
    
    /**
     * 获取默认审查结果
     */
    public ContractReviewResult getDefaultReviewResult(String content, ReviewType type) {
        ContractReviewResult result = new ContractReviewResult();
        result.setStatus("FAILED");
        result.setRiskLevel("UNKNOWN");
        result.setReviewResult("AI服务暂时不可用，请稍后重试");
        result.setAiProvider("FALLBACK");
        return result;
    }
}
```

## 监控和日志

### 1. AI服务监控
```java
@Component
public class AIServiceMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Counter successCounter;
    private final Counter failureCounter;
    private final Timer responseTimer;
    
    public AIServiceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.successCounter = Counter.builder("ai.service.success")
            .description("AI服务成功调用次数")
            .register(meterRegistry);
        this.failureCounter = Counter.builder("ai.service.failure")
            .description("AI服务失败调用次数")
            .register(meterRegistry);
        this.responseTimer = Timer.builder("ai.service.response.time")
            .description("AI服务响应时间")
            .register(meterRegistry);
    }
    
    public void recordSuccess() {
        successCounter.increment();
    }
    
    public void recordFailure() {
        failureCounter.increment();
    }
    
    public void recordResponseTime(Duration duration) {
        responseTimer.record(duration);
    }
}
```

### 2. 日志记录
```java
@Aspect
@Component
@Slf4j
public class AIServiceLogAspect {
    
    @Around("execution(* com.ruoyi.ai.service.*.*(..))")
    public Object logAIServiceCall(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        log.info("AI服务调用开始: {} 参数: {}", methodName, Arrays.toString(args));
        
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            log.info("AI服务调用成功: {} 耗时: {}ms", methodName, endTime - startTime);
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("AI服务调用失败: {} 耗时: {}ms 错误: {}", methodName, endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }
}
```

## 配置示例

### 1. application.yml配置
```yaml
# Dify配置
dify:
  base-url: https://api.dify.ai/v1
  api-key: ${DIFY_API_KEY:your-api-key}
  app-id: ${DIFY_APP_ID:your-app-id}
  timeout: 30
  retry-count: 3
  enabled: true

# LangChain4j配置
langchain4j:
  openai-api-key: ${OPENAI_API_KEY:your-openai-key}
  openai-base-url: https://api.openai.com/v1
  model-name: gpt-3.5-turbo
  max-tokens: 4000
  temperature: 0.7
  enabled: true

# AI服务配置
ai:
  service:
    primary: dify
    fallback: langchain4j
    timeout: 30
    retry-count: 3
```

## 重要配置文件

- [Dify-API参考文档.md](mdc:TunnyContract_BACK/doc/Dify-API参考文档.md) - Dify API文档
- [Dify快速开始指南.md](mdc:TunnyContract_BACK/doc/Dify快速开始指南.md) - Dify使用指南
- [Dify智能体集成说明文档.md](mdc:TunnyContract_BACK/doc/Dify智能体集成说明文档.md) - 智能体集成说明