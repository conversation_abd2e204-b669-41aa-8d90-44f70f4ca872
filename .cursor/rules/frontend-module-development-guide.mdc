---
description: "前端模块开发指导 - 基于用户模块的标准开发模式"
---

# 前端模块开发指导 - TunnyContract 标准开发模式

## 概述

本指导基于TunnyContract项目中用户模块的完整实现，提供标准化的前端模块开发模式。适用于所有CRUD业务模块的开发，包括合同管理、角色管理、部门管理等。

## 标准模块结构

### 1. 目录组织
```
src/views/system/[module]/
├── index.vue                    # 主页面组件
├── components/                  # 子组件目录
│   ├── [Module]Search.vue      # 搜索组件
│   ├── [Module]Form.vue        # 表单组件
│   └── [Module]Detail.vue      # 详情组件(可选)
└── types/                      # 类型定义(可选)
    └── [module].ts
```

### 2. 文件命名规范
- 主页面：`index.vue`
- 搜索组件：`[Module]Search.vue`
- 表单组件：`[Module]Form.vue`
- 详情组件：`[Module]Detail.vue`
- API文件：`[module].ts`
- 类型文件：`[module].ts`

## 核心开发模式

### 1. 主页面组件模式 (index.vue)

#### 基本结构
```vue
<template>
  <div class="art-full-height">
    <div class="[module]-container">
      <!-- 左侧树形结构(可选) -->
      <div class="left-sidebar" v-if="hasTree">
        <ElCard class="art-table-card" shadow="never">
          <template #header>
            <span>{{ treeTitle }}</span>
          </template>
          <el-input
            v-model="treeSearchText"
            placeholder="请输入搜索内容"
            clearable
            prefix-icon="Search"
            style="margin-bottom: 20px"
          />
          <el-tree
            ref="treeRef"
            :data="treeOptions"
            :props="treeProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            node-key="id"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </ElCard>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content art-full-height">
        <!-- 搜索区域 -->
        <[Module]Search
          v-model="searchForm"
          @search="handleSearch"
          @reset="handleReset"
        />

        <!-- 操作按钮区域 -->
        <div class="art-table-toolbar">
          <div class="art-table-toolbar-left">
            <el-button
              v-hasPermi="['[module]:add']"
              type="primary"
              @click="handleAdd"
            >
              <i class="iconfont-sys">&#xe6a0;</i>
              新增
            </el-button>
            <el-button
              v-hasPermi="['[module]:remove']"
              type="danger"
              :disabled="!multiple"
              @click="handleDelete"
            >
              <i class="iconfont-sys">&#xe6a1;</i>
              删除
            </el-button>
            <el-button
              v-hasPermi="['[module]:export']"
              type="warning"
              @click="handleExport"
            >
              <i class="iconfont-sys">&#xe6a2;</i>
              导出
            </el-button>
          </div>
          <div class="art-table-toolbar-right">
            <el-button
              type="primary"
              @click="refreshData"
            >
              <i class="iconfont-sys">&#xe6a3;</i>
              刷新
            </el-button>
          </div>
        </div>

        <!-- 表格区域 -->
        <ArtTable
          v-loading="loading"
          :data="validDataList"
          :columns="columns"
          :column-checks="columnChecks"
          :pagination="pagination"
          :pagination-mobile="paginationMobile"
          @selection-change="handleSelectionChange"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- 操作列插槽 -->
          <template #operation="{ row }">
            <el-button
              v-hasPermi="['[module]:edit']"
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              修改
            </el-button>
            <el-button
              v-hasPermi="['[module]:remove']"
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </ArtTable>
      </div>
    </div>

    <!-- 表单弹窗 -->
    <[Module]Form
      v-model:visible="dialogVisible"
      :type="dialogType"
      :data="currentData"
      :tree-options="treeOptions"
      @submit="handleFormSubmit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { ElMessageBox, ElMessage, ElCard } from 'element-plus'
import { useRouter } from 'vue-router'
import { useTable } from '@/composables/useTable'
import { [Module]Api } from '@/api/system/[module]'
import type { [Module], [Module]QueryParams } from '@/types/system/[module]'
import [Module]Search from './components/[Module]Search.vue'
import [Module]Form from './components/[Module]Form.vue'
import { formatTime } from '@/utils/date'

defineOptions({ name: '[Module]' })

const router = useRouter()

// 树形结构相关(可选)
const hasTree = ref(true) // 是否显示左侧树
const treeTitle = ref('分类列表')
const treeSearchText = ref('')
const treeOptions = ref<any[]>([])
const treeRef = ref()

// 弹窗相关
const dialogType = ref<'add' | 'edit'>('add')
const dialogVisible = ref(false)
const currentData = ref<Partial<[Module]>>({})

// 搜索表单
const searchForm = ref<[Module]QueryParams>({
  // 根据业务需求定义搜索字段
})

// 选择状态
const single = ref(true)
const multiple = ref(true)
const ids = ref<number[]>([])

// 使用useTable组合式函数
const {
  data: dataList,
  columns,
  columnChecks,
  loading,
  pagination,
  paginationMobile,
  refreshData,
  handleSizeChange,
  handleCurrentChange,
  searchParams,
  getData,
  refreshCreate,
  refreshUpdate,
  refreshRemove
} = useTable<[Module]>({
  core: {
    apiFn: [Module]Api.get[Module]List as any,
    apiParams: searchForm.value,
    immediate: true,
    columnsFactory: () => [
      // 表格列配置
      { type: 'selection', width: 50, align: 'center' },
      { type: 'index', width: 50, align: 'center', label: '序号' },
      { prop: 'name', label: '名称', minWidth: 120 },
      { prop: 'status', label: '状态', width: 80, align: 'center' },
      { prop: 'createTime', label: '创建时间', width: 160, align: 'center' },
      { type: 'operation', label: '操作', width: 160, align: 'center', fixed: 'right' }
    ]
  },
  hooks: {
    onSuccess: (data) => {
      // 数据加载成功后的处理
    }
  }
})

// 过滤有效的数据列表
const validDataList = computed(() => {
  if (!Array.isArray(dataList.value)) {
    return []
  }
  return dataList.value.filter(item => item && item.id)
})

// 树形结构相关方法(可选)
const treeProps = {
  label: 'label',
  children: 'children'
}

const filterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.includes(value)
}

const handleNodeClick = (data: any) => {
  searchForm.value.parentId = data.id
  getData()
}

// 搜索相关方法
const handleSearch = () => {
  Object.assign(searchParams, searchForm.value)
  getData()
}

const handleReset = () => {
  searchForm.value = {} as [Module]QueryParams
  Object.keys(searchParams).forEach(key => {
    delete (searchParams as any)[key]
  })
  getData()
}

// 操作相关方法
const handleAdd = () => {
  dialogType.value = 'add'
  currentData.value = {}
  dialogVisible.value = true
}

const handleEdit = (row: [Module]) => {
  dialogType.value = 'edit'
  currentData.value = { ...row }
  dialogVisible.value = true
}

const handleDelete = async (row?: [Module]) => {
  const targetIds = row ? [row.id!] : ids.value
  if (targetIds.length === 0) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm('是否确认删除选中的数据项？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await [Module]Api.del[Module](targetIds)
    ElMessage.success('删除成功')
    await refreshRemove()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleExport = async () => {
  try {
    const blob = await [Module]Api.export[Module](searchForm.value)
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `[module]_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleSelectionChange = (selection: [Module][]) => {
  ids.value = selection.map(item => item.id!)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

const handleFormSubmit = async () => {
  await refreshData()
  dialogVisible.value = false
}

// 初始化数据
onMounted(async () => {
  // 如果需要树形结构，加载树数据
  if (hasTree.value) {
    try {
      const response = await [Module]Api.getTreeData()
      treeOptions.value = response.data
    } catch (error) {
      console.error('加载树数据失败:', error)
    }
  }
})
</script>

<style lang="scss" scoped>
.[module]-container {
  display: flex;
  height: 100%;
  gap: 16px;

  .left-sidebar {
    width: 250px;
    flex-shrink: 0;
  }

  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
</style>
```

### 2. 搜索组件模式 ([Module]Search.vue)

```vue
<template>
  <ArtSearchBar
    ref="searchBarRef"
    v-model="formData"
    :items="formItems"
    :rules="rules"
    @reset="handleReset"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ArtSearchBar from '@/components/core/forms/art-search-bar/index.vue'

defineOptions({ name: '[Module]Search' })

interface SearchForm {
  name: string
  status: string
  beginTime: string
  endTime: string
}

interface Props {
  modelValue: SearchForm
}

interface Emits {
  (e: 'update:modelValue', value: SearchForm): void
  (e: 'search'): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const searchBarRef = ref()

// 表单数据双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 校验规则
const rules = {
  // 根据需要添加验证规则
}

// 表单配置
const formItems = computed(() => [
  {
    label: '名称',
    key: 'name',
    type: 'input',
    placeholder: '请输入名称',
    clearable: true
  },
  {
    label: '状态',
    key: 'status',
    type: 'select',
    placeholder: '请选择状态',
    clearable: true,
    props: {
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  },
  {
    label: '创建时间',
    key: 'daterange',
    type: 'datetime',
    props: {
      type: 'daterange',
      valueFormat: 'YYYY-MM-DD',
      rangeSeparator: '至',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    }
  }
])

const handleReset = () => {
  emit('reset')
}

const handleSearch = async () => {
  await searchBarRef.value?.validate()
  emit('search')
}
</script>
```

### 3. 表单组件模式 ([Module]Form.vue)

```vue
<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="600px"
    append-to-body
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" maxlength="50" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio value="0">正常</el-radio>
              <el-radio value="1">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { [Module]Api } from '@/api/system/[module]'
import type { [Module] } from '@/types/system/[module]'

defineOptions({ name: '[Module]Form' })

interface Props {
  visible: boolean
  type: 'add' | 'edit'
  data?: Partial<[Module]>
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'submit'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()

// 表单数据
const form = reactive<Partial<[Module]>>({
  id: undefined,
  name: '',
  status: '0',
  remark: ''
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '名称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度必须介于 2 和 50 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' }
  ]
}

// 弹窗标题
const title = computed(() => {
  return props.type === 'add' ? '新增[Module]' : '修改[Module]'
})

// 监听数据变化
watch(
  () => props.data,
  (newVal) => {
    if (newVal && props.visible) {
      Object.assign(form, newVal)
    }
  },
  { immediate: true, deep: true }
)

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      resetForm()
      if (props.type === 'add') {
        Object.assign(form, {
          id: undefined,
          name: '',
          status: '0',
          remark: ''
        })
      }
    }
  }
)

const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (props.type === 'add') {
      await [Module]Api.add[Module](form)
      ElMessage.success('新增成功')
    } else {
      await [Module]Api.update[Module](form)
      ElMessage.success('修改成功')
    }

    emit('update:visible', false)
    emit('submit')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

const cancel = () => {
  emit('update:visible', false)
}

const resetForm = () => {
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>
```

### 4. API层模式 ([module].ts)

```typescript
import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { [Module], [Module]QueryParams } from '@/types/system/[module]'

/**
 * [Module]管理API类
 */
export class [Module]Api {
  /**
   * 查询[Module]列表
   * @param params 查询参数
   * @returns [Module]列表响应
   */
  static async get[Module]List(params: [Module]QueryParams): Promise<RuoyiResponse<[Module]>> {
    return http.get('/system/[module]/list', { params })
  }

  /**
   * 查询[Module]详细信息
   * @param id [Module]ID
   * @returns [Module]详情响应
   */
  static async get[Module](id: number): Promise<RuoyiResponse<[Module]>> {
    return http.get(`/system/[module]/${id}`)
  }

  /**
   * 新增[Module]
   * @param data [Module]数据
   * @returns 操作结果
   */
  static async add[Module](data: Partial<[Module]>): Promise<RuoyiResponse> {
    return http.post('/system/[module]', data)
  }

  /**
   * 修改[Module]
   * @param data [Module]数据
   * @returns 操作结果
   */
  static async update[Module](data: Partial<[Module]>): Promise<RuoyiResponse> {
    return http.put('/system/[module]', data)
  }

  /**
   * 删除[Module]
   * @param ids [Module]ID数组
   * @returns 操作结果
   */
  static async del[Module](ids: number | number[]): Promise<RuoyiResponse> {
    const idList = Array.isArray(ids) ? ids.join(',') : ids
    return http.delete(`/system/[module]/${idList}`)
  }

  /**
   * 导出[Module]数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async export[Module](params: [Module]QueryParams): Promise<Blob> {
    const response = await http.post('/system/[module]/export', params, { responseType: 'blob' })
    return response.data
  }

  /**
   * 获取树形数据(可选)
   * @returns 树形数据响应
   */
  static async getTreeData(): Promise<RuoyiResponse<any[]>> {
    return http.get('/system/[module]/tree')
  }
}
```

### 5. 类型定义模式 ([module].ts)

```typescript
import type { RuoyiQueryParams } from '@/types/http'

/** [Module]信息 */
export interface [Module] {
  /** ID */
  id?: number
  /** 名称 */
  name: string
  /** 状态（0正常 1停用） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/** [Module]查询参数 */
export interface [Module]QueryParams extends RuoyiQueryParams {
  /** 名称 */
  name?: string
  /** 状态（0正常 1停用） */
  status?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}
```

## 开发流程

### 1. 创建模块目录
```bash
mkdir -p src/views/system/[module]/components
mkdir -p src/types/system
```

### 2. 开发顺序
1. **类型定义** - 先定义数据结构
2. **API层** - 定义接口调用
3. **搜索组件** - 实现搜索功能
4. **表单组件** - 实现增删改功能
5. **主页面** - 整合所有功能

### 3. 测试验证
1. **功能测试** - 验证CRUD操作
2. **权限测试** - 验证按钮权限
3. **搜索测试** - 验证搜索功能
4. **分页测试** - 验证分页功能

## 最佳实践

### 1. 组件设计原则
- **单一职责** - 每个组件只负责一个功能
- **可复用性** - 组件设计要考虑复用
- **可维护性** - 代码结构清晰，易于维护

### 2. 状态管理
- 使用`useTable`统一管理表格状态
- 使用`ref`和`reactive`管理组件状态
- 避免过度使用全局状态

### 3. 错误处理
- 统一的错误提示
- 网络请求错误处理
- 表单验证错误处理

### 4. 性能优化
- 使用`computed`缓存计算结果
- 使用`watch`监听数据变化
- 合理使用`v-if`和`v-show`

### 5. 代码规范
- 遵循Vue 3 Composition API规范
- 使用TypeScript类型检查
- 统一的命名规范

## 重要配置文件

- [用户模块主页面](mdc:TunnyContract_UI/src/views/system/user/index.vue) - 主页面组件示例
- [用户搜索组件](mdc:TunnyContract_UI/src/views/system/user/components/UserSearch.vue) - 搜索组件示例
- [用户表单组件](mdc:TunnyContract_UI/src/views/system/user/components/UserForm.vue) - 表单组件示例
- [用户API](mdc:TunnyContract_UI/src/api/system/user.ts) - API层示例
- [用户类型定义](mdc:TunnyContract_UI/src/types/system/user.ts) - 类型定义示例
- [useTable组合式函数](mdc:TunnyContract_UI/src/composables/useTable.ts) - 表格管理Hook