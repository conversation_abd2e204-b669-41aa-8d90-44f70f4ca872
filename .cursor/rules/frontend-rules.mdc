---
globs: *.vue,*.ts,*.js,*.scss,*.css
description: "前端开发规则 - Vue 3 + TypeScript + Element Plus"
---

# 前端开发规则 - TunnyContract UI

## 技术栈

- **框架**: Vue 3.5.12 + TypeScript 5.6.3
- **构建工具**: Vite 6.1.0
- **UI库**: Element Plus 2.10.2
- **状态管理**: Pinia 3.0.2
- **路由**: Vue Router 4.4.2
- **HTTP客户端**: Axios 1.7.5
- **包管理器**: pnpm (严格使用，不使用npm)

## 项目结构规范

### 目录组织
```
src/
├── api/                    # API接口层
│   ├── contract/          # 合同相关API
│   ├── system/            # 系统管理API
│   └── common/            # 通用API
├── components/            # 可复用组件
│   ├── core/              # 核心组件
│   ├── contract/          # 合同相关组件
│   └── custom/            # 自定义组件
├── views/                 # 页面组件
├── store/                 # Pinia状态管理
├── router/                # 路由配置
├── utils/                 # 工具函数
├── types/                 # TypeScript类型定义
├── assets/                # 静态资源
└── composables/           # 组合式函数
```

## 编码规范

### 1. Vue组件规范

#### 组件命名
- 文件名使用PascalCase: `UserProfile.vue`
- 组件名使用PascalCase: `UserProfile`
- 多词组件名避免单个单词

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入
import { ref, computed, onMounted } from 'vue'
import type { UserInfo } from '@/types/user'

// 接口定义
interface Props {
  userId: string
  readonly?: boolean
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits定义
const emit = defineEmits<{
  update: [value: string]
  delete: [id: string]
}>()

// 响应式数据
const loading = ref(false)
const userInfo = ref<UserInfo | null>(null)

// 计算属性
const isEditable = computed(() => !props.readonly)

// 方法
const handleSubmit = async () => {
  // 方法实现
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped lang="scss">
/* 样式内容 */
</style>
```

### 2. TypeScript规范

#### 类型定义
- 使用interface定义对象类型
- 使用type定义联合类型和复杂类型
- 所有API响应都要定义类型
- 组件Props必须定义类型

```typescript
// 用户信息类型
interface UserInfo {
  id: string
  name: string
  email: string
  role: UserRole
  createdAt: string
}

// 用户角色枚举
enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// API响应类型
interface ApiResponse<T> {
  code: number
  message: string
  data: T
}
```

#### 路径别名使用
```typescript
// 使用配置的路径别名
import { UserApi } from '@/api/user'
import UserProfile from '@/components/UserProfile.vue'
import { useUserStore } from '@/store/user'
import type { UserInfo } from '@/types/user'
```

### 3. API层规范

#### API类结构
```typescript
// api/user.ts
import request from '@/utils/request'
import type { UserInfo, CreateUserRequest, UpdateUserRequest } from '@/types/user'

export class UserApi {
  // 获取用户列表
  static async getUserList(params: UserListParams): Promise<ApiResponse<UserInfo[]>> {
    return request.get('/system/user/list', { params })
  }

  // 创建用户
  static async createUser(data: CreateUserRequest): Promise<ApiResponse<UserInfo>> {
    return request.post('/system/user', data)
  }

  // 更新用户
  static async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<UserInfo>> {
    return request.put(`/system/user/${id}`, data)
  }

  // 删除用户
  static async deleteUser(id: string): Promise<ApiResponse<void>> {
    return request.delete(`/system/user/${id}`)
  }
}
```

### 4. 状态管理规范

#### Pinia Store结构
```typescript
// store/user.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserApi } from '@/api/user'
import type { UserInfo } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => userInfo.value?.role)

  // 方法
  const login = async (username: string, password: string) => {
    loading.value = true
    try {
      const response = await UserApi.login({ username, password })
      token.value = response.data.token
      userInfo.value = response.data.user
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    token.value = ''
    userInfo.value = null
  }

  return {
    userInfo,
    token,
    loading,
    isLoggedIn,
    userRole,
    login,
    logout
  }
})
```

### 5. 样式规范

#### SCSS使用
- 使用scoped样式避免全局污染
- 使用CSS变量定义主题色彩
- 遵循BEM命名规范
- 使用Element Plus主题变量

```scss
.user-profile {
  padding: 20px;
  
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    &-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
  
  &__content {
    background: var(--el-bg-color);
    border-radius: 8px;
    padding: 16px;
  }
}
```

## 开发工具配置

### ESLint规则
- 使用Vue 3推荐的ESLint配置
- 启用TypeScript严格模式
- 自动修复可修复的问题

### Prettier配置
- 使用2空格缩进
- 单引号字符串
- 行尾分号
- 最大行长度120字符

### 提交规范
- 使用commitizen进行标准化提交
- 提交信息格式: `type(scope): description`
- 类型: feat, fix, docs, style, refactor, test, chore

## 性能优化

### 1. 组件懒加载
```typescript
// 路由懒加载
const routes = [
  {
    path: '/contract',
    component: () => import('@/views/contract/index.vue')
  }
]
```

### 2. 组件按需导入
```typescript
// Element Plus按需导入
import { ElButton, ElForm, ElFormItem } from 'element-plus'
```

### 3. 图片优化
- 使用WebP格式
- 实现图片懒加载
- 使用CDN加速

## 错误处理

### 1. 全局错误处理
```typescript
// 在main.ts中配置
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err, info)
  // 错误上报逻辑
}
```

### 2. API错误处理
```typescript
// 在request拦截器中处理
request.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 处理未授权
      router.push('/login')
    }
    return Promise.reject(error)
  }
)
```

## 测试规范

### 1. 单元测试
- 使用Vitest进行单元测试
- 测试覆盖率要求80%以上
- 测试文件命名: `*.test.ts` 或 `*.spec.ts`

### 2. 组件测试
```typescript
import { mount } from '@vue/test-utils'
import UserProfile from '@/components/UserProfile.vue'

describe('UserProfile', () => {
  it('renders user information correctly', () => {
    const wrapper = mount(UserProfile, {
      props: {
        userId: '123',
        readonly: false
      }
    })
    
    expect(wrapper.find('.user-profile').exists()).toBe(true)
  })
})
```

## 重要配置文件

- [package.json](mdc:TunnyContract_UI/package.json) - 项目依赖和脚本
- [vite.config.ts](mdc:TunnyContract_UI/vite.config.ts) - Vite构建配置
- [tsconfig.json](mdc:TunnyContract_UI/tsconfig.json) - TypeScript配置
- [eslint.config.mjs](mdc:TunnyContract_UI/eslint.config.mjs) - ESLint配置