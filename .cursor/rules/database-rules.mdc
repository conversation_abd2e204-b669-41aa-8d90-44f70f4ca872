---
globs: *.sql,*.xml
description: "数据库规则 - MySQL数据库设计和MyBatis映射规范"
---

# 数据库规则 - TunnyContract 数据库设计规范

## 数据库技术栈

- **数据库**: MySQL 5.7+
- **ORM框架**: MyBatis 3.5+
- **分页插件**: PageHelper 1.4.7
- **连接池**: Druid 1.2.23
- **事务管理**: Spring Transaction

## 数据库设计规范

### 1. 命名规范

#### 表命名
- 表名使用小写字母和下划线
- 系统表以`sys_`开头
- 业务表以业务模块名开头
- 关联表使用`表1_表2`格式

```sql
-- 系统表
sys_user          -- 用户表
sys_role          -- 角色表
sys_menu          -- 菜单表
sys_dept          -- 部门表

-- 业务表
contract          -- 合同表
contract_segment  -- 合同分段表
contract_review   -- 合同审查表
contract_risk     -- 合同风险表

-- 关联表
sys_user_role     -- 用户角色关联表
sys_user_post     -- 用户岗位关联表
contract_user     -- 合同用户关联表
```

#### 字段命名
- 字段名使用小写字母和下划线
- 主键统一使用`id`
- 外键使用`表名_id`格式
- 时间字段使用`create_time`、`update_time`
- 逻辑删除字段使用`del_flag`

```sql
-- 标准字段
id              -- 主键
create_by       -- 创建者
create_time     -- 创建时间
update_by       -- 更新者
update_time     -- 更新时间
del_flag        -- 删除标志(0存在 2删除)
remark          -- 备注
```

### 2. 表结构设计

#### 基础表结构
```sql
CREATE TABLE `contract` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '合同ID',
  `contract_name` varchar(100) NOT NULL COMMENT '合同名称',
  `contract_number` varchar(50) NOT NULL COMMENT '合同编号',
  `contract_type` varchar(20) NOT NULL COMMENT '合同类型',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '合同状态(0待审核 1审核中 2已通过 3已拒绝)',
  `amount` decimal(15,2) DEFAULT NULL COMMENT '合同金额',
  `start_date` date DEFAULT NULL COMMENT '合同开始日期',
  `end_date` date DEFAULT NULL COMMENT '合同结束日期',
  `party_a` varchar(100) DEFAULT NULL COMMENT '甲方',
  `party_b` varchar(100) DEFAULT NULL COMMENT '乙方',
  `file_path` varchar(500) DEFAULT NULL COMMENT '合同文件路径',
  `content` text COMMENT '合同内容',
  `review_status` char(1) DEFAULT '0' COMMENT '审查状态(0未审查 1审查中 2已审查)',
  `risk_level` varchar(10) DEFAULT NULL COMMENT '风险等级(LOW MEDIUM HIGH)',
  `risk_score` decimal(3,2) DEFAULT NULL COMMENT '风险评分',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_number` (`contract_number`),
  KEY `idx_contract_type` (`contract_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同信息表';
```

#### 合同分段表
```sql
CREATE TABLE `contract_segment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分段ID',
  `contract_id` bigint(20) NOT NULL COMMENT '合同ID',
  `title` varchar(200) DEFAULT NULL COMMENT '分段标题',
  `content` text COMMENT '分段内容',
  `segment_type` varchar(20) DEFAULT NULL COMMENT '分段类型',
  `segment_order` int(11) DEFAULT NULL COMMENT '分段序号',
  `start_position` int(11) DEFAULT NULL COMMENT '起始位置',
  `end_position` int(11) DEFAULT NULL COMMENT '结束位置',
  `review_status` char(1) DEFAULT '0' COMMENT '审查状态(0未审查 1审查中 2已审查)',
  `risk_level` varchar(10) DEFAULT NULL COMMENT '风险等级',
  `risk_score` decimal(3,2) DEFAULT NULL COMMENT '风险评分',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_segment_type` (`segment_type`),
  KEY `idx_review_status` (`review_status`),
  CONSTRAINT `fk_contract_segment_contract` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同分段表';
```

#### 合同审查结果表
```sql
CREATE TABLE `contract_review_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '审查结果ID',
  `contract_id` bigint(20) NOT NULL COMMENT '合同ID',
  `segment_id` bigint(20) DEFAULT NULL COMMENT '分段ID',
  `review_type` varchar(20) NOT NULL COMMENT '审查类型',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '审查状态(0进行中 1已完成 2失败)',
  `risk_level` varchar(10) DEFAULT NULL COMMENT '风险等级',
  `risk_score` decimal(3,2) DEFAULT NULL COMMENT '风险评分',
  `review_result` text COMMENT '审查结果',
  `suggestions` text COMMENT '改进建议',
  `ai_provider` varchar(20) DEFAULT NULL COMMENT 'AI服务提供商',
  `review_time` datetime DEFAULT NULL COMMENT '审查时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0存在 2删除)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_contract_id` (`contract_id`),
  KEY `idx_segment_id` (`segment_id`),
  KEY `idx_review_type` (`review_type`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_review_result_contract` FOREIGN KEY (`contract_id`) REFERENCES `contract` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_review_result_segment` FOREIGN KEY (`segment_id`) REFERENCES `contract_segment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同审查结果表';
```

### 3. 索引设计规范

#### 主键索引
- 每个表必须有主键
- 主键使用自增整型
- 主键字段名为`id`

#### 唯一索引
- 业务唯一字段创建唯一索引
- 唯一索引命名：`uk_字段名`

```sql
-- 唯一索引示例
UNIQUE KEY `uk_contract_number` (`contract_number`),
UNIQUE KEY `uk_user_name` (`user_name`),
UNIQUE KEY `uk_phone` (`phonenumber`)
```

#### 普通索引
- 经常查询的字段创建索引
- 外键字段创建索引
- 索引命名：`idx_字段名`

```sql
-- 普通索引示例
KEY `idx_contract_type` (`contract_type`),
KEY `idx_status` (`status`),
KEY `idx_create_time` (`create_time`),
KEY `idx_contract_id` (`contract_id`)
```

#### 联合索引
- 多字段组合查询创建联合索引
- 遵循最左前缀原则
- 索引命名：`idx_字段1_字段2`

```sql
-- 联合索引示例
KEY `idx_contract_type_status` (`contract_type`, `status`),
KEY `idx_user_dept_status` (`user_id`, `dept_id`, `status`)
```

### 4. 数据类型规范

#### 整型
```sql
-- 主键ID
id bigint(20) NOT NULL AUTO_INCREMENT

-- 外键ID
contract_id bigint(20) NOT NULL

-- 状态字段
status char(1) NOT NULL DEFAULT '0'

-- 排序字段
sort_order int(11) DEFAULT 0
```

#### 字符串
```sql
-- 短字符串(名称、编号)
contract_name varchar(100) NOT NULL
contract_number varchar(50) NOT NULL

-- 长字符串(描述、备注)
description varchar(500) DEFAULT NULL
remark varchar(500) DEFAULT NULL

-- 超长字符串(内容)
content text
```

#### 数值
```sql
-- 金额字段
amount decimal(15,2) DEFAULT NULL

-- 评分字段
risk_score decimal(3,2) DEFAULT NULL

-- 百分比字段
progress_rate decimal(5,2) DEFAULT NULL
```

#### 时间
```sql
-- 创建时间
create_time datetime DEFAULT CURRENT_TIMESTAMP

-- 更新时间
update_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

-- 业务时间
start_date date DEFAULT NULL
end_date date DEFAULT NULL
```

## MyBatis映射规范

### 1. Mapper接口规范

#### 基础CRUD方法
```java
@Mapper
public interface ContractMapper {
    
    /**
     * 查询合同列表
     * @param contract 合同信息
     * @return 合同集合
     */
    List<Contract> selectContractList(Contract contract);
    
    /**
     * 根据ID查询合同
     * @param id 合同ID
     * @return 合同信息
     */
    Contract selectContractById(Long id);
    
    /**
     * 新增合同
     * @param contract 合同信息
     * @return 结果
     */
    int insertContract(Contract contract);
    
    /**
     * 修改合同
     * @param contract 合同信息
     * @return 结果
     */
    int updateContract(Contract contract);
    
    /**
     * 删除合同
     * @param id 合同ID
     * @return 结果
     */
    int deleteContractById(Long id);
    
    /**
     * 批量删除合同
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteContractByIds(Long[] ids);
}
```

#### 复杂查询方法
```java
@Mapper
public interface ContractMapper {
    
    /**
     * 根据合同类型统计数量
     * @param contractType 合同类型
     * @return 数量
     */
    int countByContractType(String contractType);
    
    /**
     * 查询合同风险统计
     * @return 风险统计列表
     */
    List<ContractRiskStat> selectContractRiskStats();
    
    /**
     * 查询用户合同列表
     * @param userId 用户ID
     * @param contract 合同信息
     * @return 合同列表
     */
    List<Contract> selectUserContractList(@Param("userId") Long userId, @Param("contract") Contract contract);
    
    /**
     * 查询合同审查进度
     * @param contractId 合同ID
     * @return 审查进度
     */
    ContractReviewProgress selectContractReviewProgress(Long contractId);
}
```

### 2. XML映射文件规范

#### 基础映射
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractMapper">
    
    <resultMap type="Contract" id="ContractResult">
        <id     property="id"           column="id" />
        <result property="contractName" column="contract_name" />
        <result property="contractNumber" column="contract_number" />
        <result property="contractType" column="contract_type" />
        <result property="status"       column="status" />
        <result property="amount"       column="amount" />
        <result property="startDate"    column="start_date" />
        <result property="endDate"      column="end_date" />
        <result property="partyA"       column="party_a" />
        <result property="partyB"       column="party_b" />
        <result property="filePath"     column="file_path" />
        <result property="content"      column="content" />
        <result property="reviewStatus" column="review_status" />
        <result property="riskLevel"    column="risk_level" />
        <result property="riskScore"    column="risk_score" />
        <result property="createBy"     column="create_by" />
        <result property="createTime"   column="create_time" />
        <result property="updateBy"     column="update_by" />
        <result property="updateTime"   column="update_time" />
        <result property="delFlag"      column="del_flag" />
        <result property="remark"       column="remark" />
    </resultMap>

    <sql id="selectContractVo">
        select id, contract_name, contract_number, contract_type, status, amount, 
               start_date, end_date, party_a, party_b, file_path, content, 
               review_status, risk_level, risk_score, create_by, create_time, 
               update_by, update_time, del_flag, remark 
        from contract
    </sql>

    <select id="selectContractList" parameterType="Contract" resultMap="ContractResult">
        <include refid="selectContractVo"/>
        <where>  
            <if test="contractName != null  and contractName != ''"> and contract_name like concat('%', #{contractName}, '%')</if>
            <if test="contractNumber != null  and contractNumber != ''"> and contract_number = #{contractNumber}</if>
            <if test="contractType != null  and contractType != ''"> and contract_type = #{contractType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectContractById" parameterType="Long" resultMap="ContractResult">
        <include refid="selectContractVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertContract" parameterType="Contract" useGeneratedKeys="true" keyProperty="id">
        insert into contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="contractName != null and contractName != ''">contract_name,</if>
            <if test="contractNumber != null and contractNumber != ''">contract_number,</if>
            <if test="contractType != null and contractType != ''">contract_type,</if>
            <if test="status != null">status,</if>
            <if test="amount != null">amount,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="partyA != null">party_a,</if>
            <if test="partyB != null">party_b,</if>
            <if test="filePath != null">file_path,</if>
            <if test="content != null">content,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="riskScore != null">risk_score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="contractName != null and contractName != ''">#{contractName},</if>
            <if test="contractNumber != null and contractNumber != ''">#{contractNumber},</if>
            <if test="contractType != null and contractType != ''">#{contractType},</if>
            <if test="status != null">#{status},</if>
            <if test="amount != null">#{amount},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="partyA != null">#{partyA},</if>
            <if test="partyB != null">#{partyB},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="content != null">#{content},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="riskScore != null">#{riskScore},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateContract" parameterType="Contract">
        update contract
        <trim prefix="SET" suffixOverrides=",">
            <if test="contractName != null and contractName != ''">contract_name = #{contractName},</if>
            <if test="contractNumber != null and contractNumber != ''">contract_number = #{contractNumber},</if>
            <if test="contractType != null and contractType != ''">contract_type = #{contractType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="partyA != null">party_a = #{partyA},</if>
            <if test="partyB != null">party_b = #{partyB},</if>
            <if test="filePath != null">file_path = #{filePath},</if>
            <if test="content != null">content = #{content},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="riskScore != null">risk_score = #{riskScore},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractById" parameterType="Long">
        update contract set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deleteContractByIds" parameterType="String">
        update contract set del_flag = '2' where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
```

#### 复杂查询映射
```xml
<!-- 合同风险统计查询 -->
<select id="selectContractRiskStats" resultType="ContractRiskStat">
    select 
        risk_level as riskLevel,
        count(*) as count,
        avg(risk_score) as avgScore
    from contract 
    where del_flag = '0' 
    group by risk_level
    order by 
        case risk_level 
            when 'HIGH' then 1 
            when 'MEDIUM' then 2 
            when 'LOW' then 3 
        end
</select>

<!-- 用户合同列表查询 -->
<select id="selectUserContractList" resultMap="ContractResult">
    <include refid="selectContractVo"/>
    inner join contract_user cu on c.id = cu.contract_id
    where cu.user_id = #{userId}
    and c.del_flag = '0'
    <if test="contract.contractName != null and contract.contractName != ''">
        and c.contract_name like concat('%', #{contract.contractName}, '%')
    </if>
    <if test="contract.status != null and contract.status != ''">
        and c.status = #{contract.status}
    </if>
    order by c.create_time desc
</select>

<!-- 合同审查进度查询 -->
<select id="selectContractReviewProgress" parameterType="Long" resultType="ContractReviewProgress">
    select 
        c.id as contractId,
        c.contract_name as contractName,
        c.review_status as reviewStatus,
        count(cs.id) as totalSegments,
        count(case when cs.review_status = '2' then 1 end) as reviewedSegments,
        count(case when cs.review_status = '1' then 1 end) as reviewingSegments,
        count(case when cs.review_status = '0' then 1 end) as pendingSegments
    from contract c
    left join contract_segment cs on c.id = cs.contract_id and cs.del_flag = '0'
    where c.id = #{contractId} and c.del_flag = '0'
    group by c.id, c.contract_name, c.review_status
</select>
```

### 3. 动态SQL规范

#### 条件查询
```xml
<where>
    <if test="contractName != null and contractName != ''">
        and contract_name like concat('%', #{contractName}, '%')
    </if>
    <if test="contractType != null and contractType != ''">
        and contract_type = #{contractType}
    </if>
    <if test="status != null and status != ''">
        and status = #{status}
    </if>
    <if test="params.beginTime != null and params.beginTime != ''">
        and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
    </if>
    <if test="params.endTime != null and params.endTime != ''">
        and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
    </if>
</where>
```

#### 批量操作
```xml
<!-- 批量插入 -->
<insert id="batchInsertContractSegments" parameterType="java.util.List">
    insert into contract_segment (contract_id, title, content, segment_type, segment_order, start_position, end_position, create_by, create_time)
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.contractId}, #{item.title}, #{item.content}, #{item.segmentType}, 
         #{item.segmentOrder}, #{item.startPosition}, #{item.endPosition}, 
         #{item.createBy}, #{item.createTime})
    </foreach>
</insert>

<!-- 批量更新 -->
<update id="batchUpdateContractSegments" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
        update contract_segment 
        set title = #{item.title}, 
            content = #{item.content}, 
            segment_type = #{item.segmentType},
            update_by = #{item.updateBy},
            update_time = #{item.updateTime}
        where id = #{item.id}
    </foreach>
</update>
```

## 数据库优化

### 1. 查询优化
- 避免SELECT *，只查询需要的字段
- 使用合适的索引
- 避免在WHERE子句中使用函数
- 使用LIMIT限制返回结果数量

### 2. 索引优化
- 定期分析慢查询日志
- 删除未使用的索引
- 优化联合索引顺序
- 使用覆盖索引

### 3. 分页优化
```java
// 使用PageHelper进行分页
PageHelper.startPage(pageNum, pageSize);
List<Contract> contracts = contractMapper.selectContractList(contract);
PageInfo<Contract> pageInfo = new PageInfo<>(contracts);
```

## 重要配置文件

- [contract_system.sql](mdc:TunnyContract_BACK/sql/contract_system.sql) - 合同系统数据库脚本
- [ry_20250522.sql](mdc:TunnyContract_BACK/sql/ry_20250522.sql) - 若依系统数据库脚本
- [quartz.sql](mdc:TunnyContract_BACK/sql/quartz.sql) - 定时任务数据库脚本