---
description: "合同业务规则 - 智能合同审查系统核心业务逻辑"
---

# 合同业务规则 - TunnyContract 智能合同审查系统

## 业务概述

TunnyContract是一个基于AI的智能合同审查系统，主要功能包括：
- 合同文档上传和解析
- 智能条款分析和风险识别
- 合同分段审核和批注
- 审查结果生成和导出
- 合同版本管理和审批流程

## 核心业务模块

### 1. 文档处理引擎

#### 支持的文件格式
- **PDF**: 使用Apache PDFBox解析
- **Word**: 使用Apache POI解析
- **图片**: 支持OCR文字识别

#### 文档解析流程
```java
// 文档解析服务
@Service
public class DocumentParseService {
    
    /**
     * 解析合同文档
     * @param file 上传的文件
     * @return 解析结果
     */
    public ContractParseResult parseContract(MultipartFile file) {
        // 1. 文件格式验证
        validateFileFormat(file);
        
        // 2. 文件大小检查
        validateFileSize(file);
        
        // 3. 根据文件类型选择解析器
        DocumentParser parser = getParser(file.getContentType());
        
        // 4. 执行解析
        ContractParseResult result = parser.parse(file);
        
        // 5. 保存解析结果
        saveParseResult(result);
        
        return result;
    }
}
```

### 2. 智能审查引擎

#### AI集成架构
- **Dify平台**: 提供AI能力
- **LangChain4j**: 本地AI集成
- **OpenAI API**: 备用AI服务

#### 审查流程
```java
@Service
public class ContractReviewService {
    
    /**
     * 执行合同智能审查
     * @param contractId 合同ID
     * @return 审查结果
     */
    public ContractReviewResult reviewContract(Long contractId) {
        // 1. 获取合同内容
        Contract contract = contractService.getById(contractId);
        
        // 2. 分段处理
        List<ContractSegment> segments = segmentContract(contract);
        
        // 3. 并行AI审查
        List<SegmentReviewResult> segmentResults = segments.parallelStream()
            .map(this::reviewSegment)
            .collect(Collectors.toList());
        
        // 4. 汇总审查结果
        ContractReviewResult result = aggregateReviewResults(segmentResults);
        
        // 5. 保存审查结果
        saveReviewResult(contractId, result);
        
        return result;
    }
    
    /**
     * 审查单个合同段落
     */
    private SegmentReviewResult reviewSegment(ContractSegment segment) {
        // 调用AI服务进行段落审查
        return aiService.reviewSegment(segment);
    }
}
```

### 3. 合同分段审核

#### 分段策略
- **按条款分段**: 根据合同条款结构自动分段
- **按页面分段**: 根据文档页面进行分段
- **手动分段**: 支持用户手动调整分段

#### 分段审核流程
```java
@Service
public class ContractSegmentedReviewService {
    
    /**
     * 执行分段审核
     * @param contractId 合同ID
     * @param reviewConfig 审核配置
     * @return 审核结果
     */
    public SegmentedReviewResult executeSegmentedReview(Long contractId, ReviewConfig reviewConfig) {
        // 1. 获取合同分段
        List<ContractSegment> segments = getContractSegments(contractId);
        
        // 2. 并行处理每个分段
        List<SegmentReviewTask> tasks = segments.stream()
            .map(segment -> createReviewTask(segment, reviewConfig))
            .collect(Collectors.toList());
        
        // 3. 执行审核任务
        List<SegmentReviewResult> results = executeReviewTasks(tasks);
        
        // 4. 生成审核报告
        SegmentedReviewResult report = generateReviewReport(results);
        
        return report;
    }
}
```

### 4. 风险识别和评估

#### 风险类型
- **法律风险**: 条款合规性、法律效力
- **财务风险**: 金额计算、付款条件
- **操作风险**: 执行难度、时间节点
- **商业风险**: 市场变化、竞争风险

#### 风险评估模型
```java
@Component
public class RiskAssessmentEngine {
    
    /**
     * 评估合同风险
     * @param contract 合同内容
     * @return 风险评估结果
     */
    public RiskAssessmentResult assessRisk(Contract contract) {
        RiskAssessmentResult result = new RiskAssessmentResult();
        
        // 1. 法律风险评估
        LegalRisk legalRisk = assessLegalRisk(contract);
        result.setLegalRisk(legalRisk);
        
        // 2. 财务风险评估
        FinancialRisk financialRisk = assessFinancialRisk(contract);
        result.setFinancialRisk(financialRisk);
        
        // 3. 操作风险评估
        OperationalRisk operationalRisk = assessOperationalRisk(contract);
        result.setOperationalRisk(operationalRisk);
        
        // 4. 综合风险评分
        double overallScore = calculateOverallRiskScore(result);
        result.setOverallScore(overallScore);
        
        return result;
    }
}
```

### 5. 审查结果生成

#### 结果格式
- **结构化数据**: JSON格式的审查结果
- **可视化报告**: PDF格式的审查报告
- **Excel表格**: 风险点清单和统计

#### 报告生成流程
```java
@Service
public class ReportGenerationService {
    
    /**
     * 生成审查报告
     * @param reviewResult 审查结果
     * @param format 报告格式
     * @return 报告文件
     */
    public File generateReport(ContractReviewResult reviewResult, ReportFormat format) {
        switch (format) {
            case PDF:
                return generatePdfReport(reviewResult);
            case EXCEL:
                return generateExcelReport(reviewResult);
            case JSON:
                return generateJsonReport(reviewResult);
            default:
                throw new UnsupportedOperationException("不支持的报告格式: " + format);
        }
    }
}
```

## 数据模型

### 1. 合同实体
```java
@Data
@TableName("contract")
public class Contract extends BaseEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @Excel(name = "合同名称")
    private String contractName;
    
    @Excel(name = "合同编号")
    private String contractNumber;
    
    @Excel(name = "合同类型")
    private String contractType;
    
    @Excel(name = "合同状态")
    private String status;
    
    @Excel(name = "合同金额")
    private BigDecimal amount;
    
    @Excel(name = "合同期限")
    private Date startDate;
    
    @Excel(name = "合同期限")
    private Date endDate;
    
    @Excel(name = "甲方")
    private String partyA;
    
    @Excel(name = "乙方")
    private String partyB;
    
    @Excel(name = "合同文件路径")
    private String filePath;
    
    @Excel(name = "合同内容")
    @Column(type = "text")
    private String content;
    
    @Excel(name = "审查状态")
    private String reviewStatus;
    
    @Excel(name = "风险等级")
    private String riskLevel;
}
```

### 2. 合同分段实体
```java
@Data
@TableName("contract_segment")
public class ContractSegment extends BaseEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long contractId;
    
    @Excel(name = "分段标题")
    private String title;
    
    @Excel(name = "分段内容")
    @Column(type = "text")
    private String content;
    
    @Excel(name = "分段类型")
    private String segmentType;
    
    @Excel(name = "分段序号")
    private Integer segmentOrder;
    
    @Excel(name = "起始位置")
    private Integer startPosition;
    
    @Excel(name = "结束位置")
    private Integer endPosition;
    
    @Excel(name = "审查状态")
    private String reviewStatus;
}
```

### 3. 审查结果实体
```java
@Data
@TableName("contract_review_result")
public class ContractReviewResult extends BaseEntity {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long contractId;
    
    @Excel(name = "审查类型")
    private String reviewType;
    
    @Excel(name = "审查状态")
    private String status;
    
    @Excel(name = "风险等级")
    private String riskLevel;
    
    @Excel(name = "风险评分")
    private Double riskScore;
    
    @Excel(name = "审查结果")
    @Column(type = "text")
    private String reviewResult;
    
    @Excel(name = "建议措施")
    @Column(type = "text")
    private String suggestions;
    
    @Excel(name = "审查时间")
    private Date reviewTime;
}
```

## API接口规范

### 1. 合同管理接口
```java
@RestController
@RequestMapping("/contract")
public class ContractController extends BaseController {
    
    /**
     * 获取合同列表
     */
    @PreAuthorize("@ss.hasPermi('contract:list')")
    @GetMapping("/list")
    public TableDataInfo list(Contract contract) {
        startPage();
        List<Contract> list = contractService.selectContractList(contract);
        return getDataTable(list);
    }
    
    /**
     * 上传合同文件
     */
    @PreAuthorize("@ss.hasPermi('contract:upload')")
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam("file") MultipartFile file) {
        try {
            Contract contract = contractService.uploadContract(file);
            return AjaxResult.success("上传成功", contract);
        } catch (Exception e) {
            return AjaxResult.error("上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析合同内容
     */
    @PreAuthorize("@ss.hasPermi('contract:parse')")
    @PostMapping("/parse/{contractId}")
    public AjaxResult parseContract(@PathVariable Long contractId) {
        try {
            ContractParseResult result = contractService.parseContract(contractId);
            return AjaxResult.success("解析成功", result);
        } catch (Exception e) {
            return AjaxResult.error("解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行智能审查
     */
    @PreAuthorize("@ss.hasPermi('contract:review')")
    @PostMapping("/review/{contractId}")
    public AjaxResult reviewContract(@PathVariable Long contractId) {
        try {
            ContractReviewResult result = contractService.reviewContract(contractId);
            return AjaxResult.success("审查完成", result);
        } catch (Exception e) {
            return AjaxResult.error("审查失败: " + e.getMessage());
        }
    }
}
```

### 2. 分段审核接口
```java
@RestController
@RequestMapping("/contract/segment")
public class ContractSegmentController extends BaseController {
    
    /**
     * 获取合同分段列表
     */
    @PreAuthorize("@ss.hasPermi('contract:segment:list')")
    @GetMapping("/list/{contractId}")
    public AjaxResult list(@PathVariable Long contractId) {
        List<ContractSegment> segments = segmentService.getContractSegments(contractId);
        return AjaxResult.success(segments);
    }
    
    /**
     * 执行分段审核
     */
    @PreAuthorize("@ss.hasPermi('contract:segment:review')")
    @PostMapping("/review")
    public AjaxResult reviewSegments(@RequestBody SegmentedReviewRequest request) {
        try {
            SegmentedReviewResult result = segmentService.executeSegmentedReview(request);
            return AjaxResult.success("分段审核完成", result);
        } catch (Exception e) {
            return AjaxResult.error("分段审核失败: " + e.getMessage());
        }
    }
}
```

## 业务规则

### 1. 文件上传规则
- 支持的文件格式：PDF、DOC、DOCX
- 文件大小限制：50MB
- 文件命名规范：合同名称_版本号.扩展名
- 重复文件检查：基于文件MD5值

### 2. 审查规则
- 审查优先级：高风险 > 中风险 > 低风险
- 审查超时：单个分段审查超时时间30秒
- 并发限制：同时最多5个审查任务
- 结果缓存：审查结果缓存24小时

### 3. 权限规则
- 合同查看：需要contract:view权限
- 合同编辑：需要contract:edit权限
- 合同审查：需要contract:review权限
- 合同删除：需要contract:delete权限

### 4. 数据安全规则
- 敏感信息脱敏：身份证号、手机号等
- 文件加密存储：使用AES-256加密
- 访问日志记录：记录所有文件访问操作
- 数据备份：每日自动备份重要数据

## 性能优化

### 1. 异步处理
- 文档解析异步化
- 审查任务队列化
- 结果生成异步化

### 2. 缓存策略
- 解析结果缓存
- 审查结果缓存
- 用户会话缓存

### 3. 数据库优化
- 分表策略：按时间分表
- 索引优化：关键字段建立索引
- 查询优化：避免N+1查询

## 监控和告警

### 1. 业务监控
- 审查成功率监控
- 处理时间监控
- 错误率监控

### 2. 系统监控
- CPU使用率监控
- 内存使用率监控
- 磁盘空间监控

### 3. 告警规则
- 审查失败率超过10%告警
- 处理时间超过5分钟告警
- 系统资源使用率超过80%告警

## 重要配置文件

- [合同分段审核方案_1.md](mdc:PRD/合同分段审核方案_1.md) - 分段审核业务方案
- [合同分段审核方案_2.md](mdc:PRD/合同分段审核方案_2.md) - 分段审核技术方案
- [合同审查系统PRDV2.0.md](mdc:PRD/合同审查系统PRDV2.0.md) - 产品需求文档