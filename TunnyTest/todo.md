Word→PDF 评估

  - 重复下载与转换：convertWordToPdf 已经把源文件拉到本地并转换到缓存 (TunnyContract_BACK/ruoyi-system/src/main/java/com/
  ruoyi/contract/service/impl/ContractDocumentRenderServiceImpl.java:92)，随后又调用 convertWordToPdfAndUploadToMinIO 在同一
  个请求里重新下载并再次转换 (...:982)，带来双倍 I/O 与 CPU 成本，也增加失败面。
  - 缓存元数据失真：新建的 PDF 记录被写入 fileSize=0 与伪造的 MD5 (...:1094)，与实际文件不符，后续校验、去重与缓存命中都会出
  问题。
  - 临时文件与网络安全：downloadToTemp 直接拼接用户态文件名生成路径 (...:224)，若包含 ../ 等字符可逃逸到临时目录之外；同处
  openInputStream (...:249) 也没有设置连接/读取超时或大小限制，容易被慢速或超大文件拖垮。
  - 处理流程缺少流控：通用的 LibreOffice 调用没有消费 stdout/stderr (...:1261)，输出过多会阻塞进程；同一个方法在类里定义了两
  次 (...:319 与 ...:1129)，编译即失败。
  - 降级策略不可用：Apache POI 备选方案只抽取纯文本再用 PDFBox 排版 (...:473)，无法保留布局、图片与复杂样式，生产场景基本等价
  于失败。

  调整建议

  1. 只下载/转换一次：将 convertWordToPdf 产出的 PDF 路径传入上传逻辑，复用同一临时文件；同时在上传后计算真实大小与 MD5 写回
  数据库，真正利用 MD5 缓存判重。
  2. 强化边界与资源控制：对临时文件名做白名单过滤或改用随机 UUID；在 URLConnection 上配置合理的连接/读取超时并限制文件尺寸；
  为 LibreOffice 进程统一收集输出日志并确保清理脚本、临时文件。
  3. 优化转换策略：保留 LibreOffice 作为唯一渲染引擎（或引入专用服务如 JODConverter/docker 化 LibreOffice），失败时返回清晰错
  误而非劣化文本；将转换改为异步任务并限制并发，避免业务线程阻塞。