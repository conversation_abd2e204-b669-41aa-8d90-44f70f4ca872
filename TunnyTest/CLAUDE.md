# TunnyTest - 测试代码专用区域

## 📁 文件夹说明

这是 **TunnyContract** 项目的**独立测试区域**，专门用于存放所有测试相关代码和脚本。

### 🎯 核心原则

**代码隔离至上**：
- ✅ **所有测试代码**必须在此目录创建
- ✅ **所有测试脚本**必须在此目录创建  
- ✅ **所有实验性代码**必须在此目录创建
- ❌ **绝不污染**主项目代码结构
- ❌ **绝不修改**现有业务代码

## 📂 目录结构规划

```
TunnyTest/
├── CLAUDE.md                 # 本配置文件
├── frontend/                 # 前端测试
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   ├── e2e/                  # 端到端测试
│   └── performance/          # 性能测试
├── backend/                  # 后端测试
│   ├── unit/                 # 单元测试
│   ├── integration/          # 集成测试
│   ├── api/                  # API测试
│   └── load/                 # 负载测试
├── scripts/                  # 测试脚本
│   ├── setup/                # 环境搭建脚本
│   ├── data/                 # 测试数据脚本
│   └── automation/           # 自动化脚本
├── mock/                     # 模拟数据
│   ├── frontend/             # 前端模拟数据
│   └── backend/              # 后端模拟数据
├── tools/                    # 测试工具
│   ├── generators/           # 代码生成工具
│   └── utilities/            # 测试实用工具
└── reports/                  # 测试报告
    ├── coverage/             # 覆盖率报告
    └── results/              # 测试结果
```

## 🛡️ Claude Code 工作规则

### 当BOSS要求测试任务时：

1. **强制工作目录**：
```bash
# 所有测试相关命令必须在此目录执行
cd TunnyTest
```

2. **创建文件限制**：
   - ✅ 允许：在 `TunnyTest/` 下创建任何文件
   - ❌ 禁止：在主项目目录创建测试文件
   - ❌ 禁止：修改 `TunnyContract_UI/` 或 `TunnyContract_BACK/` 中的文件（除非明确要求）

3. **依赖管理**：
   - 测试依赖单独管理（独立package.json/pom.xml）
   - 不影响主项目依赖树

## 🚀 测试框架建议

### 前端测试栈
```json
{
  "frameworks": {
    "unit": "Vitest + @vue/test-utils",
    "e2e": "Playwright 或 Cypress",
    "performance": "Lighthouse CI"
  }
}
```

### 后端测试栈
```xml
<!-- 建议的Maven依赖 -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
    </dependency>
</dependencies>
```

## 🎯 Claude 执行模式

### [模式：测试研究]
```yaml
目的: 分析主项目结构，设计测试方案
约束:
  - 只读取主项目文件，不修改
  - 测试代码创建在TunnyTest目录
  - 保持主项目代码完整性
```

### [模式：测试创建]  
```yaml
目的: 在TunnyTest中创建测试代码
原则:
  - 完整的测试覆盖
  - 独立的测试环境
  - 模拟外部依赖
检查点:
  - 确认文件创建在TunnyTest目录
  - 验证不影响主项目结构
```

### [模式：测试执行]
```yaml
目的: 运行测试并生成报告
执行位置: TunnyTest目录
输出位置: TunnyTest/reports/
错误处理: 独立日志，不污染主项目
```

## 📋 快速开始命令

```bash
# 进入测试区域
cd TunnyTest

# 初始化前端测试环境
mkdir -p frontend && cd frontend
npm init -y
npm install vitest @vue/test-utils happy-dom -D

# 初始化后端测试环境  
mkdir -p backend && cd backend
mvn archetype:generate -DgroupId=com.tunny.test

# 创建测试脚本
mkdir -p scripts/setup
```

## 🔐 安全规则

1. **数据保护**：测试数据不包含真实敏感信息
2. **网络隔离**：测试不连接生产环境
3. **权限控制**：测试代码无生产系统访问权限

## 📝 开发日志

创建时间：2025-09-02  
创建者：Claude 终极编程大魔王  
目的：为TunnyContract项目提供干净的测试环境

---

**BOSS提醒**：这个目录是您的测试沙盒，我将严格遵守代码隔离原则，确保主项目的纯净！🛡️