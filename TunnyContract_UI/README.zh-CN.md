# 🎯 onePiece Pro

简体中文 | [English](./README.md)

[![Vue](https://img.shields.io/badge/Vue-3.5.12-4FC08D?logo=vue.js)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6.3-3178C6?logo=typescript)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-6.1.0-646CFF?logo=vite)](https://vitejs.dev/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.10.2-409EFF?logo=element)](https://element-plus.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

> 一款专注于用户体验和快速开发的开源后台管理解决方案

## 📖 项目简介

作为开发者，我在多个项目中需要搭建后台管理系统，但发现传统系统在用户体验和视觉设计上无法完全满足需求。因此，我创建了 **onePiece Pro**，这是一款基于 ElementPlus 设计规范的开源后台管理解决方案，经过精心视觉优化，提供更美观、更实用的前端界面，帮助你轻松构建高质量的后台系统。

## 🌟 项目特色

### ✨ 核心优势
- 🎨 **精美UI设计** - 极致的用户体验和细节处理
- 🚀 **现代化技术栈** - Vue 3 + TypeScript + Vite
- 🛠️ **开箱即用** - 内置丰富的业务组件和模板
- 🎭 **多主题支持** - 浅色/暗色主题自由切换
- 📱 **响应式设计** - 完美支持移动端适配
- 🔧 **高度可定制** - 满足个性化需求

### 🏗️ 技术架构

#### 前端框架
- **Vue 3.5.12** - 渐进式JavaScript框架
- **TypeScript 5.6.3** - 类型安全的JavaScript超集
- **Vite 6.1.0** - 下一代前端构建工具

#### UI组件库
- **Element Plus 2.10.2** - 基于Vue 3的组件库
- **@element-plus/icons-vue** - Element Plus图标库

#### 状态管理
- **Pinia 3.0.2** - Vue官方推荐的状态管理库
- **pinia-plugin-persistedstate** - 状态持久化插件

#### 路由系统
- **Vue Router 4.4.2** - Vue官方路由管理器

#### HTTP客户端
- **Axios 1.7.5** - 基于Promise的HTTP库

#### 开发工具链
- **ESLint** - 代码检查工具
- **Prettier** - 代码格式化工具
- **Stylelint** - 样式检查工具
- **Husky** - Git hooks工具
- **lint-staged** - 代码提交前检查
- **commitizen** - 规范化提交信息

#### 增强功能
- **ECharts 5.6.0** - 百度开源图表库
- **@wangeditor/editor** - 富文本编辑器
- **vue-draggable-plus** - 拖拽功能
- **vue-i18n** - 国际化支持
- **crypto-js** - 加密工具
- **xlsx** - Excel文件处理

## 📁 项目结构

```
onePiece Pro/
├── 📁 src/                          # 源代码目录
│   ├── 📁 api/                      # API接口层
│   │   ├── 📁 auth/                 # 认证相关API
│   │   ├── 📁 system/               # 系统管理API (12个模块)
│   │   ├── 📁 monitor/              # 监控相关API
│   │   └── 📁 tool/                 # 工具相关API
│   ├── 📁 components/               # 组件库
│   │   ├── 📁 core/                 # 核心组件 (43个组件)
│   │   │   ├── 📁 layouts/          # 布局组件
│   │   │   ├── 📁 forms/            # 表单组件
│   │   │   ├── 📁 tables/           # 表格组件
│   │   │   └── 📁 charts/           # 图表组件
│   │   └── 📁 custom/               # 自定义组件
│   ├── 📁 composables/              # 组合式函数 (8个hooks)
│   ├── 📁 directives/               # 自定义指令
│   ├── 📁 locales/                  # 国际化配置
│   ├── 📁 router/                   # 路由配置
│   ├── 📁 store/                    # 状态管理
│   ├── 📁 types/                    # TypeScript类型定义
│   ├── 📁 utils/                    # 工具函数库
│   ├── 📁 views/                    # 页面组件
│   │   ├── 📁 system/               # 系统管理页面 (20个模块)
│   │   ├── 📁 dashboard/            # 仪表盘页面 (34个页面)
│   │   ├── 📁 auth/                 # 认证相关页面
│   │   └── 📁 examples/             # 示例页面
│   ├── 📁 assets/                   # 静态资源
│   │   ├── 📁 styles/               # 样式文件 (14个文件)
│   │   ├── 📁 img/                  # 图片资源
│   │   └── 📁 icons/                # 图标资源
│   └── 📁 config/                   # 项目配置
├── 📁 public/                       # 公共资源
├── 📁 docs/                         # 项目文档
├── 📁 scripts/                      # 构建脚本
├── 📁 .husky/                       # Git hooks配置
├── 📄 package.json                  # 项目依赖配置
├── 📄 vite.config.ts                # Vite构建配置
├── 📄 tsconfig.json                 # TypeScript配置
├── 📄 eslint.config.mjs             # ESLint配置
├── 📄 .prettierrc                   # Prettier配置
└── 📄 .stylelintrc.cjs              # Stylelint配置
```

## 🎨 功能特性

### 🎯 核心功能
- ✅ **用户管理系统** - 用户管理、角色管理、权限控制
- ✅ **菜单管理系统** - 动态菜单、权限验证、多级菜单
- ✅ **组织架构管理** - 部门管理、岗位管理、用户组织
- ✅ **系统监控** - 在线用户、登录日志、操作日志
- ✅ **数据字典** - 动态配置、键值管理
- ✅ **代码生成** - 自动代码生成、模板管理

### 🎨 界面特性
- 🎭 **主题切换** - 浅色/暗色主题无缝切换
- 🔍 **全局搜索** - 快速定位页面和功能
- 🔒 **锁屏功能** - 增强系统安全性
- 📑 **多标签页** - 提升操作效率
- 🍞 **面包屑导航** - 清晰的页面层级
- 🌍 **多语言支持** - 中英文切换
- 📱 **移动端适配** - 响应式设计

### 🛠️ 开发特性
- 🔧 **组件自动导入** - 无需手动导入组件
- 📝 **TypeScript支持** - 完整的类型安全
- 🚀 **热重载** - 开发时实时预览
- 📦 **代码分割** - 优化打包体积
- 🗜️ **资源压缩** - Gzip压缩优化
- 🎯 **开发工具** - Vue DevTools集成

### 🔒 安全特性
- 🛡️ **路由级权限** - 基于角色的访问控制
- 🔐 **按钮级权限** - 细粒度权限控制
- 🏷️ **权限指令** - 简洁的权限控制语法
- 💾 **数据持久化** - 本地存储安全管理
- 🔒 **数据验证** - 前后端数据校验

## 🚀 快速开始

### 📋 环境要求

- **Node.js**: ≥ 18.12.0 (推荐使用 LTS 版本)
- **pnpm**: ≥ 8.0.0 (推荐包管理器)
- **Git**: ≥ 2.30.0

### 📦 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-username/onePiece-pro.git
cd onePiece-pro

# 安装依赖 (推荐)
pnpm install

# 如果安装失败，尝试忽略脚本
pnpm install --ignore-scripts
```

### 🏃‍♂️ 运行项目

```bash
# 开发环境启动
pnpm dev

# 构建生产版本
pnpm build

# 预览生产版本
pnpm serve
```

### 📋 可用脚本

```json
{
  "scripts": {
    "dev": "vite --open",              // 开发服务器
    "build": "vue-tsc --noEmit && vite build",  // 生产构建
    "serve": "vite preview",           // 预览构建结果
    "lint": "eslint",                  // 代码检查
    "fix": "eslint --fix",             // 自动修复代码
    "lint:prettier": "prettier --write **/*.{js,ts,vue,css,scss}",  // 格式化代码
    "lint:stylelint": "stylelint **/*.{css,scss,vue} --fix",  // 样式检查
    "lint:lint-staged": "lint-staged", // 提交前检查
    "commit": "git-cz",                // 规范化提交
    "clean:dev": "tsx scripts/clean-dev.ts"  // 清理开发环境
  }
}
```

## 🏗️ 项目架构

### 📚 核心架构

#### 1. 组件体系
```
ArtSearchBar      - 统一搜索栏组件
ArtTable         - 表格组件（分页、选择、排序）
ArtTableHeader   - 表格头部操作栏
useTable         - 表格数据管理组合式函数
useDict          - 字典数据管理组合式函数
```

#### 2. API架构
```typescript
// API层标准结构
export class [Module]Api {
  static async get[Module]List(params?: QueryParams) {
    return http.get('/system/[module]/list', { params })
  }
  static async add[Module](data: Partial<Entity>) {
    return http.put('/system/[module]', data)
  }
  // ... 其他CRUD方法
}
```

#### 3. 类型系统
```typescript
// 完整的TypeScript类型定义
export interface [Module] {
  [module]Id?: number
  [module]Name: string
  status?: string
  // ... 其他字段
}

export interface [Module]QueryParams extends RuoyiQueryParams {
  [module]Name?: string
  status?: string
  // ... 查询参数
}
```

### 🔧 开发规范

#### 代码规范
- **ESLint** - JavaScript/TypeScript代码检查
- **Prettier** - 代码自动格式化
- **Stylelint** - CSS/SCSS样式检查
- **commitlint** - Git提交信息规范

#### 文件命名
- **组件**: PascalCase (UserCard.vue)
- **组合式函数**: camelCase (useTable.ts)
- **工具函数**: camelCase (formatDate.ts)
- **类型定义**: PascalCase (UserInfo.ts)

#### 目录结构规范
```
src/
├── api/           # API接口
├── components/    # 可复用组件
├── composables/   # 组合式函数
├── views/         # 页面组件
├── types/         # 类型定义
└── utils/         # 工具函数
```

## 🤝 贡献指南

欢迎参与项目贡献！请遵循以下步骤：

1. **Fork** 本仓库
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

### 📋 开发流程

1. 安装依赖: `pnpm install`
2. 启动开发服务器: `pnpm dev`
3. 编写代码并测试
4. 运行代码检查: `pnpm lint`
5. 格式化代码: `pnpm lint:prettier`
6. 提交代码: `pnpm commit`

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

## 📞 技术支持

- 📖 **官方文档**: [https://www.lingchen.kim/one-piece-pro/docs/](https://www.lingchen.kim/one-piece-pro/docs/)
- 💬 **QQ交流群**: [821834289](https://qm.qq.com/cgi-bin/qm/qr?k=Gg6yzZLFaNgmRhK0T5Qcjf7-XcAFWWXm&jump_from=webapi&authKey=YpRKVJQyFKYbGTiKw0GJ/YQXnNF+GdXNZC5beQQqnGZTvuLlXoMO7nw5fNXvmVhA)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-username/onePiece-pro/issues)

## 💝 捐赠支持

如果这个项目对你有帮助，欢迎捐赠支持！你的支持将用于：

- 🛠️ 购买开发工具 (ChatGPT、Cursor等)
- 📚 学习和研究新技术
- ☁️ 服务器和CDN费用
- 📖 项目文档完善

![捐赠二维码](https://www.qiniu.lingchen.kim/%E7%BB%84%202%402x%202.png)

---

⭐ 如果这个项目对你有帮助，请给它一个 Star！
