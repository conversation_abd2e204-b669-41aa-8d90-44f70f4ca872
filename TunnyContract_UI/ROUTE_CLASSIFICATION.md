# 路由分类管理与演示路由切换功能

## 🎯 功能概述

本功能实现了前端路由的分类管理，特别是为演示路由提供了动态显示/隐藏的能力。用户可以通过设置面板方便地控制演示相关页面的显示状态。

## 🏗️ 架构设计

### 路由分类

路由按功能分为三大类：

1. **演示路由 (demo)** - Dashboard、Templates、Widgets、Examples等展示页面
2. **业务路由 (business)** - 实际业务功能页面，由后端动态提供
3. **系统路由 (system)** - 登录、注册、异常页面等系统基础页面

### 技术实现

```mermaid
graph TD
    A[设置面板] --> B[演示路由开关]
    B --> C[setting store]
    C --> D[showDemoRoutes状态]
    D --> E[menu store]
    E --> F[路由过滤逻辑]
    F --> G[菜单重新生成]
    G --> H[页面菜单更新]
```

## 🔧 核心文件

### 1. 设置存储扩展
- **文件**: `src/store/modules/setting.ts`
- **功能**: 添加演示路由显示控制状态
- **新增内容**:
  - `showDemoRoutes` 状态
  - `setShowDemoRoutes()` 方法

### 2. 路由类型定义
- **文件**: `src/types/router/index.ts`
- **功能**: 扩展路由元数据类型
- **新增内容**:
  - `routeType` 字段，支持 'demo' | 'business' | 'system'

### 3. 路由分类工具
- **文件**: `src/utils/router/routeClassification.ts`
- **功能**: 提供路由分类和过滤工具函数
- **主要方法**:
  - `isDemoRoute()` - 判断是否为演示路由
  - `filterDemoRoutes()` - 过滤演示路由
  - `getRouteStatistics()` - 获取路由统计信息

### 4. 菜单存储增强
- **文件**: `src/store/modules/menu.ts`
- **功能**: 在菜单生成时应用路由过滤
- **核心逻辑**: `setMenuList()` 方法根据设置动态过滤演示路由

### 5. 演示路由设置组件
- **文件**: `src/components/core/layouts/art-settings-panel/widget/DemoRouteSettings.vue`
- **功能**: 提供演示路由显示切换UI
- **特性**:
  - 开关式交互
  - 实时切换效果
  - 操作反馈提示

### 6. 路由标识更新
- **文件**: `src/router/routes/staticRoutes.ts`
- **功能**: 为演示路由添加 `routeType: 'demo'` 标识
- **包含路由**:
  - Dashboard 相关页面
  - Templates 模板页面
  - Widgets 组件展示页面
  - Examples 示例页面

## 🚀 使用方法

### 用户操作
1. 点击右上角设置按钮打开设置面板
2. 找到"演示路由"设置项
3. 使用开关控制演示路由的显示/隐藏
4. 设置立即生效，菜单自动更新

### 开发者扩展
```typescript
// 1. 为新路由添加类型标识
{
  path: '/your-demo-page',
  name: 'YourDemoPage',
  component: () => import('@/views/your-demo-page/index.vue'),
  meta: {
    title: '您的演示页面',
    routeType: 'demo' // 标识为演示路由
  }
}

// 2. 使用工具函数
import { isDemoRoute, filterDemoRoutes } from '@/utils/router/routeClassification'

// 判断路由类型
const isDemo = isDemoRoute(route)

// 过滤演示路由
const filteredRoutes = filterDemoRoutes(routes, showDemo)
```

## 🎨 UI设计

演示路由设置参考现有的主题切换和菜单布局切换设计：
- 使用开关组件提供直观的on/off操作
- 提供操作说明文字
- 切换后显示成功提示
- 设置状态持久化保存

## 📊 功能特性

### ✅ 已实现
- [x] 路由分类管理
- [x] 演示路由动态显示/隐藏
- [x] 设置面板UI集成
- [x] 状态持久化
- [x] 国际化支持(中英文)
- [x] 类型安全的TypeScript实现
- [x] 完整的工具函数库

### 🔮 可扩展
- 业务路由分组管理
- 路由权限与分类结合
- 路由使用统计分析
- 自定义路由分类规则

## 🎯 最佳实践

1. **路由分类原则**
   - 演示路由：展示、模板、示例等非生产功能
   - 业务路由：实际业务功能和数据管理
   - 系统路由：认证、异常、基础功能

2. **性能考虑**
   - 路由过滤在菜单生成阶段进行
   - 避免频繁的路由重新计算
   - 使用computed优化组件渲染

3. **用户体验**
   - 设置变更立即生效
   - 提供清晰的操作反馈
   - 保持设置状态的持久化

## 🔗 相关文件清单

```
src/
├── store/modules/
│   ├── setting.ts          # 设置存储扩展
│   └── menu.ts             # 菜单存储增强
├── types/router/
│   └── index.ts            # 路由类型定义扩展
├── utils/router/
│   └── routeClassification.ts  # 路由分类工具
├── components/core/layouts/art-settings-panel/
│   ├── index.vue           # 主设置面板
│   └── widget/
│       └── DemoRouteSettings.vue  # 演示路由设置组件
├── router/routes/
│   └── staticRoutes.ts     # 静态路由配置
└── locales/langs/
    ├── zh.json             # 中文国际化
    └── en.json             # 英文国际化
```

这个实现完全符合现有系统的设计模式，提供了完整的路由分类管理能力，特别是演示路由的动态控制功能。