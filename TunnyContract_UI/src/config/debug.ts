/**
 * 调试配置
 * 用于控制不同环境下的日志输出级别
 */

export interface DebugConfig {
  // 是否启用调试模式
  enabled: boolean
  // 日志级别
  level: 'none' | 'error' | 'warn' | 'info' | 'debug'
  // 是否显示API请求日志
  showApiLogs: boolean
  // 是否显示缓存日志
  showCacheLogs: boolean
  // 是否显示事件日志
  showEventLogs: boolean
  // 是否显示路由日志
  showRouteLogs: boolean
}

// 根据环境变量设置调试配置
const isDevelopment = import.meta.env.DEV
const isProduction = import.meta.env.PROD

export const debugConfig: DebugConfig = {
  enabled: isDevelopment,
  level: isProduction ? 'error' : 'debug',
  showApiLogs: isDevelopment,
  showCacheLogs: isDevelopment,
  showEventLogs: isDevelopment,
  showRouteLogs: isDevelopment
}

/**
 * 调试日志工具类
 */
export class DebugLogger {
  private static instance: DebugLogger
  private config: DebugConfig

  private constructor(config: DebugConfig) {
    this.config = config
  }

  static getInstance(config?: DebugConfig): DebugLogger {
    if (!DebugLogger.instance) {
      DebugLogger.instance = new DebugLogger(config || debugConfig)
    }
    return DebugLogger.instance
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<DebugConfig>) {
    this.config = { ...this.config, ...config }
  }

  /**
   * 检查是否应该输出日志
   */
  private shouldLog(level: string): boolean {
    if (!this.config.enabled) return false

    const levels = ['none', 'error', 'warn', 'info', 'debug']
    const currentLevel = levels.indexOf(this.config.level)
    const targetLevel = levels.indexOf(level)

    return targetLevel <= currentLevel
  }

  /**
   * 错误日志
   */
  error(message: string, ...args: any[]) {
    if (this.shouldLog('error')) {
      console.error(`[DEBUG] ${message}`, ...args)
    }
  }

  /**
   * 警告日志
   */
  warn(message: string, ...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn(`[DEBUG] ${message}`, ...args)
    }
  }

  /**
   * 信息日志
   */
  info(message: string, ...args: any[]) {
    if (this.shouldLog('info')) {
      console.info(`[DEBUG] ${message}`, ...args)
    }
  }

  /**
   * 调试日志
   */
  debug(message: string, ...args: any[]) {
    if (this.shouldLog('debug')) {
      console.log(`[DEBUG] ${message}`, ...args)
    }
  }

  /**
   * API请求日志
   */
  api(message: string, ...args: any[]) {
    if (this.config.showApiLogs && this.shouldLog('debug')) {
      console.log(`[API] ${message}`, ...args)
    }
  }

  /**
   * 缓存日志
   */
  cache(message: string, ...args: any[]) {
    if (this.config.showCacheLogs && this.shouldLog('debug')) {
      console.log(`[CACHE] ${message}`, ...args)
    }
  }

  /**
   * 事件日志
   */
  event(message: string, ...args: any[]) {
    if (this.config.showEventLogs && this.shouldLog('debug')) {
      console.log(`[EVENT] ${message}`, ...args)
    }
  }

  /**
   * 路由日志
   */
  route(message: string, ...args: any[]) {
    if (this.config.showRouteLogs && this.shouldLog('debug')) {
      console.log(`[ROUTE] ${message}`, ...args)
    }
  }
}

// 导出默认实例
export const debugLogger = DebugLogger.getInstance()

// 导出便捷方法
export const log = {
  error: (message: string, ...args: any[]) => debugLogger.error(message, ...args),
  warn: (message: string, ...args: any[]) => debugLogger.warn(message, ...args),
  info: (message: string, ...args: any[]) => debugLogger.info(message, ...args),
  debug: (message: string, ...args: any[]) => debugLogger.debug(message, ...args),
  api: (message: string, ...args: any[]) => debugLogger.api(message, ...args),
  cache: (message: string, ...args: any[]) => debugLogger.cache(message, ...args),
  event: (message: string, ...args: any[]) => debugLogger.event(message, ...args),
  route: (message: string, ...args: any[]) => debugLogger.route(message, ...args)
}
