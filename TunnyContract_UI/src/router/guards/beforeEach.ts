import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { ref, nextTick } from 'vue'
import NProgress from 'nprogress'
import { useSettingStore } from '@/store/modules/setting'
import { useUserStore } from '@/store/modules/user'
import { useMenuStore } from '@/store/modules/menu'
import { setWorktab } from '@/utils/navigation'
import { setPageTitle, setSystemTheme } from '../utils/utils'
import { registerDynamicRoutes } from '../utils/registerRoutes'
import { AppRouteRecord } from '@/types/router'
import { RoutesAlias } from '../routesAlias'
import { loadingService } from '@/utils/ui'
import { useCommon } from '@/composables/useCommon'
import { useWorktabStore } from '@/store/modules/worktab'
import { UserService } from '@/api/usersApi'
// 白名单路由 - 无需登录即可访问的路由
const whiteList: string[] = [
  RoutesAlias.Login,
  RoutesAlias.Register,
  RoutesAlias.ForgetPassword,
  RoutesAlias.Exception403,
  RoutesAlias.Exception404,
  RoutesAlias.Exception500
]

// 是否已注册动态路由
const isRouteRegistered = ref(false)

// 是否正在获取用户信息（防止并发调用）
const isGettingUserInfo = ref(false)

// 跟踪是否需要关闭 loading
const pendingLoading = ref(false)

/**
 * 设置路由全局前置守卫
 * 基于RuoYi路由守卫逻辑，直接处理RuoYi权限数据
 */
export function setupBeforeEachGuard(router: Router): void {
  router.beforeEach(
    async (
      to: RouteLocationNormalized,
      from: RouteLocationNormalized,
      next: NavigationGuardNext
    ) => {
      try {
        await handleRouteGuard(to, from, next, router)
      } catch (error) {
        console.error('路由守卫处理失败:', error)
        next(RoutesAlias.Exception500)
      }
    }
  )

  // 设置后置守卫以关闭 loading 和进度条
  setupAfterEachGuard(router)
}

/**
 * 设置路由全局后置守卫
 */
function setupAfterEachGuard(router: Router): void {
  router.afterEach(() => {
    // 关闭进度条
    const settingStore = useSettingStore()
    if (settingStore.showNprogress) {
      NProgress.done()
    }

    // 关闭 loading 效果
    if (pendingLoading.value) {
      nextTick(() => {
        loadingService.hideLoading()
        pendingLoading.value = false
      })
    }
  })
}

/**
 * 处理路由守卫逻辑
 * 基于RuoYi路由守卫模式，直接处理RuoYi权限数据
 */
async function handleRouteGuard(
  to: RouteLocationNormalized,
  _from: RouteLocationNormalized,
  next: NavigationGuardNext,
  router: Router
): Promise<void> {
  const settingStore = useSettingStore()
  const userStore = useUserStore()

  // 处理进度条
  if (settingStore.showNprogress) {
    NProgress.start()
  }

  // 设置页面标题
  if (to.meta?.title) {
    setPageTitle(to)
  }

  // 设置系统主题
  setSystemTheme(to)

  // 检查是否在白名单中
  if (isInWhiteList(to.path)) {
    next()
    return
  }

  // 检查登录状态
  if (!userStore.isLogin || !userStore.token) {
    // 未登录，跳转到登录页并携带重定向参数
    next(`${RoutesAlias.Login}?redirect=${encodeURIComponent(to.fullPath)}`)
    return
  }

  // 已登录，检查用户信息
  if (!userStore.userInfo.userId || !userStore.getRoles.length) {
    // 防止并发调用，如果正在获取用户信息则等待
    if (isGettingUserInfo.value) {
      // 等待当前请求完成
      let retryCount = 0
      while (isGettingUserInfo.value && retryCount < 50) {
        // 最多等待 5秒
        await new Promise((resolve) => setTimeout(resolve, 100))
        retryCount++
      }

      // 如果还在获取中，直接跳转到登录页
      if (isGettingUserInfo.value) {
        next(`${RoutesAlias.Login}?redirect=${encodeURIComponent(to.fullPath)}`)
        return
      }

      // 检查是否已获取到用户信息
      if (userStore.userInfo.userId && userStore.getRoles.length) {
        // 用户信息已获取，直接跳转
        if (!isRouteRegistered.value) {
          try {
            await generateAccessRoutes(router)
            next({ ...to, replace: true })
            return
          } catch (error) {
            console.error('生成路由失败:', error)
          }
        }
      } else {
        // 用户信息获取失败，重新登录
        userStore.logOut()
        return
      }
    } else {
      try {
        isGettingUserInfo.value = true
        // 获取用户信息和权限
        await getUserInfoAndPermissions(userStore)
        // 生成动态路由
        await generateAccessRoutes(router)
        // 重新导航到目标路由
        next({ ...to, replace: true })
        return
      } catch (error) {
        console.error('获取用户信息失败:', error)
        userStore.logOut()
        return
      } finally {
        isGettingUserInfo.value = false
      }
    }
  } else {
    // 如果路由还没有注册，强制生成路由
    if (!isRouteRegistered.value) {
      try {
        await generateAccessRoutes(router)
        next({ ...to, replace: true })
        return
      } catch (error) {
        console.error('强制生成路由失败:', error)
      }
    }
  }

  // 处理根路径跳转到首页
  if (to.path === '/' && handleRootPathRedirect(to, next)) {
    return
  }

  // 设置工作台和页面标题
  setWorktab(to)
  setPageTitle(to)
  next()
}

/**
 * 检查路径是否在白名单中
 * @param path 路由路径
 * @returns 是否在白名单中
 */
function isInWhiteList(path: string): boolean {
  return whiteList.includes(path)
}

/**
 * 获取用户信息和权限
 * @param userStore 用户store
 */
async function getUserInfoAndPermissions(
  userStore: ReturnType<typeof useUserStore>
): Promise<void> {
  try {
    const response = await UserService.getUserInfo()
    // 从HTTP响应中提取用户信息数据，兼容RuoYi响应格式
    const data = (response as any).data
    // 直接使用RuoYi格式的用户信息
    userStore.setUserInfo(data.user)
    userStore.setRoles(data.roles || [])
    userStore.setPermissions(data.permissions || [])
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

/**
 * 生成可访问路由
 * @param router 路由实例
 */
async function generateAccessRoutes(router: Router): Promise<void> {
  try {
    // 显示 loading
    pendingLoading.value = true
    loadingService.showLoading()

    // 根据权限生成路由
    if (useCommon().isFrontendMode.value) {
      // 前端权限模式：根据角色过滤路由
      await processFrontendMenu(router)
    } else {
      // 后端权限模式：从后端获取菜单
      await processBackendMenu(router)
    }

    // 标记路由已注册
    isRouteRegistered.value = true

    // 验证工作台
    useWorktabStore().validateWorktabs(router)
  } catch (error) {
    console.error('生成动态路由失败:', error)
    throw error
  }
}

/**
 * 处理前端控制模式的菜单逻辑
 */
async function processFrontendMenu(router: Router): Promise<void> {
  // 导入异步路由
  const { asyncRoutes } = await import('../routes/asyncRoutes')
  const { menuDataToRouter } = await import('../utils/menuToRouter')

  const menuList = asyncRoutes.map((route) => menuDataToRouter(route))
  const userStore = useUserStore()
  const roles = userStore.getRoles

  if (!roles || roles.length === 0) {
    throw new Error('获取用户角色失败')
  }

  const filteredMenuList = filterMenuByRoles(menuList, roles)

  await registerAndStoreMenu(router, filteredMenuList)
}

/**
 * 处理后端控制模式的菜单逻辑
 */
async function processBackendMenu(router: Router): Promise<void> {
  try {
    // 动态导入菜单服务
    const { menuService } = await import('@/api/menuApi')
    // 尝试使用后端路由，如果失败则fallback到静态路由
    const { menuList } = await menuService.getMenuList(300, true)
    await registerAndStoreMenu(router, menuList)
    //console.log('后端菜单处理完成，路由数量:', menuList.length)
  } catch (error) {
    console.error('后端菜单处理失败:', error)
    // 如果后端菜单处理失败，使用前端静态路由作为fallback
    await processFrontendMenu(router)
  }
}

/**
 * 递归过滤空菜单项
 */
function filterEmptyMenus(menuList: AppRouteRecord[]): AppRouteRecord[] {
  return menuList
    .map((item) => {
      // 如果有子菜单，先递归过滤子菜单
      if (item.children && item.children.length > 0) {
        const filteredChildren = filterEmptyMenus(item.children)
        return {
          ...item,
          children: filteredChildren
        }
      }
      return item
    })
    .filter((item) => {
      // 过滤掉布局组件且没有子菜单的项
      const isEmptyLayoutMenu =
        item.component === RoutesAlias.Layout && (!item.children || item.children.length === 0)

      // 过滤掉组件为空字符串且没有子菜单的项
      const isEmptyComponentMenu =
        item.component === '' &&
        (!item.children || item.children.length === 0) &&
        item.meta.isIframe !== true

      return !(isEmptyLayoutMenu || isEmptyComponentMenu)
    })
}

/**
 * 注册路由并存储菜单数据
 */
async function registerAndStoreMenu(router: Router, menuList: AppRouteRecord[]): Promise<void> {
  if (!isValidMenuList(menuList)) {
    throw new Error('获取菜单列表失败，请重新登录')
  }
  const menuStore = useMenuStore()
  // 递归过滤掉为空的菜单项
  const list = filterEmptyMenus(menuList)
  menuStore.setMenuList(list)
  registerDynamicRoutes(router, list)
  isRouteRegistered.value = true
  useWorktabStore().validateWorktabs(router)
}

/**
 * 根据角色过滤菜单
 */
const filterMenuByRoles = (menu: AppRouteRecord[], roles: string[]): AppRouteRecord[] => {
  return menu.reduce((acc: AppRouteRecord[], item) => {
    const itemRoles = item.meta?.roles
    const hasPermission = !itemRoles || itemRoles.some((role) => roles?.includes(role))

    if (hasPermission) {
      const filteredItem = { ...item }
      if (filteredItem.children?.length) {
        filteredItem.children = filterMenuByRoles(filteredItem.children, roles)
      }
      acc.push(filteredItem)
    }

    return acc
  }, [])
}

/**
 * 验证菜单列表是否有效
 */
function isValidMenuList(menuList: AppRouteRecord[]): boolean {
  return Array.isArray(menuList) && menuList.length > 0
}

/**
 * 重置路由相关状态
 */
export function resetRouterState(): void {
  isRouteRegistered.value = false
  isGettingUserInfo.value = false
  const menuStore = useMenuStore()
  menuStore.removeAllDynamicRoutes()
  menuStore.setMenuList([])
}

/**
 * 处理根路径跳转到首页
 */
function handleRootPathRedirect(to: RouteLocationNormalized, next: NavigationGuardNext): boolean {
  if (to.path === '/') {
    const { homePath } = useCommon()
    if (homePath.value) {
      next({ path: homePath.value, replace: true })
      return true
    } else {
      // 如果没有设置首页，跳转到默认的控制台页面（现在是静态路由）
      next({ path: '/dashboard/console', replace: true })
      return true
    }
  }
  return false
}
