/**
 * 岗位管理相关类型定义
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 岗位信息 */
export interface Post {
  /** 岗位ID */
  postId?: number
  /** 岗位编码 */
  postCode: string
  /** 岗位名称 */
  postName: string
  /** 显示顺序 */
  postSort?: number
  /** 状态（0正常 1停用） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 用户是否存在此岗位标识 默认不存在 */
  flag?: boolean
}

/** 岗位查询参数 */
export interface PostQueryParams extends RuoyiQueryParams {
  /** 岗位编码 */
  postCode?: string
  /** 岗位名称 */
  postName?: string
  /** 状态 */
  status?: string
}
