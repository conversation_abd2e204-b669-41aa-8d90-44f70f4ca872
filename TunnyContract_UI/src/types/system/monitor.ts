/**
 * 监控相关类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 操作日志 */
export interface OperLog {
  /** 日志主键 */
  operId?: number
  /** 模块标题 */
  title?: string
  /** 业务类型（0其它 1新增 2修改 3删除） */
  businessType?: number
  /** 方法名称 */
  method?: string
  /** 请求方式 */
  requestMethod?: string
  /** 操作类别（0其它 1后台用户 2手机端用户） */
  operatorType?: number
  /** 操作人员 */
  operName?: string
  /** 部门名称 */
  deptName?: string
  /** 请求URL */
  operUrl?: string
  /** 主机地址 */
  operIp?: string
  /** 操作地点 */
  operLocation?: string
  /** 请求参数 */
  operParam?: string
  /** 返回参数 */
  jsonResult?: string
  /** 操作状态（0正常 1异常） */
  status?: number
  /** 错误消息 */
  errorMsg?: string
  /** 操作时间 */
  operTime?: string
  /** 消耗时间 */
  costTime?: number
}

/** 操作日志查询参数 */
export interface OperLogQueryParams extends RuoyiQueryParams {
  /** 模块标题 */
  title?: string
  /** 操作人员 */
  operName?: string
  /** 业务类型 */
  businessType?: number
  /** 状态 */
  status?: number
}

/** 登录日志 */
export interface LoginInfo {
  /** 访问ID */
  infoId?: number
  /** 用户账号 */
  userName?: string
  /** 登录IP地址 */
  ipaddr?: string
  /** 登录地点 */
  loginLocation?: string
  /** 浏览器类型 */
  browser?: string
  /** 操作系统 */
  os?: string
  /** 登录状态（0成功 1失败） */
  status?: string
  /** 提示消息 */
  msg?: string
  /** 访问时间 */
  loginTime?: string
}

/** 登录日志查询参数 */
export interface LoginInfoQueryParams extends RuoyiQueryParams {
  /** 用户账号 */
  userName?: string
  /** 登录IP地址 */
  ipaddr?: string
  /** 登录状态 */
  status?: string
}

/** 服务器信息 */
export interface ServerInfo {
  /** CPU相关信息 */
  cpu: CpuInfo
  /** 內存相关信息 */
  mem: MemInfo
  /** JVM相关信息 */
  jvm: JvmInfo
  /** 服务器相关信息 */
  sys: SysInfo
  /** 磁盘相关信息 */
  sysFiles: SysFileInfo[]
}

/** CPU相关信息 */
export interface CpuInfo {
  /** 核心数 */
  cpuNum: number
  /** CPU总的使用率 */
  total: number
  /** CPU当前使用率 */
  used: number
  /** CPU当前空闲率 */
  free: number
}

/** 內存相关信息 */
export interface MemInfo {
  /** 内存总量 */
  total: number
  /** 已用内存 */
  used: number
  /** 剩余内存 */
  free: number
  /** 使用率 */
  usage: number
}

/** JVM相关信息 */
export interface JvmInfo {
  /** 当前JVM占用的内存总数(M) */
  total: number
  /** JVM最大可用内存总数(M) */
  max: number
  /** JVM空闲内存(M) */
  free: number
  /** JDK版本 */
  version: string
  /** JDK路径 */
  home: string
}

/** 服务器相关信息 */
export interface SysInfo {
  /** 服务器名称 */
  computerName: string
  /** 服务器Ip */
  computerIp: string
  /** 项目路径 */
  userDir: string
  /** 操作系统 */
  osName: string
  /** 系统架构 */
  osArch: string
}

/** 磁盘相关信息 */
export interface SysFileInfo {
  /** 盘符路径 */
  dirName: string
  /** 盘符类型 */
  sysTypeName: string
  /** 文件类型 */
  typeName: string
  /** 总大小 */
  total: string
  /** 剩余大小 */
  free: string
  /** 已经使用量 */
  used: string
  /** 资源的使用率 */
  usage: number
}

/** 缓存信息 */
export interface CacheInfo {
  /** 缓存名称 */
  cacheName: string
  /** 缓存键名 */
  cacheKey: string
  /** 缓存内容 */
  cacheValue: string
  /** 备注 */
  remark?: string
}

/** 缓存查询参数 */
export interface CacheQueryParams {
  /** 缓存名称 */
  cacheName?: string
  /** 缓存键名 */
  cacheKey?: string
}
