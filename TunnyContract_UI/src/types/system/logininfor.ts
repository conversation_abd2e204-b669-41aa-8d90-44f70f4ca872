/**
 * 登录日志相关类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 登录日志信息 */
export interface LoginInfor {
  /** 访问ID */
  infoId?: number
  /** 用户账号 */
  userName?: string
  /** 登录IP地址 */
  ipaddr?: string
  /** 登录地点 */
  loginLocation?: string
  /** 浏览器类型 */
  browser?: string
  /** 操作系统 */
  os?: string
  /** 登录状态（0成功 1失败） */
  status?: string
  /** 提示消息 */
  msg?: string
  /** 访问时间 */
  loginTime?: string
}

/** 登录日志查询参数 */
export interface LoginInforQueryParams extends RuoyiQueryParams {
  /** 用户账号 */
  userName?: string
  /** 登录IP地址 */
  ipaddr?: string
  /** 登录状态 */
  status?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}
