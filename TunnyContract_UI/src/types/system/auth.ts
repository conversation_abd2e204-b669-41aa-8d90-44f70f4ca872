/**
 * 认证相关类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

/** 登录请求参数 */
export interface LoginParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 验证码 */
  code?: string
  /** 唯一标识 */
  uuid?: string
}

/** 登录响应数据 */
export interface LoginResponse {
  /** 访问令牌 */
  token: string
}

/** 验证码响应数据 */
export interface CaptchaResponse {
  /** 是否开启验证码 */
  captchaEnabled: boolean
  /** 验证码图片 */
  img?: string
  /** 唯一标识 */
  uuid?: string
}

/** 用户信息响应数据 */
export interface UserInfoResponse {
  /** 用户信息 */
  user: UserInfo
  /** 角色集合 */
  roles: string[]
  /** 权限集合 */
  permissions: string[]
}

/** 用户基本信息 */
export interface UserInfo {
  /** 用户ID */
  userId: number
  /** 用户账号 */
  userName: string
  /** 用户昵称 */
  nickName: string
  /** 用户邮箱 */
  email?: string
  /** 手机号码 */
  phonenumber?: string
  /** 用户性别 */
  sex?: string
  /** 头像地址 */
  avatar?: string
  /** 部门ID */
  deptId?: number
  /** 部门名称 */
  deptName?: string
  /** 岗位ID列表 */
  postIds?: number[]
  /** 角色ID列表 */
  roleIds?: number[]
}

/** 注册请求参数 */
export interface RegisterParams {
  /** 用户名 */
  username: string
  /** 密码 */
  password: string
  /** 确认密码 */
  confirmPassword: string
  /** 验证码 */
  code?: string
  /** 唯一标识 */
  uuid?: string
}

/** 修改密码请求参数 */
export interface UpdatePasswordParams {
  /** 旧密码 */
  oldPassword: string
  /** 新密码 */
  newPassword: string
}

/** 重置密码请求参数 */
export interface ResetPasswordParams {
  /** 用户ID */
  userId: number
  /** 新密码 */
  password: string
}

/** 个人信息修改参数 */
export interface UpdateProfileParams {
  /** 用户昵称 */
  nickName?: string
  /** 用户邮箱 */
  email?: string
  /** 手机号码 */
  phonenumber?: string
  /** 用户性别 */
  sex?: string
}

/** 头像上传响应 */
export interface AvatarUploadResponse {
  /** 头像地址 */
  imgUrl: string
}

/** 在线用户信息 */
export interface OnlineUser {
  /** 会话编号 */
  tokenId: string
  /** 用户名称 */
  userName: string
  /** 登录IP地址 */
  ipaddr: string
  /** 登录地址 */
  loginLocation: string
  /** 浏览器类型 */
  browser: string
  /** 操作系统 */
  os: string
  /** 登录时间 */
  loginTime: string
}

/** 在线用户查询参数 */
export interface OnlineUserQueryParams {
  /** 用户名称 */
  userName?: string
  /** 登录IP地址 */
  ipaddr?: string
}
