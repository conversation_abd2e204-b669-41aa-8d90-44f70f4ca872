/**
 * 系统类型统一导出
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

// 用户相关类型
export type {
  User,
  Dept,
  Role,
  Post,
  UserQueryParams,
  DeptQueryParams,
  RoleQueryParams,
  PostQueryParams
} from './user'

// 菜单相关类型
export type {
  Menu,
  MenuQueryParams,
  RouterConfig,
  RouterMeta,
  MenuTreeOption,
  RoleMenuTreeSelectResponse
} from './menu'

// 字典相关类型
export type {
  DictType,
  DictData,
  DictTypeQueryParams,
  DictDataQueryParams,
  DictOption,
  Config,
  ConfigQueryParams,
  Notice,
  NoticeQueryParams
} from './dict'

// 认证相关类型
export type {
  LoginParams,
  LoginResponse,
  CaptchaResponse,
  UserInfoResponse,
  UserInfo,
  RegisterParams,
  UpdatePasswordParams,
  ResetPasswordParams,
  UpdateProfileParams,
  AvatarUploadResponse,
  OnlineUser,
  OnlineUserQueryParams
} from './auth'

// 监控相关类型
export type {
  OperLog,
  OperLogQueryParams,
  LoginInfo,
  LoginInfoQueryParams,
  ServerInfo,
  CpuInfo,
  MemInfo,
  JvmInfo,
  SysInfo,
  SysFileInfo,
  CacheInfo,
  CacheQueryParams
} from './monitor'

// 操作日志类型
export type { OperLog, OperLogQueryParams } from './operlog'

// 登录日志类型
export type { LoginInfor, LoginInforQueryParams } from './logininfor'
