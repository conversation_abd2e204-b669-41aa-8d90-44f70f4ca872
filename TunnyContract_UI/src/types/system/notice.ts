import type { RuoyiQueryParams } from '@/types/http'

/** 通知公告信息 */
export interface Notice {
  /** 通知公告ID */
  noticeId?: number
  /** 公告标题 */
  noticeTitle: string
  /** 公告类型（1通知 2公告） */
  noticeType: string
  /** 公告内容 */
  noticeContent?: string
  /** 公告状态（0正常 1关闭） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/** 通知公告查询参数 */
export interface NoticeQueryParams extends RuoyiQueryParams {
  /** 公告标题 */
  noticeTitle?: string
  /** 公告类型 */
  noticeType?: string
  /** 创建者 */
  createBy?: string
  /** 公告状态 */
  status?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}
