/**
 * 合同分类相关类型定义
 * 基于若依数据结构，保持字段名称与后端实体类一致
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 合同分类信息 */
export interface ContractCategory {
  /** 分类ID */
  id?: number
  /** 分类名称 */
  categoryName: string
  /** 分类描述 */
  categoryDesc?: string
  /** 显示顺序 */
  sortOrder?: number
  /** 状态（0正常 1停用） */
  status?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 扩展字段1 */
  attr1?: string
  /** 扩展字段2 */
  attr2?: string
  /** 扩展字段3 */
  attr3?: string
}

/** 合同分类查询参数 */
export interface ContractCategoryQueryParams extends RuoyiQueryParams {
  /** 分类名称 */
  categoryName?: string
  /** 状态（0正常 1停用） */
  status?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}

/** 合同分类表单数据 */
export interface ContractCategoryForm {
  /** 分类ID */
  id?: number
  /** 分类名称 */
  categoryName: string
  /** 分类描述 */
  categoryDesc?: string
  /** 显示顺序 */
  sortOrder?: number
  /** 状态（0正常 1停用） */
  status?: string
  /** 备注 */
  remark?: string
}
