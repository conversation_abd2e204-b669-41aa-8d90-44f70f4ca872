import type { RuoyiQueryParams } from '@/types/http'

/**
 * 合同审查策略对象
 */
export interface ContractReviewStrategy {
  /** 策略ID */
  id?: number
  /** 策略名称 */
  strategyName: string
  /** 策略描述 */
  strategyDesc?: string
  /** 关联合同分类ID */
  categoryId: number
  /** 关联合同分类名称 */
  categoryName?: string
  /** 审查立场(1-甲方，2-乙方) */
  reviewPosition: string
  /** 审查立场显示名称 */
  reviewPositionName?: string
  /** 策略状态(0-草稿，1-已发布) */
  strategyStatus: string
  /** 策略状态显示名称 */
  strategyStatusName?: string
  /** 策略版本号 */
  version?: string
  /** 是否默认策略(1-是，0-否) */
  isDefault?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/**
 * 合同审查策略查询参数
 */
export interface ContractReviewStrategyQueryParams extends RuoyiQueryParams {
  /** 策略名称 */
  strategyName?: string
  /** 关联合同分类ID */
  categoryId?: number
  /** 审查立场(1-甲方，2-乙方) */
  reviewPosition?: string
  /** 策略状态(0-草稿，1-已发布) */
  strategyStatus?: string
  /** 是否默认策略(1-是，0-否) */
  isDefault?: string
  /** 创建时间范围 */
  params?: {
    beginTime?: string
    endTime?: string
  }
}

/**
 * 合同审查策略表单数据
 */
export interface ContractReviewStrategyForm {
  /** 策略ID */
  id?: number
  /** 策略名称 */
  strategyName: string
  /** 策略描述 */
  strategyDesc?: string
  /** 关联合同分类ID */
  categoryId?: number
  /** 审查立场(1-甲方，2-乙方) */
  reviewPosition: string
  /** 策略状态(0-草稿，1-已发布) */
  strategyStatus?: string
  /** 策略版本号 */
  version?: string
  /** 是否默认策略(1-是，0-否) */
  isDefault?: string
  /** 备注 */
  remark?: string
}

/**
 * 审查立场枚举
 */
export enum ReviewPosition {
  /** 甲方 */
  PARTY_A = '1',
  /** 乙方 */
  PARTY_B = '2'
}

/**
 * 策略状态枚举
 */
export enum StrategyStatus {
  /** 草稿 */
  DRAFT = '0',
  /** 已发布 */
  PUBLISHED = '1'
}

/**
 * 审查立场选项
 */
export const REVIEW_POSITION_OPTIONS = [
  { label: '甲方', value: ReviewPosition.PARTY_A },
  { label: '乙方', value: ReviewPosition.PARTY_B }
]

/**
 * 策略状态选项
 */
export const STRATEGY_STATUS_OPTIONS = [
  { label: '草稿', value: StrategyStatus.DRAFT },
  { label: '已发布', value: StrategyStatus.PUBLISHED }
]
