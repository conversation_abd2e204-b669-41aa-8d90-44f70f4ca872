import type { RuoyiQueryParams } from '@/types/http'

/**
 * 合同审查任务对象
 */
export interface ContractReviewTask {
  /** 任务ID */
  id?: number
  /** 任务编号 */
  taskNo?: string
  /** 任务名称 */
  taskName: string
  /** 合同分类ID */
  categoryId: number
  /** 合同分类名称 */
  categoryName?: string
  /** 审查策略ID */
  strategyId: number
  /** 审查策略名称 */
  strategyName?: string
  /** 审查立场(1-甲方，2-乙方) */
  reviewPosition: string
  /** 审查立场显示名称 */
  reviewPositionName?: string
  /** 任务状态(0-待处理，1-处理中，2-已完成，3-失败) */
  taskStatus: string
  /** 任务状态显示名称 */
  taskStatusName?: string
  /** 开始处理时间 */
  startTime?: string
  /** 完成时间 */
  endTime?: string
  /** 识别风险总数 */
  totalRiskCount?: number
  /** 重大风险数量 */
  highRiskCount?: number
  /** 一般风险数量 */
  normalRiskCount?: number
  /** 错误信息 */
  errorMessage?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 删除标志 */
  delFlag?: string
  /** 保留字段1 */
  attr1?: string
  /** 保留字段2 */
  attr2?: string
  /** 保留字段3 */
  attr3?: string
  /** 文件总数 */
  totalFiles?: number
  /** 已解析文件数 */
  parsedFiles?: number
  /** 解析失败文件数 */
  failedFiles?: number
  /** 解析中文件数 */
  parsingFiles?: number
  /** 待解析文件数 */
  pendingFiles?: number
  /** 文件解析汇总状态 (0-未开始，1-解析中，2-全部成功，3-部分失败) */
  fileParseStatus?: string
}

/**
 * 合同审查任务查询参数
 */
export interface ContractReviewTaskQueryParams extends RuoyiQueryParams {
  /** 任务名称 */
  taskName?: string
  /** 合同分类ID */
  categoryId?: number
  /** 审查策略ID */
  strategyId?: number
  /** 审查立场(1-甲方，2-乙方) */
  reviewPosition?: string
  /** 任务状态(0-待处理，1-处理中，2-已完成，3-失败) */
  taskStatus?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间范围 */
  params?: {
    beginTime?: string
    endTime?: string
  }
}

/**
 * 合同审查任务表单数据
 */
export interface ContractReviewTaskForm {
  /** 任务ID */
  id?: number
  /** 任务名称 */
  taskName: string
  /** 合同分类ID */
  categoryId?: number
  /** 审查策略ID */
  strategyId?: number
  /** 审查立场(1-甲方，2-乙方) */
  reviewPosition: string
  /** 备注 */
  remark?: string
}

/**
 * 审查立场枚举
 */
export enum ReviewPosition {
  /** 甲方 */
  PARTY_A = '1',
  /** 乙方 */
  PARTY_B = '2'
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  /** 待处理 */
  PENDING = '0',
  /** 处理中 */
  PROCESSING = '1',
  /** 已完成 */
  COMPLETED = '2',
  /** 失败 */
  FAILED = '3'
}

/**
 * 审查立场选项
 */
export const REVIEW_POSITION_OPTIONS = [
  { label: '甲方', value: ReviewPosition.PARTY_A },
  { label: '乙方', value: ReviewPosition.PARTY_B }
]

/**
 * 任务状态选项
 */
export const TASK_STATUS_OPTIONS = [
  { label: '待处理', value: TaskStatus.PENDING },
  { label: '处理中', value: TaskStatus.PROCESSING },
  { label: '已完成', value: TaskStatus.COMPLETED },
  { label: '失败', value: TaskStatus.FAILED }
]

/**
 * 任务状态标签类型映射
 */
export const TASK_STATUS_TAG_TYPE = {
  [TaskStatus.PENDING]: 'info',
  [TaskStatus.PROCESSING]: 'warning',
  [TaskStatus.COMPLETED]: 'success',
  [TaskStatus.FAILED]: 'danger'
} as const

/**
 * 任务状态显示名称映射
 */
export const TASK_STATUS_LABELS = {
  [TaskStatus.PENDING]: '待处理',
  [TaskStatus.PROCESSING]: '处理中',
  [TaskStatus.COMPLETED]: '已完成',
  [TaskStatus.FAILED]: '失败'
} as const

/**
 * 审查立场显示名称映射
 */
export const REVIEW_POSITION_LABELS = {
  [ReviewPosition.PARTY_A]: '甲方',
  [ReviewPosition.PARTY_B]: '乙方'
} as const
