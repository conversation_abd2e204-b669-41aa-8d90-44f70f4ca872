import type { RuoyiQueryParams } from '@/types/http'

/**
 * 合同审查条款对象
 */
export interface ContractReviewClause {
  /** 条款ID */
  id?: number
  /** 关联策略ID */
  strategyId: number
  /** 条款名称 */
  clauseName: string
  /** 条款说明 */
  clauseDesc?: string
  /** 条款详细内容 */
  clauseContent?: string
  /** 排序序号 */
  sortOrder?: number
  /** 条款状态(1-启用，0-禁用) */
  clauseStatus?: string
  /** 条款状态显示名称 */
  clauseStatusName?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 扩展字段1 */
  attr1?: string
  /** 扩展字段2 */
  attr2?: string
  /** 扩展字段3 */
  attr3?: string
}

/**
 * 合同审查条款查询参数
 */
export interface ContractReviewClauseQueryParams extends RuoyiQueryParams {
  /** 关联策略ID */
  strategyId?: number
  /** 条款名称 */
  clauseName?: string
  /** 条款说明 */
  clauseDesc?: string
  /** 条款状态(1-启用，0-禁用) */
  clauseStatus?: string
  /** 创建时间范围 */
  params?: {
    beginTime?: string
    endTime?: string
  }
}

/**
 * 合同审查条款表单数据
 */
export interface ContractReviewClauseForm {
  /** 条款ID */
  id?: number
  /** 关联策略ID */
  strategyId: number
  /** 条款名称 */
  clauseName: string
  /** 条款说明 */
  clauseDesc?: string
  /** 条款详细内容 */
  clauseContent?: string
  /** 排序序号 */
  sortOrder?: number
  /** 条款状态(1-启用，0-禁用) */
  clauseStatus?: string
}

/**
 * 条款状态枚举
 */
export enum ClauseStatus {
  /** 禁用 */
  DISABLED = '0',
  /** 启用 */
  ENABLED = '1'
}

/**
 * 条款状态选项
 */
export const CLAUSE_STATUS_OPTIONS = [
  { label: '启用', value: ClauseStatus.ENABLED },
  { label: '禁用', value: ClauseStatus.DISABLED }
]

/**
 * 条款排序方向枚举
 */
export enum ClauseSortDirection {
  /** 上移 */
  UP = 'up',
  /** 下移 */
  DOWN = 'down'
}
