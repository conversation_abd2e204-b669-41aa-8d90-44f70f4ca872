import type { RuoyiQueryParams } from '@/types/http'

/**
 * 合同风险点对象
 */
export interface ContractRiskPoint {
  /** 风险点ID */
  id?: number
  /** 关联条款ID */
  clauseId: number
  /** 风险点名称 */
  riskName: string
  /** 风险点描述 */
  riskDesc?: string
  /** 风险等级(1-重大风险，2-一般风险) */
  riskLevel: string
  /** 风险等级显示名称 */
  riskLevelName?: string
  /** 风险分析 */
  riskAnalysis?: string
  /** 修改建议 */
  suggestModify?: string
  /** 关键词匹配模式 */
  keywordPattern?: string
  /** 排序序号 */
  sortOrder?: number
  /** 风险点状态(1-启用，0-禁用) */
  riskStatus?: string
  /** 风险点状态显示名称 */
  riskStatusName?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 扩展字段1 */
  attr1?: string
  /** 扩展字段2 */
  attr2?: string
  /** 扩展字段3 */
  attr3?: string
}

/**
 * 合同风险点查询参数
 */
export interface ContractRiskPointQueryParams extends RuoyiQueryParams {
  /** 关联条款ID */
  clauseId?: number
  /** 风险点名称 */
  riskName?: string
  /** 风险等级(1-重大风险，2-一般风险) */
  riskLevel?: string
  /** 风险点状态(1-启用，0-禁用) */
  riskStatus?: string
  /** 创建时间范围 */
  params?: {
    beginTime?: string
    endTime?: string
  }
}

/**
 * 合同风险点表单数据
 */
export interface ContractRiskPointForm {
  /** 风险点ID */
  id?: number
  /** 关联条款ID */
  clauseId: number
  /** 风险点名称 */
  riskName: string
  /** 风险点描述 */
  riskDesc?: string
  /** 风险等级(1-重大风险，2-一般风险) */
  riskLevel: string
  /** 风险分析 */
  riskAnalysis?: string
  /** 修改建议 */
  suggestModify?: string
  /** 关键词匹配模式 */
  keywordPattern?: string
  /** 排序序号 */
  sortOrder?: number
  /** 风险点状态(1-启用，0-禁用) */
  riskStatus?: string
}

/**
 * 风险等级枚举
 */
export enum RiskLevel {
  /** 重大风险 */
  MAJOR = '1',
  /** 一般风险 */
  GENERAL = '2'
}

/**
 * 风险点状态枚举
 */
export enum RiskStatus {
  /** 禁用 */
  DISABLED = '0',
  /** 启用 */
  ENABLED = '1'
}

/**
 * 风险等级选项
 */
export const RISK_LEVEL_OPTIONS = [
  { label: '重大风险', value: RiskLevel.MAJOR, color: 'danger', icon: 'Warning' },
  { label: '一般风险', value: RiskLevel.GENERAL, color: 'warning', icon: 'InfoFilled' }
]

/**
 * 风险点状态选项
 */
export const RISK_STATUS_OPTIONS = [
  { label: '启用', value: RiskStatus.ENABLED },
  { label: '禁用', value: RiskStatus.DISABLED }
]
