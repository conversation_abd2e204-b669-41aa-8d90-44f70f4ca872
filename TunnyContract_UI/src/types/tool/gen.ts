import type { RuoyiQueryParams } from '@/types/http'

/** 代码生成表信息 */
export interface GenTable {
  /** 编号 */
  tableId?: number
  /** 表名称 */
  tableName?: string
  /** 表描述 */
  tableComment?: string
  /** 关联子表的表名 */
  subTableName?: string
  /** 子表关联的外键名 */
  subTableFkName?: string
  /** 实体类名称 */
  className?: string
  /** 使用的模板（crud单表操作 tree树表操作 sub主子表操作） */
  tplCategory?: string
  /** 生成包路径 */
  packageName?: string
  /** 生成模块名 */
  moduleName?: string
  /** 生成业务名 */
  businessName?: string
  /** 生成功能名 */
  functionName?: string
  /** 生成功能作者 */
  functionAuthor?: string
  /** 生成代码方式（0zip压缩包 1自定义路径） */
  genType?: string
  /** 生成路径（不填默认项目路径） */
  genPath?: string
  /** 其它生成选项 */
  options?: string
  /** 备注 */
  remark?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 表列信息 */
  columns?: GenTableColumn[]
  /** 主键信息 */
  pkColumn?: GenTableColumn
  /** 主键列表 */
  pkColumns?: GenTableColumn[]
  /** 子表信息 */
  subTable?: GenTable
  /** 树编码字段 */
  treeCode?: string
  /** 树父编码字段 */
  treeParentCode?: string
  /** 树名称字段 */
  treeName?: string
  /** 上级菜单ID字段 */
  parentMenuId?: string
  /** 上级菜单名称字段 */
  parentMenuName?: string
}

/** 代码生成表列信息 */
export interface GenTableColumn {
  /** 编号 */
  columnId?: number
  /** 表编号 */
  tableId?: number
  /** 列名称 */
  columnName?: string
  /** 列描述 */
  columnComment?: string
  /** 列类型 */
  columnType?: string
  /** JAVA类型 */
  javaType?: string
  /** JAVA字段名 */
  javaField?: string
  /** 是否主键（1是） */
  isPk?: string
  /** 是否自增（1是） */
  isIncrement?: string
  /** 是否必填（1是） */
  isRequired?: string
  /** 是否为插入字段（1是） */
  isInsert?: string
  /** 是否编辑字段（1是） */
  isEdit?: string
  /** 是否列表字段（1是） */
  isList?: string
  /** 是否查询字段（1是） */
  isQuery?: string
  /** 查询方式（等于、不等于、大于、小于、范围） */
  queryType?: string
  /** 显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件） */
  htmlType?: string
  /** 字典类型 */
  dictType?: string
  /** 排序 */
  sort?: number
}

/** 数据库表信息 */
export interface DbTable {
  /** 表名称 */
  tableName?: string
  /** 表描述 */
  tableComment?: string
  /** 创建时间 */
  createTime?: string
  /** 更新时间 */
  updateTime?: string
}

/** 代码生成查询参数 */
export interface GenTableQueryParams extends RuoyiQueryParams {
  /** 表名称 */
  tableName?: string
  /** 表描述 */
  tableComment?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}

/** 数据库表查询参数 */
export interface DbTableQueryParams extends RuoyiQueryParams {
  /** 表名称 */
  tableName?: string
  /** 表描述 */
  tableComment?: string
}

/** 导入表参数 */
export interface ImportTableParams {
  /** 表名列表 */
  tables?: string
}
