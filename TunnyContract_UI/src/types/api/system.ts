/**
 * 系统管理API类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiResponse } from '@/types/http'
import type {
  User,
  Dept,
  Role,
  Post,
  Menu,
  DictType,
  DictData,
  Config,
  Notice,
  LoginResponse as SystemLoginResponse,
  CaptchaResponse as SystemCaptchaResponse,
  UserInfoResponse as SystemUserInfoResponse,
  OnlineUser,
  OperLog,
  LoginInfo,
  ServerInfo,
  CacheInfo,
  MenuTreeOption,
  RoleMenuTreeSelectResponse,
  DictOption
} from '@/types/system'

/** 用户管理API类型 */
export type UserApiListResponse = RuoyiResponse<User>
export type UserApiDetailResponse = RuoyiResponse<{
  data: User
  posts: Post[]
  roles: Role[]
  postIds: number[]
  roleIds: number[]
}>
export type UserApiSaveResponse = RuoyiResponse<null>
export type UserApiDeleteResponse = RuoyiResponse<null>
export type UserApiStatusResponse = RuoyiResponse<null>
export type UserApiResetPwdResponse = RuoyiResponse<null>
export type UserApiDeptTreeResponse = RuoyiResponse<Dept[]>

/** 角色管理API类型 */
export type RoleApiListResponse = RuoyiResponse<Role>
export type RoleApiDetailResponse = RuoyiResponse<Role>
export type RoleApiSaveResponse = RuoyiResponse<null>
export type RoleApiDeleteResponse = RuoyiResponse<null>
export type RoleApiStatusResponse = RuoyiResponse<null>
export type RoleApiDataScopeResponse = RuoyiResponse<null>
export type RoleApiMenuTreeResponse = RuoyiResponse<RoleMenuTreeSelectResponse>
export type RoleApiDeptTreeResponse = RuoyiResponse<{
  depts: Dept[]
  checkedKeys: number[]
}>

/** 菜单管理API类型 */
export type MenuApiListResponse = RuoyiResponse<Menu[]>
export type MenuApiDetailResponse = RuoyiResponse<Menu>
export type MenuApiSaveResponse = RuoyiResponse<null>
export type MenuApiDeleteResponse = RuoyiResponse<null>
export type MenuApiTreeResponse = RuoyiResponse<MenuTreeOption[]>
export type MenuApiRoutersResponse = RuoyiResponse<Menu[]>

/** 部门管理API类型 */
export type DeptApiListResponse = RuoyiResponse<Dept[]>
export type DeptApiDetailResponse = RuoyiResponse<Dept>
export type DeptApiSaveResponse = RuoyiResponse<null>
export type DeptApiDeleteResponse = RuoyiResponse<null>
export type DeptApiTreeResponse = RuoyiResponse<Dept[]>

/** 岗位管理API类型 */
export type PostApiListResponse = RuoyiResponse<Post>
export type PostApiDetailResponse = RuoyiResponse<Post>
export type PostApiSaveResponse = RuoyiResponse<null>
export type PostApiDeleteResponse = RuoyiResponse<null>

/** 字典管理API类型 */
export type DictApiTypeListResponse = RuoyiResponse<DictType>
export type DictApiTypeDetailResponse = RuoyiResponse<DictType>
export type DictApiDataListResponse = RuoyiResponse<DictData>
export type DictApiDataDetailResponse = RuoyiResponse<DictData>
export type DictApiOptionsResponse = RuoyiResponse<DictOption[]>
export type DictApiSaveResponse = RuoyiResponse<null>
export type DictApiDeleteResponse = RuoyiResponse<null>

/** 参数配置API类型 */
export type ConfigApiListResponse = RuoyiResponse<Config>
export type ConfigApiDetailResponse = RuoyiResponse<Config>
export type ConfigApiSaveResponse = RuoyiResponse<null>
export type ConfigApiDeleteResponse = RuoyiResponse<null>
export type ConfigApiKeyResponse = RuoyiResponse<string>

/** 通知公告API类型 */
export type NoticeApiListResponse = RuoyiResponse<Notice>
export type NoticeApiDetailResponse = RuoyiResponse<Notice>
export type NoticeApiSaveResponse = RuoyiResponse<null>
export type NoticeApiDeleteResponse = RuoyiResponse<null>

/** 认证API类型 */
export type AuthApiLoginResponse = RuoyiResponse<SystemLoginResponse>
export type AuthApiCaptchaResponse = RuoyiResponse<SystemCaptchaResponse>
export type AuthApiUserInfoResponse = RuoyiResponse<SystemUserInfoResponse>
export type AuthApiRegisterResponse = RuoyiResponse<null>
export type AuthApiLogoutResponse = RuoyiResponse<null>

/** 监控API类型 */
export type MonitorApiOnlineListResponse = RuoyiResponse<OnlineUser>
export type MonitorApiOperLogListResponse = RuoyiResponse<OperLog>
export type MonitorApiLoginLogListResponse = RuoyiResponse<LoginInfo>
export type MonitorApiServerInfoResponse = RuoyiResponse<ServerInfo>
export type MonitorApiCacheInfoResponse = RuoyiResponse<CacheInfo[]>
