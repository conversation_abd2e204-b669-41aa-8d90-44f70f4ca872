/**
 * 岗位管理相关类型定义
 * 基于RuoYi数据结构，支持one-piece-pro组件
 */

// 岗位基础信息接口
export interface Post {
  postId?: number
  postCode: string
  postName: string
  postSort: number
  status: string
  remark?: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
}

// 岗位查询参数
export interface PostQueryParams {
  pageNum?: number
  pageSize?: number
  postCode?: string
  postName?: string
  status?: string
}

// 岗位表单数据
export interface PostForm {
  postId?: number
  postCode: string
  postName: string
  postSort: number
  status: string
  remark?: string
}

// 岗位列表响应类型
export interface PostListResponse {
  code: number
  msg: string
  rows: Post[]
  total: number
}

// 岗位详情响应类型
export interface PostDetailResponse {
  code: number
  msg: string
  data: Post
}

// 岗位选项类型（用于下拉选择）
export interface PostOption {
  postId: number
  postName: string
  postCode: string
}
