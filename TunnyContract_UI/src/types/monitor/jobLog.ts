import type { RuoyiQueryParams } from '@/types/api/common'

/**
 * 定时任务调度日志对象
 */
export interface JobLog {
  /** 日志序号 */
  jobLogId?: number
  /** 任务名称 */
  jobName?: string
  /** 任务组名 */
  jobGroup?: string
  /** 调用目标字符串 */
  invokeTarget?: string
  /** 日志信息 */
  jobMessage?: string
  /** 执行状态（0正常 1失败） */
  status?: string
  /** 异常信息 */
  exceptionInfo?: string
  /** 开始时间 */
  startTime?: string
  /** 停止时间 */
  stopTime?: string
  /** 创建时间 */
  createTime?: string
}

/**
 * 定时任务调度日志查询参数
 */
export interface JobLogQueryParams extends RuoyiQueryParams {
  /** 任务名称 */
  jobName?: string
  /** 任务组名 */
  jobGroup?: string
  /** 执行状态 */
  status?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}
