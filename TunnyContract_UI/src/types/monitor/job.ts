import type { RuoyiQueryParams } from '@/types/api/common'

/**
 * 定时任务对象
 */
export interface Job {
  /** 任务ID */
  jobId?: number
  /** 任务名称 */
  jobName: string
  /** 任务组名 */
  jobGroup: string
  /** 调用目标字符串 */
  invokeTarget: string
  /** cron执行表达式 */
  cronExpression: string
  /** 计划执行错误策略（1立即执行 2执行一次 3放弃执行） */
  misfirePolicy?: string
  /** 是否并发执行（0允许 1禁止） */
  concurrent?: string
  /** 状态（0正常 1暂停） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注信息 */
  remark?: string
}

/**
 * 定时任务查询参数
 */
export interface JobQueryParams extends RuoyiQueryParams {
  /** 任务名称 */
  jobName?: string
  /** 任务组名 */
  jobGroup?: string
  /** 状态 */
  status?: string
}

/**
 * 定时任务表单
 */
export interface JobForm {
  /** 任务ID */
  jobId?: number
  /** 任务名称 */
  jobName: string
  /** 任务组名 */
  jobGroup: string
  /** 调用目标字符串 */
  invokeTarget: string
  /** cron执行表达式 */
  cronExpression: string
  /** 计划执行错误策略 */
  misfirePolicy?: string
  /** 是否并发执行 */
  concurrent?: string
  /** 状态 */
  status?: string
  /** 备注信息 */
  remark?: string
  /** 创建时间 */
  createTime?: string
  /** 下次执行时间 */
  nextValidTime?: string
  /** 创建者 */
  createBy?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
}
