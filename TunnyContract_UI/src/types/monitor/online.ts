import type { RuoyiQueryParams } from '@/types/http'

/** 在线用户信息 */
export interface OnlineUser {
  /** 会话Token ID */
  tokenId: string
  /** 用户名称 */
  userName: string
  /** 所属部门 */
  deptName?: string
  /** 登录IP地址 */
  ipaddr: string
  /** 登录地点 */
  loginLocation?: string
  /** 操作系统 */
  os?: string
  /** 浏览器类型 */
  browser?: string
  /** 登录时间 */
  loginTime: string
}

/** 在线用户查询参数 */
export interface OnlineUserQueryParams extends RuoyiQueryParams {
  /** 登录IP地址 */
  ipaddr?: string
  /** 用户名称 */
  userName?: string
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}
