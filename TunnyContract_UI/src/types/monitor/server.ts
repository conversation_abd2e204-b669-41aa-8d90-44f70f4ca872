/**
 * CPU信息接口
 */
export interface CpuInfo {
  /** CPU核心数 */
  cpuNum?: number
  /** 总计 */
  total?: number
  /** 用户使用率 */
  used?: number
  /** 系统使用率 */
  sys?: number
  /** 等待使用率 */
  wait?: number
  /** 当前空闲率 */
  free?: number
}

/**
 * 内存信息接口
 */
export interface MemInfo {
  /** 总内存(G) */
  total?: number
  /** 已用内存(G) */
  used?: number
  /** 剩余内存(G) */
  free?: number
  /** 使用率(%) */
  usage?: number
}

/**
 * JVM信息接口
 */
export interface JvmInfo {
  /** 总内存(M) */
  total?: number
  /** 最大内存(M) */
  max?: number
  /** 已用内存(M) */
  used?: number
  /** 剩余内存(M) */
  free?: number
  /** 使用率(%) */
  usage?: number
  /** JVM版本 */
  version?: string
  /** JVM主目录 */
  home?: string
  /** JVM名称 */
  name?: string
  /** 启动时间 */
  startTime?: string
  /** 运行时长 */
  runTime?: string
  /** 输入参数 */
  inputArgs?: string
}

/**
 * 系统信息接口
 */
export interface SystemInfo {
  /** 服务器名称 */
  computerName?: string
  /** 服务器IP */
  computerIp?: string
  /** 操作系统 */
  osName?: string
  /** 系统架构 */
  osArch?: string
  /** 项目路径 */
  userDir?: string
}

/**
 * 系统文件接口
 */
export interface SysFile {
  /** 盘符路径 */
  dirName?: string
  /** 盘符类型 */
  sysTypeName?: string
  /** 文件类型 */
  typeName?: string
  /** 总大小 */
  total?: string
  /** 剩余大小 */
  free?: string
  /** 已经使用量 */
  used?: string
  /** 资源的使用率 */
  usage?: number
}

/**
 * 服务器监控信息接口
 */
export interface Server {
  /** CPU信息 */
  cpu?: CpuInfo
  /** 内存信息 */
  mem?: MemInfo
  /** JVM信息 */
  jvm?: JvmInfo
  /** 系统信息 */
  sys?: SystemInfo
  /** 系统文件信息 */
  sysFiles?: SysFile[]
}
