import type { RuoyiQueryParams } from '@/types/http'

/**
 * 缓存信息接口
 */
export interface CacheInfo {
  /** Redis版本 */
  redis_version?: string
  /** 运行模式 */
  redis_mode?: string
  /** 端口 */
  tcp_port?: string
  /** 客户端数 */
  connected_clients?: string
  /** 运行时间(天) */
  uptime_in_days?: string
  /** 使用内存 */
  used_memory_human?: string
  /** 使用CPU */
  used_cpu_user_children?: string
  /** 内存配置 */
  maxmemory_human?: string
  /** AOF是否开启 */
  aof_enabled?: string
  /** RDB是否成功 */
  rdb_last_bgsave_status?: string
  /** 网络入口速率 */
  instantaneous_input_kbps?: string
  /** 网络出口速率 */
  instantaneous_output_kbps?: string
}

/**
 * 命令统计信息接口
 */
export interface CommandStats {
  /** 命令名称 */
  name?: string
  /** 调用次数 */
  value?: number
}

/**
 * 缓存监控数据接口
 */
export interface Cache {
  /** 基本信息 */
  info?: CacheInfo
  /** 数据库大小 */
  dbSize?: number
  /** 命令统计 */
  commandStats?: CommandStats[]
}

/**
 * 缓存名称信息接口
 */
export interface CacheName {
  /** 缓存名称 */
  cacheName?: string
  /** 缓存键名 */
  cacheKey?: string
  /** 缓存值 */
  cacheValue?: string
  /** 备注 */
  remark?: string
}

/**
 * 缓存键值对接口
 */
export interface CacheKeyValue {
  /** 缓存名称 */
  cacheName?: string
  /** 缓存键名 */
  cacheKey?: string
  /** 缓存值 */
  cacheValue?: string
}

/**
 * 缓存查询参数接口
 */
export interface CacheQueryParams extends RuoyiQueryParams {
  /** 缓存名称 */
  cacheName?: string
  /** 缓存键名 */
  cacheKey?: string
}
