/**
 * HTTP相关类型定义
 * 直接支持RuoYi响应格式，不使用适配器模式
 */

/** RuoYi标准响应格式 */
export interface RuoyiResponse<T = any> {
  /** 状态码：200-成功，500-服务器错误，601-警告，401-未授权 */
  code: number
  /** 响应消息 */
  msg: string
  /** 列表数据（分页查询时使用） */
  rows?: T[]
  /** 总记录数（分页查询时使用） */
  total?: number
  /** 单个数据对象（详情查询时使用） */
  data?: T
  /** 其他扩展字段 */
  [key: string]: any
}

/** RuoYi分页查询参数 */
export interface RuoyiPageParams {
  /** 页码 */
  pageNum?: number
  /** 每页大小 */
  pageSize?: number
}

/** RuoYi排序参数 */
export interface RuoyiSortParams {
  /** 排序字段 */
  orderByColumn?: string
  /** 排序方向：asc-升序，desc-降序 */
  isAsc?: 'asc' | 'desc'
}

/** RuoYi查询参数基类 */
export interface RuoyiQueryParams extends RuoyiPageParams, RuoyiSortParams {
  /** 开始时间 */
  beginTime?: string
  /** 结束时间 */
  endTime?: string
}

/** HTTP请求配置扩展 */
export interface HttpRequestConfig {
  /** 请求URL */
  url: string
  /** 请求参数 */
  params?: any
  /** 请求体数据 */
  data?: any
  /** 请求头 */
  headers?: Record<string, string>
  /** 是否显示错误消息 */
  showErrorMessage?: boolean
  /** 是否需要token */
  isToken?: boolean
  /** 是否防重复提交 */
  repeatSubmit?: boolean
  /** 响应类型 */
  responseType?: 'json' | 'blob' | 'arraybuffer'
}

/** RuoYi错误码枚举 */
export enum RuoyiStatusCode {
  /** 成功 */
  SUCCESS = 200,
  /** 未授权 */
  UNAUTHORIZED = 401,
  /** 服务器错误 */
  SERVER_ERROR = 500,
  /** 警告 */
  WARNING = 601
}
