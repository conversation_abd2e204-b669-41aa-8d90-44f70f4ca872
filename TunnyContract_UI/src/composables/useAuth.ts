import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store/modules/user'

/**
 * 权限验证composable
 * 直接支持RuoYi权限标识格式，如 'system:user:add'
 * 用法：
 * const { hasAuth, hasRole, hasPermiOr, hasPermiAnd, hasRoleOr, hasRoleAnd } = useAuth()
 * hasAuth('system:user:add') // 检查是否拥有指定权限
 * hasRole('admin') // 检查是否拥有指定角色
 */
export const useAuth = () => {
  const userStore = useUserStore()
  const { permissions, roles } = storeToRefs(userStore)

  // 获取用户权限列表
  // const userPermissions = computed(() => permissions.value || [])
  // 获取用户角色列表
  // const userRoles = computed(() => roles.value || [])

  /**
   * 验证用户是否具备某权限
   * @param permission 权限标识，如 'system:user:add'
   * @returns 是否有权限
   */
  const hasAuth = (permission: string): boolean => {
    if (!permission || permission.length === 0) {
      return false
    }

    const all_permission = '*:*:*'
    const currentPermissions = permissions.value

    return currentPermissions.some((v: string) => {
      return all_permission === v || v === permission
    })
  }

  /**
   * 验证用户是否含有指定权限，只需包含其中一个
   * @param permissionList 权限标识数组
   * @returns 是否有权限
   */
  const hasPermiOr = (permissionList: string[]): boolean => {
    return permissionList.some((item) => hasAuth(item))
  }

  /**
   * 验证用户是否含有指定权限，必须全部拥有
   * @param permissionList 权限标识数组
   * @returns 是否有权限
   */
  const hasPermiAnd = (permissionList: string[]): boolean => {
    return permissionList.every((item) => hasAuth(item))
  }

  /**
   * 验证用户是否具备某角色
   * @param role 角色标识，如 'admin'
   * @returns 是否有角色
   */
  const hasRole = (role: string): boolean => {
    if (!role || role.length === 0) {
      return false
    }

    const super_admin = 'admin'
    const currentRoles = roles.value

    return currentRoles.some((v: string) => {
      return super_admin === v || v === role
    })
  }

  /**
   * 验证用户是否含有指定角色，只需包含其中一个
   * @param roleList 角色标识数组
   * @returns 是否有角色
   */
  const hasRoleOr = (roleList: string[]): boolean => {
    return roleList.some((item) => hasRole(item))
  }

  /**
   * 验证用户是否含有指定角色，必须全部拥有
   * @param roleList 角色标识数组
   * @returns 是否有角色
   */
  const hasRoleAnd = (roleList: string[]): boolean => {
    return roleList.every((item) => hasRole(item))
  }

  return {
    hasAuth,
    hasPermiOr,
    hasPermiAnd,
    hasRole,
    hasRoleOr,
    hasRoleAnd,
    permissions,
    roles
  }
}
