<template>
  <div class="config-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:config:add'" type="primary" @click="handleAdd" v-ripple>
            新增参数
          </ElButton>
          <ElButton
            v-auth="'system:config:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'system:config:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:config:export'" type="warning" @click="handleExport" v-ripple>
            导出
          </ElButton>
          <ElButton
            v-auth="'system:config:remove'"
            type="info"
            @click="handleRefreshCache"
            v-ripple
          >
            刷新缓存
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="configId"
        :loading="loading"
        :columns="columns"
        :data="configList"
        :stripe="true"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #operation="{ row }">
          <el-tooltip content="修改" placement="top">
            <el-button
              v-ripple
              v-auth="'system:config:edit'"
              link
              type="primary"
              @click="handleUpdate(row)"
            >
              编辑
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-ripple
              v-auth="'system:config:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>

      <!-- 添加或修改参数配置对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="500px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElFormItem label="参数名称" prop="configName">
            <ElInput v-model="form.configName" placeholder="请输入参数名称" />
          </ElFormItem>
          <ElFormItem label="参数键名" prop="configKey">
            <ElInput v-model="form.configKey" placeholder="请输入参数键名" />
          </ElFormItem>
          <ElFormItem label="参数键值" prop="configValue">
            <ElInput
              v-model="form.configValue"
              type="textarea"
              placeholder="请输入参数键值"
              :rows="3"
            />
          </ElFormItem>
          <ElFormItem label="系统内置" prop="configType">
            <ElRadioGroup v-model="form.configType">
              <ElRadio value="Y">是</ElRadio>
              <ElRadio value="N">否</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3" />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  // import { useAuth } from '@/composables/useAuth'
  import { ConfigApi } from '@/api/system/config'
  import type { Config } from '@/types/system/dict'
  import type { FormInstance, FormRules } from 'element-plus'

  defineOptions({ name: 'SystemConfig' })

  // const { hasAuth } = useAuth()

  const dialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    configName: '',
    configKey: '',
    configType: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 表单数据
  const form = reactive<Config>({
    configId: undefined,
    configName: '',
    configKey: '',
    configValue: '',
    configType: 'Y',
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    configName: [{ required: true, message: '参数名称不能为空', trigger: 'blur' }],
    configKey: [{ required: true, message: '参数键名不能为空', trigger: 'blur' }],
    configValue: [{ required: true, message: '参数键值不能为空', trigger: 'blur' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改参数' : '新增参数'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '参数名称',
      key: 'configName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '参数键名',
      key: 'configKey',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '系统内置',
      key: 'configType',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '是', value: 'Y' },
        { label: '否', value: 'N' }
      ]
    }
  ])

  // 使用useTable组合式函数
  const {
    data: configList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<Config>({
    core: {
      apiFn: (params: any) => ConfigApi.getConfigList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        configName: '',
        configKey: '',
        configType: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'configId',
          label: '参数主键',
          width: 100,
          checked: true
        },
        {
          prop: 'configName',
          label: '参数名称',
          minWidth: 120,
          checked: true
        },
        {
          prop: 'configKey',
          label: '参数键名',
          minWidth: 120,
          checked: true
        },
        {
          prop: 'configValue',
          label: '参数键值',
          minWidth: 120,
          checked: true
        },
        {
          prop: 'configType',
          label: '系统内置',
          width: 100,
          checked: true,
          formatter: (row: Config) => {
            return h(ElTag, { type: row.configType === 'Y' ? 'success' : 'info' }, () =>
              row.configType === 'Y' ? '是' : '否'
            )
          }
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 120,
          checked: false,
          formatter: (row: Config) => row.remark || '--'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          checked: true,
          formatter: (row: Config) => row.createTime || '--'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // useTable会自动在mounted时加载数据

  const handleRefresh = () => {
    console.log('参数配置管理刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: Config[]) => {
    ids.value = selection.map((item) => item.configId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    dialogVisible.value = true
    isEdit.value = false
  }

  // 修改按钮操作
  const handleUpdate = async (row?: Config) => {
    resetForm()
    const configId = row?.configId || ids.value[0]

    try {
      const response = await ConfigApi.getConfigDetail(configId)
      const configData = (response.data as any)?.data || response.data
      Object.assign(form, configData)
      dialogVisible.value = true
      isEdit.value = true
    } catch (_error) {
      console.error('获取参数配置信息失败:', _error)
      ElMessage.error('获取参数配置信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row?: Config) => {
    const configIds = row?.configId ? [row.configId] : ids.value

    try {
      await ElMessageBox.confirm(
        `是否确认删除参数编号为"${configIds.join(',')}"的数据项？`,
        '系统提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await ConfigApi.deleteConfig(configIds.length === 1 ? configIds[0] : configIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (_error) {
      if (_error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 导出按钮操作
  const handleExport = async () => {
    try {
      const response = await ConfigApi.exportConfig(searchParams as any)
      const { handleExportResponse } = await import('@/utils/download')
      handleExportResponse(response, `config_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (_error) {
      // console.error('导出失败:', _error)
      ElMessage.error('导出失败')
    }
  }

  // 刷新缓存按钮操作
  const handleRefreshCache = async () => {
    try {
      await ConfigApi.refreshConfigCache()
      ElMessage.success('刷新缓存成功')
    } catch {
      ElMessage.error('刷新缓存失败')
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.configId) {
            await ConfigApi.updateConfig(form)
            ElMessage.success('修改成功')
          } else {
            await ConfigApi.addConfig(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          refreshData()
        } catch {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.configId = undefined
    form.configName = ''
    form.configKey = ''
    form.configValue = ''
    form.configType = 'Y'
    form.remark = ''

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }
</script>

<style lang="scss" scoped>
  .config-page {
    .dialog-footer {
      text-align: right;
    }

    .art-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
