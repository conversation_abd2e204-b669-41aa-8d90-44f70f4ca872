<template>
  <div class="art-full-height">
    <ElCard class="art-table-card" shadow="never">
      <h4 class="form-header">基本信息</h4>
      <el-form :model="form" label-width="80px" style="margin-bottom: 30px">
        <el-row>
          <el-col :span="8" :offset="2">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8" :offset="2">
            <el-form-item label="登录账号" prop="userName">
              <el-input v-model="form.userName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <h4 class="form-header">角色信息</h4>
      <ArtTable
        rowKey="roleId"
        :loading="loading"
        :data="paginatedRoles"
        :columns="columns"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
        @row-click="clickRow"
        :row-selection="{
          selectable: checkSelectable,
          preserveSelection: true
        }"
      >
        <template #createTime="{ row }">
          <span>{{ formatTime(row.createTime) }}</span>
        </template>
      </ArtTable>

      <div class="form-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="close">返回</el-button>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, nextTick, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElCard } from 'element-plus'
  import { UserApi } from '@/api/system/user'
  import { formatTime } from '@/utils/date'
  import type { Role } from '@/types/system/role'
  import type { User } from '@/types/system/user'
  import ArtTable from '@/components/core/tables/art-table/index.vue'

  defineOptions({ name: 'UserAuthRole' })

  const route = useRoute()
  const router = useRouter()

  const loading = ref(true)
  const roleIds = ref<number[]>([])
  const roles = ref<Role[]>([])
  const pagination = reactive({
    current: 1,
    size: 10,
    total: 0
  })

  const form = ref<Partial<User>>({
    nickName: '',
    userName: '',
    userId: undefined
  })

  const columns = computed(() => [
    {
      type: 'selection',
      width: 50,
      checked: true,
      disabled: true
    },
    {
      prop: 'roleId',
      label: '角色编号',
      width: 80,
      checked: true
    },
    {
      prop: 'roleName',
      label: '角色名称',
      checked: true,
      showOverflowTooltip: true
    },
    {
      prop: 'roleKey',
      label: '权限字符',
      checked: true,
      showOverflowTooltip: true
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      checked: true,
      useSlot: true
    }
  ])

  // 分页数据
  const paginatedRoles = computed(() => {
    const start = (pagination.current - 1) * pagination.size
    const end = start + pagination.size
    return roles.value.slice(start, end)
  })

  /** 单击选中行数据 */
  function clickRow(row: Role) {
    if (checkSelectable(row)) {
      // 通过 ArtTable 的选择功能来切换选中状态
      const isSelected = roleIds.value.includes(row.roleId!)
      if (isSelected) {
        roleIds.value = roleIds.value.filter((id) => id !== row.roleId!)
      } else {
        roleIds.value.push(row.roleId!)
      }
    }
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection: Role[]) {
    roleIds.value = selection.map((item) => item.roleId!)
  }

  /** 检查角色状态 */
  function checkSelectable(row: Role) {
    return row.status === '0'
  }

  /** 分页大小变化处理 */
  function handleSizeChange(size: number) {
    pagination.size = size
    pagination.current = 1
  }

  /** 当前页变化处理 */
  function handleCurrentChange(page: number) {
    pagination.current = page
  }

  /** 关闭按钮 */
  function close() {
    router.push('/system/user')
  }

  /** 提交按钮 */
  async function submitForm() {
    try {
      const userId = form.value.userId!
      const rIds = roleIds.value.join(',')
      await UserApi.updateAuthRole({ userId, roleIds: rIds })
      ElMessage.success('授权成功')
      close()
    } catch (error) {
      console.error('授权失败:', error)
      ElMessage.error('授权失败')
    }
  }

  /** 获取用户角色数据 */
  async function getUserRoleData() {
    const userId = route.params.userId as string
    if (userId) {
      try {
        loading.value = true
        const response = await UserApi.getAuthRole(Number(userId))
        const { user, roles: roleList } = response.data

        form.value = user
        roles.value = roleList || []
        pagination.total = roles.value.length

        // 设置已选中的角色
        await nextTick()
        const selectedRoles = roles.value.filter((role) => role.flag)
        roleIds.value = selectedRoles.map((role) => role.roleId!)

        loading.value = false
      } catch (error) {
        console.error('获取用户角色数据失败:', error)
        ElMessage.error('获取用户角色数据失败')
        loading.value = false
      }
    }
  }

  // 组件挂载时获取数据
  onMounted(() => {
    getUserRoleData()
  })
</script>

<style lang="scss" scoped>
  .form-header {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .form-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e6e6e6;
  }

  .art-table-card {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
</style>
