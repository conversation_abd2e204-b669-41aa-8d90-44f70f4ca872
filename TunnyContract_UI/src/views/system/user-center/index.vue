<template>
  <div class="page-content user" v-loading="loading">
    <div class="content">
      <div class="left-wrap">
        <div class="user-wrap box-style">
          <img class="bg" src="@imgs/user/bg.webp" />
          <img
            class="avatar"
            :src="userProfile.avatar || '/src/assets/img/user/profile.jpg'"
            @error="handleAvatarError"
          />
          <h2 class="name">{{ userProfile.userName || userInfo.userName }}</h2>
          <p class="des">
            {{ userProfile.remark || ' onePiece Pro 是一款漂亮的后台管理系统模版.' }}
          </p>

          <div class="outer-info">
            <div>
              <i class="iconfont-sys">&#xe72e;</i>
              <span>{{ userProfile.email || '暂无邮箱' }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe608;</i>
              <span>{{ postGroup || '暂无岗位' }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe736;</i>
              <span>{{ userProfile.phonenumber || '暂无手机号' }}</span>
            </div>
            <div>
              <i class="iconfont-sys">&#xe811;</i>
              <span>{{ userProfile.dept?.deptName || '暂无部门' }}</span>
            </div>
          </div>

          <div class="lables">
            <h3>角色</h3>
            <div>
              <div
                v-for="role in roleGroup
                  .split(',')
                  .filter((r) => r)
                  .slice(0, 6)"
                :key="role"
              >
                {{ role }}
              </div>
            </div>
          </div>
        </div>

        <!-- <el-carousel class="gallery" height="160px"
          :interval="5000"
          indicator-position="none"
        >
          <el-carousel-item class="item" v-for="item in galleryList" :key="item">
            <img :src="item"/>
          </el-carousel-item>
        </el-carousel> -->
      </div>
      <div class="right-wrap">
        <div class="info box-style">
          <h1 class="title">基本设置</h1>

          <ElForm
            :model="form"
            class="form"
            ref="ruleFormRef"
            :rules="rules"
            label-width="86px"
            label-position="top"
          >
            <ElRow>
              <ElFormItem label="用户昵称" prop="nickName">
                <el-input v-model="form.nickName" :disabled="!isEdit" maxlength="30" />
              </ElFormItem>
              <ElFormItem label="性别" prop="sex" class="right-input">
                <ElSelect v-model="form.sex" placeholder="请选择" :disabled="!isEdit">
                  <ElOption
                    v-for="item in sexOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElRow>

            <ElRow>
              <ElFormItem label="手机号码" prop="phonenumber">
                <ElInput v-model="form.phonenumber" :disabled="!isEdit" maxlength="11" />
              </ElFormItem>
              <ElFormItem label="邮箱" prop="email" class="right-input">
                <ElInput v-model="form.email" :disabled="!isEdit" maxlength="50" />
              </ElFormItem>
            </ElRow>

            <ElFormItem label="个人简介" prop="remark" :style="{ height: '130px' }">
              <ElInput
                type="textarea"
                :rows="4"
                v-model="form.remark"
                :disabled="!isEdit"
                maxlength="500"
              />
            </ElFormItem>

            <div class="el-form-item-right">
              <ElButton type="primary" style="width: 90px" v-ripple @click="edit">
                {{ isEdit ? '保存' : '编辑' }}
              </ElButton>
            </div>
          </ElForm>
        </div>

        <div class="info box-style" style="margin-top: 20px">
          <h1 class="title">更改密码</h1>

          <ElForm
            :model="pwdForm"
            class="form"
            ref="pwdFormRef"
            :rules="pwdRules"
            label-width="86px"
            label-position="top"
          >
            <ElFormItem label="当前密码" prop="oldPassword">
              <ElInput
                v-model="pwdForm.oldPassword"
                type="password"
                :disabled="!isEditPwd"
                show-password
                placeholder="请输入当前密码"
              />
            </ElFormItem>

            <ElFormItem label="新密码" prop="newPassword">
              <ElInput
                v-model="pwdForm.newPassword"
                type="password"
                :disabled="!isEditPwd"
                show-password
                placeholder="请输入新密码"
              />
            </ElFormItem>

            <ElFormItem label="确认新密码" prop="confirmPassword">
              <ElInput
                v-model="pwdForm.confirmPassword"
                type="password"
                :disabled="!isEditPwd"
                show-password
                placeholder="请再次输入新密码"
              />
            </ElFormItem>

            <div class="el-form-item-right">
              <ElButton type="primary" style="width: 90px" v-ripple @click="editPwd">
                {{ isEditPwd ? '保存' : '编辑' }}
              </ElButton>
            </div>
          </ElForm>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { useUserStore } from '@/store/modules/user'
  import { ElForm, FormInstance, FormRules, ElMessage } from 'element-plus'
  import { UserApi } from '@/api/system/user'
  import type { User } from '@/types/system/user'

  defineOptions({ name: 'UserCenter' })

  const userStore = useUserStore()
  const userInfo = computed(() => userStore.getUserInfo)

  const loading = ref(false)
  const isEdit = ref(false)
  const isEditPwd = ref(false)
  const date = ref('')

  // 用户个人信息
  const userProfile = ref<User>({} as User)
  const roleGroup = ref('')
  const postGroup = ref('')

  // 基本信息表单
  const form = reactive({
    nickName: '',
    email: '',
    phonenumber: '',
    sex: '0',
    remark: ''
  })

  // 密码表单
  const pwdForm = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const ruleFormRef = ref<FormInstance>()
  const pwdFormRef = ref<FormInstance>()

  // 基本信息验证规则
  const rules = reactive<FormRules>({
    nickName: [
      { required: true, message: '用户昵称不能为空', trigger: 'blur' },
      { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
    ],
    phonenumber: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
  })

  // 密码验证规则
  const pwdRules = reactive<FormRules>({
    oldPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' }],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请再次输入新密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== pwdForm.newPassword) {
            callback(new Error('两次输入密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  })

  const sexOptions = [
    {
      value: '0',
      label: '男'
    },
    {
      value: '1',
      label: '女'
    },
    {
      value: '2',
      label: '未知'
    }
  ]

  onMounted(() => {
    getDate()
    getUserProfile()
  })

  // 获取用户个人信息
  const getUserProfile = async () => {
    loading.value = true
    try {
      const res = await UserApi.getUserProfile()
      console.log('getUserProfile 响应数据:', res)

      if (res.data?.code === 200 && res.data?.data) {
        userProfile.value = res.data.data
        roleGroup.value = res.data.roleGroup || ''
        postGroup.value = res.data.postGroup || ''

        console.log('用户信息:', userProfile.value)
        console.log('角色组:', roleGroup.value)
        console.log('岗位组:', postGroup.value)

        // 初始化表单数据
        form.nickName = res.data.data.nickName || ''
        form.email = res.data.data.email || ''
        form.phonenumber = res.data.data.phonenumber || ''
        form.sex = res.data.data.sex || '0'
        form.remark = res.data.data.remark || ''

        console.log('表单数据:', form)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    } finally {
      loading.value = false
    }
  }

  const getDate = () => {
    const d = new Date()
    const h = d.getHours()
    let text = ''

    if (h >= 6 && h < 9) {
      text = '早上好'
    } else if (h >= 9 && h < 11) {
      text = '上午好'
    } else if (h >= 11 && h < 13) {
      text = '中午好'
    } else if (h >= 13 && h < 18) {
      text = '下午好'
    } else if (h >= 18 && h < 24) {
      text = '晚上好'
    } else if (h >= 0 && h < 6) {
      text = '很晚了，早点睡'
    }

    date.value = text
  }

  // 编辑/保存基本信息
  const edit = async () => {
    if (!isEdit.value) {
      // 进入编辑模式
      isEdit.value = true
    } else {
      // 保存信息
      const valid = await ruleFormRef.value?.validate()
      if (!valid) return

      loading.value = true
      try {
        const res = await UserApi.updateUserProfile(form)
        console.log('updateUserProfile 响应数据:', res)

        // 检查不同的响应结构
        if (res.code === 200 || res.data?.code === 200) {
          ElMessage.success('修改成功')
          isEdit.value = false

          // 更新store中的用户信息
          await userStore.getUserInfoFromServer()
          // 重新获取个人信息
          await getUserProfile()
        } else {
          ElMessage.error('修改失败')
        }
      } catch (error) {
        console.error('更新用户信息失败:', error)
        ElMessage.error('修改失败，请重试')
      } finally {
        loading.value = false
      }
    }
  }

  // 编辑/保存密码
  const editPwd = async () => {
    if (!isEditPwd.value) {
      // 进入编辑模式
      isEditPwd.value = true
      // 清空密码表单
      pwdForm.oldPassword = ''
      pwdForm.newPassword = ''
      pwdForm.confirmPassword = ''
    } else {
      // 保存密码
      const valid = await pwdFormRef.value?.validate()
      if (!valid) return

      loading.value = true
      try {
        const res = await UserApi.updateUserPwd(pwdForm.oldPassword, pwdForm.newPassword)
        console.log('updateUserPwd 响应数据:', res)

        // 检查不同的响应结构
        if (res.code === 200 || res.data?.code === 200) {
          ElMessage.success('密码修改成功')
          isEditPwd.value = false

          // 清空密码表单
          pwdForm.oldPassword = ''
          pwdForm.newPassword = ''
          pwdForm.confirmPassword = ''
        } else {
          ElMessage.error('密码修改失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error('密码修改失败，请重试')
      } finally {
        loading.value = false
      }
    }
  }

  /**
   * 处理头像加载错误
   */
  const handleAvatarError = (event: Event): void => {
    const img = event.target as HTMLImageElement
    if (img && img.src !== '/src/assets/img/user/profile.jpg') {
      img.src = '/src/assets/img/user/profile.jpg'
    }
  }
</script>

<style lang="scss">
  .user {
    .icon {
      width: 1.4em;
      height: 1.4em;
      overflow: hidden;
      vertical-align: -0.15em;
      fill: currentcolor;
    }
  }
</style>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100%;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;

    $box-radius: calc(var(--custom-radius) + 4px);

    .box-style {
      border: 1px solid var(--art-border-color);
    }

    .content {
      position: relative;
      display: flex;
      justify-content: space-between;
      margin-top: 10px;

      .left-wrap {
        width: 450px;
        margin-right: 25px;

        .user-wrap {
          position: relative;
          height: 600px;
          padding: 35px 40px;
          overflow: hidden;
          text-align: center;
          background: var(--art-main-bg-color);
          border-radius: $box-radius;

          .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 200px;
            object-fit: cover;
          }

          .avatar {
            position: relative;
            z-index: 10;
            width: 80px;
            height: 80px;
            margin-top: 120px;
            object-fit: cover;
            border: 2px solid #fff;
            border-radius: 50%;
          }

          .name {
            margin-top: 20px;
            font-size: 22px;
            font-weight: 400;
          }

          .des {
            margin-top: 20px;
            font-size: 14px;
          }

          .outer-info {
            width: 300px;
            margin: auto;
            margin-top: 30px;
            text-align: left;

            > div {
              margin-top: 10px;

              span {
                margin-left: 8px;
                font-size: 14px;
              }
            }
          }

          .lables {
            margin-top: 40px;

            h3 {
              font-size: 15px;
              font-weight: 500;
            }

            > div {
              display: flex;
              flex-wrap: wrap;
              justify-content: center;
              margin-top: 15px;

              > div {
                padding: 3px 6px;
                margin: 0 10px 10px 0;
                font-size: 12px;
                background: var(--art-main-bg-color);
                border: 1px solid var(--art-border-color);
                border-radius: 2px;
              }
            }
          }
        }

        .gallery {
          margin-top: 25px;
          border-radius: 10px;

          .item {
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }

      .right-wrap {
        flex: 1;
        overflow: hidden;
        border-radius: $box-radius;

        .info {
          background: var(--art-main-bg-color);
          border-radius: $box-radius;

          .title {
            padding: 15px 25px;
            font-size: 20px;
            font-weight: 400;
            color: var(--art-text-gray-800);
            border-bottom: 1px solid var(--art-border-color);
          }

          .form {
            box-sizing: border-box;
            padding: 30px 25px;

            > .el-row {
              .el-form-item {
                width: calc(50% - 10px);
              }

              .el-input,
              .el-select {
                width: 100%;
              }
            }

            .right-input {
              margin-left: 20px;
            }

            .el-form-item-right {
              display: flex;
              align-items: center;
              justify-content: end;

              .el-button {
                width: 110px !important;
              }
            }
          }
        }
      }
    }
  }

  @media only screen and (max-width: $device-ipad-vertical) {
    .page-content {
      .content {
        display: block;
        margin-top: 5px;

        .left-wrap {
          width: 100%;
        }

        .right-wrap {
          width: 100%;
          margin-top: 15px;
        }
      }
    }
  }
</style>
