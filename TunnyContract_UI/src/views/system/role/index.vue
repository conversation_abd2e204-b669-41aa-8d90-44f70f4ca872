<template>
  <div class="art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:role:add'" type="primary" @click="handleAdd" v-ripple
            >新增角色</ElButton
          >
          <ElButton
            v-auth="'system:role:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
            >修改</ElButton
          >
          <ElButton
            v-auth="'system:role:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
            >删除</ElButton
          >
          <ElButton v-auth="'system:role:export'" type="warning" @click="handleExport" v-ripple
            >导出</ElButton
          >
        </template>
      </ArtTableHeader>
      <ArtTable
        :loading="loading"
        :columns="columns"
        :data="validRoleList"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #status="{ row }">
          <ElSwitch
            v-if="row && row.roleId && row.status !== undefined"
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
          />
          <span v-else class="text-gray-400">加载中...</span>
        </template>

        <template #operation="{ row }">
          <template v-if="row.roleId !== 1">
            <el-tooltip content="修改" placement="top">
              <el-button
                v-ripple
                v-auth="'system:role:edit'"
                link
                type="primary"
                @click="handleUpdate(row)"
              >
                编辑
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-ripple
                v-auth="'system:role:remove'"
                link
                type="danger"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </el-tooltip>
            <el-tooltip content="数据权限" placement="top">
              <el-button
                v-ripple
                v-auth="'system:role:edit'"
                link
                type="warning"
                @click="handleDataScope(row)"
              >
                数据权限
              </el-button>
            </el-tooltip>
            <el-tooltip content="分配用户" placement="top">
              <el-button
                v-ripple
                v-auth="'system:role:edit'"
                link
                type="info"
                @click="handleAuthUser(row)"
              >
                分配用户
              </el-button>
            </el-tooltip>
          </template>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 添加或修改角色配置对话框 -->
    <ElDialog :title="title" v-model="open" width="500px" append-to-body>
      <ElForm ref="roleRef" :model="form" :rules="rules" label-width="100px">
        <ElFormItem label="角色名称" prop="roleName">
          <ElInput v-model="form.roleName" placeholder="请输入角色名称" />
        </ElFormItem>
        <ElFormItem prop="roleKey">
          <template #label>
            <span>
              <ElTooltip
                content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)"
                placement="top"
              >
                <ElIcon><QuestionFilled /></ElIcon>
              </ElTooltip>
              权限字符
            </span>
          </template>
          <ElInput v-model="form.roleKey" placeholder="请输入权限字符" />
        </ElFormItem>
        <ElFormItem label="角色顺序" prop="roleSort">
          <ElInputNumber v-model="form.roleSort" controls-position="right" :min="0" />
        </ElFormItem>
        <ElFormItem label="状态">
          <ElRadioGroup v-model="form.status">
            <ElRadio v-for="dict in statusOptions" :key="dict.value" :value="dict.value">{{
              dict.label
            }}</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem label="菜单权限">
          <ElCheckbox
            v-model="menuExpand"
            @change="(val: any) => handleCheckedTreeExpand(!!val, 'menu')"
            >展开/折叠</ElCheckbox
          >
          <ElCheckbox
            v-model="menuNodeAll"
            @change="(val: any) => handleCheckedTreeNodeAll(!!val, 'menu')"
            >全选/全不选</ElCheckbox
          >
          <ElCheckbox
            v-model="form.menuCheckStrictly"
            @change="(val: any) => handleCheckedTreeConnect(!!val, 'menu')"
            >父子联动</ElCheckbox
          >
          <ElTree
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            ref="menuRef"
            node-key="id"
            :check-strictly="!form.menuCheckStrictly"
            empty-text="加载中，请稍候"
            :props="{ label: 'label', children: 'children' }"
          />
        </ElFormItem>
        <ElFormItem label="备注">
          <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 分配角色数据权限对话框 -->
    <ElDialog :title="title" v-model="openDataScope" width="500px" append-to-body>
      <ElForm :model="form" label-width="80px">
        <ElFormItem label="角色名称">
          <ElInput v-model="form.roleName" :disabled="true" />
        </ElFormItem>
        <ElFormItem label="权限字符">
          <ElInput v-model="form.roleKey" :disabled="true" />
        </ElFormItem>
        <ElFormItem label="权限范围">
          <ElSelect v-model="form.dataScope" @change="dataScopeSelectChange">
            <ElOption
              v-for="item in dataScopeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="数据权限" v-show="form.dataScope == '2'">
          <ElCheckbox
            v-model="deptExpand"
            @change="(val: any) => handleCheckedTreeExpand(!!val, 'dept')"
            >展开/折叠</ElCheckbox
          >
          <ElCheckbox
            v-model="deptNodeAll"
            @change="(val: any) => handleCheckedTreeNodeAll(!!val, 'dept')"
            >全选/全不选</ElCheckbox
          >
          <ElCheckbox
            :model-value="!form.deptCheckStrictly"
            @change="(val: any) => handleCheckedTreeConnect(!val, 'dept')"
            >父子联动</ElCheckbox
          >
          <ElTree
            class="tree-border"
            :data="deptOptions"
            show-checkbox
            default-expand-all
            ref="deptRef"
            node-key="deptId"
            :check-strictly="form.deptCheckStrictly"
            empty-text="加载中，请稍候"
            :props="{ label: 'deptName', children: 'children' }"
          />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton type="primary" @click="submitDataScope">确 定</ElButton>
          <ElButton @click="cancelDataScope">取 消</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, ElSwitch } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { QuestionFilled } from '@element-plus/icons-vue'
  import { RoleApi } from '@/api/system/role'
  import { treeselect } from '@/api/system/menu'
  import { DeptApi } from '@/api/system/dept'
  import { formatTime } from '@/utils/date'
  import type { Role } from '@/types/system/role'
  import { useRouter } from 'vue-router'
  import { useTable } from '@/composables/useTable'

  defineOptions({ name: 'Role' })

  const router = useRouter()

  const open = ref(false)
  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)
  const title = ref('')

  const menuOptions = ref<any[]>([])
  const menuExpand = ref(false)
  const menuNodeAll = ref(false)
  const deptExpand = ref(true)
  const deptNodeAll = ref(false)
  const deptOptions = ref<any[]>([])
  const openDataScope = ref(false)
  const menuRef = ref()
  const deptRef = ref()
  const roleRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    roleName: '',
    roleKey: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })
  const appliedFilters = reactive({ ...initialSearchState })

  /** 数据范围选项*/
  const dataScopeOptions = ref([
    { value: '1', label: '全部数据权限' },
    { value: '2', label: '自定数据权限' },
    { value: '3', label: '本部门数据权限' },
    { value: '4', label: '本部门及以下数据权限' },
    { value: '5', label: '仅本人数据权限' }
  ])

  /** 状态选项 */
  const statusOptions = ref([
    { value: '0', label: '正常' },
    { value: '1', label: '停用' }
  ])

  // 搜索表单配置项
  const formItems = computed(() => [
    {
      label: '角色名称',
      key: 'roleName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入角色名称' }
    },
    {
      label: '权限字符',
      key: 'roleKey',
      type: 'input',
      props: { clearable: true, placeholder: '请输入权限字符' }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true, placeholder: '角色状态' },
      options: statusOptions.value
    }
  ])

  // 使用useTable组合式函数
  const {
    data: roleList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<Role>({
    core: {
      apiFn: (params: any) => RoleApi.getRoleList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: '',
        roleKey: '',
        status: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'roleId',
          label: '角色编号',
          width: 120,
          checked: true
        },
        {
          prop: 'roleName',
          label: '角色名称',
          minWidth: 150,
          showOverflowTooltip: true,
          checked: true
        },
        {
          prop: 'roleKey',
          label: '权限字符',
          minWidth: 150,
          showOverflowTooltip: true,
          checked: true
        },
        {
          prop: 'roleSort',
          label: '显示顺序',
          width: 100,
          checked: true
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          useSlot: true,
          checked: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          formatter: (row: Role) => formatTime(row.createTime) || '--',
          checked: true
        },
        {
          prop: 'operation',
          label: '操作',
          width: 250,
          fixed: 'right',
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // 过滤有效的角色数据，确保必要字段存在
  const validRoleList = computed(() => {
    if (!Array.isArray(roleList.value)) {
      console.warn('roleList不是数组:', typeof roleList.value, roleList.value)
      return []
    }
    return roleList.value.filter(
      (role) => role && role.roleId && role.roleName && role.status !== undefined
    )
  })

  const form = reactive<Partial<Role>>({
    roleId: undefined,
    roleName: '',
    roleKey: '',
    roleSort: 0,
    status: '0',
    menuIds: [],
    deptIds: [],
    menuCheckStrictly: true,
    deptCheckStrictly: true,
    remark: ''
  })

  const rules = reactive<FormRules>({
    roleName: [{ required: true, message: '角色名称不能为空', trigger: 'blur' }],
    roleKey: [{ required: true, message: '权限字符不能为空', trigger: 'blur' }],
    roleSort: [{ required: true, message: '角色顺序不能为空', trigger: 'blur' }]
  })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(appliedFilters, { ...formFilters })
    Object.assign(searchParams, { ...appliedFilters, pageNum: 1 })
    refreshData()
  }

  // 刷新处理
  const handleRefresh = () => {
    console.log('刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection: Role[]) {
    ids.value = selection.map((item) => item.roleId!)
    single.value = selection.length != 1
    multiple.value = !selection.length
  }

  /** 角色状态修改 */
  function handleStatusChange(row: Role) {
    // 由于模板已有条件渲染，这里的数据一定是完整的
    const newStatus = row.status // ElSwitch已经切换后的新值
    const originalStatus = newStatus === '0' ? '1' : '0' // 计算出原始值

    // 立即恢复到原始状态
    row.status = originalStatus

    // 根据新状态确定操作文本
    const text = newStatus === '0' ? '启用' : '停用'

    ElMessageBox.confirm(`确认要"${text}""${row.roleName}"角色吗?`, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        // 用户确认后，切换到新状态
        row.status = newStatus
        return RoleApi.changeRoleStatus(row.roleId!, newStatus)
      })
      .then(() => {
        ElMessage.success(text + '成功')
      })
      .catch(() => {
        // 用户取消或API失败时，保持原状态
        row.status = originalStatus
      })
  }

  /** 删除按钮操作 */
  function handleDelete(row?: Role) {
    const roleIds = row?.roleId || ids.value
    ElMessageBox.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项?', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        return RoleApi.delRole(roleIds)
      })
      .then(() => {
        refreshData()
        ElMessage.success('删除成功')
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  function handleExport() {
    // 实现导出功能
    ElMessage.info('导出功能待实现')
  }

  /** 分配用户 */
  function handleAuthUser(row: Role) {
    router.push('/system/role-auth/user/' + row.roleId)
  }

  /** 查询菜单树结构 */
  async function getMenuTreeselect() {
    try {
      console.log('开始获取菜单树数据...')
      const response = await treeselect()
      console.log('菜单树API响应:', response)

      // 处理RuoYi的响应格式
      const menuData = (response.data as any)?.data || response.data || []
      menuOptions.value = Array.isArray(menuData) ? menuData : []
      console.log('设置的菜单选项:', menuOptions.value)
      console.log('菜单选项数量:', menuOptions.value.length)
    } catch (error) {
      console.error('获取菜单树失败:', error)
      menuOptions.value = []
    }
  }

  /** 所有部门节点数据 */
  function getDeptAllCheckedKeys() {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value.getCheckedKeys()
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value.getHalfCheckedKeys()
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys
  }

  /** 重置新增的表单以及其他数据  */
  function reset() {
    if (menuRef.value != undefined) {
      menuRef.value.setCheckedKeys([])
    }
    menuExpand.value = false
    menuNodeAll.value = false
    deptExpand.value = true
    deptNodeAll.value = false
    Object.assign(form, {
      roleId: undefined,
      roleName: '',
      roleKey: '',
      roleSort: 0,
      status: '0',
      menuIds: [],
      deptIds: [],
      menuCheckStrictly: true,
      deptCheckStrictly: true,
      remark: ''
    })
    if (roleRef.value) {
      roleRef.value.resetFields()
    }
  }

  /** 添加角色 */
  async function handleAdd() {
    reset()
    console.log('点击新增角色，开始加载菜单数据...')
    await getMenuTreeselect()
    console.log('菜单数据加载完成，打开对话框')
    open.value = true
    title.value = '添加角色'
  }

  /** 修改角色 */
  function handleUpdate(row?: Role) {
    reset()
    const roleId = row?.roleId || ids.value[0]
    const roleMenu = getRoleMenuTreeselect(roleId)
    RoleApi.getRole(roleId).then((response) => {
      Object.assign(form, response.data?.data || response.data)
      form.roleSort = Number(form.roleSort)
      open.value = true
      nextTick(() => {
        roleMenu.then((res) => {
          let checkedKeys = res?.checkedKeys || []
          checkedKeys.forEach((v: any) => {
            nextTick(() => {
              if (menuRef.value) {
                menuRef.value.setChecked(v, true, false)
              }
            })
          })
        })
      })
    })
    title.value = '修改角色'
  }

  /** 根据角色ID查询菜单树结构 */
  function getRoleMenuTreeselect(roleId: number) {
    return RoleApi.roleMenuTreeselect(roleId).then((response) => {
      menuOptions.value = response.data?.menus || []
      return response.data
    })
  }

  /** 树权限（展开/折叠）*/
  function handleCheckedTreeExpand(value: boolean, type: string) {
    if (type == 'menu') {
      let treeList = menuOptions.value
      for (let i = 0; i < treeList.length; i++) {
        if (menuRef.value?.store?.nodesMap) {
          menuRef.value.store.nodesMap[treeList[i].id].expanded = value
        }
      }
    } else if (type == 'dept') {
      let treeList = deptOptions.value
      for (let i = 0; i < treeList.length; i++) {
        if (deptRef.value?.store?.nodesMap) {
          deptRef.value.store.nodesMap[treeList[i].deptId].expanded = value
        }
      }
    }
  }

  /** 树权限（全选/全不选） */
  function handleCheckedTreeNodeAll(value: boolean, type: string) {
    if (type == 'menu') {
      menuRef.value?.setCheckedNodes(value ? menuOptions.value : [])
    } else if (type == 'dept') {
      deptRef.value?.setCheckedNodes(value ? deptOptions.value : [])
    }
  }

  /** 树权限（父子联动） */
  function handleCheckedTreeConnect(value: boolean, type: string) {
    if (type == 'menu') {
      form.menuCheckStrictly = value
    } else if (type == 'dept') {
      form.deptCheckStrictly = value
    }
  }

  /** 所有菜单节点数据 */
  function getMenuAllCheckedKeys() {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value?.getCheckedKeys() || []
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value?.getHalfCheckedKeys() || []
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
    return checkedKeys
  }

  /** 提交按钮 */
  function submitForm() {
    if (!roleRef.value) return
    roleRef.value.validate((valid) => {
      if (valid) {
        if (form.roleId != undefined) {
          form.menuIds = getMenuAllCheckedKeys()
          RoleApi.updateRole(form as Role).then(() => {
            ElMessage.success('修改成功')
            open.value = false
            refreshData()
          })
        } else {
          form.menuIds = getMenuAllCheckedKeys()
          RoleApi.addRole(form as Role).then(() => {
            ElMessage.success('新增成功')
            open.value = false
            refreshData()
          })
        }
      }
    })
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false
    reset()
  }

  /** 选择角色权限范围触发 */
  function dataScopeSelectChange(value: string) {
    if (value !== '2') {
      deptRef.value?.setCheckedKeys([])
    }
  }

  /** 分配数据权限操作 */
  async function handleDataScope(row: Role) {
    reset()
    console.log('开始分配数据权限，角色ID:', row.roleId)

    try {
      // 并行获取角色信息和完整部门列表
      const [roleResponse, deptResponse] = await Promise.all([
        RoleApi.getRole(row.roleId!),
        DeptApi.getDeptList() // 获取完整部门列表供用户选择
      ])

      // 处理角色数据
      const roleData = roleResponse.data?.data || roleResponse.data
      Object.assign(form, roleData)
      console.log('角色数据:', roleData)

      // 设置部门权限默认开启父子联动（在Object.assign之后设置，避免被覆盖）
      // 设置为false表示启用父子联动，UI中会显示为选中状态
      form.deptCheckStrictly = false

      // 处理部门数据，构建树形结构
      const deptData = (deptResponse.data as any)?.data || deptResponse.data || []
      const flatDeptList = Array.isArray(deptData) ? deptData : []
      deptOptions.value = handleTree(flatDeptList, 'deptId', 'parentId') as any
      console.log('完整部门树已加载，数量:', deptOptions.value.length)

      openDataScope.value = true
      title.value = '分配数据权限'

      // 如果角色已有自定义数据权限，获取其选中的部门
      if ((roleData as any)?.dataScope === '2' || (roleData as any)?.dataScope === 2) {
        nextTick(async () => {
          try {
            const roleSpecificDeptData = await RoleApi.deptTreeSelect(row.roleId!)
            console.log('角色特定部门数据:', roleSpecificDeptData)
            const checkedKeys = (roleSpecificDeptData.data as any)?.checkedKeys || []
            console.log('设置选中的部门节点:', checkedKeys)

            nextTick(() => {
              if (deptRef.value) {
                deptRef.value.setCheckedKeys(checkedKeys)
              }
            })
          } catch (error) {
            console.error('获取角色特定部门数据失败:', error)
          }
        })
      }
    } catch (error) {
      console.error('获取数据权限相关数据失败:', error)
    }
  }

  /** 提交按钮（数据权限） */
  function submitDataScope() {
    if (form.roleId != undefined) {
      form.deptIds = getDeptAllCheckedKeys()
      const dataScopeData = {
        roleId: form.roleId,
        roleName: form.roleName || '',
        roleKey: form.roleKey || '',
        dataScope: form.dataScope || '1',
        deptIds: form.deptIds || []
      }
      RoleApi.dataScope(dataScopeData).then(() => {
        ElMessage.success('修改成功')
        openDataScope.value = false
        refreshData()
      })
    }
  }

  /** 取消按钮（数据权限）*/
  function cancelDataScope() {
    openDataScope.value = false
    reset()
  }

  // 树形数据处理工具函数
  const handleTree = (data: any[], id: string, parentId: string, children = 'children') => {
    const config = {
      id: id || 'id',
      parentId: parentId || 'parentId',
      childrenList: children || 'children'
    }

    const childrenListMap: any = {}
    const nodeIds: any = {}
    const tree = []

    for (const d of data) {
      const parentId = d[config.parentId]
      if (childrenListMap[parentId] == null) {
        childrenListMap[parentId] = []
      }
      nodeIds[d[config.id]] = d
      childrenListMap[parentId].push(d)
    }

    for (const d of data) {
      const parentId = d[config.parentId]
      if (nodeIds[parentId] == null) {
        tree.push(d)
      }
    }

    for (const t of tree) {
      adaptToChildrenList(t)
    }

    function adaptToChildrenList(o: any) {
      if (childrenListMap[o[config.id]] !== null) {
        o[config.childrenList] = childrenListMap[o[config.id]]
      }
      if (o[config.childrenList]) {
        for (const c of o[config.childrenList]) {
          adaptToChildrenList(c)
        }
      }
    }

    return tree
  }

  // useTable会自动在mounted时加载数据
</script>

<style lang="scss" scoped>
  .page-content {
    .svg-icon {
      width: 1.8em;
      height: 1.8em;
      overflow: hidden;
      vertical-align: -8px;
      fill: currentcolor;
    }
  }
</style>
