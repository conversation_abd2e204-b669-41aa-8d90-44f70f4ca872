<template>
  <!-- 选择用户对话框 -->
  <ElDialog
    title="选择用户"
    v-model="visible"
    width="800px"
    append-to-body
    :close-on-click-modal="false"
  >
    <!-- 搜索表单 -->
    <ElForm :model="queryParams" ref="queryRef" :inline="true" style="margin-bottom: 20px">
      <ElFormItem label="用户名称" prop="userName">
        <ElInput
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </ElFormItem>
      <ElFormItem label="手机号码" prop="phonenumber">
        <ElInput
          v-model="queryParams.phonenumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 180px"
          @keyup.enter="handleQuery"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton type="primary" icon="Search" @click="handleQuery">搜索</ElButton>
        <ElButton icon="Refresh" @click="resetQuery">重置</ElButton>
      </ElFormItem>
    </ElForm>

    <!-- 用户表格 -->
    <ElTable
      ref="tableRef"
      :data="userList"
      @selection-change="handleSelectionChange"
      @row-click="clickRow"
      height="260px"
      v-loading="loading"
    >
      <ElTableColumn type="selection" width="55" />
      <ElTableColumn label="用户名称" prop="userName" show-overflow-tooltip />
      <ElTableColumn label="用户昵称" prop="nickName" show-overflow-tooltip />
      <ElTableColumn label="邮箱" prop="email" show-overflow-tooltip />
      <ElTableColumn label="手机" prop="phonenumber" show-overflow-tooltip />
      <ElTableColumn label="状态" align="center" prop="status" width="80">
        <template #default="{ row }">
          <ElTag :type="row.status === '0' ? 'success' : 'danger'">
            {{ row.status === '0' ? '正常' : '停用' }}
          </ElTag>
        </template>
      </ElTableColumn>
      <ElTableColumn label="创建时间" align="center" prop="createTime" width="180">
        <template #default="{ row }">
          <span>{{ formatTime(row.createTime) || '--' }}</span>
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页 -->
    <ElPagination
      v-show="total > 0"
      :current-page="queryParams.pageNum"
      :page-size="queryParams.pageSize"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; text-align: right"
    />

    <template #footer>
      <div class="dialog-footer">
        <ElButton type="primary" @click="handleSelectUser">确 定</ElButton>
        <ElButton @click="visible = false">取 消</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ElMessage, ElTable, ElTag, ElForm } from 'element-plus'
  import { RoleApi } from '@/api/system/role'
  import { formatTime } from '@/utils/date'

  defineOptions({ name: 'SelectUser' })

  interface Props {
    roleId: number
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    ok: []
  }>()

  const userList = ref<any[]>([])
  const visible = ref(false)
  const loading = ref(false)
  const total = ref(0)
  const userIds = ref<number[]>([])
  const tableRef = ref<InstanceType<typeof ElTable>>()
  const queryRef = ref<InstanceType<typeof ElForm>>()

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    roleId: 0,
    userName: '',
    phonenumber: ''
  })

  /** 显示弹框 */
  function show() {
    queryParams.roleId = props.roleId
    resetQuery()
    visible.value = true
    getList()
  }

  /** 选择行 */
  function clickRow(row: any) {
    tableRef.value?.toggleRowSelection(row)
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection: any[]) {
    userIds.value = selection.map((item) => item.userId)
  }

  /** 查询未授权用户列表 */
  async function getList() {
    loading.value = true
    try {
      const response = await RoleApi.unallocatedUserList(queryParams)
      const data = response.data?.data || response.data
      userList.value = data?.rows || data?.records || data || []
      total.value = data?.total || 0
    } catch (error) {
      console.error('获取未授权用户列表失败:', error)
      ElMessage.error('获取未授权用户列表失败')
      userList.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1
    getList()
  }

  /** 重置按钮操作 */
  function resetQuery() {
    queryRef.value?.resetFields()
    queryParams.userName = ''
    queryParams.phonenumber = ''
    handleQuery()
  }

  /** 分页大小变化处理 */
  function handleSizeChange(size: number) {
    queryParams.pageSize = size
    queryParams.pageNum = 1
    getList()
  }

  /** 当前页变化处理 */
  function handleCurrentChange(page: number) {
    queryParams.pageNum = page
    getList()
  }

  /** 选择授权用户操作 */
  async function handleSelectUser() {
    const uIds = userIds.value.join(',')
    if (!uIds) {
      ElMessage.error('请选择要分配的用户')
      return
    }

    try {
      await RoleApi.authUserSelectAll({
        roleId: queryParams.roleId,
        userIds: uIds
      })
      ElMessage.success('分配用户成功')
      visible.value = false
      emit('ok')
    } catch (error) {
      console.error('分配用户失败:', error)
      ElMessage.error('分配用户失败')
    }
  }

  // 暴露方法供父组件调用
  defineExpose({
    show
  })
</script>

<style lang="scss" scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
