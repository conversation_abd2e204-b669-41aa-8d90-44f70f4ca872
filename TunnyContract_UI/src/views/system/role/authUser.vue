<template>
  <div class="art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:role:add'" type="primary" @click="openSelectUser" v-ripple>
            添加用户
          </ElButton>
          <ElButton
            v-auth="'system:role:remove'"
            type="danger"
            :disabled="multiple"
            @click="cancelAuthUserAll"
            v-ripple
          >
            批量取消授权
          </ElButton>
          <ElButton type="warning" @click="handleClose" v-ripple> 关闭 </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        :loading="loading"
        :columns="columns"
        :data="userList"
        :pagination="paginationConfig"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #status="{ row }">
          <ElTag :type="row.status === '0' ? 'success' : 'danger'">
            {{ row.status === '0' ? '正常' : '停用' }}
          </ElTag>
        </template>

        <template #createTime="{ row }">
          <span>{{ formatTime(row.createTime) || '--' }}</span>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 选择用户对话框 -->
    <SelectUser ref="selectRef" :roleId="roleId" @ok="handleRefresh" />
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useRoute, useRouter } from 'vue-router'
  import { RoleApi } from '@/api/system/role'
  import { formatTime } from '@/utils/date'
  import { useTableColumns } from '@/composables/useTableColumns'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'
  import SelectUser from './selectUser.vue'

  defineOptions({ name: 'AuthUser' })

  const route = useRoute()
  const router = useRouter()

  const userList = ref<any[]>([])
  const loading = ref(true)
  const multiple = ref(true)
  const total = ref(0)
  const userIds = ref<number[]>([])
  const selectRef = ref()

  // 从路由参数获取角色ID
  const roleId = ref(Number(route.params.roleId))

  // 定义表单搜索初始值
  const initialSearchState = {
    userName: '',
    phonenumber: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })
  const appliedFilters = reactive({ ...initialSearchState })

  // 搜索表单配置项
  const formItems = computed(() => [
    {
      label: '用户名称',
      key: 'userName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入用户名称' }
    },
    {
      label: '手机号码',
      key: 'phonenumber',
      type: 'input',
      props: { clearable: true, placeholder: '请输入手机号码' }
    }
  ])

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      type: 'selection',
      width: 55
    },
    {
      prop: 'userName',
      label: '用户名称',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'nickName',
      label: '用户昵称',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'email',
      label: '邮箱',
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'phonenumber',
      label: '手机',
      width: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      useSlot: true
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      useSlot: true
    },
    {
      prop: 'operation',
      label: '操作',
      width: 120,
      fixed: 'right',
      formatter: (row: any) => {
        return h(ArtButtonTable, {
          type: 'delete',
          label: '取消授权',
          hideIcon: true,
          onClick: () => cancelAuthUser(row),
          auth: 'system:role:remove'
        })
      }
    }
  ])

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    roleId: roleId.value,
    userName: undefined as string | undefined,
    phonenumber: undefined as string | undefined
  })

  // ArtTable分页配置
  const paginationConfig = computed(() => ({
    current: queryParams.pageNum,
    size: queryParams.pageSize,
    total: total.value
  }))

  /** 查询授权用户列表 */
  async function getList() {
    loading.value = true
    try {
      const params = {
        pageNum: queryParams.pageNum,
        pageSize: queryParams.pageSize,
        roleId: roleId.value,
        ...appliedFilters
      }

      const response = await RoleApi.allocatedUserList(params)
      const responseData = (response as any).data?.data || (response as any).data
      const dataList = responseData?.rows || responseData?.records || responseData || []
      userList.value = Array.isArray(dataList) ? dataList : []
      total.value = responseData?.total || 0
    } catch (error) {
      console.error('获取授权用户列表失败:', error)
      ElMessage.error('获取授权用户列表失败')
      userList.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    queryParams.pageNum = 1
    getList()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(appliedFilters, { ...formFilters })
    queryParams.pageNum = 1
    getList()
  }

  // 刷新处理
  const handleRefresh = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    queryParams.pageNum = 1
    getList()
  }

  /** 分页大小变化处理 */
  function handleSizeChange(size: number) {
    queryParams.pageSize = size
    queryParams.pageNum = 1
    getList()
  }

  /** 当前页变化处理 */
  function handleCurrentChange(page: number) {
    queryParams.pageNum = page
    getList()
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection: any[]) {
    userIds.value = selection.map((item) => item.userId)
    multiple.value = !selection.length
  }

  /** 返回按钮 */
  function handleClose() {
    router.push('/system/role')
  }

  /** 打开授权用户表弹窗 */
  function openSelectUser() {
    selectRef.value?.show()
  }

  /** 取消授权按钮操作 */
  function cancelAuthUser(row: any) {
    ElMessageBox.confirm(`确认要取消该用户"${row.userName}"角色吗？`, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        return RoleApi.authUserCancel({ userId: row.userId, roleId: roleId.value })
      })
      .then(() => {
        getList()
        ElMessage.success('取消授权成功')
      })
      .catch(() => {})
  }

  /** 批量取消授权按钮操作 */
  function cancelAuthUserAll() {
    const uIds = userIds.value.join(',')
    ElMessageBox.confirm('是否取消选中用户授权数据项?', '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        return RoleApi.authUserCancelAll({ roleId: roleId.value, userIds: uIds })
      })
      .then(() => {
        getList()
        ElMessage.success('取消授权成功')
      })
      .catch(() => {})
  }

  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
  // 样式可以根据需要添加
</style>
