<template>
  <div class="dict-type-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:dict:add'" type="primary" @click="handleAdd" v-ripple>
            新增字典
          </ElButton>
          <ElButton
            v-auth="'system:dict:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'system:dict:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:dict:export'" type="warning" @click="handleExport" v-ripple>
            导出
          </ElButton>
          <ElButton v-auth="'system:dict:remove'" type="info" @click="handleRefreshCache" v-ripple>
            刷新缓存
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="dictId"
        :loading="loading"
        :columns="columns"
        :data="dictTypeList"
        :stripe="true"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
        <template #dictType="{ row }">
          <router-link :to="`/system/dict-data/index/${row.dictId}`" class="link-type">
            <span>{{ row.dictType }}</span>
          </router-link>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="修改" placement="top">
            <el-button
              v-ripple
              v-auth="'system:dict:edit'"
              link
              type="primary"
              @click="handleUpdate(row)"
            >
              编辑
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-ripple
              v-auth="'system:dict:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>

      <!-- 添加或修改字典类型对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="500px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElFormItem label="字典名称" prop="dictName">
            <ElInput v-model="form.dictName" placeholder="请输入字典名称" />
          </ElFormItem>
          <ElFormItem label="字典类型" prop="dictType">
            <ElInput v-model="form.dictType" placeholder="请输入字典类型" />
          </ElFormItem>
          <ElFormItem label="状态" prop="status">
            <ElRadioGroup v-model="form.status">
              <ElRadio value="0">正常</ElRadio>
              <ElRadio value="1">停用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3" />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, ref, reactive, computed, h } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { DictApi } from '@/api/system/dict'
  import type { DictType } from '@/types/system/dict'
  import type { FormInstance, FormRules } from 'element-plus'

  import { useDictStore } from '@/store/modules/dict'

  defineOptions({ name: 'SystemDictType' })

  const dialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    dictName: '',
    dictType: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 表单数据
  const form = reactive<DictType>({
    dictId: undefined,
    dictName: '',
    dictType: '',
    status: '0',
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    dictName: [{ required: true, message: '字典名称不能为空', trigger: 'blur' }],
    dictType: [{ required: true, message: '字典类型不能为空', trigger: 'blur' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改字典类型' : '新增字典类型'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '字典名称',
      key: 'dictName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '字典类型',
      key: 'dictType',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  ])

  // 使用useTable组合式函数
  const {
    data: dictTypeList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<DictType>({
    core: {
      apiFn: (params: any) => DictApi.getDictTypeList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        dictName: '',
        dictType: '',
        status: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'dictId',
          label: '字典编号',
          width: 100,
          checked: true
        },
        {
          prop: 'dictName',
          label: '字典名称',
          minWidth: 120,
          checked: true
        },
        {
          prop: 'dictType',
          label: '字典类型',
          minWidth: 120,
          checked: true,
          useSlot: true,
          slotName: 'dictType'
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          checked: true,
          formatter: (row: DictType) => {
            return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () =>
              row.status === '0' ? '正常' : '停用'
            )
          }
        },
        {
          prop: 'remark',
          label: '备注',
          minWidth: 120,
          checked: false,
          formatter: (row: DictType) => row.remark || '--'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          checked: true,
          formatter: (row: DictType) => row.createTime || '--'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // useTable会自动在mounted时加载数据

  const handleRefresh = () => {
    console.log('字典类型管理刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    refreshData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: DictType[]) => {
    ids.value = selection.map((item) => item.dictId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    dialogVisible.value = true
    isEdit.value = false
  }

  // 修改按钮操作
  const handleUpdate = async (row?: DictType) => {
    resetForm()
    const dictId = row?.dictId || ids.value[0]

    try {
      const response = await DictApi.getDictTypeDetail(dictId)
      const dictData = (response.data as any)?.data || response.data
      Object.assign(form, dictData)
      dialogVisible.value = true
      isEdit.value = true
    } catch (_error) {
      console.error('获取字典类型信息失败:', _error)
      ElMessage.error('获取字典类型信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row?: DictType) => {
    const dictIds = row?.dictId ? [row.dictId] : ids.value

    try {
      await ElMessageBox.confirm(
        `是否确认删除字典编号为"${dictIds.join(',')}"的数据项？`,
        '系统提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await DictApi.deleteDictType(dictIds.length === 1 ? dictIds[0] : dictIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 导出按钮操作
  const handleExport = async () => {
    try {
      const response = await DictApi.exportDictType(searchParams as any)
      const { handleExportResponse } = await import('@/utils/download')
      handleExportResponse(response, `dict_type_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  // 刷新缓存按钮操作
  const handleRefreshCache = async () => {
    try {
      await DictApi.refreshDictCache()
      // 清空本地字典缓存
      const dictStore = useDictStore()
      dictStore.cleanDict()
      ElMessage.success('刷新缓存成功')
    } catch {
      ElMessage.error('刷新缓存失败')
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.dictId) {
            await DictApi.updateDictType(form)
            ElMessage.success('修改成功')
          } else {
            await DictApi.addDictType(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          refreshData()
        } catch {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.dictId = undefined
    form.dictName = ''
    form.dictType = ''
    form.status = '0'
    form.remark = ''

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }
</script>

<style lang="scss" scoped>
  .dict-type-page {
    .dialog-footer {
      text-align: right;
    }

    .art-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }

    .link-type {
      color: var(--el-color-primary);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
</style>
