<template>
  <div class="notice-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:notice:add'" type="primary" @click="handleAdd" v-ripple>
            新增公告
          </ElButton>
          <ElButton
            v-auth="'system:notice:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'system:notice:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'system:notice:export'" type="warning" @click="handleExport" v-ripple>
            导出
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="noticeId"
        :loading="loading"
        :columns="columns"
        :data="noticeList"
        :stripe="true"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #operation="{ row }">
          <el-tooltip content="修改" placement="top">
            <el-button
              v-ripple
              v-auth="'system:notice:edit'"
              link
              type="primary"
              @click="handleUpdate(row)"
            >
              编辑
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-ripple
              v-auth="'system:notice:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 对话框 -->
    <ElDialog :title="dialogTitle" v-model="dialogVisible" width="780px" align-center>
      <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="公告标题" prop="noticeTitle">
              <ElInput v-model="form.noticeTitle" placeholder="请输入公告标题" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="公告类型" prop="noticeType">
              <ElSelect v-model="form.noticeType" placeholder="请选择公告类型" style="width: 100%">
                <ElOption
                  v-for="dict in sys_notice_type || []"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="状态">
              <ElRadioGroup v-model="form.status">
                <ElRadio
                  v-for="dict in sys_notice_status || []"
                  :key="dict.value"
                  :value="dict.value"
                >
                  {{ dict.label }}
                </ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="内容" prop="noticeContent">
              <ElInput
                v-model="form.noticeContent"
                type="textarea"
                placeholder="请输入公告内容"
                :rows="4"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>

      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="dialogVisible = false">取 消</ElButton>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
        </span>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, h } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { useDict } from '@/composables/useDict'
  import { NoticeApi } from '@/api/system/notice'
  import type { Notice } from '@/types/system/notice'
  import type { FormInstance, FormRules } from 'element-plus'

  defineOptions({ name: 'SystemNotice' })

  const { sys_notice_type, sys_notice_status } = useDict('sys_notice_type', 'sys_notice_status')

  const dialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    noticeTitle: '',
    createBy: '',
    noticeType: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 表单数据
  const form = reactive<Notice>({
    noticeId: undefined,
    noticeTitle: '',
    noticeType: '',
    noticeContent: '',
    status: '0'
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    noticeTitle: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
    noticeType: [{ required: true, message: '公告类型不能为空', trigger: 'change' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改公告' : '新增公告'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '公告标题',
      key: 'noticeTitle',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '操作人员',
      key: 'createBy',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '公告类型',
      key: 'noticeType',
      type: 'select',
      props: { clearable: true },
      options: sys_notice_type.value || []
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true },
      options: sys_notice_status.value || []
    }
  ])

  // 使用useTable组合式函数
  const {
    data: noticeList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<Notice>({
    core: {
      apiFn: (params: any) => NoticeApi.getNoticeList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: '',
        createBy: '',
        noticeType: '',
        status: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'noticeId',
          label: '编号',
          width: 100,
          checked: true
        },
        {
          prop: 'noticeTitle',
          label: '公告标题',
          minWidth: 200,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'noticeType',
          label: '公告类型',
          width: 100,
          checked: true,
          formatter: (row: Notice) => {
            const option = sys_notice_type.value?.find(
              (item: any) => item?.value === row.noticeType
            )
            const label = option?.label || row.noticeType || '未知'
            return h(ElTag, { type: row.noticeType === '1' ? 'primary' : 'success' }, () => label)
          }
        },
        {
          prop: 'status',
          label: '状态',
          width: 80,
          checked: true,
          formatter: (row: Notice) => {
            const option = sys_notice_status.value?.find((item: any) => item?.value === row.status)
            const label = option?.label || row.status || '未知'
            return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () => label)
          }
        },
        {
          prop: 'createBy',
          label: '创建者',
          width: 120,
          checked: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          checked: true,
          formatter: (row: Notice) => row.createTime || '--'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 160,
          fixed: 'right',
          checked: false
        }
      ]
    }
  })

  // 刷新处理
  const handleRefresh = () => {
    refreshData()
  }

  // 表格选择变化
  const handleSelectionChange = (selection: Notice[]) => {
    ids.value = selection.map((item) => item.noticeId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 重置编辑表单
  const resetForm = () => {
    form.noticeId = undefined
    form.noticeTitle = ''
    form.noticeType = ''
    form.noticeContent = ''
    form.status = '0'
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }

  // 新增公告
  const handleAdd = () => {
    resetForm()
    isEdit.value = false
    dialogVisible.value = true
  }

  // 修改公告
  const handleUpdate = async (row?: Notice) => {
    const noticeId = row?.noticeId || ids.value[0]
    console.log('准备编辑通知公告，ID:', noticeId)

    try {
      const response = await NoticeApi.getNotice(noticeId)
      console.log('API响应数据:', response)
      // 处理RuoYi响应格式
      const noticeData = (response.data as any)?.data || response.data
      console.log('处理后的通知数据:', noticeData)

      // 重置表单并清除验证
      resetForm()
      // 设置表单数据
      Object.assign(form, noticeData)
      console.log('表单数据:', form)

      isEdit.value = true
      dialogVisible.value = true
    } catch (error) {
      console.error('获取公告详情失败:', error)
      ElMessage.error('获取公告详情失败')
    }
  }

  // 删除公告
  const handleDelete = async (row?: Notice) => {
    const noticeIds = row?.noticeId ? [row.noticeId] : ids.value
    const noticeNames = row?.noticeTitle ? [row.noticeTitle] : '选中的公告'

    try {
      await ElMessageBox.confirm(
        `确定删除公告"${Array.isArray(noticeNames) ? noticeNames[0] : noticeNames}"吗？`,
        '提示',
        { type: 'warning' }
      )
      await NoticeApi.deleteNotice(noticeIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除公告失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 导出数据
  const handleExport = async () => {
    try {
      const blob = await NoticeApi.exportNotice(formFilters)
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '通知公告数据.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    try {
      await formRef.value.validate()

      if (isEdit.value && form.noticeId) {
        await NoticeApi.updateNotice(form)
        ElMessage.success('修改成功')
      } else {
        await NoticeApi.addNotice(form)
        ElMessage.success('新增成功')
      }

      dialogVisible.value = false
      refreshData()
    } catch (error) {
      if (error !== 'validation_failed') {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .notice-page {
    padding: 20px;

    .art-table-card {
      margin-top: 20px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
