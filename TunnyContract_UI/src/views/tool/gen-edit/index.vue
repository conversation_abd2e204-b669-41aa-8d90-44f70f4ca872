<template>
  <div class="art-full-height">
    <div v-if="loading" style="text-align: center; padding: 100px">
      <ElIcon class="is-loading" style="font-size: 48px; color: #409eff">
        <Loading />
      </ElIcon>
      <div style="margin-top: 16px; color: #666">加载中...</div>
    </div>

    <ElCard v-else class="art-table-card" shadow="never">
      <template #header>
        <div class="header-actions">
          <span>编辑代码生成配置</span>
          <div>
            <ElButton @click="handleBack" v-ripple>返回</ElButton>
            <ElButton type="primary" @click="handleSave" :loading="saving" v-ripple>
              保存
            </ElButton>
          </div>
        </div>
      </template>

      <ElTabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <ElTabPane label="基本信息" name="basic">
          <ElForm
            ref="basicFormRef"
            :model="form"
            :rules="basicRules"
            label-width="120px"
            class="form-container"
          >
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="表名称" prop="tableName">
                  <ElInput v-model="form.tableName" disabled />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="表描述" prop="tableComment">
                  <ElInput v-model="form.tableComment" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="实体类名称" prop="className">
                  <ElInput v-model="form.className" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="生成包路径" prop="packageName">
                  <ElInput v-model="form.packageName" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="生成模块名" prop="moduleName">
                  <ElInput v-model="form.moduleName" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="生成业务名" prop="businessName">
                  <ElInput v-model="form.businessName" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="生成功能名" prop="functionName">
                  <ElInput v-model="form.functionName" />
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="生成功能作者" prop="functionAuthor">
                  <ElInput v-model="form.functionAuthor" />
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow>
              <ElCol :span="24">
                <ElFormItem label="备注" prop="remark">
                  <ElInput
                    v-model="form.remark"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入备注"
                  />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </ElTabPane>

        <!-- 字段信息 -->
        <ElTabPane label="字段信息" name="columns">
          <div class="columns-container">
            <ElTable :data="form.columns" border max-height="500px" class="columns-table">
              <ElTableColumn type="index" label="序号" width="60" align="center" />
              <ElTableColumn prop="columnName" label="字段名称" width="120" show-overflow-tooltip />
              <ElTableColumn
                prop="columnComment"
                label="字段描述"
                width="120"
                show-overflow-tooltip
              />
              <ElTableColumn prop="columnType" label="字段类型" width="120" show-overflow-tooltip />
              <ElTableColumn prop="javaType" label="Java类型" width="100" show-overflow-tooltip />
              <ElTableColumn prop="javaField" label="Java字段" width="120" show-overflow-tooltip />
              <ElTableColumn label="插入" width="60" align="center">
                <template #default="{ row }">
                  <ElCheckbox v-model="row.isInsert" true-label="1" false-label="0" />
                </template>
              </ElTableColumn>
              <ElTableColumn label="编辑" width="60" align="center">
                <template #default="{ row }">
                  <ElCheckbox v-model="row.isEdit" true-label="1" false-label="0" />
                </template>
              </ElTableColumn>
              <ElTableColumn label="列表" width="60" align="center">
                <template #default="{ row }">
                  <ElCheckbox v-model="row.isList" true-label="1" false-label="0" />
                </template>
              </ElTableColumn>
              <ElTableColumn label="查询" width="60" align="center">
                <template #default="{ row }">
                  <ElCheckbox v-model="row.isQuery" true-label="1" false-label="0" />
                </template>
              </ElTableColumn>
              <ElTableColumn label="必填" width="60" align="center">
                <template #default="{ row }">
                  <ElCheckbox v-model="row.isRequired" true-label="1" false-label="0" />
                </template>
              </ElTableColumn>
              <ElTableColumn prop="htmlType" label="显示类型" width="120">
                <template #default="{ row }">
                  <ElSelect v-model="row.htmlType" placeholder="请选择">
                    <ElOption label="文本框" value="input" />
                    <ElOption label="文本域" value="textarea" />
                    <ElOption label="下拉框" value="select" />
                    <ElOption label="单选框" value="radio" />
                    <ElOption label="复选框" value="checkbox" />
                    <ElOption label="日期控件" value="datetime" />
                    <ElOption label="图片上传" value="imageUpload" />
                    <ElOption label="文件上传" value="fileUpload" />
                    <ElOption label="富文本控件" value="editor" />
                  </ElSelect>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="dictType" label="字典类型" width="120">
                <template #default="{ row }">
                  <ElInput v-model="row.dictType" placeholder="请输入字典类型" />
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </ElTabPane>

        <!-- 生成信息 -->
        <ElTabPane label="生成信息" name="genInfo">
          <ElForm
            ref="genFormRef"
            :model="form"
            :rules="genRules"
            label-width="120px"
            class="form-container"
          >
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="生成模板" prop="tplCategory">
                  <ElSelect v-model="form.tplCategory" placeholder="请选择生成模板">
                    <ElOption label="单表（增删改查）" value="crud" />
                    <ElOption label="树表（增删改查）" value="tree" />
                    <ElOption label="主子表（增删改查）" value="sub" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="12">
                <ElFormItem label="生成代码方式" prop="genType">
                  <ElRadioGroup v-model="form.genType">
                    <ElRadio label="0">zip压缩包</ElRadio>
                    <ElRadio label="1">自定义路径</ElRadio>
                  </ElRadioGroup>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow v-if="form.genType === '1'">
              <ElCol :span="24">
                <ElFormItem label="生成路径" prop="genPath">
                  <ElInput v-model="form.genPath" placeholder="不填默认项目路径" />
                </ElFormItem>
              </ElCol>
            </ElRow>
          </ElForm>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import type { FormInstance, FormRules } from 'element-plus'
  import { Loading } from '@element-plus/icons-vue'
  import { useRoute, useRouter } from 'vue-router'

  import { GenApi } from '@/api/tool/gen'
  import type { GenTable } from '@/types/tool/gen'

  defineOptions({ name: 'ToolGenEdit' })

  const route = useRoute()
  const router = useRouter()

  // 响应式数据
  const activeTab = ref('basic')
  const saving = ref(false)
  const loading = ref(true)
  const basicFormRef = ref<FormInstance>()
  const genFormRef = ref<FormInstance>()

  // 表单数据
  const form = reactive<GenTable>({
    tableId: undefined,
    tableName: '',
    tableComment: '',
    className: '',
    packageName: 'com.ruoyi.system',
    moduleName: 'system',
    businessName: '',
    functionName: '',
    functionAuthor: 'ruoyi',
    tplCategory: 'crud',
    genType: '0',
    genPath: '',
    remark: '',
    columns: []
  })

  // 表单验证规则
  const basicRules: FormRules = {
    tableComment: [{ required: true, message: '请输入表描述', trigger: 'blur' }],
    className: [{ required: true, message: '请输入实体类名称', trigger: 'blur' }],
    packageName: [{ required: true, message: '请输入生成包路径', trigger: 'blur' }],
    moduleName: [{ required: true, message: '请输入生成模块名', trigger: 'blur' }],
    businessName: [{ required: true, message: '请输入生成业务名', trigger: 'blur' }],
    functionName: [{ required: true, message: '请输入生成功能名', trigger: 'blur' }],
    functionAuthor: [{ required: true, message: '请输入生成功能作者', trigger: 'blur' }]
  }

  const genRules: FormRules = {
    tplCategory: [{ required: true, message: '请选择生成模板', trigger: 'change' }],
    genType: [{ required: true, message: '请选择生成代码方式', trigger: 'change' }]
  }

  onMounted(() => {
    getGenTableInfo()
  })

  /** 获取表详细信息 */
  async function getGenTableInfo() {
    loading.value = true

    try {
      // 优先从route.params获取tableId，再从路径获取
      let tableId = Number(route.params.tableId)

      if (!tableId || isNaN(tableId)) {
        // 从路径中提取tableId，支持 /tool/gen-edit/index/123 格式
        const pathSegments = route.path.split('/')
        const tableIdStr = pathSegments[pathSegments.length - 1]
        tableId = Number(tableIdStr)
      }

      if (!tableId || isNaN(tableId)) {
        ElMessage.error('表ID无效')
        handleBack()
        return
      }

      const response = await GenApi.getGenTable(tableId)

      // 正确处理API响应格式 - RuoYi返回的是嵌套结构
      const responseData = (response.data as any)?.data || response.data
      const genTableData = responseData?.info || responseData
      const columnsData = responseData?.rows || genTableData?.columns || []

      // 合并表格基本信息和列信息
      Object.assign(form, genTableData)
      if (columnsData.length > 0) {
        form.columns = columnsData
      }
    } catch (error) {
      console.error('获取代码生成表详情失败:', error)
      ElMessage.error('获取代码生成表详情失败')
    } finally {
      loading.value = false
    }
  }

  /** 保存配置 */
  async function handleSave() {
    // 验证基本信息表单
    if (basicFormRef.value) {
      try {
        await basicFormRef.value.validate()
      } catch {
        activeTab.value = 'basic'
        return
      }
    }

    // 验证生成信息表单
    if (genFormRef.value) {
      try {
        await genFormRef.value.validate()
      } catch {
        activeTab.value = 'genInfo'
        return
      }
    }

    saving.value = true
    try {
      await GenApi.updateGenTable(form)
      ElMessage.success('保存成功')
      handleBack()
    } catch (error) {
      console.error('保存代码生成配置失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  }

  /** 返回列表 */
  function handleBack() {
    // 返回代码生成列表页面，保持与其他模块一致
    router.push('/tool/gen')
  }
</script>

<style lang="scss" scoped>
  .art-table-card {
    margin-top: 20px;

    .header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .form-container {
    padding: 20px;
  }

  .columns-container {
    padding: 20px;

    .columns-table {
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
        }
      }
    }
  }
</style>
