<template>
  <div class="code-preview">
    <ElRow :gutter="20">
      <!-- 文件树 -->
      <ElCol :span="6">
        <ElCard shadow="never" class="file-tree-card">
          <template #header>
            <span>文件列表</span>
          </template>
          <div v-loading="loading">
            <ElTree
              :data="fileTree"
              :props="treeProps"
              @node-click="handleNodeClick"
              :highlight-current="true"
              default-expand-all
            >
              <template #default="{ data }">
                <span class="custom-tree-node">
                  <ElIcon class="file-icon">
                    <Document v-if="!data.children" />
                    <Folder v-else />
                  </ElIcon>
                  <span class="node-label">{{ data.label }}</span>
                </span>
              </template>
            </ElTree>
          </div>
        </ElCard>
      </ElCol>

      <!-- 代码内容 -->
      <ElCol :span="18">
        <ElCard shadow="never" class="code-content-card">
          <template #header>
            <div class="content-header">
              <span>{{ currentFileName || '请选择文件' }}</span>
              <ElButton v-if="currentCode" type="primary" size="small" @click="handleCopy" v-ripple>
                <ElIcon><CopyDocument /></ElIcon>
                复制
              </ElButton>
            </div>
          </template>
          <div v-loading="loading" class="code-container">
            <ElInput
              v-if="currentCode"
              v-model="currentCode"
              type="textarea"
              :rows="25"
              readonly
              class="code-textarea"
            />
            <ElEmpty v-else description="请选择文件查看代码" />
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <div class="dialog-footer">
      <ElButton @click="$emit('close')" v-ripple>关闭</ElButton>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { Document, Folder, CopyDocument } from '@element-plus/icons-vue'
  import { GenApi } from '@/api/tool/gen'

  defineOptions({ name: 'CodePreview' })

  // 定义props
  interface Props {
    tableId: number
  }

  const props = defineProps<Props>()

  // 定义事件
  defineEmits<{
    close: []
  }>()

  // 响应式数据
  const loading = ref(false)
  const previewData = ref<Record<string, string>>({})
  const fileTree = ref<any[]>([])
  const currentFileName = ref('')
  const currentCode = ref('')

  // 树形组件配置
  const treeProps = {
    children: 'children',
    label: 'label'
  }

  onMounted(() => {
    getPreviewData()
  })

  /** 获取预览数据 */
  async function getPreviewData() {
    if (!props.tableId) return

    loading.value = true
    try {
      const response = await GenApi.previewTable(props.tableId)
      const data = (response.data as any)?.data || response.data
      previewData.value = data || {}

      // 构建文件树
      buildFileTree()
    } catch (error) {
      console.error('获取代码预览失败:', error)
      ElMessage.error('获取代码预览失败')
    } finally {
      loading.value = false
    }
  }

  /** 构建文件树 */
  function buildFileTree() {
    const tree: any[] = []
    const pathMap: Record<string, any> = {}

    Object.keys(previewData.value).forEach((filePath) => {
      const parts = filePath.split('/')
      let currentPath = ''
      let currentNode = { children: tree }

      parts.forEach((part, index) => {
        currentPath = currentPath ? `${currentPath}/${part}` : part

        if (!pathMap[currentPath]) {
          const isFile = index === parts.length - 1
          const node = {
            label: part,
            fullPath: filePath,
            isFile,
            children: isFile ? undefined : []
          }

          pathMap[currentPath] = node
          currentNode.children.push(node)
        }

        currentNode = pathMap[currentPath]
      })
    })

    fileTree.value = tree
  }

  /** 节点点击处理 */
  function handleNodeClick(data: any) {
    if (data.isFile && data.fullPath) {
      currentFileName.value = data.fullPath
      currentCode.value = previewData.value[data.fullPath] || ''
    }
  }

  /** 复制代码 */
  async function handleCopy() {
    if (!currentCode.value) return

    try {
      await navigator.clipboard.writeText(currentCode.value)
      ElMessage.success('复制成功')
    } catch {
      // 降级处理
      const textArea = document.createElement('textarea')
      textArea.value = currentCode.value
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('复制成功')
    }
  }
</script>

<style lang="scss" scoped>
  .code-preview {
    .file-tree-card,
    .code-content-card {
      height: 600px;
    }

    .file-tree-card {
      :deep(.el-card__body) {
        height: calc(100% - 60px);
        overflow: auto;
      }
    }

    .code-content-card {
      :deep(.el-card__body) {
        height: calc(100% - 60px);
        overflow: hidden;
      }
    }

    .content-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .custom-tree-node {
      display: flex;
      align-items: center;

      .file-icon {
        margin-right: 5px;
        font-size: 14px;
      }

      .node-label {
        font-size: 13px;
      }
    }

    .code-container {
      height: 100%;

      .code-textarea {
        height: 100%;

        :deep(.el-textarea__inner) {
          height: 100% !important;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          line-height: 1.5;
          border: none;
          resize: none;
        }
      }
    }

    .dialog-footer {
      margin-top: 20px;
      text-align: right;
    }
  }
</style>
