<template>
  <div class="import-table">
    <!-- 搜索栏 -->
    <ElForm :model="queryParams" ref="queryRef" :inline="true" class="search-form">
      <ElFormItem label="表名称" prop="tableName">
        <ElInput
          v-model="queryParams.tableName"
          placeholder="请输入表名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </ElFormItem>
      <ElFormItem label="表描述" prop="tableComment">
        <ElInput
          v-model="queryParams.tableComment"
          placeholder="请输入表描述"
          clearable
          @keyup.enter="handleQuery"
        />
      </ElFormItem>
      <ElFormItem>
        <ElButton type="primary" @click="handleQuery" v-ripple>
          <ElIcon><Search /></ElIcon>
          搜索
        </ElButton>
        <ElButton @click="handleReset" v-ripple>
          <ElIcon><Refresh /></ElIcon>
          重置
        </ElButton>
      </ElFormItem>
    </ElForm>

    <!-- 表格 -->
    <ElTable
      v-loading="loading"
      :data="tableData"
      @selection-change="handleSelectionChange"
      border
      style="width: 100%"
      max-height="400px"
    >
      <ElTableColumn type="selection" width="50" align="center" />
      <ElTableColumn label="序号" type="index" width="60" align="center" />
      <ElTableColumn label="表名称" prop="tableName" show-overflow-tooltip min-width="120" />
      <ElTableColumn label="表描述" prop="tableComment" show-overflow-tooltip min-width="120" />
      <ElTableColumn label="创建时间" prop="createTime" width="160" align="center">
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </ElTableColumn>
      <ElTableColumn label="更新时间" prop="updateTime" width="160" align="center">
        <template #default="{ row }">
          {{ formatDate(row.updateTime) }}
        </template>
      </ElTableColumn>
    </ElTable>

    <!-- 分页 -->
    <ElPagination
      v-show="total > 0"
      :current-page="queryParams.pageNum || 1"
      :page-size="queryParams.pageSize || 10"
      :page-sizes="[10, 20, 30, 50]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="pagination"
    />

    <!-- 底部按钮 -->
    <div class="dialog-footer">
      <ElButton @click="$emit('close')" v-ripple>取消</ElButton>
      <ElButton type="primary" :disabled="!hasSelected" @click="handleImport" v-ripple>
        导入
      </ElButton>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Search, Refresh } from '@element-plus/icons-vue'
  import { GenApi } from '@/api/tool/gen'
  import type { DbTable, DbTableQueryParams } from '@/types/tool/gen'

  defineOptions({ name: 'ImportTable' })

  // 定义事件
  const emit = defineEmits<{
    success: []
    close: []
  }>()

  // 响应式数据
  const queryParams = ref<DbTableQueryParams>({
    pageNum: 1,
    pageSize: 10,
    tableName: '',
    tableComment: ''
  })

  const tableData = ref<DbTable[]>([])
  const loading = ref(false)
  const total = ref(0)
  const selectedTables = ref<DbTable[]>([])

  // 计算属性
  const hasSelected = computed(() => selectedTables.value.length > 0)

  onMounted(() => {
    getList()
  })

  /** 获取表格数据 */
  async function getList() {
    loading.value = true
    try {
      const response = await GenApi.getDbTableList(queryParams.value)
      const data = (response.data as any)?.data || response.data

      if (data && typeof data === 'object') {
        tableData.value = data.rows || data.records || data.list || []
        total.value = data.total || data.count || tableData.value.length
      } else if (Array.isArray(data)) {
        tableData.value = data
        total.value = data.length
      } else {
        tableData.value = []
        total.value = 0
      }
    } catch (error) {
      console.error('获取数据库表列表失败:', error)
      ElMessage.error('获取数据库表列表失败')
    } finally {
      loading.value = false
    }
  }

  /** 搜索 */
  function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
  }

  /** 重置 */
  function handleReset() {
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      tableName: '',
      tableComment: ''
    }
    getList()
  }

  /** 选择变化 */
  function handleSelectionChange(selection: DbTable[]) {
    selectedTables.value = selection
  }

  /** 分页大小变化 */
  function handleSizeChange(val: number) {
    queryParams.value.pageSize = val
    getList()
  }

  /** 当前页变化 */
  function handleCurrentChange(val: number) {
    queryParams.value.pageNum = val
    getList()
  }

  /** 格式化日期 */
  function formatDate(dateStr?: string): string {
    if (!dateStr) return '-'
    return new Date(dateStr).toLocaleString('zh-CN')
  }

  /** 导入表 */
  async function handleImport() {
    if (!hasSelected.value) {
      ElMessage.warning('请选择要导入的表')
      return
    }

    try {
      const tableNames = selectedTables.value.map((table) => table.tableName).join(',')
      await ElMessageBox.confirm(`确认要导入选中的${selectedTables.value.length}个表吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await GenApi.importTable({ tables: tableNames })
      ElMessage.success('导入成功')
      emit('success')
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('导入失败:', error)
        ElMessage.error('导入失败')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .import-table {
    .search-form {
      margin-bottom: 20px;
    }

    .pagination {
      margin-top: 20px;
      text-align: right;
    }

    .dialog-footer {
      margin-top: 20px;
      text-align: right;

      .el-button {
        margin-left: 10px;
      }
    }
  }
</style>
