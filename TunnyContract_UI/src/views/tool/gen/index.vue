<template>
  <div class="art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton
            v-auth="'tool:gen:code'"
            type="primary"
            :disabled="multiple"
            @click="handleBatchGenCode"
            v-ripple
          >
            生成
          </ElButton>
          <ElButton v-auth="'tool:gen:import'" type="info" @click="handleImport" v-ripple>
            导入
          </ElButton>
          <ElButton
            v-auth="'tool:gen:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'tool:gen:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        rowKey="tableId"
        :loading="loading"
        :columns="columns"
        :data="validGenTableList"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #tableName="{ row }">
          <ElLink type="primary" @click="handlePreview(row)">
            {{ row.tableName }}
          </ElLink>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="预览" placement="top">
            <el-button
              v-ripple
              v-auth="'tool:gen:preview'"
              link
              type="primary"
              @click="handlePreview(row)"
            >
              预览
            </el-button>
          </el-tooltip>
          <el-tooltip content="编辑" placement="top">
            <el-button
              v-ripple
              v-auth="'tool:gen:edit'"
              link
              type="success"
              @click="handleUpdate(row)"
            >
              编辑
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-ripple
              v-auth="'tool:gen:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-tooltip>
          <ElDropdown @command="(command) => handleCommand(command, row)">
            <el-button v-ripple link type="info">
              更多<ElIcon class="el-icon--right"><ArrowDown /></ElIcon>
            </el-button>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem command="sync" v-auth="'tool:gen:edit'"> 同步 </ElDropdownItem>
                <ElDropdownItem command="genCode" v-auth="'tool:gen:code'">
                  生成代码
                </ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 导入表对话框 -->
    <ElDialog v-model="importVisible" title="导入表" width="800px" append-to-body destroy-on-close>
      <ImportTable
        v-if="importVisible"
        @success="handleImportSuccess"
        @close="importVisible = false"
      />
    </ElDialog>

    <!-- 代码预览对话框 -->
    <ElDialog v-model="previewVisible" title="代码预览" width="90%" append-to-body destroy-on-close>
      <CodePreview
        v-if="previewVisible && currentTableId"
        :table-id="currentTableId"
        @close="previewVisible = false"
      />
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ArrowDown } from '@element-plus/icons-vue'
  import { useRouter } from 'vue-router'

  import { useTable } from '@/composables/useTable'
  import { GenApi } from '@/api/tool/gen'
  import type { GenTable } from '@/types/tool/gen'
  import ImportTable from './importTable.vue'
  import CodePreview from './codePreview.vue'

  defineOptions({ name: 'ToolGen' })

  const router = useRouter()

  // 搜索表单状态
  const initialSearchState = {
    tableName: '',
    tableComment: '',
    beginTime: '',
    endTime: '',
    dateRange: [] as string[]
  }
  const formFilters = reactive({ ...initialSearchState })

  // 选择状态
  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 对话框状态
  const importVisible = ref(false)
  const previewVisible = ref(false)
  const currentTableId = ref<number>()

  // 计算属性
  const formItems = computed(() => [
    {
      label: '表名称',
      key: 'tableName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入表名称' }
    },
    {
      label: '表描述',
      key: 'tableComment',
      type: 'input',
      props: { clearable: true, placeholder: '请输入表描述' }
    },
    {
      label: '创建时间',
      key: 'dateRange',
      type: 'daterange',
      props: {
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        valueFormat: 'YYYY-MM-DD'
      }
    }
  ])

  // useTable 组合式函数
  const {
    data: genTableList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<GenTable>({
    core: {
      apiFn: (params: any) => GenApi.getGenTableList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        tableName: '',
        tableComment: '',
        beginTime: '',
        endTime: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'tableName',
          label: '表名称',
          minWidth: 180,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'tableComment',
          label: '表描述',
          minWidth: 180,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'className',
          label: '实体',
          minWidth: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          checked: true,
          formatter: (row: GenTable) => row.createTime || '--'
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
          checked: true,
          formatter: (row: GenTable) => row.updateTime || '--'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 260,
          fixed: 'right',
          checked: false
        }
      ]
    }
  })

  // 计算有效的表格列表
  const validGenTableList = computed(() => genTableList.value || [])

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    const filters: any = { ...formFilters }

    // 处理日期范围
    if (filters.dateRange && Array.isArray(filters.dateRange) && filters.dateRange.length === 2) {
      filters.beginTime = filters.dateRange[0]
      filters.endTime = filters.dateRange[1]
    }
    delete filters.dateRange

    Object.assign(searchParams, { ...filters, pageNum: 1 })
    refreshData()
  }

  // 刷新处理
  const handleRefresh = () => {
    refreshData()
  }

  // 表格选择变化
  const handleSelectionChange = (selection: GenTable[]) => {
    ids.value = selection.map((item) => item.tableId!).filter(Boolean)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 批量生成代码
  const handleBatchGenCode = async () => {
    const genTableIds = ids.value
    if (genTableIds.length === 0) {
      ElMessage.warning('请选择要生成的表')
      return
    }

    try {
      const selectedTables = validGenTableList.value.filter((table) =>
        genTableIds.includes(table.tableId!)
      )
      const tableNames = selectedTables.map((table) => table.tableName!).filter(Boolean)

      await ElMessageBox.confirm(`确定要生成${tableNames.length}个表的代码吗？`, '提示', {
        type: 'warning'
      })

      const blob = await GenApi.batchGenCode(tableNames)

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '代码生成.zip'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success('代码生成成功')
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('生成代码失败:', error)
        ElMessage.error('生成代码失败')
      }
    }
  }

  // 导入表
  const handleImport = () => {
    importVisible.value = true
  }

  // 修改
  const handleUpdate = async (row?: GenTable) => {
    const genTableId = row?.tableId || ids.value[0]
    if (!genTableId || genTableId === undefined) {
      ElMessage.warning('请选择要编辑的表')
      return
    }

    // 使用标准的路由跳转方式，保持与其他模块一致
    router.push(`/tool/gen-edit/index/${genTableId}`)
  }

  // 删除
  const handleDelete = async (row?: GenTable) => {
    const genTableIds = row?.tableId ? [row.tableId] : ids.value
    const genTableNames = row?.tableName ? [row.tableName] : '选中的表'

    try {
      await ElMessageBox.confirm(
        `确定删除代码生成表"${Array.isArray(genTableNames) ? genTableNames[0] : genTableNames}"吗？`,
        '提示',
        { type: 'warning' }
      )
      await GenApi.deleteGenTable(genTableIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除代码生成表失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 预览代码
  const handlePreview = (row: GenTable) => {
    if (!row.tableId) {
      ElMessage.warning('表ID不存在')
      return
    }
    currentTableId.value = row.tableId
    previewVisible.value = true
  }

  // 命令处理
  const handleCommand = async (command: string, row: GenTable) => {
    switch (command) {
      case 'sync':
        await handleSync(row)
        break
      case 'genCode':
        await handleGenCode(row)
        break
    }
  }

  // 同步数据库
  const handleSync = async (row: GenTable) => {
    try {
      await ElMessageBox.confirm(`确认要同步表"${row.tableName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await GenApi.synchDb(row.tableName!)
      ElMessage.success('同步成功')
      refreshData()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('同步失败:', error)
        ElMessage.error('同步失败')
      }
    }
  }

  // 生成代码
  const handleGenCode = async (row: GenTable) => {
    try {
      await ElMessageBox.confirm(`确认要生成表"${row.tableName}"的代码吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await GenApi.genCode(row.tableName!)
      ElMessage.success('生成成功')
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('生成失败:', error)
        ElMessage.error('生成失败')
      }
    }
  }

  // 导入成功处理
  const handleImportSuccess = () => {
    importVisible.value = false
    refreshData()
  }
</script>

<style lang="scss" scoped>
  .art-table-card {
    margin-top: 20px;
  }
</style>
