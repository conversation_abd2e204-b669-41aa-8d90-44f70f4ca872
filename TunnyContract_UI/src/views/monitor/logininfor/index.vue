<template>
  <div class="logininfor-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton
            v-auth="'monitor:logininfor:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
            >删除</ElButton
          >
          <ElButton v-auth="'monitor:logininfor:remove'" type="danger" @click="handleClean" v-ripple
            >清空</ElButton
          >
          <ElButton
            v-auth="'monitor:logininfor:unlock'"
            type="primary"
            :disabled="single"
            @click="() => handleUnlock()"
            v-ripple
            >解锁</ElButton
          >
          <ElButton
            v-auth="'monitor:logininfor:export'"
            type="warning"
            @click="handleExport"
            v-ripple
            >导出</ElButton
          >
        </template>
      </ArtTableHeader>

      <ArtTable
        :loading="loading"
        :columns="columns"
        :data="validLoginInforList"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #status="{ row }">
          <ElTag :type="row.status === '0' ? 'success' : 'danger'">
            {{ row.status === '0' ? '成功' : '失败' }}
          </ElTag>
        </template>

        <template #loginTime="{ row }">
          <span>{{ formatTime(row.loginTime) }}</span>
        </template>
      </ArtTable>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { LoginInforApi } from '@/api/monitor/logininfor'
  import { formatTime } from '@/utils/date'
  import type { LoginInfor } from '@/types/system/logininfor'
  import { useTable } from '@/composables/useTable'

  defineOptions({ name: 'MonitorLogininfor' })

  const ids = ref<number[]>([])
  const userNames = ref<string[]>([])
  const single = ref(true)
  const multiple = ref(true)

  // 定义表单搜索初始值
  const initialSearchState = {
    ipaddr: '',
    userName: '',
    status: '',
    beginTime: '',
    endTime: '',
    dateRange: [] as string[]
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })
  const appliedFilters = reactive({ ...initialSearchState })

  // 搜索表单配置项
  const formItems = computed(() => [
    {
      label: '登录地址',
      key: 'ipaddr',
      type: 'input',
      props: { clearable: true, placeholder: '请输入登录地址' }
    },
    {
      label: '用户名称',
      key: 'userName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入用户名称' }
    },
    {
      label: '登录状态',
      key: 'status',
      type: 'select',
      props: { clearable: true, placeholder: '登录状态' },
      options: [
        { label: '成功', value: '0' },
        { label: '失败', value: '1' }
      ]
    },
    {
      label: '登录时间',
      key: 'dateRange',
      type: 'daterange',
      props: {
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        'value-format': 'YYYY-MM-DD'
      }
    }
  ])

  // 使用useTable组合式函数
  const {
    data: loginInforList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<LoginInfor>({
    core: {
      apiFn: (params: any) => LoginInforApi.getLoginInforList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        ipaddr: '',
        userName: '',
        status: '',
        beginTime: '',
        endTime: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'infoId',
          label: '访问编号',
          width: 110,
          checked: true
        },
        {
          prop: 'userName',
          label: '用户名称',
          width: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'ipaddr',
          label: '登录地址',
          width: 130,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'loginLocation',
          label: '登录地点',
          width: 180,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'browser',
          label: '浏览器',
          width: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'os',
          label: '操作系统',
          width: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'status',
          label: '登录状态',
          width: 100,
          checked: true,
          useSlot: true
        },
        {
          prop: 'msg',
          label: '操作信息',
          minWidth: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'loginTime',
          label: '登录日期',
          width: 160,
          checked: true,
          useSlot: true
        }
      ]
    }
  })

  // 过滤有效的登录信息数据
  const validLoginInforList = computed(() => {
    if (!Array.isArray(loginInforList.value)) {
      console.warn('loginInforList不是数组:', typeof loginInforList.value, loginInforList.value)
      return []
    }
    return loginInforList.value.filter(
      (info) => info && info.infoId && info.userName && info.status !== undefined
    )
  })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(appliedFilters, { ...formFilters })

    // 处理日期范围
    const searchData: any = { ...appliedFilters }
    if (formFilters.dateRange && formFilters.dateRange.length === 2) {
      searchData.beginTime = formFilters.dateRange[0]
      searchData.endTime = formFilters.dateRange[1]
    }
    delete searchData.dateRange

    Object.assign(searchParams, { ...searchData, pageNum: 1 })
    refreshData()
  }

  // 刷新处理
  const handleRefresh = () => {
    console.log('刷新按钮被点击')
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection: LoginInfor[]) {
    ids.value = selection.map((item) => item.infoId!)
    userNames.value = selection.map((item) => item.userName!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  /** 删除按钮操作 */
  async function handleDelete(row?: LoginInfor) {
    const infoIds = row?.infoId ? [row.infoId] : ids.value
    try {
      await ElMessageBox.confirm(`是否确认删除访问编号为"${infoIds.join(',')}"的数据项？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await LoginInforApi.deleteLoginInfor(infoIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除登录日志失败:', error)
        ElMessage.error('删除登录日志失败')
      }
    }
  }

  /** 清空按钮操作 */
  async function handleClean() {
    try {
      await ElMessageBox.confirm('是否确认清空所有登录日志数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await LoginInforApi.cleanLoginInfor()
      ElMessage.success('清空成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清空登录日志失败:', error)
        ElMessage.error('清空登录日志失败')
      }
    }
  }

  /** 解锁按钮操作 */
  async function handleUnlock(row?: LoginInfor) {
    const userName = row?.userName || userNames.value[0]
    try {
      await ElMessageBox.confirm(`是否确认解锁用户"${userName}"数据项？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await LoginInforApi.unlockLoginInfor(userName)
      ElMessage.success('用户' + userName + '解锁成功')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('解锁用户失败:', error)
        ElMessage.error('解锁用户失败')
      }
    }
  }

  /** 导出按钮操作 */
  async function handleExport() {
    try {
      const exportParams: any = {
        ...appliedFilters
      }
      delete exportParams.dateRange

      const blob = await LoginInforApi.exportLoginInfor(exportParams)
      const { downloadBlob } = await import('@/utils/download')
      downloadBlob(blob, `logininfor_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出登录日志失败:', error)
      ElMessage.error('导出登录日志失败')
    }
  }
</script>

<style lang="scss" scoped>
  .logininfor-page {
    padding: 20px;

    .art-table-card {
      margin-top: 20px;
    }
  }
</style>
