<template>
  <div class="operlog-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton
            v-auth="'monitor:operlog:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
            >删除</ElButton
          >
          <ElButton v-auth="'monitor:operlog:remove'" type="danger" @click="handleClean" v-ripple
            >清空</ElButton
          >
          <ElButton v-auth="'monitor:operlog:export'" type="warning" @click="handleExport" v-ripple
            >导出</ElButton
          >
        </template>
      </ArtTableHeader>

      <ArtTable
        :loading="loading"
        :columns="columns"
        :data="validOperLogList"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #businessType="{ row }">
          <ElTag
            :type="
              row.businessType === 1
                ? 'primary'
                : row.businessType === 2
                  ? 'success'
                  : row.businessType === 3
                    ? 'danger'
                    : 'info'
            "
          >
            {{
              row.businessType === 1
                ? '新增'
                : row.businessType === 2
                  ? '修改'
                  : row.businessType === 3
                    ? '删除'
                    : '其它'
            }}
          </ElTag>
        </template>

        <template #status="{ row }">
          <ElTag :type="row.status === 0 ? 'success' : 'danger'">
            {{ row.status === 0 ? '成功' : '失败' }}
          </ElTag>
        </template>

        <template #operTime="{ row }">
          <span>{{ formatTime(row.operTime) }}</span>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="详细" placement="top">
            <el-button
              v-ripple
              v-auth="'monitor:operlog:query'"
              link
              type="primary"
              @click="handleView(row)"
            >
              详细
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 操作日志详细 -->
    <ElDialog title="操作日志详细" v-model="open" width="700px" append-to-body>
      <ElForm ref="formRef" :model="form" label-width="100px" readonly>
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="操作模块："
              >{{ form.title }} / {{ operTypeFormat(form) }}</ElFormItem
            >
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="登录信息："
              >{{ form.operName }} / {{ form.operIp }} / {{ form.operLocation }}</ElFormItem
            >
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="请求地址：">{{ form.operUrl }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="请求方式：">{{ form.requestMethod }}</ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="操作方法：">{{ form.method }}</ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="请求参数：">
              <ElInput
                v-model="form.operParam"
                type="textarea"
                readonly
                :rows="6"
                style="resize: none"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="24">
            <ElFormItem label="返回参数：">
              <ElInput
                v-model="form.jsonResult"
                type="textarea"
                readonly
                :rows="6"
                style="resize: none"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="操作状态：">
              <div>
                <ElTag :type="form.status === 0 ? 'success' : 'danger'">
                  {{ form.status === 0 ? '成功' : '失败' }}
                </ElTag>
              </div>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="操作时间：">{{ formatTime(form.operTime) }}</ElFormItem>
          </ElCol>
          <ElCol :span="10">
            <ElFormItem label="消耗时间：">{{ form.costTime }}毫秒</ElFormItem>
          </ElCol>
          <ElCol :span="24" v-if="form.status === 1">
            <ElFormItem label="异常信息：">
              <ElInput
                v-model="form.errorMsg"
                type="textarea"
                readonly
                :rows="4"
                style="resize: none"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="open = false">关 闭</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { OperLogApi } from '@/api/monitor/operlog'
  import { formatTime } from '@/utils/date'
  import type { OperLog } from '@/types/system/operlog'
  import { useTable } from '@/composables/useTable'

  defineOptions({ name: 'MonitorOperlog' })

  const open = ref(false)
  const ids = ref<number[]>([])
  const multiple = ref(true)

  // 定义表单搜索初始值
  const initialSearchState = {
    title: '',
    operName: '',
    businessType: '',
    status: '',
    operIp: '',
    beginTime: '',
    endTime: '',
    dateRange: [] as string[]
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })
  const appliedFilters = reactive({ ...initialSearchState })

  // 详情表单数据
  const form = reactive<Partial<OperLog>>({
    operId: undefined,
    title: '',
    businessType: undefined,
    method: '',
    requestMethod: '',
    operatorType: undefined,
    operName: '',
    deptName: '',
    operUrl: '',
    operIp: '',
    operLocation: '',
    operParam: '',
    jsonResult: '',
    status: undefined,
    errorMsg: '',
    operTime: '',
    costTime: undefined
  })

  // 搜索表单配置项
  const formItems = computed(() => [
    {
      label: '系统模块',
      key: 'title',
      type: 'input',
      props: { clearable: true, placeholder: '请输入系统模块' }
    },
    {
      label: '操作人员',
      key: 'operName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入操作人员' }
    },
    {
      label: '业务类型',
      key: 'businessType',
      type: 'select',
      props: { clearable: true, placeholder: '业务类型' },
      options: [
        { label: '新增', value: '1' },
        { label: '修改', value: '2' },
        { label: '删除', value: '3' },
        { label: '其它', value: '0' }
      ]
    },
    {
      label: '操作状态',
      key: 'status',
      type: 'select',
      props: { clearable: true, placeholder: '操作状态' },
      options: [
        { label: '成功', value: '0' },
        { label: '失败', value: '1' }
      ]
    },
    {
      label: '操作地址',
      key: 'operIp',
      type: 'input',
      props: { clearable: true, placeholder: '请输入操作地址' }
    },
    {
      label: '操作时间',
      key: 'dateRange',
      type: 'daterange',
      props: {
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        'value-format': 'YYYY-MM-DD'
      }
    }
  ])

  // 使用useTable组合式函数
  const {
    data: operLogList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<OperLog>({
    core: {
      apiFn: (params: any) => OperLogApi.getOperLogList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        title: '',
        operName: '',
        businessType: '',
        status: '',
        operIp: '',
        beginTime: '',
        endTime: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          prop: 'operId',
          label: '日志编号',
          width: 110,
          checked: true
        },
        {
          prop: 'title',
          label: '系统模块',
          minWidth: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'operName',
          label: '操作人员',
          width: 100,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'operIp',
          label: '操作地址',
          width: 130,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'operLocation',
          label: '操作地点',
          width: 130,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'businessType',
          label: '业务类型',
          width: 100,
          checked: true,
          useSlot: true
        },
        {
          prop: 'status',
          label: '状态',
          width: 80,
          checked: true,
          useSlot: true
        },
        {
          prop: 'operTime',
          label: '操作日期',
          width: 160,
          checked: true,
          useSlot: true
        },
        {
          prop: 'costTime',
          label: '消耗时间',
          width: 110,
          checked: true,
          formatter: (row: OperLog) => `${row.costTime}毫秒`
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          fixed: 'right',
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // 过滤有效的操作日志数据
  const validOperLogList = computed(() => {
    if (!Array.isArray(operLogList.value)) {
      console.warn('operLogList不是数组:', typeof operLogList.value, operLogList.value)
      return []
    }
    return operLogList.value.filter(
      (log) => log && log.operId && log.title && log.status !== undefined
    )
  })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(appliedFilters, { ...formFilters })

    // 处理日期范围
    const searchData: any = { ...appliedFilters }
    if (formFilters.dateRange && formFilters.dateRange.length === 2) {
      searchData.beginTime = formFilters.dateRange[0]
      searchData.endTime = formFilters.dateRange[1]
    }
    delete searchData.dateRange

    // 处理businessType类型转换
    if (searchData.businessType) {
      searchData.businessType = Number(searchData.businessType)
    }
    if (searchData.status) {
      searchData.status = Number(searchData.status)
    }

    Object.assign(searchParams, { ...searchData, pageNum: 1 })
    refreshData()
  }

  // 刷新处理
  const handleRefresh = () => {
    console.log('刷新按钮被点击')
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection: OperLog[]) {
    ids.value = selection.map((item) => item.operId!)
    multiple.value = !selection.length
  }

  /** 详细按钮操作 */
  function handleView(row: OperLog) {
    // 直接使用表格行数据，无需调用API
    Object.assign(form, row)
    open.value = true
  }

  /** 删除按钮操作 */
  async function handleDelete(row?: OperLog) {
    const operIds = row?.operId ? [row.operId] : ids.value
    try {
      await ElMessageBox.confirm(`是否确认删除日志编号为"${operIds.join(',')}"的数据项？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await OperLogApi.deleteOperLog(operIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除操作日志失败:', error)
        ElMessage.error('删除操作日志失败')
      }
    }
  }

  /** 清空按钮操作 */
  async function handleClean() {
    try {
      await ElMessageBox.confirm('是否确认清空所有操作日志数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await OperLogApi.cleanOperLog()
      ElMessage.success('清空成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清空操作日志失败:', error)
        ElMessage.error('清空操作日志失败')
      }
    }
  }

  /** 导出按钮操作 */
  async function handleExport() {
    try {
      const exportParams: any = {
        ...appliedFilters
      }
      delete exportParams.dateRange

      // 处理businessType类型转换
      if (exportParams.businessType) {
        exportParams.businessType = Number(exportParams.businessType)
      }
      if (exportParams.status) {
        exportParams.status = Number(exportParams.status)
      }

      const blob = await OperLogApi.exportOperLog(exportParams)
      const { downloadBlob } = await import('@/utils/download')
      downloadBlob(blob, `operlog_${new Date().getTime()}.xlsx`)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出操作日志失败:', error)
      ElMessage.error('导出操作日志失败')
    }
  }

  /** 操作类型字典翻译 */
  function operTypeFormat(row: Partial<OperLog>) {
    switch (row.businessType) {
      case 1:
        return '新增'
      case 2:
        return '修改'
      case 3:
        return '删除'
      default:
        return '其它'
    }
  }
</script>

<style lang="scss" scoped>
  .operlog-page {
    padding: 20px;

    .art-table-card {
      margin-top: 20px;
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
