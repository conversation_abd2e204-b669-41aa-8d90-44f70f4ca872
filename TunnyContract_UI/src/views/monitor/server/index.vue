<template>
  <div class="server-page">
    <ElRow :gutter="20">
      <!-- CPU信息 -->
      <ElCol :span="12" class="info-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Cpu />
              </ElIcon>
              <span>CPU</span>
            </div>
          </template>
          <div class="info-table">
            <table>
              <thead>
                <tr>
                  <th>属性</th>
                  <th>值</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>核心数</td>
                  <td>{{ server.cpu?.cpuNum || '-' }}</td>
                </tr>
                <tr>
                  <td>用户使用率</td>
                  <td :class="getUsageClass(server.cpu?.used)">
                    {{ server.cpu?.used ? `${server.cpu.used}%` : '-' }}
                  </td>
                </tr>
                <tr>
                  <td>系统使用率</td>
                  <td :class="getUsageClass(server.cpu?.sys)">
                    {{ server.cpu?.sys ? `${server.cpu.sys}%` : '-' }}
                  </td>
                </tr>
                <tr>
                  <td>当前空闲率</td>
                  <td>{{ server.cpu?.free ? `${server.cpu.free}%` : '-' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </ElCard>
      </ElCol>

      <!-- 内存信息 -->
      <ElCol :span="12" class="info-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Tickets />
              </ElIcon>
              <span>内存</span>
            </div>
          </template>
          <div class="info-table">
            <table>
              <thead>
                <tr>
                  <th>属性</th>
                  <th>内存</th>
                  <th>JVM</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>总内存</td>
                  <td>{{ server.mem?.total ? `${server.mem.total}G` : '-' }}</td>
                  <td>{{ server.jvm?.total ? `${server.jvm.total}M` : '-' }}</td>
                </tr>
                <tr>
                  <td>已用内存</td>
                  <td>{{ server.mem?.used ? `${server.mem.used}G` : '-' }}</td>
                  <td>{{ server.jvm?.used ? `${server.jvm.used}M` : '-' }}</td>
                </tr>
                <tr>
                  <td>剩余内存</td>
                  <td>{{ server.mem?.free ? `${server.mem.free}G` : '-' }}</td>
                  <td>{{ server.jvm?.free ? `${server.jvm.free}M` : '-' }}</td>
                </tr>
                <tr>
                  <td>使用率</td>
                  <td :class="getUsageClass(server.mem?.usage)">
                    {{ server.mem?.usage ? `${server.mem.usage}%` : '-' }}
                  </td>
                  <td :class="getUsageClass(server.jvm?.usage)">
                    {{ server.jvm?.usage ? `${server.jvm.usage}%` : '-' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </ElCard>
      </ElCol>

      <!-- 服务器信息 -->
      <ElCol :span="24" class="info-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Monitor />
              </ElIcon>
              <span>服务器信息</span>
              <ElButton type="primary" @click="handleRefresh" v-ripple>
                <ElIcon><Refresh /></ElIcon>
                刷新
              </ElButton>
            </div>
          </template>
          <div class="info-table">
            <table>
              <thead>
                <tr>
                  <th>服务器名称</th>
                  <th>服务器IP</th>
                  <th>操作系统</th>
                  <th>系统架构</th>
                  <th>项目路径</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ server.sys?.computerName || '-' }}</td>
                  <td>{{ server.sys?.computerIp || '-' }}</td>
                  <td>{{ server.sys?.osName || '-' }}</td>
                  <td>{{ server.sys?.osArch || '-' }}</td>
                  <td class="path-cell" :title="server.sys?.userDir">
                    {{ server.sys?.userDir || '-' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </ElCard>
      </ElCol>

      <!-- Java虚拟机信息 -->
      <ElCol :span="24" class="info-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Platform />
              </ElIcon>
              <span>Java虚拟机信息</span>
            </div>
          </template>
          <div class="info-table">
            <table>
              <thead>
                <tr>
                  <th>Java名称</th>
                  <th>Java版本</th>
                  <th>启动时间</th>
                  <th>运行时长</th>
                  <th>安装路径</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ server.jvm?.name || '-' }}</td>
                  <td>{{ server.jvm?.version || '-' }}</td>
                  <td>{{ server.jvm?.startTime || '-' }}</td>
                  <td>{{ server.jvm?.runTime || '-' }}</td>
                  <td class="path-cell" :title="server.jvm?.home">
                    {{ server.jvm?.home || '-' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </ElCard>
      </ElCol>

      <!-- 磁盘状态 -->
      <ElCol :span="24" class="info-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <FolderOpened />
              </ElIcon>
              <span>磁盘状态</span>
            </div>
          </template>
          <div class="disk-table">
            <ElTable :data="server.sysFiles || []" border size="default" table-layout="auto">
              <ElTableColumn label="盘符路径" prop="dirName" show-overflow-tooltip />
              <ElTableColumn label="文件系统" prop="sysTypeName" />
              <ElTableColumn label="盘符类型" prop="typeName" />
              <ElTableColumn label="总大小" prop="total" />
              <ElTableColumn label="可用大小" prop="free" />
              <ElTableColumn label="已用大小" prop="used" />
              <ElTableColumn label="已用百分比" prop="usage" width="120">
                <template #default="{ row }">
                  <div class="usage-cell">
                    <span :class="getUsageClass(row.usage)">
                      {{ row.usage ? `${row.usage}%` : '-' }}
                    </span>
                    <ElProgress
                      :percentage="row.usage || 0"
                      :status="row.usage > 80 ? 'exception' : undefined"
                      :show-text="false"
                      class="usage-progress"
                    />
                  </div>
                </template>
              </ElTableColumn>
            </ElTable>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { Cpu, Tickets, Monitor, Platform, FolderOpened, Refresh } from '@element-plus/icons-vue'
  import { ServerApi } from '@/api/monitor/server'
  import type { Server } from '@/types/monitor/server'

  defineOptions({ name: 'MonitorServer' })

  // 响应式数据
  const server = ref<Server>({})
  const loading = ref(false)

  onMounted(() => {
    getServerInfo()
    // 设置定时刷新，每30秒刷新一次
    const timer = setInterval(() => {
      getServerInfo()
    }, 30000)

    onBeforeUnmount(() => {
      clearInterval(timer)
    })
  })

  /** 获取服务器信息 */
  async function getServerInfo() {
    if (loading.value) return

    loading.value = true
    try {
      const response = await ServerApi.getServer()
      // 从控制台日志可以看出数据结构是 response.data.data，而不是直接 response.data
      const serverData = (response.data as any)?.data || response.data
      server.value = serverData || {}
      console.log('原始响应:', response.data)
      console.log('解析后的服务器数据:', server.value)
    } catch (error) {
      console.error('获取服务器信息失败:', error)
      ElMessage.error('获取服务器信息失败')
    } finally {
      loading.value = false
    }
  }

  /** 获取使用率样式类 */
  function getUsageClass(usage?: number): string {
    if (!usage) return ''
    if (usage > 80) return 'text-danger'
    if (usage > 60) return 'text-warning'
    return 'text-success'
  }

  /** 手动刷新 */
  function handleRefresh() {
    getServerInfo()
  }
</script>

<style lang="scss" scoped>
  .server-page {
    padding: 20px;

    .info-card {
      margin-bottom: 20px;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-icon {
        margin-right: 8px;
      }
    }

    .info-table {
      table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 12px;
          border: 1px solid var(--art-border-color);
          text-align: center;
        }

        th {
          background-color: var(--art-gray-50);
          font-weight: 500;
        }

        .path-cell {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .disk-table {
      .usage-cell {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .usage-progress {
          width: 80px;
        }
      }
    }

    .text-success {
      color: var(--el-color-success);
    }

    .text-warning {
      color: var(--el-color-warning);
    }

    .text-danger {
      color: var(--el-color-danger);
    }
  }
</style>
