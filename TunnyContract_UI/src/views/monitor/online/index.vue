<template>
  <div class="online-user-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'monitor:online:export'" type="warning" @click="handleExport" v-ripple>
            导出
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="tokenId"
        :loading="loading"
        :columns="columns"
        :data="onlineUserList"
        :stripe="true"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
        <template #loginTime="{ row }">
          <span>{{ formatTime(row.loginTime) }}</span>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="强退" placement="top">
            <el-button v-ripple link type="danger" @click="handleForceLogout(row)">
              强退
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { reactive, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { OnlineUserApi } from '@/api/monitor/online'
  import { formatTime } from '@/utils/date'
  import type { OnlineUser } from '@/types/monitor/online'

  defineOptions({ name: 'MonitorOnlineUser' })

  // 定义表单搜索初始值
  const initialSearchState = {
    ipaddr: '',
    userName: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '登录地址',
      key: 'ipaddr',
      type: 'input',
      props: { clearable: true, placeholder: '请输入登录地址' }
    },
    {
      label: '用户名称',
      key: 'userName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入用户名称' }
    }
  ])

  // 使用useTable组合式函数
  const {
    data: onlineUserList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<OnlineUser>({
    core: {
      apiFn: (params: any) => OnlineUserApi.getOnlineUserList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        ipaddr: '',
        userName: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'index',
          label: '序号',
          width: 60,
          checked: true,
          disabled: true
        },
        {
          prop: 'tokenId',
          label: '会话编号',
          minWidth: 180,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'userName',
          label: '登录名称',
          width: 120,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'deptName',
          label: '所属部门',
          width: 120,
          checked: true,
          showOverflowTooltip: true,
          formatter: (row: OnlineUser) => row.deptName || '--'
        },
        {
          prop: 'ipaddr',
          label: '主机',
          width: 130,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'loginLocation',
          label: '登录地点',
          width: 120,
          checked: true,
          showOverflowTooltip: true,
          formatter: (row: OnlineUser) => row.loginLocation || '--'
        },
        {
          prop: 'os',
          label: '操作系统',
          width: 120,
          checked: true,
          showOverflowTooltip: true,
          formatter: (row: OnlineUser) => row.os || '--'
        },
        {
          prop: 'browser',
          label: '浏览器',
          width: 120,
          checked: true,
          showOverflowTooltip: true,
          formatter: (row: OnlineUser) => row.browser || '--'
        },
        {
          prop: 'loginTime',
          label: '登录时间',
          width: 180,
          checked: true,
          useSlot: true
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          fixed: 'right',
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // 刷新处理
  const handleRefresh = () => {
    refreshData()
  }

  // 强退用户
  const handleForceLogout = async (row: OnlineUser) => {
    try {
      await ElMessageBox.confirm(`是否确认强退名称为"${row.userName}"的用户?`, '提示', {
        type: 'warning'
      })
      await OnlineUserApi.forceLogout(row.tokenId)
      ElMessage.success('强退成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('强退用户失败:', error)
        ElMessage.error('强退失败')
      }
    }
  }

  // 导出数据
  const handleExport = async () => {
    try {
      const blob = await OnlineUserApi.exportOnlineUser(formFilters)
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '在线用户数据.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }
</script>

<style lang="scss" scoped>
  .online-user-page {
    padding: 20px;

    .art-table-card {
      margin-top: 20px;
    }
  }
</style>
