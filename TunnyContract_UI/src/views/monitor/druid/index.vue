<template>
  <div class="druid-page">
    <ElCard class="art-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <ElIcon class="header-icon">
            <Monitor />
          </ElIcon>
          <span>数据监控</span>
          <div class="header-actions">
            <ElButton type="primary" @click="handleRefresh" v-ripple>
              <ElIcon><Refresh /></ElIcon>
              刷新
            </ElButton>
            <ElButton type="success" @click="handleOpenNew" v-ripple>
              <ElIcon><Link /></ElIcon>
              新窗口打开
            </ElButton>
          </div>
        </div>
      </template>

      <div class="iframe-container">
        <iframe
          ref="druidFrameRef"
          :src="druidUrl"
          frameborder="0"
          width="100%"
          height="100%"
          class="druid-iframe"
          @load="handleFrameLoad"
        />
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { Monitor, Refresh, Link } from '@element-plus/icons-vue'

  defineOptions({ name: 'MonitorDruid' })

  // 响应式数据
  const druidFrameRef = ref<HTMLIFrameElement>()
  const loading = ref(true)

  // Druid监控URL
  const druidUrl = computed(() => {
    const baseAPI = import.meta.env.VITE_APP_BASE_API
    return `${baseAPI}/druid/login.html`
  })

  onMounted(() => {
    // 页面加载完成后设置加载状态
    setTimeout(() => {
      loading.value = false
    }, 1000)
  })

  /** 处理iframe加载完成 */
  function handleFrameLoad() {
    loading.value = false
    ElMessage.success('数据监控页面加载完成')
  }

  /** 刷新页面 */
  function handleRefresh() {
    if (druidFrameRef.value) {
      loading.value = true
      druidFrameRef.value.src = druidUrl.value
    }
  }

  /** 新窗口打开 */
  function handleOpenNew() {
    window.open(druidUrl.value, '_blank')
  }
</script>

<style lang="scss" scoped>
  .druid-page {
    padding: 20px;

    .art-table-card {
      height: calc(100vh - 120px);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-icon {
        margin-right: 8px;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .iframe-container {
      height: calc(100% - 60px);
      position: relative;
      border: 1px solid var(--art-border-color);
      border-radius: 6px;
      overflow: hidden;

      .druid-iframe {
        display: block;
        border: none;
        background: white;
      }
    }

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      padding: 20px;
    }
  }
</style>
