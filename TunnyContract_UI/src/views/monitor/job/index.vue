<template>
  <div class="job-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'monitor:job:add'" type="primary" @click="handleAdd" v-ripple>
            新增任务
          </ElButton>
          <ElButton
            v-auth="'monitor:job:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'monitor:job:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'monitor:job:export'" type="warning" @click="handleExport" v-ripple>
            导出
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="jobId"
        :loading="loading"
        :columns="columns"
        :data="jobList"
        :stripe="true"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #jobGroup="{ row }">
          <ElTag>{{ getJobGroupLabel(row.jobGroup || '') }}</ElTag>
        </template>

        <template #status="{ row }">
          <ElTag :type="getJobStatusTagType(row.status || '0')">
            {{ getJobStatusLabel(row.status || '0') }}
          </ElTag>
        </template>

        <template #createTime="{ row }">
          <span>{{ row.createTime ? formatTime(row.createTime) : '-' }}</span>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="修改" placement="top">
            <el-button
              v-auth="'monitor:job:edit'"
              link
              type="primary"
              @click="handleUpdate(row)"
              v-ripple
            >
              修改
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-auth="'monitor:job:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
              v-ripple
            >
              删除
            </el-button>
          </el-tooltip>
          <el-tooltip content="执行一次" placement="top">
            <el-button
              v-auth="'monitor:job:changeStatus'"
              link
              type="info"
              @click="handleRun(row)"
              v-ripple
            >
              执行
            </el-button>
          </el-tooltip>
          <el-tooltip content="任务详细" placement="top">
            <el-button
              v-auth="'monitor:job:query'"
              link
              type="success"
              @click="handleDetail(row)"
              v-ripple
            >
              详细
            </el-button>
          </el-tooltip>
          <el-tooltip content="调度日志" placement="top">
            <el-button
              v-auth="'monitor:job:query'"
              link
              type="warning"
              @click="handleJobLog(row)"
              v-ripple
            >
              日志
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 添加/修改对话框 -->
    <ElDialog v-model="open" :title="title" width="800px" :close-on-click-modal="false">
      <ElForm ref="jobRef" :model="form" :rules="rules" label-width="120px">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="任务名称" prop="jobName">
              <ElInput v-model="form.jobName" placeholder="请输入任务名称" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="任务分组" prop="jobGroup">
              <ElSelect v-model="form.jobGroup" placeholder="请选择任务分组" clearable>
                <ElOption
                  v-for="dict in jobGroupOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="调用方法" prop="invokeTarget">
              <ElInput v-model="form.invokeTarget" placeholder="请输入调用目标字符串" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="cron表达式" prop="cronExpression">
              <ElInput v-model="form.cronExpression" placeholder="请输入cron执行表达式">
                <template #append>
                  <ElButton @click="openCronDialog">生成表达式</ElButton>
                </template>
              </ElInput>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="执行策略" prop="misfirePolicy">
              <ElRadioGroup v-model="form.misfirePolicy">
                <ElRadio value="1">立即执行</ElRadio>
                <ElRadio value="2">执行一次</ElRadio>
                <ElRadio value="3">放弃执行</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="并发执行" prop="concurrent">
              <ElRadioGroup v-model="form.concurrent">
                <ElRadio value="0">允许</ElRadio>
                <ElRadio value="1">禁止</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="状态" prop="status">
              <ElRadioGroup v-model="form.status">
                <ElRadio v-for="dict in jobStatusOptions" :key="dict.value" :value="dict.value">
                  {{ dict.label }}
                </ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="备注" prop="remark">
              <ElInput v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>

      <template #footer>
        <ElButton @click="cancel">取 消</ElButton>
        <ElButton type="primary" @click="submitForm">确 定</ElButton>
      </template>
    </ElDialog>

    <!-- 任务详细对话框 -->
    <ElDialog v-model="openView" title="任务详细" width="700px">
      <ElForm label-width="120px">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="任务编号：">{{ form.jobId }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="任务名称：">{{ form.jobName }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="任务分组：">{{ form.jobGroup }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="创建时间：">{{
              form.createTime ? formatTime(form.createTime) : '-'
            }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="cron表达式：">{{ form.cronExpression }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="下次执行时间：">{{ form.nextValidTime || '-' }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="调用目标方法：">{{ form.invokeTarget }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="任务状态：">
              <ElTag :type="getJobStatusTagType(form.status || '0')">
                {{ getJobStatusLabel(form.status || '0') }}
              </ElTag>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="并发执行：">
              <ElTag :type="form.concurrent === '0' ? 'success' : 'warning'">
                {{ form.concurrent === '0' ? '允许' : '禁止' }}
              </ElTag>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="执行策略：">
              {{
                form.misfirePolicy === '1'
                  ? '立即执行'
                  : form.misfirePolicy === '2'
                    ? '执行一次'
                    : '放弃执行'
              }}
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="备注信息：">{{ form.remark }}</ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>

      <template #footer>
        <ElButton @click="openView = false">关 闭</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { reactive, computed, ref, nextTick } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { useDict } from '@/composables/useDict'
  import { JobApi } from '@/api/monitor/job'
  import { formatTime } from '@/utils/date'
  import { useRouter } from 'vue-router'
  import { RoutesAlias } from '@/router/routesAlias'
  import type { DictOption } from '@/types/system/dict'
  import type { Job, JobForm } from '@/types/monitor/job'

  defineOptions({ name: 'MonitorJob' })

  const router = useRouter()

  // 字典数据
  const { sys_job_group, sys_job_status } = useDict('sys_job_group', 'sys_job_status')

  // 调试输出字典数据
  console.log('字典数据 sys_job_group:', sys_job_group.value)
  console.log('字典数据 sys_job_status:', sys_job_status.value)

  // 定义表单搜索初始值
  const initialSearchState = {
    jobName: '',
    jobGroup: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)
  const tableRef = ref()

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 提供fallback字典数据
  const fallbackJobGroup: DictOption[] = [
    { label: '默认', value: 'DEFAULT' },
    { label: '系统', value: 'SYSTEM' }
  ]

  const fallbackJobStatus: DictOption[] = [
    { label: '正常', value: '0' },
    { label: '暂停', value: '1' }
  ]

  // 计算属性确保类型安全
  const jobGroupOptions = computed(() => {
    return sys_job_group.value?.length ? sys_job_group.value : fallbackJobGroup
  })

  const jobStatusOptions = computed(() => {
    return sys_job_status.value?.length ? sys_job_status.value : fallbackJobStatus
  })

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '任务名称',
      key: 'jobName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入任务名称' }
    },
    {
      label: '任务组名',
      key: 'jobGroup',
      type: 'select',
      options: jobGroupOptions.value,
      props: { clearable: true, placeholder: '请选择任务组名' }
    },
    {
      label: '任务状态',
      key: 'status',
      type: 'select',
      options: jobStatusOptions.value,
      props: { clearable: true, placeholder: '请选择任务状态' }
    }
  ])

  // 使用useTable组合式函数
  const {
    data: jobList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<Job>({
    core: {
      apiFn: (params: any) => JobApi.getJobList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '',
        status: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 50,
          checked: true,
          disabled: true
        },
        {
          type: 'index',
          label: '序号',
          width: 60,
          checked: true,
          disabled: true
        },
        {
          prop: 'jobId',
          label: '任务编号',
          width: 100,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'jobName',
          label: '任务名称',
          width: 150,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'jobGroup',
          label: '任务组名',
          width: 120,
          checked: true,
          useSlot: true
        },
        {
          prop: 'invokeTarget',
          label: '调用目标字符串',
          minWidth: 200,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'cronExpression',
          label: 'cron表达式',
          width: 150,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'status',
          label: '状态',
          width: 80,
          checked: true,
          useSlot: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          checked: true,
          useSlot: true
        },
        {
          prop: 'operation',
          label: '操作',
          width: 260,
          fixed: 'right',
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // 刷新处理
  const handleRefresh = () => {
    refreshData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: Job[]) => {
    ids.value = selection.map((item) => item.jobId!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 对话框状态
  const open = ref(false)
  const openView = ref(false)
  const title = ref('')

  // 表单数据
  const form = ref<JobForm>({
    jobName: '',
    jobGroup: '',
    invokeTarget: '',
    cronExpression: '',
    misfirePolicy: '1',
    concurrent: '1',
    status: '0'
  })

  // 表单验证规则
  const rules = {
    jobName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
    jobGroup: [{ required: true, message: '任务分组不能为空', trigger: 'blur' }],
    invokeTarget: [{ required: true, message: '调用目标字符串不能为空', trigger: 'blur' }],
    cronExpression: [{ required: true, message: 'cron执行表达式不能为空', trigger: 'blur' }]
  }

  const jobRef = ref()

  // 重置表单
  const resetForm = () => {
    form.value = {
      jobName: '',
      jobGroup: '',
      invokeTarget: '',
      cronExpression: '',
      misfirePolicy: '1',
      concurrent: '1',
      status: '0'
    }
    // 确保在下次渲染时重置字段
    nextTick(() => {
      jobRef.value?.resetFields()
    })
  }

  // 取消按钮
  const cancel = () => {
    open.value = false
    resetForm()
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    open.value = true
    title.value = '添加任务'
  }

  // 修改按钮操作 - 支持从行数据或选中数据获取
  const handleUpdate = async (row?: Job) => {
    const jobId = row?.jobId || ids.value[0]

    console.log('handleUpdate - jobId:', jobId)
    console.log('handleUpdate - row:', row)
    console.log('handleUpdate - ids.value:', ids.value)

    if (!jobId) {
      ElMessage.warning('请选择一条记录进行修改')
      return
    }

    try {
      const jobData = await JobApi.getJob(jobId)
      console.log('API返回的任务数据:', jobData)

      // 先重置表单
      resetForm()

      // 延迟填充数据，确保表单已重置
      await nextTick()
      Object.assign(form.value, jobData)
      console.log('填充后的表单数据:', form.value)

      open.value = true
      title.value = '修改任务'
    } catch (error) {
      console.error('获取任务详情失败:', error)
      ElMessage.error('获取任务详情失败')
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!jobRef.value) return

    await jobRef.value.validate(async (valid: boolean) => {
      if (valid) {
        try {
          if (form.value.jobId) {
            await JobApi.updateJob(form.value)
            ElMessage.success('修改成功')
          } else {
            await JobApi.addJob(form.value)
            ElMessage.success('新增成功')
          }
          open.value = false
          refreshData()
        } catch (error) {
          console.error('保存失败:', error)
          ElMessage.error('保存失败')
        }
      }
    })
  }

  // 删除按钮操作
  const handleDelete = async (row?: Job) => {
    const jobIds = row?.jobId ? [row.jobId] : ids.value
    if (!jobIds.length) {
      ElMessage.warning('请选择要删除的数据')
      return
    }

    try {
      await ElMessageBox.confirm(
        `是否确认删除定时任务编号为"${jobIds.join(',')}"的数据项?`,
        '提示',
        {
          type: 'warning'
        }
      )
      await JobApi.delJob(jobIds.length === 1 ? jobIds[0] : jobIds.join(','))
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 立即执行一次
  const handleRun = async (row: Job) => {
    try {
      await ElMessageBox.confirm(`确认要立即执行一次"${row.jobName}"任务吗?`, '警告', {
        type: 'warning'
      })
      await JobApi.runJob(row.jobId!, row.jobGroup!)
      ElMessage.success('执行成功')
    } catch (error) {
      if (error !== 'cancel') {
        console.error('执行失败:', error)
        ElMessage.error('执行失败')
      }
    }
  }

  // 任务详细信息
  const handleDetail = async (row: Job) => {
    resetForm()
    try {
      const response = await JobApi.getJob(row.jobId!)
      const jobData = (response.data as any)?.data || response.data
      Object.assign(form.value, jobData)
      openView.value = true
    } catch (error) {
      console.error('获取任务详情失败:', error)
      ElMessage.error('获取任务详情失败')
    }
  }

  // 任务日志 - 先尝试路由跳转，如果失败则提示
  const handleJobLog = (row: Job) => {
    try {
      router.push({
        path: RoutesAlias.JobLog,
        query: { jobId: row.jobId }
      })
    } catch (error) {
      console.error('路由跳转失败:', error)
      ElMessage.error('日志页面路由配置有误，请检查路由配置')
    }
  }

  // 导出数据
  const handleExport = async () => {
    try {
      const response = await JobApi.exportJob(formFilters)
      const blob = response.data as Blob
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '定时任务数据.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  // 打开cron表达式生成器
  const openCronDialog = () => {
    // TODO: 实现cron表达式生成器
    ElMessage.info('cron表达式生成器功能待实现')
  }

  // 字典标签处理函数
  const getJobGroupLabel = (value: string) => {
    const dict = jobGroupOptions.value.find((item) => item.value === value)
    return dict?.label || value
  }

  const getJobStatusLabel = (value: string) => {
    const dict = jobStatusOptions.value.find((item) => item.value === value)
    return dict?.label || value
  }

  const getJobStatusTagType = (status: string) => {
    return status === '0' ? 'success' : 'danger'
  }
</script>

<style lang="scss" scoped>
  .job-page {
    height: 100%;
  }
</style>
