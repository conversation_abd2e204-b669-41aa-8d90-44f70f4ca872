<template>
  <div class="job-log-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton
            v-auth="'monitor:jobLog:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
          <ElButton v-auth="'monitor:jobLog:remove'" type="danger" @click="handleClean" v-ripple>
            清空
          </ElButton>
          <ElButton v-auth="'monitor:jobLog:export'" type="warning" @click="handleExport" v-ripple>
            导出
          </ElButton>
          <ElButton @click="handleClose" v-ripple> 关闭 </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="jobLogId"
        :loading="loading"
        :columns="columns"
        :data="jobLogList"
        :stripe="true"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #jobGroup="{ row }">
          <ElTag>{{ getJobGroupLabel(row.jobGroup) }}</ElTag>
        </template>

        <template #status="{ row }">
          <ElTag :type="getJobLogStatusTagType(row.status)">
            {{ getJobLogStatusLabel(row.status) }}
          </ElTag>
        </template>

        <template #createTime="{ row }">
          <span>{{ formatTime(row.createTime) }}</span>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="详细" placement="top">
            <el-button link type="primary" @click="handleView(row)" v-ripple> 详细 </el-button>
          </el-tooltip>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 调度日志详细 -->
    <ElDialog v-model="open" title="调度日志详细" width="700px">
      <ElForm label-width="120px">
        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="日志序号：">{{ form.jobLogId }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="任务名称：">{{ form.jobName }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="任务分组：">{{ form.jobGroup }}</ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="创建时间：">{{ formatTime(form.createTime) }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="调用目标字符串：">{{ form.invokeTarget }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="24">
            <ElFormItem label="日志信息：">{{ form.jobMessage }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow>
          <ElCol :span="12">
            <ElFormItem label="执行状态：">
              <ElTag :type="getJobLogStatusTagType(form.status || '0')">
                {{ getJobLogStatusLabel(form.status || '0') }}
              </ElTag>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="执行时间：">{{ form.startTime }} - {{ form.stopTime }}</ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow v-if="form.status === '1'">
          <ElCol :span="24">
            <ElFormItem label="异常信息：">
              <ElInput
                v-model="form.exceptionInfo"
                type="textarea"
                :rows="6"
                readonly
                placeholder="无异常信息"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>

      <template #footer>
        <ElButton @click="open = false">关 闭</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { reactive, computed, ref, onMounted } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { useDict } from '@/composables/useDict'
  import { JobLogApi } from '@/api/monitor/jobLog'
  import { formatTime } from '@/utils/date'
  import { useRoute, useRouter } from 'vue-router'
  import type { JobLog } from '@/types/monitor/jobLog'

  defineOptions({ name: 'MonitorJobLog' })

  const route = useRoute()
  const router = useRouter()

  // 字典数据
  const { sys_job_group, sys_common_status } = useDict('sys_job_group', 'sys_common_status')

  // 定义表单搜索初始值
  const initialSearchState = {
    jobName: '',
    jobGroup: '',
    status: '',
    beginTime: '',
    endTime: '',
    dateRange: [] as string[]
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const multiple = ref(true)
  const tableRef = ref()

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    const { dateRange, ...otherFilters } = formFilters
    const searchData = { ...otherFilters }

    if (dateRange && dateRange.length === 2) {
      searchData.beginTime = dateRange[0]
      searchData.endTime = dateRange[1]
    }

    Object.assign(searchParams, { ...searchData, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '任务名称',
      key: 'jobName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入任务名称' }
    },
    {
      label: '任务组名',
      key: 'jobGroup',
      type: 'select',
      options: sys_job_group.value || [],
      props: { clearable: true, placeholder: '请选择任务组名' }
    },
    {
      label: '执行状态',
      key: 'status',
      type: 'select',
      options: sys_common_status.value || [],
      props: { clearable: true, placeholder: '请选择执行状态' }
    },
    {
      label: '执行时间',
      key: 'dateRange',
      type: 'daterange',
      props: {
        clearable: true,
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期',
        'value-format': 'YYYY-MM-DD'
      }
    }
  ])

  // 使用useTable组合式函数
  const {
    data: jobLogList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<JobLog>({
    core: {
      apiFn: (params: any) => JobLogApi.getJobLogList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '',
        status: '',
        beginTime: '',
        endTime: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 50,
          checked: true,
          disabled: true
        },
        {
          type: 'index',
          label: '序号',
          width: 60,
          checked: true,
          disabled: true
        },
        {
          prop: 'jobLogId',
          label: '日志编号',
          width: 100,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'jobName',
          label: '任务名称',
          width: 150,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'jobGroup',
          label: '任务组名',
          width: 120,
          checked: true,
          useSlot: true
        },
        {
          prop: 'invokeTarget',
          label: '调用目标字符串',
          minWidth: 200,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'jobMessage',
          label: '日志信息',
          minWidth: 200,
          checked: true,
          showOverflowTooltip: true
        },
        {
          prop: 'status',
          label: '执行状态',
          width: 100,
          checked: true,
          useSlot: true
        },
        {
          prop: 'createTime',
          label: '执行时间',
          width: 180,
          checked: true,
          useSlot: true
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          fixed: 'right',
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // 刷新处理
  const handleRefresh = () => {
    refreshData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: JobLog[]) => {
    ids.value = selection.map((item) => item.jobLogId!)
    multiple.value = !selection.length
  }

  // 对话框状态
  const open = ref(false)

  // 表单数据
  const form = ref<JobLog>({})

  // 查看日志详细
  const handleView = (row: JobLog) => {
    Object.assign(form.value, row)
    open.value = true
  }

  // 删除按钮操作
  const handleDelete = async (row?: JobLog) => {
    const jobLogIds = row?.jobLogId ? [row.jobLogId] : ids.value
    if (!jobLogIds.length) {
      ElMessage.warning('请选择要删除的数据')
      return
    }

    try {
      await ElMessageBox.confirm(
        `是否确认删除调度日志编号为"${jobLogIds.join(',')}"的数据项?`,
        '提示',
        {
          type: 'warning'
        }
      )
      await JobLogApi.delJobLog(jobLogIds.length === 1 ? jobLogIds[0] : jobLogIds.join(','))
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    }
  }

  // 清空按钮操作
  const handleClean = async () => {
    try {
      await ElMessageBox.confirm('是否确认清空所有调度日志数据项?', '警告', {
        type: 'warning'
      })
      await JobLogApi.cleanJobLog()
      ElMessage.success('清空成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清空失败:', error)
        ElMessage.error('清空失败')
      }
    }
  }

  // 导出数据
  const handleExport = async () => {
    try {
      const { dateRange, ...otherFilters } = formFilters
      const exportData = { ...otherFilters }

      if (dateRange && dateRange.length === 2) {
        exportData.beginTime = dateRange[0]
        exportData.endTime = dateRange[1]
      }

      const response = await JobLogApi.exportJobLog(exportData)
      const blob = response.data as Blob
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = '调度日志数据.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  // 关闭
  const handleClose = () => {
    const obj = { path: '/monitor/job' }
    router.push(obj)
  }

  // 字典标签处理函数
  const getJobGroupLabel = (value: string) => {
    const dict = sys_job_group.value?.find((item) => item.value === value)
    return dict?.label || value
  }

  const getJobLogStatusLabel = (value: string) => {
    const dict = sys_common_status.value?.find((item) => item.value === value)
    return dict?.label || value
  }

  const getJobLogStatusTagType = (status: string) => {
    return status === '0' ? 'success' : 'danger'
  }

  // 组件挂载时处理路由参数
  onMounted(() => {
    if (route.query.jobId) {
      // 如果有jobId参数，设置为默认查询条件
      // 这里可以根据需要处理特定任务的日志查询
    }
  })
</script>

<style lang="scss" scoped>
  .job-log-page {
    height: 100%;
  }
</style>
