<template>
  <div class="cache-list-page">
    <ElRow :gutter="20">
      <!-- 缓存列表 -->
      <ElCol :span="8">
        <ElCard shadow="never" class="cache-names-card">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Collection />
              </ElIcon>
              <span>缓存列表</span>
              <ElButton type="primary" size="small" @click="refreshCacheNames" v-ripple>
                <ElIcon><Refresh /></ElIcon>
              </ElButton>
            </div>
          </template>

          <ElTable
            v-loading="loadingNames"
            :data="cacheNames"
            highlight-current-row
            @row-click="handleRowClick"
            size="default"
            :height="tableHeight"
          >
            <ElTableColumn label="序号" width="60" type="index" align="center" />
            <ElTableColumn
              label="缓存名称"
              prop="cacheName"
              show-overflow-tooltip
              :formatter="nameFormatter"
            />
            <ElTableColumn label="备注" prop="remark" show-overflow-tooltip />
            <ElTableColumn label="操作" width="80" align="center">
              <template #default="{ row }">
                <ElButton
                  v-auth="'monitor:cache:remove'"
                  type="danger"
                  size="small"
                  @click="handleClearName(row)"
                  v-ripple
                >
                  清理
                </ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>

      <!-- 键名列表 -->
      <ElCol :span="8">
        <ElCard shadow="never" class="cache-keys-card">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Key />
              </ElIcon>
              <span>键名列表</span>
              <ElButton
                type="primary"
                size="small"
                :disabled="!currentCacheName"
                @click="refreshCacheKeys"
                v-ripple
              >
                <ElIcon><Refresh /></ElIcon>
              </ElButton>
            </div>
          </template>

          <ElTable
            v-loading="loadingKeys"
            :data="cacheKeys"
            highlight-current-row
            @row-click="getCacheValue"
            size="default"
            :height="tableHeight"
          >
            <ElTableColumn label="序号" width="60" type="index" align="center" />
            <ElTableColumn
              label="缓存键名"
              prop="cacheKey"
              show-overflow-tooltip
              :formatter="keyFormatter"
            />
            <ElTableColumn label="操作" width="80" align="center">
              <template #default="{ row }">
                <ElButton
                  v-auth="'monitor:cache:remove'"
                  type="danger"
                  size="small"
                  @click="handleClearKey(row)"
                  v-ripple
                >
                  清理
                </ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElCol>

      <!-- 缓存内容 -->
      <ElCol :span="8">
        <ElCard shadow="never" class="cache-value-card">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Document />
              </ElIcon>
              <span>缓存内容</span>
              <ElButton
                type="primary"
                size="small"
                :disabled="!currentCacheKey"
                @click="refreshCacheValue"
                v-ripple
              >
                <ElIcon><Refresh /></ElIcon>
              </ElButton>
            </div>
          </template>

          <div class="cache-content">
            <ElDescriptions v-if="cacheValue.cacheName" :column="1" border>
              <ElDescriptionsItem label="缓存名称">
                {{ cacheValue.cacheName }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="缓存键名">
                {{ cacheValue.cacheKey }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="缓存内容">
                <ElInput
                  v-model="cacheValue.cacheValue"
                  type="textarea"
                  :rows="20"
                  readonly
                  class="cache-textarea"
                />
              </ElDescriptionsItem>
            </ElDescriptions>
            <ElEmpty v-else description="请选择缓存键查看内容" />
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Collection, Key, Document, Refresh } from '@element-plus/icons-vue'
  import { CacheApi } from '@/api/monitor/cache'
  import type { CacheKeyValue } from '@/types/monitor/cache'

  defineOptions({ name: 'MonitorCacheList' })

  // 响应式数据
  const cacheNames = ref<Array<{ cacheName: string; remark: string }>>([])
  const cacheKeys = ref<Array<{ cacheKey: string }>>([])
  const cacheValue = ref<CacheKeyValue>({})

  // 加载状态
  const loadingNames = ref(false)
  const loadingKeys = ref(false)
  const loadingValue = ref(false)

  // 当前选中项
  const currentCacheName = ref('')
  const currentCacheKey = ref('')

  // 表格高度
  const tableHeight = ref(500)

  onMounted(() => {
    getCacheNames()
    calculateTableHeight()
    window.addEventListener('resize', calculateTableHeight)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', calculateTableHeight)
  })

  /** 计算表格高度 */
  function calculateTableHeight() {
    // 动态计算表格高度，适应页面大小
    const windowHeight = window.innerHeight
    tableHeight.value = Math.max(400, windowHeight - 300)
  }

  /** 获取缓存名称列表 */
  async function getCacheNames() {
    loadingNames.value = true
    try {
      const response = await CacheApi.listCacheName()
      const namesData = (response.data as any)?.data || response.data
      const names = namesData || []
      // 直接使用API返回的对象结构，包含cacheName和remark
      cacheNames.value = names.map((item: any) => ({
        cacheName: item.cacheName || item,
        remark: item.remark || getCacheRemark(item.cacheName || item)
      }))
    } catch (error) {
      console.error('获取缓存名称列表失败:', error)
      ElMessage.error('获取缓存名称列表失败')
    } finally {
      loadingNames.value = false
    }
  }

  /** 获取缓存键列表 */
  async function getCacheKeys(cacheName: string) {
    if (!cacheName) return

    loadingKeys.value = true
    currentCacheName.value = cacheName
    try {
      const response = await CacheApi.listCacheKey(cacheName)
      const keysData = (response.data as any)?.data || response.data
      const keys = keysData || []
      cacheKeys.value = keys.map((key) => ({ cacheKey: key }))
      // 清空当前选中的缓存值
      cacheValue.value = {}
      currentCacheKey.value = ''
    } catch (error) {
      console.error('获取缓存键列表失败:', error)
      ElMessage.error('获取缓存键列表失败')
    } finally {
      loadingKeys.value = false
    }
  }

  /** 获取缓存值 */
  async function getCacheValue(row: { cacheKey: string }) {
    if (!currentCacheName.value || !row.cacheKey) return

    loadingValue.value = true
    currentCacheKey.value = row.cacheKey
    try {
      const response = await CacheApi.getCacheValue(currentCacheName.value, row.cacheKey)
      const valueData = (response.data as any)?.data || response.data
      cacheValue.value = valueData || {}
    } catch (error) {
      console.error('获取缓存值失败:', error)
      ElMessage.error('获取缓存值失败')
    } finally {
      loadingValue.value = false
    }
  }

  /** 获取缓存备注 */
  function getCacheRemark(name: string): string {
    const remarks: Record<string, string> = {
      'sys-config': '参数管理',
      'sys-dict': '数据字典',
      captcha_codes: '验证码',
      login_tokens: '登录令牌',
      'sys-user': '用户信息',
      'sys-dept': '部门信息',
      'sys-role': '角色信息',
      'sys-menu': '菜单信息'
    }
    return remarks[name] || '系统缓存'
  }

  /** 缓存名称格式化 */
  function nameFormatter(row: any, column: any, cellValue: string) {
    return cellValue || '-'
  }

  /** 缓存键名格式化 */
  function keyFormatter(row: any, column: any, cellValue: string) {
    return cellValue || '-'
  }

  /** 处理行点击 */
  function handleRowClick(row: { cacheName: string }) {
    getCacheKeys(row.cacheName)
  }

  /** 清理指定名称缓存 */
  async function handleClearName(row: { cacheName: string }) {
    try {
      await ElMessageBox.confirm(`确认要清理缓存名称"${row.cacheName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await CacheApi.clearCacheName(row.cacheName)
      ElMessage.success('清理成功')

      // 刷新相关数据
      await getCacheNames()
      if (currentCacheName.value === row.cacheName) {
        cacheKeys.value = []
        cacheValue.value = {}
        currentCacheName.value = ''
        currentCacheKey.value = ''
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('清理缓存失败:', error)
        ElMessage.error('清理缓存失败')
      }
    }
  }

  /** 清理指定键名缓存 */
  async function handleClearKey(row: { cacheKey: string }) {
    try {
      await ElMessageBox.confirm(`确认要清理缓存键"${row.cacheKey}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await CacheApi.clearCacheKey(row.cacheKey)
      ElMessage.success('清理成功')

      // 刷新键列表
      await getCacheKeys(currentCacheName.value)
      if (currentCacheKey.value === row.cacheKey) {
        cacheValue.value = {}
        currentCacheKey.value = ''
      }
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('清理缓存键失败:', error)
        ElMessage.error('清理缓存键失败')
      }
    }
  }

  /** 刷新缓存名称 */
  function refreshCacheNames() {
    getCacheNames()
  }

  /** 刷新缓存键 */
  function refreshCacheKeys() {
    if (currentCacheName.value) {
      getCacheKeys(currentCacheName.value)
    }
  }

  /** 刷新缓存值 */
  function refreshCacheValue() {
    if (currentCacheName.value && currentCacheKey.value) {
      getCacheValue({ cacheKey: currentCacheKey.value })
    }
  }
</script>

<style lang="scss" scoped>
  .cache-list-page {
    padding: 20px;

    .cache-names-card,
    .cache-keys-card,
    .cache-value-card {
      height: calc(100vh - 140px);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-icon {
        margin-right: 8px;
      }
    }

    .cache-content {
      height: calc(100% - 60px);
      overflow: auto;

      .cache-textarea {
        :deep(.el-textarea__inner) {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    :deep(.el-card__body) {
      height: calc(100% - 60px);
      overflow: hidden;
    }
  }
</style>
