<template>
  <div class="cache-page">
    <ElRow :gutter="20">
      <!-- 基本信息 -->
      <ElCol :span="24" class="cache-info-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <Monitor />
              </ElIcon>
              <span>基本信息</span>
            </div>
          </template>
          <div class="info-table">
            <table>
              <tbody>
                <tr>
                  <td>Redis版本</td>
                  <td>{{ cache.info?.redis_version || '-' }}</td>
                  <td>运行模式</td>
                  <td>{{ cache.info?.redis_mode === 'standalone' ? '单机' : '集群' }}</td>
                  <td>端口</td>
                  <td>{{ cache.info?.tcp_port || '-' }}</td>
                  <td>客户端数</td>
                  <td>{{ cache.info?.connected_clients || '-' }}</td>
                </tr>
                <tr>
                  <td>运行时间(天)</td>
                  <td>{{ cache.info?.uptime_in_days || '-' }}</td>
                  <td>使用内存</td>
                  <td>{{ cache.info?.used_memory_human || '-' }}</td>
                  <td>使用CPU</td>
                  <td>{{ getCpuUsage() }}</td>
                  <td>内存配置</td>
                  <td>{{ cache.info?.maxmemory_human || '-' }}</td>
                </tr>
                <tr>
                  <td>AOF是否开启</td>
                  <td>{{ cache.info?.aof_enabled === '0' ? '否' : '是' }}</td>
                  <td>RDB是否成功</td>
                  <td>{{ cache.info?.rdb_last_bgsave_status || '-' }}</td>
                  <td>Key数量</td>
                  <td>{{ cache.dbSize || 0 }}</td>
                  <td>网络入口/出口</td>
                  <td>{{ getNetworkRate() }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </ElCard>
      </ElCol>

      <!-- 命令统计和内存信息 -->
      <ElCol :span="12" class="charts-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <PieChart />
              </ElIcon>
              <span>命令统计</span>
            </div>
          </template>
          <div ref="commandStatsRef" class="chart-container"></div>
        </ElCard>
      </ElCol>

      <ElCol :span="12" class="charts-card">
        <ElCard shadow="never">
          <template #header>
            <div class="card-header">
              <ElIcon class="header-icon">
                <PieChart />
              </ElIcon>
              <span>内存信息</span>
            </div>
          </template>
          <div ref="memoryInfoRef" class="chart-container"></div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 缓存列表 -->
    <ElCard class="art-table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <ElIcon class="header-icon">
            <List />
          </ElIcon>
          <span>缓存列表</span>
          <div class="header-actions">
            <ElButton
              v-auth="'monitor:cache:remove'"
              type="danger"
              @click="handleClearAll"
              v-ripple
            >
              清理全部
            </ElButton>
            <ElButton type="primary" @click="handleRefresh" v-ripple> 刷新 </ElButton>
          </div>
        </div>
      </template>

      <ElTable :data="cacheNames" border size="default" table-layout="auto" row-key="name">
        <ElTableColumn label="序号" type="index" width="80" align="center" />
        <ElTableColumn label="缓存名称" prop="name" show-overflow-tooltip />
        <ElTableColumn label="备注" prop="remark" show-overflow-tooltip />
        <ElTableColumn label="操作" align="center" width="200">
          <template #default="{ row }">
            <ElButton
              v-auth="'monitor:cache:query'"
              type="primary"
              size="small"
              @click="handleViewKeys(row.name)"
              v-ripple
            >
              查看
            </ElButton>
            <ElButton
              v-auth="'monitor:cache:remove'"
              type="danger"
              size="small"
              @click="handleClearName(row.name)"
              v-ripple
            >
              清理
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>

    <!-- 缓存键列表对话框 -->
    <ElDialog
      v-model="keyListVisible"
      :title="`缓存键列表 - ${currentCacheName}`"
      width="800px"
      append-to-body
    >
      <ElTable :data="cacheKeys" border size="default" max-height="400">
        <ElTableColumn label="序号" type="index" width="80" align="center" />
        <ElTableColumn label="缓存键名" prop="key" show-overflow-tooltip />
        <ElTableColumn label="操作" align="center" width="150">
          <template #default="{ row }">
            <ElButton type="primary" size="small" @click="handleViewValue(row.key)" v-ripple>
              查看
            </ElButton>
            <ElButton type="danger" size="small" @click="handleClearKey(row.key)" v-ripple>
              清理
            </ElButton>
          </template>
        </ElTableColumn>
      </ElTable>
    </ElDialog>

    <!-- 缓存值查看对话框 -->
    <ElDialog v-model="valueViewVisible" title="缓存值详情" width="600px" append-to-body>
      <ElDescriptions :column="1" border>
        <ElDescriptionsItem label="缓存名称">
          {{ cacheDetail.cacheName }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="缓存键名">
          {{ cacheDetail.cacheKey }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="缓存值">
          <ElInput v-model="cacheDetail.cacheValue" type="textarea" :rows="6" readonly />
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Monitor, PieChart, List } from '@element-plus/icons-vue'
  import * as echarts from 'echarts'
  import { CacheApi } from '@/api/monitor/cache'
  import type { Cache, CacheKeyValue } from '@/types/monitor/cache'

  defineOptions({ name: 'MonitorCache' })

  // 响应式数据
  const cache = ref<Cache>({})
  const cacheNames = ref<Array<{ name: string; remark: string }>>([])
  const cacheKeys = ref<Array<{ key: string }>>([])
  const cacheDetail = ref<CacheKeyValue>({})

  // 对话框显示状态
  const keyListVisible = ref(false)
  const valueViewVisible = ref(false)
  const currentCacheName = ref('')

  // 图表引用
  const commandStatsRef = ref<HTMLElement>()
  const memoryInfoRef = ref<HTMLElement>()
  let commandStatsChart: echarts.ECharts | null = null
  let memoryInfoChart: echarts.ECharts | null = null

  onMounted(() => {
    getCache()
    getCacheNames()
    initCharts()
  })

  onBeforeUnmount(() => {
    if (commandStatsChart) {
      commandStatsChart.dispose()
    }
    if (memoryInfoChart) {
      memoryInfoChart.dispose()
    }
  })

  /** 获取缓存监控数据 */
  async function getCache() {
    try {
      const response = await CacheApi.getCache()
      const cacheData = (response.data as any)?.data || response.data
      cache.value = cacheData || {}
      updateCharts()
    } catch (error) {
      console.error('获取缓存信息失败:', error)
      ElMessage.error('获取缓存信息失败')
    }
  }

  /** 获取缓存名称列表 */
  async function getCacheNames() {
    try {
      const response = await CacheApi.listCacheName()
      const namesData = (response.data as any)?.data || response.data
      const names = namesData || []
      // 直接使用API返回的对象结构，包含cacheName和remark
      cacheNames.value = names.map((item: any) => ({
        name: item.cacheName || item,
        remark: item.remark || getCacheRemark(item.cacheName || item)
      }))
    } catch (error) {
      console.error('获取缓存名称列表失败:', error)
      ElMessage.error('获取缓存名称列表失败')
    }
  }

  /** 获取缓存备注 */
  function getCacheRemark(name: string): string {
    const remarks: Record<string, string> = {
      'sys-config': '参数管理',
      'sys-dict': '数据字典',
      captcha_codes: '验证码',
      login_tokens: '登录令牌',
      'sys-user': '用户信息',
      'sys-dept': '部门信息',
      'sys-role': '角色信息',
      'sys-menu': '菜单信息'
    }
    return remarks[name] || '系统缓存'
  }

  /** 初始化图表 */
  function initCharts() {
    nextTick(() => {
      if (commandStatsRef.value) {
        commandStatsChart = echarts.init(commandStatsRef.value)
      }
      if (memoryInfoRef.value) {
        memoryInfoChart = echarts.init(memoryInfoRef.value)
      }
      updateCharts()
    })
  }

  /** 更新图表数据 */
  function updateCharts() {
    if (commandStatsChart && cache.value.commandStats) {
      const commandData = cache.value.commandStats.map((item) => ({
        name: item.name,
        value: item.value
      }))

      commandStatsChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: '命令',
            type: 'pie',
            radius: '70%',
            data: commandData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      })
    }

    if (memoryInfoChart && cache.value.info) {
      const used = parseFloat(cache.value.info.used_memory_human?.replace(/[^\d.]/g, '') || '0')
      const total = parseFloat(cache.value.info.maxmemory_human?.replace(/[^\d.]/g, '') || '100')
      const free = Math.max(0, total - used)

      memoryInfoChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}MB ({d}%)'
        },
        series: [
          {
            name: '内存',
            type: 'pie',
            radius: '70%',
            data: [
              { name: '已用内存', value: used },
              { name: '剩余内存', value: free }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      })
    }
  }

  /** 获取CPU使用率 */
  function getCpuUsage(): string {
    if (!cache.value.info?.used_cpu_user_children) return '-'
    return parseFloat(cache.value.info.used_cpu_user_children).toFixed(2)
  }

  /** 获取网络速率 */
  function getNetworkRate(): string {
    const input = cache.value.info?.instantaneous_input_kbps || '0'
    const output = cache.value.info?.instantaneous_output_kbps || '0'
    return `${input}kps/${output}kps`
  }

  /** 查看缓存键 */
  async function handleViewKeys(cacheName: string) {
    try {
      currentCacheName.value = cacheName
      const response = await CacheApi.listCacheKey(cacheName)
      const keysData = (response.data as any)?.data || response.data
      const keys = keysData || []
      cacheKeys.value = keys.map((key) => ({ key }))
      keyListVisible.value = true
    } catch (error) {
      console.error('获取缓存键列表失败:', error)
      ElMessage.error('获取缓存键列表失败')
    }
  }

  /** 查看缓存值 */
  async function handleViewValue(cacheKey: string) {
    try {
      const response = await CacheApi.getCacheValue(currentCacheName.value, cacheKey)
      const valueData = (response.data as any)?.data || response.data
      cacheDetail.value = valueData || {}
      valueViewVisible.value = true
    } catch (error) {
      console.error('获取缓存值失败:', error)
      ElMessage.error('获取缓存值失败')
    }
  }

  /** 清理指定名称缓存 */
  async function handleClearName(cacheName: string) {
    try {
      await ElMessageBox.confirm(`确认要清理缓存名称"${cacheName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await CacheApi.clearCacheName(cacheName)
      ElMessage.success('清理成功')
      await getCache()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('清理缓存失败:', error)
        ElMessage.error('清理缓存失败')
      }
    }
  }

  /** 清理指定键名缓存 */
  async function handleClearKey(cacheKey: string) {
    try {
      await ElMessageBox.confirm(`确认要清理缓存键"${cacheKey}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await CacheApi.clearCacheKey(cacheKey)
      ElMessage.success('清理成功')
      // 重新获取键列表
      await handleViewKeys(currentCacheName.value)
      await getCache()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('清理缓存键失败:', error)
        ElMessage.error('清理缓存键失败')
      }
    }
  }

  /** 清理全部缓存 */
  async function handleClearAll() {
    try {
      await ElMessageBox.confirm('确认要清理全部缓存吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await CacheApi.clearCacheAll()
      ElMessage.success('清理成功')
      await getCache()
      await getCacheNames()
    } catch (error: any) {
      if (error !== 'cancel') {
        console.error('清理全部缓存失败:', error)
        ElMessage.error('清理全部缓存失败')
      }
    }
  }

  /** 刷新数据 */
  function handleRefresh() {
    getCache()
    getCacheNames()
  }
</script>

<style lang="scss" scoped>
  .cache-page {
    padding: 20px;

    .cache-info-card,
    .charts-card {
      margin-bottom: 20px;
    }

    .art-table-card {
      margin-top: 20px;
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-icon {
        margin-right: 8px;
      }

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .info-table {
      table {
        width: 100%;
        border-collapse: collapse;

        td {
          padding: 12px;
          border: 1px solid var(--art-border-color);
          text-align: center;

          &:nth-child(odd) {
            background-color: var(--art-gray-50);
            font-weight: 500;
          }
        }
      }
    }

    .chart-container {
      height: 350px;
    }
  }
</style>
