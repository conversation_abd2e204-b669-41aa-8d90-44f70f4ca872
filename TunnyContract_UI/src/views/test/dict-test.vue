<template>
  <div class="dict-test-page">
    <ElCard>
      <template #header>
        <div class="card-header">
          <span>字典系统测试页面</span>
        </div>
      </template>

      <div class="test-section">
        <h3>1. useDict Composable 测试</h3>
        <ElButton @click="testUseDict" type="primary">测试 useDict</ElButton>
        <div v-if="dictData.sys_normal_disable" class="dict-result">
          <h4>sys_normal_disable 字典数据：</h4>
          <pre>{{ JSON.stringify(dictData.sys_normal_disable, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>2. DictTag 组件测试</h3>
        <div class="dict-tag-demo">
          <p>状态字典标签测试：</p>
          <DictTag :options="dictData.sys_normal_disable || []" value="0" />
          <DictTag :options="dictData.sys_normal_disable || []" value="1" />
        </div>
      </div>

      <div class="test-section">
        <h3>3. 多字典获取测试</h3>
        <ElButton @click="testSingleDict" type="success">测试单个字典获取</ElButton>
        <div v-if="globalDictData.sys_user_sex" class="dict-result">
          <h4>sys_user_sex 字典数据：</h4>
          <pre>{{ JSON.stringify(globalDictData.sys_user_sex, null, 2) }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>4. 字典缓存测试</h3>
        <ElButton @click="testDictCache" type="warning">测试缓存机制</ElButton>
        <ElButton @click="clearDictCache" type="danger">清空缓存</ElButton>
        <div v-if="cacheTestResult" class="dict-result">
          <h4>缓存测试结果：</h4>
          <pre>{{ cacheTestResult }}</pre>
        </div>
      </div>

      <div class="test-section">
        <h3>5. 多字典批量获取测试</h3>
        <ElButton @click="testMultipleDict" type="info">测试多字典获取</ElButton>
        <div v-if="multipleDictData" class="dict-result">
          <h4>多字典数据：</h4>
          <pre>{{ JSON.stringify(multipleDictData, null, 2) }}</pre>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, onMounted } from 'vue'
  import { useDict, refreshDict } from '@/composables/useDict'
  import { useDictStore } from '@/store/modules/dict'
  import DictTag from '@/components/DictTag/index.vue'
  import type { DictOption } from '@/types/system/dict'

  defineOptions({ name: 'DictTest' })

  const dictStore = useDictStore()

  // 测试数据
  const dictData = reactive<Record<string, DictOption[]>>({})
  const globalDictData = reactive<Record<string, DictOption[]>>({})
  const multipleDictData = ref<Record<string, DictOption[]> | null>(null)
  const cacheTestResult = ref<string>('')

  // 测试 useDict composable
  const testUseDict = () => {
    console.log('开始测试 useDict composable...')

    // 使用 composable 方式
    const { sys_normal_disable } = useDict('sys_normal_disable')
    console.log('useDict 返回的响应式对象:', sys_normal_disable)

    // 监听数据变化
    watch(
      sys_normal_disable,
      (newVal) => {
        console.log('watch 监听到数据变化:', newVal)
        if (newVal && newVal.length > 0) {
          dictData.sys_normal_disable = newVal
          console.log('useDict 获取到字典数据:', newVal)
        } else {
          console.log('字典数据为空或未定义:', newVal)
        }
      },
      { immediate: true }
    )
  }

  // 测试单个字典获取
  const testSingleDict = () => {
    console.log('开始测试单个字典获取...')

    const { sys_user_sex } = useDict('sys_user_sex')

    watch(
      sys_user_sex,
      (newVal) => {
        if (newVal && newVal.length > 0) {
          globalDictData.sys_user_sex = newVal
          console.log('获取到 sys_user_sex 字典数据:', newVal)
        }
      },
      { immediate: true }
    )
  }

  // 测试字典缓存
  const testDictCache = () => {
    console.log('开始测试字典缓存...')

    // 第一次获取（从API）
    const startTime1 = Date.now()
    const { sys_normal_disable: dict1 } = useDict('sys_normal_disable')

    watch(
      dict1,
      () => {
        const endTime1 = Date.now()
        const time1 = endTime1 - startTime1

        // 第二次获取（从缓存）
        setTimeout(() => {
          const startTime2 = Date.now()
          const { sys_normal_disable: dict2 } = useDict('sys_normal_disable')

          watch(
            dict2,
            () => {
              const endTime2 = Date.now()
              const time2 = endTime2 - startTime2

              cacheTestResult.value = `第一次获取耗时: ${time1}ms (从API)\n第二次获取耗时: ${time2}ms (从缓存)`
            },
            { immediate: true }
          )
        }, 100)
      },
      { immediate: true }
    )
  }

  // 清空字典缓存
  const clearDictCache = () => {
    refreshDict()
    console.log('字典缓存已清空')
    cacheTestResult.value = '字典缓存已清空'
  }

  // 测试多字典同时获取
  const testMultipleDict = () => {
    console.log('开始测试多字典同时获取...')

    const { sys_normal_disable, sys_user_sex, sys_show_hide } = useDict(
      'sys_normal_disable',
      'sys_user_sex',
      'sys_show_hide'
    )

    // 等待所有字典数据加载完成
    const checkAllLoaded = () => {
      if (
        sys_normal_disable.value?.length > 0 &&
        sys_user_sex.value?.length > 0 &&
        sys_show_hide.value?.length > 0
      ) {
        multipleDictData.value = {
          sys_normal_disable: sys_normal_disable.value,
          sys_user_sex: sys_user_sex.value,
          sys_show_hide: sys_show_hide.value
        }
        console.log('多字典数据获取完成:', multipleDictData.value)
      }
    }

    watch([sys_normal_disable, sys_user_sex, sys_show_hide], checkAllLoaded, { immediate: true })
  }

  // 页面加载时自动测试基本功能
  onMounted(() => {
    console.log('字典测试页面已加载')
    console.log('字典Store状态:', dictStore.$state)
  })
</script>

<style lang="scss" scoped>
  .dict-test-page {
    padding: 20px;

    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      h3 {
        margin-top: 0;
        color: #303133;
      }

      .dict-result {
        margin-top: 15px;
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;

        h4 {
          margin-top: 0;
          color: #606266;
        }

        pre {
          background-color: #fff;
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto;
        }
      }

      .dict-tag-demo {
        margin-top: 10px;

        p {
          margin-bottom: 10px;
        }
      }
    }
  }
</style>
