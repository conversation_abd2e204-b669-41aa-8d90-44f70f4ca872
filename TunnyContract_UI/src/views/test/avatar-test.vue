<template>
  <div class="avatar-test-page">
    <h2>头像组件测试页面</h2>

    <div class="test-section">
      <h3>1. 正常头像显示</h3>
      <AvatarImage :src="'@imgs/user/profile.jpg'" class="test-avatar" alt="正常头像" />
      <p>使用正常的头像URL</p>
    </div>

    <div class="test-section">
      <h3>2. 错误URL回退到默认头像</h3>
      <AvatarImage
        :src="'https://invalid-url.com/invalid.jpg'"
        class="test-avatar"
        alt="错误URL头像"
      />
      <p>使用无效的URL，应该回退到profile.jpg</p>
    </div>

    <div class="test-section">
      <h3>3. 空值回退到默认头像</h3>
      <AvatarImage :src="''" class="test-avatar" alt="空值头像" />
      <p>传入空字符串，应该直接显示profile.jpg</p>
    </div>

    <div class="test-section">
      <h3>4. undefined/null回退到默认头像</h3>
      <AvatarImage :src="undefined" class="test-avatar" alt="undefined头像" />
      <p>传入undefined，应该直接显示profile.jpg</p>
    </div>

    <div class="test-section">
      <h3>5. 自定义默认头像</h3>
      <AvatarImage
        :src="'https://invalid-url.com/invalid2.jpg'"
        :default-src="'@imgs/user/avatar.webp'"
        class="test-avatar"
        alt="自定义默认头像"
      />
      <p>使用自定义默认头像，回退到avatar.webp</p>
    </div>

    <div class="test-section">
      <h3>6. 模拟用户头像显示场景</h3>
      <div class="user-scenario">
        <div class="scenario" v-for="(user, index) in testUsers" :key="index">
          <AvatarImage :src="user.avatar" class="user-avatar" :alt="`${user.name}的头像`" />
          <p>{{ user.name }}</p>
          <small>{{ user.description }}</small>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AvatarImage from '@/components/AvatarImage/index.vue'

  defineOptions({ name: 'AvatarTest' })

  const testUsers = [
    {
      name: '张三',
      avatar: '@imgs/user/profile.jpg',
      description: '正常头像'
    },
    {
      name: '李四',
      avatar: 'https://invalid-url.com/user1.jpg',
      description: '无效URL，回退到默认'
    },
    {
      name: '王五',
      avatar: '',
      description: '空字符串，使用默认'
    },
    {
      name: '赵六',
      avatar: null,
      description: 'null值，使用默认'
    }
  ]
</script>

<style scoped>
  .avatar-test-page {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid var(--art-border-color);
    border-radius: 8px;
    background: var(--art-main-bg-color);
  }

  .test-section h3 {
    margin-bottom: 15px;
    color: var(--art-text-color);
  }

  .test-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--art-border-color);
    margin-bottom: 10px;
  }

  .user-scenario {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }

  .scenario {
    text-align: center;
    padding: 15px;
    border: 1px solid var(--art-border-dashed-color);
    border-radius: 6px;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
  }

  .scenario p {
    margin: 8px 0 4px 0;
    font-weight: 500;
  }

  .scenario small {
    color: var(--art-text-gray-500);
    font-size: 12px;
  }
</style>
