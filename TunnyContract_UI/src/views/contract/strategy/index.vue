<template>
  <div class="contract-strategy-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'contract:strategy:add'" type="primary" @click="handleAdd" v-ripple>
            新增策略
          </ElButton>
          <ElButton
            v-auth="'contract:strategy:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'contract:strategy:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="id"
        :loading="loading"
        :columns="columns"
        :data="strategyList"
        :stripe="true"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
        <template #strategyName="{ row }">
          <span
            class="link-type strategy-name-link"
            @click="handleStrategyEdit(row)"
            :title="'点击编辑策略：' + row.strategyName"
          >
            {{ row.strategyName }}
          </span>
        </template>

        <template #operation="{ row }">
          <el-tooltip content="修改" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:strategy:edit'"
              link
              type="primary"
              @click="handleUpdate(row)"
            >
              编辑
            </el-button>
          </el-tooltip>
          <el-tooltip content="复制" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:strategy:add'"
              link
              type="success"
              @click="handleCopy(row)"
            >
              复制
            </el-button>
          </el-tooltip>
          <el-tooltip
            :content="row.strategyStatus === '1' ? '取消发布' : '发布策略'"
            placement="top"
          >
            <el-button
              v-ripple
              v-auth="'contract:strategy:edit'"
              link
              :type="row.strategyStatus === '1' ? 'warning' : 'success'"
              @click="handleToggleStatus(row)"
            >
              {{ row.strategyStatus === '1' ? '取消发布' : '发布' }}
            </el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:strategy:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>

      <!-- 添加或修改策略对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="600px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="100px">
          <ElFormItem label="策略名称" prop="strategyName">
            <ElInput v-model="form.strategyName" placeholder="请输入策略名称" />
          </ElFormItem>
          <ElFormItem label="策略描述" prop="strategyDesc">
            <ElInput
              v-model="form.strategyDesc"
              type="textarea"
              placeholder="请输入策略描述"
              :rows="3"
            />
          </ElFormItem>
          <ElFormItem label="合同分类" prop="categoryId">
            <ElSelect v-model="form.categoryId" placeholder="请选择合同分类" style="width: 100%">
              <ElOption
                v-for="category in categoryOptions"
                :key="category.id"
                :label="category.categoryName"
                :value="category.id!"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="审查立场" prop="reviewPosition">
            <ElRadioGroup v-model="form.reviewPosition">
              <ElRadio value="1">甲方</ElRadio>
              <ElRadio value="2">乙方</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="策略状态" prop="strategyStatus">
            <ElRadioGroup v-model="form.strategyStatus">
              <ElRadio value="0">草稿</ElRadio>
              <ElRadio value="1">已发布</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
          <ElFormItem label="备注" prop="remark">
            <ElInput v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
          </ElFormItem>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>

      <!-- 复制策略对话框 -->
      <CopyStrategyDialog
        v-model:visible="copyDialogVisible"
        :source-strategy="sourceStrategy"
        @success="handleCopySuccess"
      />
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, ref, reactive, computed, h, onMounted } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useTable } from '@/composables/useTable'
  import { ContractReviewStrategyApi } from '@/api/contract/strategy'
  import { ContractCategoryApi } from '@/api/contract/category'
  import type {
    ContractReviewStrategy,
    ContractReviewStrategyForm
  } from '@/types/contract/strategy'
  import type { ContractCategory } from '@/types/contract/category'
  import type { FormInstance, FormRules } from 'element-plus'

  // 组件导入
  import CopyStrategyDialog from './components/CopyStrategyDialog.vue'

  defineOptions({ name: 'ContractStrategy' })

  // Vue Router实例
  const router = useRouter()

  const dialogVisible = ref(false)
  const copyDialogVisible = ref(false)
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    strategyName: '',
    categoryId: undefined,
    reviewPosition: '',
    strategyStatus: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)
  const categoryOptions = ref<ContractCategory[]>([])
  const sourceStrategy = ref<ContractReviewStrategy>({} as ContractReviewStrategy)

  // 表单数据
  const form = reactive<ContractReviewStrategyForm>({
    id: undefined,
    strategyName: '',
    strategyDesc: '',
    categoryId: undefined,
    reviewPosition: '1',
    strategyStatus: '0',
    remark: ''
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    strategyName: [{ required: true, message: '策略名称不能为空', trigger: 'blur' }],
    categoryId: [{ required: true, message: '请选择合同分类', trigger: 'change' }],
    reviewPosition: [{ required: true, message: '请选择审查立场', trigger: 'change' }]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改审查策略' : '新增审查策略'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '策略名称',
      key: 'strategyName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '合同分类',
      key: 'categoryId',
      type: 'select',
      props: { clearable: true },
      options: categoryOptions.value.map((item) => ({
        label: item.categoryName,
        value: item.id!
      }))
    },
    {
      label: '审查立场',
      key: 'reviewPosition',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '甲方', value: '1' },
        { label: '乙方', value: '2' }
      ]
    },
    {
      label: '策略状态',
      key: 'strategyStatus',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '草稿', value: '0' },
        { label: '已发布', value: '1' }
      ]
    }
  ])

  // 使用useTable组合式函数
  const {
    data: strategyList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<ContractReviewStrategy>({
    core: {
      apiFn: (params: any) => ContractReviewStrategyApi.getStrategyList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        strategyName: '',
        categoryId: undefined,
        reviewPosition: '',
        strategyStatus: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          type: 'index',
          label: '序号',
          width: 80,
          checked: true,
          disabled: true
        },
        {
          prop: 'strategyName',
          label: '策略名称',
          minWidth: 200,
          checked: true,
          useSlot: true,
          slotName: 'strategyName'
        },
        {
          prop: 'categoryName',
          label: '关联分类',
          width: 120,
          checked: true,
          formatter: (row: ContractReviewStrategy) => {
            return h(ElTag, { type: 'info', size: 'small' }, () => row.categoryName || '未知分类')
          }
        },
        {
          prop: 'reviewPosition',
          label: '审查立场',
          width: 100,
          checked: true,
          formatter: (row: ContractReviewStrategy) => {
            return h(
              ElTag,
              {
                type: row.reviewPosition === '1' ? 'primary' : 'success',
                size: 'small'
              },
              () => (row.reviewPosition === '1' ? '甲方' : '乙方')
            )
          }
        },
        {
          prop: 'strategyStatus',
          label: '策略状态',
          width: 100,
          checked: true,
          formatter: (row: ContractReviewStrategy) => {
            return h(
              ElTag,
              {
                type: row.strategyStatus === '1' ? 'success' : 'warning',
                size: 'small'
              },
              () => (row.strategyStatus === '1' ? '已发布' : '草稿')
            )
          }
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
          checked: true,
          formatter: (row: ContractReviewStrategy) => row.updateTime || '--'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 250,
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // useTable会自动在mounted时加载数据

  const handleRefresh = () => {
    console.log('策略管理刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    refreshData()
  }

  // 多选框选中数据
  const handleSelectionChange = (selection: ContractReviewStrategy[]) => {
    ids.value = selection.map((item) => item.id!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 获取分类列表
  const getCategoryList = async () => {
    try {
      console.log('开始获取合同分类列表...')
      const response = await ContractCategoryApi.getCategoryList({
        pageNum: 1,
        pageSize: 100,
        status: '0' // 只获取启用状态的分类
      })
      console.log('合同分类API响应:', response)

      // 使用统一的异常处理机制，依赖HTTP拦截器
      categoryOptions.value = response.data?.rows || []
      console.log('合同分类数据设置完成:', categoryOptions.value)
    } catch (error) {
      console.error('获取分类列表失败:', error)
      ElMessage.error('获取合同分类列表失败')
    }
  }

  // 新增按钮操作
  const handleAdd = () => {
    resetForm()
    dialogVisible.value = true
    isEdit.value = false
  }

  // 跳转到策略维护页面
  const handleStrategyEdit = (row: ContractReviewStrategy) => {
    if (!row.id) {
      ElMessage.error('策略ID不存在')
      return
    }

    router.push({
      name: 'ContractStrategyEditDetail',
      params: { strategyId: row.id.toString() }
    })
  }

  // 修改按钮操作
  const handleUpdate = async (row?: ContractReviewStrategy) => {
    resetForm()
    const strategyId = row?.id || ids.value[0]

    try {
      const response = await ContractReviewStrategyApi.getStrategy(strategyId)
      const strategyData = (response.data as any)?.data || response.data
      Object.assign(form, strategyData)
      dialogVisible.value = true
      isEdit.value = true
    } catch (_error) {
      console.error('获取策略信息失败:', _error)
      ElMessage.error('获取策略信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row?: ContractReviewStrategy) => {
    const strategyIds = row?.id ? [row.id] : ids.value

    try {
      await ElMessageBox.confirm(`是否确认删除选中的${strategyIds.length}个策略？`, '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ContractReviewStrategyApi.delStrategy(
        strategyIds.length === 1 ? strategyIds[0] : strategyIds
      )
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 切换策略状态
  const handleToggleStatus = async (row: ContractReviewStrategy) => {
    const action = row.strategyStatus === '1' ? '取消发布' : '发布'

    try {
      await ElMessageBox.confirm(`确认${action}策略"${row.strategyName}"吗？`, '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      if (row.strategyStatus === '1') {
        await ContractReviewStrategyApi.unpublishStrategy(row.id!)
      } else {
        await ContractReviewStrategyApi.publishStrategy(row.id!)
      }

      ElMessage.success(`${action}成功`)
      refreshData()
    } catch (error) {
      if (error !== 'cancel') {
        console.error(`${action}失败:`, error)
        ElMessage.error(`${action}失败`)
      }
    }
  }

  // 复制策略
  const handleCopy = (row: ContractReviewStrategy) => {
    sourceStrategy.value = row
    copyDialogVisible.value = true
  }

  // 复制成功
  const handleCopySuccess = () => {
    copyDialogVisible.value = false
    refreshData()
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.id) {
            await ContractReviewStrategyApi.updateStrategy(form)
            ElMessage.success('修改成功')
          } else {
            await ContractReviewStrategyApi.addStrategy(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          refreshData()
        } catch {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.id = undefined
    form.strategyName = ''
    form.strategyDesc = ''
    form.categoryId = undefined
    form.reviewPosition = '1'
    form.strategyStatus = '0'
    form.remark = ''

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }

  // 页面初始化
  onMounted(() => {
    getCategoryList()
  })
</script>

<style lang="scss" scoped>
  .contract-strategy-page {
    .dialog-footer {
      text-align: right;
    }

    .art-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }

    .link-type {
      color: var(--el-color-primary);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    .strategy-name-link {
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        color: var(--el-color-primary-dark-2);
        text-decoration: underline;
        transform: translateX(2px);
      }

      &:active {
        color: var(--el-color-primary-light-3);
      }
    }
  }
</style>
