<template>
  <div class="contract-strategy-edit-page">
    <!-- 页面头部 -->
    <ElCard class="page-header" shadow="never">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">
            <ElIcon class="title-icon"><Document /></ElIcon>
            策略维护: {{ strategyInfo.strategyName || (loading ? '加载中...' : '未知策略') }}
          </h2>
          <!-- 面包屑导航已隐藏 -->
        </div>
        <div class="header-right">
          <ElButton @click="handleGoBack" :icon="ArrowLeft">返回列表</ElButton>
        </div>
      </div>
    </ElCard>

    <!-- 审查立场显示 -->
    <ElCard class="position-selector" shadow="never">
      <div class="position-content">
        <span class="position-label">审查立场:</span>
        <div class="position-display">
          <ElTag :type="currentPosition === '1' ? 'primary' : 'success'" size="large" effect="dark">
            {{ currentPosition === '1' ? '甲方' : '乙方' }}
          </ElTag>
        </div>
        <div class="position-description">
          <span class="position-desc">
            {{ strategyInfo.strategyDesc || '暂无策略描述' }}
          </span>
        </div>
      </div>
    </ElCard>

    <!-- 主要内容区域 - 左右分栏布局 -->
    <ElRow :gutter="20" class="main-content">
      <!-- 左侧：条款列表 -->
      <ElCol :span="8">
        <ElCard class="clause-list-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">
                <ElIcon><List /></ElIcon>
                审查条款列表
              </span>
              <ElButton type="primary" size="small" @click="handleAddClause">
                <ElIcon><Plus /></ElIcon>
                新增条款
              </ElButton>
            </div>
          </template>

          <div class="clause-list-content">
            <!-- 搜索框 -->
            <div class="clause-search">
              <ElInput
                v-model="clauseSearchText"
                placeholder="搜索条款名称..."
                :prefix-icon="Search"
                clearable
                @input="handleClauseSearch"
              />
            </div>

            <div v-if="clauseLoading" class="loading-container">
              <ElSkeleton :rows="5" animated />
            </div>
            <div v-else-if="filteredClauseList.length === 0" class="empty-container">
              <ElEmpty description="暂无条款数据" />
            </div>
            <div v-else class="clause-items">
              <div
                v-for="clause in filteredClauseList"
                :key="clause.id"
                class="clause-item"
                :class="{
                  active: selectedClause?.id === clause.id,
                  disabled: clause.clauseStatus === '0'
                }"
                @click="handleSelectClause(clause)"
              >
                <div class="clause-content">
                  <!-- 右上角状态标签 -->
                  <div class="clause-status-badge">
                    <ElTag
                      :type="clause.clauseStatus === '1' ? 'success' : 'info'"
                      size="small"
                      effect="plain"
                      round
                    >
                      {{ clause.clauseStatus === '1' ? '启用' : '禁用' }}
                    </ElTag>
                  </div>

                  <div class="clause-header">
                    <span class="clause-name">{{ clause.clauseName }}</span>
                    <div class="clause-actions">
                      <ElTooltip content="编辑" placement="top">
                        <ElButton type="text" size="small" @click.stop="handleEditClause(clause)">
                          <ElIcon><Edit /></ElIcon>
                        </ElButton>
                      </ElTooltip>
                      <ElTooltip content="删除" placement="top">
                        <ElButton type="text" size="small" @click.stop="handleDeleteClause(clause)">
                          <ElIcon><Delete /></ElIcon>
                        </ElButton>
                      </ElTooltip>
                    </div>
                  </div>
                  <div class="clause-desc" v-if="clause.clauseDesc">
                    {{ clause.clauseDesc }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <!-- 右侧：条款详情和风险点管理 -->
      <ElCol :span="16">
        <div class="detail-content">
          <!-- 条款详情区域 -->
          <ElCard class="clause-detail-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <ElIcon><Edit /></ElIcon>
                  条款详情
                </span>
              </div>
            </template>

            <div class="clause-detail-content">
              <div v-if="!selectedClause" class="no-selection">
                <ElEmpty description="请选择左侧条款进行编辑" />
              </div>
              <div v-else class="selected-clause-info">
                <!-- 条款基本信息 -->
                <div class="clause-header-section">
                  <div class="clause-title-row">
                    <div class="title-and-status">
                      <h3 class="clause-title">{{ selectedClause.clauseName }}</h3>
                      <ElTag
                        :type="selectedClause.clauseStatus === '1' ? 'success' : 'info'"
                        size="default"
                        effect="dark"
                      >
                        {{ selectedClause.clauseStatus === '1' ? '启用' : '禁用' }}
                      </ElTag>
                    </div>
                    <div class="clause-actions">
                      <ElButton
                        type="primary"
                        size="small"
                        @click="handleEditClause(selectedClause)"
                      >
                        <ElIcon><Edit /></ElIcon>
                        编辑条款
                      </ElButton>
                      <ElButton
                        type="danger"
                        size="small"
                        @click="handleDeleteClause(selectedClause)"
                      >
                        <ElIcon><Delete /></ElIcon>
                        删除条款
                      </ElButton>
                    </div>
                  </div>
                </div>

                <ElDivider style="margin: 16px 0" />

                <!-- 条款详细信息 -->
                <div class="clause-detail-section">
                  <div class="detail-item" v-if="selectedClause.clauseDesc">
                    <div class="detail-content">{{ selectedClause.clauseDesc }}</div>
                  </div>

                  <div class="detail-item" v-if="selectedClause.clauseContent">
                    <div class="detail-content clause-content">{{
                      selectedClause.clauseContent
                    }}</div>
                  </div>
                </div>
              </div>
            </div>
          </ElCard>

          <!-- 风险点管理区域 -->
          <ElCard class="risk-point-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <ElIcon><Warning /></ElIcon>
                  风险点列表
                </span>
                <ElButton
                  type="danger"
                  size="small"
                  @click="handleAddRiskPoint"
                  :disabled="!selectedClause"
                >
                  <ElIcon><Plus /></ElIcon>
                  添加风险点
                </ElButton>
              </div>
            </template>

            <div class="risk-point-content">
              <div v-if="!selectedClause" class="no-selection">
                <ElEmpty description="请先选择条款" />
              </div>
              <div v-else>
                <!-- 风险点搜索 -->
                <div class="risk-point-search">
                  <ElInput
                    v-model="riskPointSearchText"
                    placeholder="搜索风险点..."
                    clearable
                    :prefix-icon="Search"
                  />
                </div>

                <!-- 风险点列表 -->
                <div class="risk-point-items" v-loading="riskPointLoading">
                  <div v-if="filteredRiskPointList.length === 0" class="empty-placeholder">
                    <ElEmpty description="暂无风险点数据" />
                  </div>
                  <div
                    v-for="riskPoint in filteredRiskPointList"
                    :key="riskPoint.id"
                    class="risk-point-item"
                    :class="{
                      active: selectedRiskPoint?.id === riskPoint.id,
                      disabled: riskPoint.riskStatus === '0'
                    }"
                    @click="handleSelectRiskPoint(riskPoint)"
                  >
                    <div class="risk-point-content">
                      <!-- 右上角状态标签 -->
                      <div class="risk-point-status-badge">
                        <ElTag
                          :type="riskPoint.riskStatus === '1' ? 'success' : 'info'"
                          size="small"
                          effect="plain"
                          round
                        >
                          {{ riskPoint.riskStatus === '1' ? '启用' : '禁用' }}
                        </ElTag>
                      </div>

                      <div class="risk-point-header">
                        <div class="risk-point-title-row">
                          <div class="risk-level-icon">
                            <ElIcon
                              :color="
                                getRiskLevelConfig(riskPoint.riskLevel).color === 'danger'
                                  ? '#f56c6c'
                                  : '#e6a23c'
                              "
                              :size="16"
                            >
                              <Warning v-if="riskPoint.riskLevel === '1'" />
                              <InfoFilled v-else />
                            </ElIcon>
                          </div>
                          <span class="risk-point-name">{{ riskPoint.riskName }}</span>
                        </div>
                        <div class="risk-point-actions">
                          <ElTooltip content="编辑" placement="top">
                            <ElButton
                              type="text"
                              size="small"
                              @click.stop="handleEditRiskPoint(riskPoint)"
                            >
                              <ElIcon><Edit /></ElIcon>
                            </ElButton>
                          </ElTooltip>
                          <ElTooltip content="删除" placement="top">
                            <ElButton
                              type="text"
                              size="small"
                              @click.stop="handleDeleteRiskPoint(riskPoint)"
                            >
                              <ElIcon><Delete /></ElIcon>
                            </ElButton>
                          </ElTooltip>
                        </div>
                      </div>
                      <div class="risk-point-desc" v-if="riskPoint.riskDesc">
                        {{ riskPoint.riskDesc }}
                      </div>
                      <div class="risk-point-level">
                        <ElTag
                          :type="getRiskLevelConfig(riskPoint.riskLevel).color"
                          size="small"
                          effect="light"
                        >
                          {{ getRiskLevelConfig(riskPoint.riskLevel).label }}
                        </ElTag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ElCard>
        </div>
      </ElCol>
    </ElRow>

    <!-- 条款编辑对话框 -->
    <ElDialog
      v-model="clauseDialogVisible"
      :title="isEditingClause ? '编辑条款' : '新增条款'"
      width="600px"
      :close-on-click-modal="false"
    >
      <ElForm ref="clauseFormRef" :model="clauseForm" :rules="clauseFormRules" label-width="100px">
        <ElFormItem label="条款名称" prop="clauseName">
          <ElInput
            v-model="clauseForm.clauseName"
            placeholder="请输入条款名称"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="条款说明" prop="clauseDesc">
          <ElInput
            v-model="clauseForm.clauseDesc"
            type="textarea"
            :rows="3"
            placeholder="请输入条款说明"
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>
        <ElFormItem label="排序序号" prop="sortOrder">
          <ElInputNumber
            v-model="clauseForm.sortOrder"
            :min="1"
            :max="999"
            placeholder="排序序号"
          />
        </ElFormItem>
        <ElFormItem label="条款状态" prop="clauseStatus">
          <ElRadioGroup v-model="clauseForm.clauseStatus">
            <ElRadio value="1">启用</ElRadio>
            <ElRadio value="0">禁用</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancelClauseEdit">取消</ElButton>
          <ElButton type="primary" @click="handleSaveClause" :loading="clauseSaving">
            确定
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 风险点编辑对话框 -->
    <ElDialog
      v-model="riskPointDialogVisible"
      :title="isEditingRiskPoint ? '编辑风险点' : '新增风险点'"
      width="600px"
      :close-on-click-modal="false"
    >
      <ElForm
        ref="riskPointFormRef"
        :model="riskPointForm"
        :rules="riskPointFormRules"
        label-width="100px"
      >
        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="风险点名称" prop="riskName">
              <ElInput v-model="riskPointForm.riskName" placeholder="请输入风险点名称" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="风险等级" prop="riskLevel">
              <ElSelect v-model="riskPointForm.riskLevel" placeholder="请选择风险等级">
                <ElOption
                  v-for="option in RISK_LEVEL_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                >
                  <div style="display: flex; align-items: center; gap: 8px">
                    <ElIcon :color="option.color === 'danger' ? '#f56c6c' : '#e6a23c'">
                      <Warning v-if="option.icon === 'Warning'" />
                      <InfoFilled v-else />
                    </ElIcon>
                    <span>{{ option.label }}</span>
                  </div>
                </ElOption>
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="风险描述" prop="riskDesc">
          <ElInput
            v-model="riskPointForm.riskDesc"
            type="textarea"
            :rows="3"
            placeholder="请输入风险点描述"
          />
        </ElFormItem>

        <!-- 注释掉的字段 - 根据PRD要求暂时隐藏 -->
        <!--
        <ElFormItem label="风险分析" prop="riskAnalysis">
          <ElInput
            v-model="riskPointForm.riskAnalysis"
            type="textarea"
            :rows="4"
            placeholder="请输入详细的风险分析"
          />
        </ElFormItem>

        <ElFormItem label="修改建议" prop="suggestModify">
          <ElInput
            v-model="riskPointForm.suggestModify"
            type="textarea"
            :rows="4"
            placeholder="请输入修改建议"
          />
        </ElFormItem>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="关键词模式" prop="keywordPattern">
              <ElInput v-model="riskPointForm.keywordPattern" placeholder="请输入关键词匹配模式" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="排序序号" prop="sortOrder">
              <ElInputNumber
                v-model="riskPointForm.sortOrder"
                :min="1"
                :max="999"
                controls-position="right"
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        -->

        <ElFormItem label="状态">
          <ElRadioGroup v-model="riskPointForm.riskStatus">
            <ElRadio label="1">启用</ElRadio>
            <ElRadio label="0">禁用</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleCancelRiskPointEdit">取消</ElButton>
          <ElButton type="primary" @click="handleSaveRiskPoint" :loading="riskPointSaving">
            确定
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Document,
    ArrowLeft,
    List,
    Plus,
    Edit,
    Warning,
    Search,
    Delete,
    InfoFilled
  } from '@element-plus/icons-vue'
  import { ContractReviewStrategyApi } from '@/api/contract/strategy'
  import { ContractReviewClauseApi } from '@/api/contract/clause'
  import { ContractRiskPointApi } from '@/api/contract/riskPoint'
  import type { ContractReviewStrategy } from '@/types/contract/strategy'
  import type { ContractReviewClause } from '@/types/contract/clause'
  import type { ContractRiskPoint, ContractRiskPointForm } from '@/types/contract/riskPoint'
  import { RISK_LEVEL_OPTIONS, RiskLevel, RiskStatus } from '@/types/contract/riskPoint'

  defineOptions({ name: 'ContractStrategyEdit' })

  // Vue Router实例
  const route = useRoute()
  const router = useRouter()

  // 页面状态
  const loading = ref(true)

  // 条款相关状态
  const clauseLoading = ref(false)
  const clauseSaving = ref(false)
  const clauseDialogVisible = ref(false)
  const isEditingClause = ref(false)
  const clauseSearchText = ref('')
  const clauseFormRef = ref()

  // 风险点相关状态
  const riskPointLoading = ref(false)
  const riskPointSaving = ref(false)
  const riskPointDialogVisible = ref(false)
  const isEditingRiskPoint = ref(false)
  const riskPointSearchText = ref('')
  const riskPointFormRef = ref()

  // 策略基础信息
  const strategyInfo = reactive<Partial<ContractReviewStrategy>>({
    id: undefined,
    strategyName: '',
    strategyDesc: '',
    categoryId: undefined,
    categoryName: '',
    reviewPosition: '1',
    strategyStatus: '0'
  })

  // 当前审查立场
  const currentPosition = ref<string>('1')

  // 条款相关数据
  const clauseList = ref<ContractReviewClause[]>([])
  const selectedClause = ref<ContractReviewClause | null>(null)

  // 风险点相关数据
  const riskPointList = ref<ContractRiskPoint[]>([])
  const selectedRiskPoint = ref<ContractRiskPoint | null>(null)

  // 条款表单数据
  const clauseForm = reactive({
    id: undefined as number | undefined,
    strategyId: 0,
    clauseName: '',
    clauseDesc: '',
    clauseContent: '',
    sortOrder: 1,
    clauseStatus: '1'
  })

  // 风险点表单数据
  const riskPointForm = reactive<ContractRiskPointForm>({
    id: undefined,
    clauseId: 0,
    riskName: '',
    riskDesc: '',
    riskLevel: RiskLevel.GENERAL,
    riskStatus: RiskStatus.ENABLED
    // 注释掉的字段 - 根据PRD要求暂时隐藏
    /*
  riskAnalysis: '',
  suggestModify: '',
  keywordPattern: '',
  sortOrder: 1
  */
  })

  // 条款表单验证规则
  const clauseFormRules = {
    clauseName: [
      { required: true, message: '请输入条款名称', trigger: 'blur' },
      { min: 2, max: 200, message: '条款名称长度在 2 到 200 个字符', trigger: 'blur' }
    ],
    sortOrder: [{ required: true, message: '请输入排序序号', trigger: 'blur' }]
  }

  // 风险点表单验证规则
  const riskPointFormRules = {
    riskName: [
      { required: true, message: '请输入风险点名称', trigger: 'blur' },
      { min: 1, max: 100, message: '风险点名称长度在 1 到 100 个字符', trigger: 'blur' }
    ],
    riskLevel: [{ required: true, message: '请选择风险等级', trigger: 'change' }]
    // 注释掉的验证规则 - 对应隐藏的字段
    /*
  riskAnalysis: [
    { max: 1000, message: '风险分析长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  suggestModify: [
    { max: 1000, message: '修改建议长度不能超过 1000 个字符', trigger: 'blur' }
  ]
  */
  }

  // 计算属性
  const filteredClauseList = computed(() => {
    if (!clauseSearchText.value) {
      return clauseList.value
    }
    return clauseList.value.filter((clause) =>
      clause.clauseName.toLowerCase().includes(clauseSearchText.value.toLowerCase())
    )
  })

  // 过滤后的风险点列表
  const filteredRiskPointList = computed(() => {
    if (!riskPointSearchText.value) {
      return riskPointList.value
    }
    return riskPointList.value.filter((riskPoint) =>
      riskPoint.riskName.toLowerCase().includes(riskPointSearchText.value.toLowerCase())
    )
  })
  const strategyId = computed(() => {
    const id = route.params.strategyId
    return typeof id === 'string' ? parseInt(id) : 0
  })

  // 获取策略基础信息
  const loadStrategyInfo = async () => {
    try {
      loading.value = true
      console.log('开始加载策略信息，策略ID:', strategyId.value)
      const response = await ContractReviewStrategyApi.getStrategy(strategyId.value)
      console.log('策略信息API响应:', response)

      // 根据若依框架的响应格式处理数据
      let data = response.data
      if (data && typeof data === 'object' && data.data) {
        data = data.data // 如果数据在data.data中
      }

      console.log('解析后的策略数据:', data)
      Object.assign(strategyInfo, data)
      // 从策略数据中获取审查立场，不允许修改
      currentPosition.value = data.reviewPosition || '1'

      console.log('策略信息加载成功:', strategyInfo)
      ElMessage.success('策略信息加载成功')
    } catch (error) {
      console.error('加载策略信息失败:', error)
      ElMessage.error('加载策略信息失败，请检查策略ID是否正确')
      // 不自动跳转，让用户手动返回
    } finally {
      loading.value = false
    }
  }

  // 审查立场为只读，从策略数据中获取

  // 返回列表页
  const handleGoBack = () => {
    router.push({ name: 'ContractStrategy' })
  }

  // 加载条款列表
  const loadClauseList = async () => {
    try {
      clauseLoading.value = true
      console.log('开始加载条款列表，策略ID:', strategyId.value)
      const response = await ContractReviewClauseApi.getClauseListByStrategy(strategyId.value)
      console.log('条款列表API响应:', response)

      // 根据若依框架的响应格式，数据可能在response.data.rows中
      const data = response.data
      if (Array.isArray(data)) {
        clauseList.value = data
      } else if (data && Array.isArray(data.rows)) {
        clauseList.value = data.rows
      } else if (data && Array.isArray(data.data)) {
        clauseList.value = data.data
      } else {
        clauseList.value = []
      }

      console.log('条款列表加载成功，数量:', clauseList.value.length)
    } catch (error) {
      console.error('加载条款列表失败:', error)
      ElMessage.error('加载条款列表失败')
      clauseList.value = []
    } finally {
      clauseLoading.value = false
    }
  }

  // 加载风险点列表
  const loadRiskPointList = async (clauseId?: number) => {
    if (!clauseId && !selectedClause.value?.id) return

    try {
      riskPointLoading.value = true
      const targetClauseId = clauseId || selectedClause.value!.id!
      console.log('开始加载风险点列表，条款ID:', targetClauseId)
      const response = await ContractRiskPointApi.getRiskPointListByClause(targetClauseId)
      console.log('风险点列表API响应:', response)

      // 根据若依框架的响应格式，数据可能在response.data.rows中
      const data = response.data
      if (Array.isArray(data)) {
        riskPointList.value = data
      } else if (data && Array.isArray(data.rows)) {
        riskPointList.value = data.rows
      } else if (data && Array.isArray(data.data)) {
        riskPointList.value = data.data
      } else {
        riskPointList.value = []
      }

      console.log('风险点列表加载成功，数量:', riskPointList.value.length)
    } catch (error) {
      console.error('加载风险点列表失败:', error)
      ElMessage.error('加载风险点列表失败')
    } finally {
      riskPointLoading.value = false
    }
  }

  // 条款搜索
  const handleClauseSearch = () => {
    // 搜索逻辑已在计算属性中实现
  }

  // 选择条款
  const handleSelectClause = (clause: ContractReviewClause) => {
    selectedClause.value = clause
    // 选择条款时自动加载对应的风险点列表
    loadRiskPointList(clause.id)
  }

  // 新增条款
  const handleAddClause = () => {
    resetClauseForm()
    clauseForm.strategyId = strategyId.value
    clauseForm.sortOrder = clauseList.value.length + 1
    isEditingClause.value = false
    clauseDialogVisible.value = true
  }

  // 编辑条款
  const handleEditClause = (clause: ContractReviewClause) => {
    resetClauseForm()
    Object.assign(clauseForm, clause)
    isEditingClause.value = true
    clauseDialogVisible.value = true
  }

  // 重置条款表单
  const resetClauseForm = () => {
    Object.assign(clauseForm, {
      id: undefined,
      strategyId: 0,
      clauseName: '',
      clauseDesc: '',
      clauseContent: '',
      sortOrder: 1,
      clauseStatus: '1'
    })
    clauseFormRef.value?.resetFields()
  }

  // 取消条款编辑
  const handleCancelClauseEdit = () => {
    clauseDialogVisible.value = false
    resetClauseForm()
    isEditingClause.value = false
  }

  // 重置风险点表单
  const resetRiskPointForm = () => {
    Object.assign(riskPointForm, {
      id: undefined,
      clauseId: selectedClause.value?.id || 0,
      riskName: '',
      riskDesc: '',
      riskLevel: RiskLevel.GENERAL,
      riskStatus: RiskStatus.ENABLED
      // 注释掉的字段 - 根据PRD要求暂时隐藏
      /*
    riskAnalysis: '',
    suggestModify: '',
    keywordPattern: '',
    sortOrder: riskPointList.value.length + 1
    */
    })
    riskPointFormRef.value?.resetFields()
  }

  // 新增风险点
  const handleAddRiskPoint = () => {
    if (!selectedClause.value) {
      ElMessage.warning('请先选择条款')
      return
    }
    resetRiskPointForm()
    riskPointForm.clauseId = selectedClause.value.id!
    // 注释掉排序相关逻辑
    // riskPointForm.sortOrder = riskPointList.value.length + 1
    isEditingRiskPoint.value = false
    riskPointDialogVisible.value = true
  }

  // 编辑风险点
  const handleEditRiskPoint = (riskPoint: ContractRiskPoint) => {
    resetRiskPointForm()
    Object.assign(riskPointForm, riskPoint)
    isEditingRiskPoint.value = true
    riskPointDialogVisible.value = true
  }

  // 取消风险点编辑
  const handleCancelRiskPointEdit = () => {
    riskPointDialogVisible.value = false
    resetRiskPointForm()
    isEditingRiskPoint.value = false
  }

  // 保存风险点
  const handleSaveRiskPoint = async () => {
    try {
      await riskPointFormRef.value?.validate()
      riskPointSaving.value = true

      if (isEditingRiskPoint.value) {
        await ContractRiskPointApi.updateRiskPoint(riskPointForm)
        ElMessage.success('风险点更新成功')
      } else {
        await ContractRiskPointApi.addRiskPoint(riskPointForm)
        ElMessage.success('风险点添加成功')
      }

      riskPointDialogVisible.value = false
      await loadRiskPointList()
    } catch (error) {
      console.error('保存风险点失败:', error)
      ElMessage.error('保存风险点失败')
    } finally {
      riskPointSaving.value = false
    }
  }

  // 删除风险点
  const handleDeleteRiskPoint = async (riskPoint: ContractRiskPoint) => {
    try {
      await ElMessageBox.confirm(`确认删除风险点"${riskPoint.riskName}"吗？`, '删除确认', {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ContractRiskPointApi.delRiskPoint(riskPoint.id!)
      ElMessage.success('风险点删除成功')

      // 如果删除的是当前选中的风险点，清空选中状态
      if (selectedRiskPoint.value?.id === riskPoint.id) {
        selectedRiskPoint.value = null
      }

      await loadRiskPointList()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除风险点失败:', error)
        ElMessage.error('删除风险点失败')
      }
    }
  }

  // 选择风险点
  const handleSelectRiskPoint = (riskPoint: ContractRiskPoint) => {
    selectedRiskPoint.value = riskPoint
  }

  // 获取风险等级配置
  const getRiskLevelConfig = (level: string) => {
    return RISK_LEVEL_OPTIONS.find((option) => option.value === level) || RISK_LEVEL_OPTIONS[1]
  }

  // 保存条款
  const handleSaveClause = async () => {
    try {
      await clauseFormRef.value?.validate()
      clauseSaving.value = true

      if (isEditingClause.value) {
        await ContractReviewClauseApi.updateClause(clauseForm)
        ElMessage.success('条款更新成功')
      } else {
        await ContractReviewClauseApi.addClause(clauseForm)
        ElMessage.success('条款添加成功')
      }

      clauseDialogVisible.value = false
      await loadClauseList()
    } catch (error) {
      console.error('保存条款失败:', error)
      ElMessage.error('保存条款失败')
    } finally {
      clauseSaving.value = false
    }
  }

  // 删除条款
  const handleDeleteClause = async (clause: ContractReviewClause) => {
    try {
      await ElMessageBox.confirm(`确认删除条款"${clause.clauseName}"吗？`, '删除确认', {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ContractReviewClauseApi.delClause(clause.id!)
      ElMessage.success('条款删除成功')

      // 如果删除的是当前选中的条款，清空选中状态
      if (selectedClause.value?.id === clause.id) {
        selectedClause.value = null
      }

      await loadClauseList()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除条款失败:', error)
        ElMessage.error('删除条款失败')
      }
    }
  }

  // 页面初始化
  onMounted(async () => {
    console.log('页面初始化，路由参数:', route.params)
    console.log('解析的策略ID:', strategyId.value)

    if (strategyId.value && strategyId.value > 0) {
      // 并行加载策略信息和条款列表，互不影响
      Promise.all([loadStrategyInfo(), loadClauseList()]).catch((error) => {
        console.error('页面初始化失败:', error)
      })
    } else {
      ElMessage.error('策略ID无效')
      console.error('无效的策略ID:', strategyId.value)
    }
  })
</script>

<style lang="scss" scoped>
  .contract-strategy-edit-page {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: calc(100vh - 84px);

    .page-header {
      margin-bottom: 20px;
      border: none;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-left {
          .page-title {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            display: flex;
            align-items: center;

            .title-icon {
              margin-right: 8px;
              color: var(--el-color-primary);
            }
          }

          .breadcrumb-info {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #909399;

            .breadcrumb-item {
              color: #909399;
            }

            .breadcrumb-current {
              color: var(--el-color-primary);
              font-weight: 500;
            }

            .breadcrumb-separator {
              margin: 0 8px;
              font-size: 12px;
            }
          }
        }

        .header-right {
          display: flex;
          gap: 12px;
        }
      }
    }

    .position-selector {
      margin-bottom: 20px;
      border: none;

      .position-content {
        display: flex;
        align-items: center;
        gap: 20px;

        .position-label {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }

        .position-display {
          .el-tag {
            padding: 8px 16px;
            font-size: 14px;

            .el-icon {
              margin-right: 6px;
            }
          }
        }

        .position-description {
          margin-left: auto;

          .position-desc {
            font-size: 14px;
            color: #606266;
            font-style: italic;
          }
        }
      }
    }

    .main-content {
      .clause-list-card,
      .clause-detail-card,
      .risk-point-card {
        border: none;
        margin-bottom: 20px;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .card-title {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
            display: flex;
            align-items: center;

            .el-icon {
              margin-right: 6px;
              color: var(--el-color-primary);
            }
          }
        }
      }

      .clause-list-card {
        height: calc(100vh - 260px);
        min-height: 500px;
        display: flex;
        flex-direction: column;

        // 确保ElCard内容区域能够正确计算高度
        :deep(.el-card__body) {
          flex: 1;
          padding: 12px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
        }

        .clause-list-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          .loading-container,
          .empty-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .clause-search {
            flex-shrink: 0;
            margin-bottom: 12px;
          }

          .clause-items {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 6px; // 为滚动条留出空间
            margin-right: -3px; // 抵消padding，保持视觉对齐

            // 自定义滚动条样式
            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 3px;

              &:hover {
                background: #a8a8a8;
              }
            }

            .clause-item {
              margin-bottom: 8px;
              padding: 12px;
              background-color: #fff;
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.2s ease;
              flex-shrink: 0; // 防止在flex容器中被压缩
              position: relative; // 为状态标签定位

              &:hover {
                border-color: var(--el-color-primary);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
                transform: translateY(-1px);
              }

              &.active {
                border-color: var(--el-color-primary);
                background-color: var(--el-color-primary-light-9);
                box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
              }

              &.disabled {
                opacity: 0.6;
                background-color: #f5f7fa;
              }

              .clause-content {
                .clause-status-badge {
                  position: absolute;
                  top: 8px;
                  right: 8px;
                  z-index: 1;
                }

                .clause-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  margin-bottom: 6px;
                  padding-right: 50px; // 为右上角状态标签留出空间

                  .clause-name {
                    font-size: 14px;
                    font-weight: 500;
                    color: #303133;
                    flex: 1;
                    line-height: 1.3;
                    margin-right: 8px;
                    // 限制标题最多显示2行
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  .clause-actions {
                    display: flex;
                    gap: 2px;
                    opacity: 0.7;
                    transition: opacity 0.2s ease;
                    flex-shrink: 0;

                    .el-button {
                      padding: 3px;
                      min-height: auto;
                      width: 24px;
                      height: 24px;

                      .el-icon {
                        font-size: 12px;
                      }
                    }
                  }
                }

                .clause-desc {
                  font-size: 12px;
                  color: #909399;
                  line-height: 1.3;
                  margin-top: 4px;
                  padding-right: 50px; // 为右上角状态标签留出空间
                  // 限制描述最多显示2行
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              &:hover .clause-actions {
                opacity: 1;
              }
            }
          }
        }

        // 响应式设计
        @media (max-height: 800px) {
          height: calc(100vh - 240px);
          min-height: 400px;
        }

        @media (max-height: 600px) {
          height: calc(100vh - 200px);
          min-height: 300px;
        }
      }

      .detail-content {
        .clause-detail-card {
          margin-bottom: 20px;

          .clause-detail-content {
            overflow-y: visible;

            // 确保内容能够自适应
            .selected-clause-info {
              display: flex;
              flex-direction: column;
            }

            .no-selection {
              padding: 60px 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #909399;
            }

            .selected-clause-info {
              .clause-header-section {
                margin-bottom: 20px;

                .clause-title-row {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  margin-bottom: 0;

                  .title-and-status {
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    flex: 1;

                    .clause-title {
                      margin: 0;
                      font-size: 18px;
                      font-weight: 600;
                      color: #303133;
                      line-height: 1.4;
                    }
                  }

                  .clause-actions {
                    display: flex;
                    gap: 8px;
                    flex-shrink: 0;
                  }
                }
              }

              .clause-detail-section {
                .detail-item {
                  margin-bottom: 16px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .detail-content {
                    font-size: 14px;
                    color: #606266;
                    line-height: 1.6;
                    padding: 16px;
                    background-color: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;

                    &.clause-content {
                      white-space: pre-wrap;
                      font-size: 15px;
                      color: #303133;
                      min-height: auto;
                    }

                    // 如果内容为空，隐藏该项
                    &:empty {
                      display: none;
                    }
                  }
                }
              }

              .clause-basic-info {
                h4 {
                  margin: 0 0 8px 0;
                  font-size: 16px;
                  font-weight: 600;
                  color: #303133;
                }

                .clause-desc {
                  margin: 0 0 12px 0;
                  font-size: 14px;
                  color: #606266;
                  line-height: 1.5;
                }

                .clause-meta {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .clause-order {
                    font-size: 12px;
                    color: #909399;
                  }
                }
              }
            }

            .detail-placeholder {
              background-color: #f8f9fa;
              border-radius: 6px;
              border: 1px dashed #dcdfe6;
              padding: 20px;
              text-align: center;
              color: #909399;

              .el-icon {
                margin-right: 6px;
              }
            }
          }
        }

        .risk-point-card {
          .risk-point-content {
            .no-selection {
              padding: 60px 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #909399;
            }

            .risk-point-search {
              margin-bottom: 12px;
            }

            .risk-point-items {
              .empty-placeholder {
                padding: 40px 20px;
                text-align: center;
                color: #909399;
              }

              .risk-point-item {
                margin-bottom: 8px;
                padding: 12px;
                background-color: #fff;
                border: 1px solid #e4e7ed;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
                position: relative;

                &:hover {
                  border-color: var(--el-color-primary);
                  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
                  transform: translateY(-1px);
                }

                &.active {
                  border-color: var(--el-color-primary);
                  background-color: var(--el-color-primary-light-9);
                  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
                }

                &.disabled {
                  opacity: 0.6;
                  background-color: #f5f7fa;
                }

                .risk-point-content {
                  .risk-point-status-badge {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    z-index: 1;
                  }

                  .risk-point-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 6px;
                    padding-right: 50px;

                    .risk-point-title-row {
                      display: flex;
                      align-items: center;
                      gap: 8px;
                      flex: 1;

                      .risk-level-icon {
                        flex-shrink: 0;
                      }

                      .risk-point-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: #303133;
                        line-height: 1.3;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                      }
                    }

                    .risk-point-actions {
                      display: flex;
                      gap: 2px;
                      opacity: 0.7;
                      transition: opacity 0.2s ease;
                      flex-shrink: 0;

                      .el-button {
                        padding: 3px;
                        min-height: auto;
                        width: 24px;
                        height: 24px;

                        .el-icon {
                          font-size: 12px;
                        }
                      }
                    }
                  }

                  .risk-point-desc {
                    font-size: 12px;
                    color: #909399;
                    line-height: 1.3;
                    margin-bottom: 6px;
                    padding-right: 50px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  .risk-point-level {
                    display: flex;
                    align-items: center;
                  }
                }

                &:hover .risk-point-actions {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 1200px) {
    .contract-strategy-edit-page {
      .main-content {
        .clause-list-card {
          height: auto;
          min-height: 400px;
        }

        .detail-content {
          .clause-detail-card {
            .clause-detail-content {
              .selected-clause-info {
                .clause-header-section {
                  .clause-title-row {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 12px;

                    .title-and-status {
                      width: 100%;
                      justify-content: space-between;
                    }

                    .clause-actions {
                      width: 100%;
                      justify-content: flex-end;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .contract-strategy-edit-page {
      padding: 10px;

      .main-content {
        .clause-detail-content {
          .selected-clause-info {
            .clause-header-section {
              .clause-title-row {
                .clause-actions {
                  flex-direction: column;
                  gap: 8px;

                  .el-button {
                    width: 100%;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
</style>
