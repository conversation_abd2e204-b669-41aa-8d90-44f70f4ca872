<template>
  <ElDialog
    :model-value="visible"
    title="复制策略"
    width="500px"
    :before-close="handleClose"
    @update:model-value="$emit('update:visible', $event)"
  >
    <div class="copy-strategy-content">
      <ElAlert title="复制说明" type="info" :closable="false" show-icon>
        <template #default>
          <p>将复制策略的基本信息和所有条款配置，您可以修改新策略的名称。</p>
        </template>
      </ElAlert>

      <div class="source-info">
        <h4>源策略信息</h4>
        <ElDescriptions :column="1" border size="small">
          <ElDescriptionsItem label="策略名称">
            {{ sourceStrategy.strategyName }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="合同分类">
            {{ sourceStrategy.categoryName }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="审查立场">
            <ElTag
              :type="sourceStrategy.reviewPosition === '1' ? 'primary' : 'success'"
              size="small"
            >
              {{ sourceStrategy.reviewPosition === '1' ? '甲方' : '乙方' }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="当前状态">
            <ElTag
              :type="sourceStrategy.strategyStatus === '1' ? 'success' : 'warning'"
              size="small"
            >
              {{ sourceStrategy.strategyStatus === '1' ? '已发布' : '草稿' }}
            </ElTag>
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>

      <ElForm ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right">
        <ElFormItem label="新策略名称" prop="newStrategyName">
          <ElInput
            v-model="form.newStrategyName"
            placeholder="请输入新策略名称"
            maxlength="200"
            show-word-limit
            clearable
          />
        </ElFormItem>
      </ElForm>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">
          确认复制
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
  import { ref, reactive, watch } from 'vue'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
  import { ContractReviewStrategyApi } from '@/api/contract/strategy'
  import type { ContractReviewStrategy } from '@/types/contract/strategy'

  interface Props {
    visible: boolean
    sourceStrategy: ContractReviewStrategy
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 响应式数据
  const formRef = ref<FormInstance>()
  const submitLoading = ref(false)

  // 表单数据
  const form = reactive({
    newStrategyName: ''
  })

  // 表单验证规则
  const rules: FormRules = {
    newStrategyName: [
      { required: true, message: '请输入新策略名称', trigger: 'blur' },
      { min: 2, max: 200, message: '策略名称长度在 2 到 200 个字符', trigger: 'blur' }
    ]
  }

  // 监听对话框显示状态
  watch(
    () => props.visible,
    (visible) => {
      if (visible && props.sourceStrategy.strategyName) {
        // 自动生成新策略名称
        form.newStrategyName = `${props.sourceStrategy.strategyName}_副本`
      }
    }
  )

  // 关闭对话框
  const handleClose = () => {
    form.newStrategyName = ''
    formRef.value?.clearValidate()
    emit('update:visible', false)
  }

  // 提交复制
  const handleSubmit = async () => {
    if (!formRef.value) return

    try {
      const valid = await formRef.value.validate()
      if (!valid) return

      submitLoading.value = true

      await ContractReviewStrategyApi.copyStrategy(props.sourceStrategy.id!, form.newStrategyName)

      ElMessage.success('复制成功')
      emit('success')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
    } finally {
      submitLoading.value = false
    }
  }
</script>

<style scoped>
  .copy-strategy-content {
    padding: 0 4px;
  }

  .source-info {
    margin: 20px 0;
  }

  .source-info h4 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }

  .dialog-footer {
    text-align: right;
  }

  :deep(.el-alert) {
    margin-bottom: 20px;
  }

  :deep(.el-descriptions) {
    margin-bottom: 20px;
  }
</style>
