<template>
  <div class="art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton type="primary" @click="handleAdd" v-ripple> 新增分类 </ElButton>
          <ElButton
            v-auth="'contract:contract:edit'"
            type="success"
            :disabled="single"
            @click="() => handleUpdate()"
            v-ripple
          >
            修改
          </ElButton>
          <ElButton
            v-auth="'contract:contract:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            删除
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="id"
        :loading="loading"
        :columns="columns"
        :data="categoryList"
        :stripe="true"
        :pagination="pagination"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
        @selection-change="handleSelectionChange"
      >
        <template #status="{ row }">
          <el-switch
            v-if="row && row.id && row.status !== undefined"
            v-model="row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(row)"
          />
          <span v-else class="text-gray-400">加载中...</span>
        </template>

        <template #action="{ row }">
          <template v-if="row && row.id">
            <el-tooltip content="修改" placement="top">
              <el-button
                v-ripple
                v-auth="'contract:contract:edit'"
                link
                type="primary"
                :disabled="loading"
                @click="handleUpdate(row)"
              >
                编辑
              </el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-ripple
                v-auth="'contract:contract:remove'"
                link
                type="danger"
                :disabled="loading"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </el-tooltip>
          </template>
          <template v-else>
            <span class="text-gray-400">数据加载中...</span>
          </template>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 添加或修改合同分类对话框 -->
    <ElDialog :title="title" v-model="open" width="500px" append-to-body>
      <ElForm ref="categoryRef" :model="form" :rules="rules" label-width="100px">
        <ElFormItem label="分类名称" prop="categoryName">
          <ElInput v-model="form.categoryName" placeholder="请输入分类名称" />
        </ElFormItem>
        <ElFormItem label="分类描述" prop="categoryDesc">
          <ElInput
            v-model="form.categoryDesc"
            type="textarea"
            :rows="4"
            placeholder="请输入分类描述"
          />
        </ElFormItem>
        <ElFormItem label="显示顺序" prop="sortOrder">
          <ElInputNumber
            v-model="form.sortOrder"
            controls-position="right"
            :min="0"
            placeholder="请输入显示顺序"
          />
        </ElFormItem>
        <ElFormItem label="状态">
          <ElRadioGroup v-model="form.status">
            <ElRadio v-for="dict in statusOptions" :key="dict.value" :value="dict.value">
              {{ dict.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem label="备注">
          <ElInput v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
          <ElButton @click="cancel">取 消</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed, watch } from 'vue'
  import type { FormInstance } from 'element-plus'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useTable } from '@/composables/useTable'
  import { formatTime } from '@/utils/date'
  import { ContractCategoryApi } from '@/api/contract/category'
  import type {
    ContractCategory,
    ContractCategoryForm,
    ContractCategoryQueryParams
  } from '@/types/contract/category'

  // 页面状态
  const open = ref(false)
  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)
  const title = ref('')
  const categoryRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState: ContractCategoryQueryParams = {
    categoryName: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })
  const appliedFilters = reactive({ ...initialSearchState })

  // 表单数据
  const form = ref<ContractCategoryForm>({
    id: undefined,
    categoryName: '',
    categoryDesc: '',
    sortOrder: 0,
    status: '0',
    remark: ''
  })

  // 状态选项
  const statusOptions = ref([
    { value: '0', label: '正常' },
    { value: '1', label: '停用' }
  ])

  // 搜索表单配置项
  const formItems = computed(() => [
    {
      label: '分类名称',
      key: 'categoryName',
      type: 'input',
      props: { clearable: true, placeholder: '请输入分类名称' }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true, placeholder: '分类状态' },
      options: statusOptions.value
    }
  ])

  // 表单验证规则
  const rules = reactive({
    categoryName: [
      { required: true, message: '分类名称不能为空', trigger: 'blur' },
      { min: 2, max: 30, message: '分类名称长度必须介于 2 和 30 之间', trigger: 'blur' }
    ],
    categoryDesc: [{ max: 200, message: '分类描述长度不能超过 200 个字符', trigger: 'blur' }]
  })

  // 使用useTable组合式函数
  const {
    data: categoryList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<ContractCategory>({
    core: {
      apiFn: (params: any) => ContractCategoryApi.getCategoryList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        categoryName: '',
        status: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      immediate: true,
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          type: 'index',
          label: '序号',
          width: 80,
          checked: true,
          disabled: true
        },
        {
          prop: 'categoryName',
          label: '分类名称',
          minWidth: 150,
          showOverflowTooltip: true,
          checked: true
        },
        {
          prop: 'categoryDesc',
          label: '分类描述',
          minWidth: 200,
          showOverflowTooltip: true,
          checked: true
        },
        {
          prop: 'sortOrder',
          label: '显示顺序',
          width: 100,
          checked: true
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          checked: true,
          useSlot: true
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180,
          formatter: (row: ContractCategory) => formatTime(row.createTime) || '--',
          checked: true
        },
        {
          prop: 'action',
          label: '操作',
          width: 160,
          fixed: 'right',
          checked: true,
          disabled: true,
          useSlot: true
        }
      ]
    }
  })

  // 添加表格引用
  const tableRef = ref()

  // 简化的数据加载监听（仅用于调试）
  watch(
    () => categoryList.value,
    (newData) => {
      if (newData && Array.isArray(newData)) {
        console.log('合同分类数据加载成功:', newData.length, '条记录')
      }
    },
    { immediate: true }
  )

  // 重置搜索表单
  function handleReset() {
    Object.assign(formFilters, initialSearchState)
    Object.assign(appliedFilters, initialSearchState)
    handleSearch()
  }

  // 执行搜索
  function handleSearch() {
    Object.assign(appliedFilters, formFilters)
    Object.assign(searchParams, appliedFilters, { pageNum: 1 })
    refreshData()
  }

  // 刷新数据
  function handleRefresh() {
    refreshData()
  }

  // 多选框选中数据
  function handleSelectionChange(selection: ContractCategory[]) {
    ids.value = selection.map((item) => item.id!).filter(Boolean)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增分类
  function handleAdd() {
    resetForm()
    open.value = true
    title.value = '添加合同分类'
  }

  // 修改分类
  async function handleUpdate(row?: ContractCategory) {
    const categoryId = row?.id || ids.value[0]

    if (!categoryId) {
      ElMessage.error('请选择要修改的分类')
      return
    }

    try {
      // 先重置表单
      resetForm()

      // 设置标题并打开对话框
      title.value = '修改合同分类'
      open.value = true

      // 异步加载数据
      const response = await ContractCategoryApi.getCategory(categoryId)
      console.log('API响应数据:', response)

      if (response && response.data) {
        const data = response.data.data

        // 设置表单数据
        form.value = {
          id: data.id,
          categoryName: data.categoryName || '',
          categoryDesc: data.categoryDesc || '',
          sortOrder: data.sortOrder || 0,
          status: data.status || '0',
          remark: data.remark || ''
        }
      } else {
        ElMessage.error('获取分类信息失败')
        open.value = false
      }
    } catch (error) {
      console.error('获取分类信息失败:', error)
      ElMessage.error('获取分类信息失败')
      open.value = false
    }
  }

  // 状态修改 - 简化版本，因为已有条件渲染保护
  function handleStatusChange(row: ContractCategory) {
    // 由于模板已有条件渲染，这里的数据一定是完整的
    const newStatus = row.status // ElSwitch已经切换后的新值
    const originalStatus = newStatus === '0' ? '1' : '0' // 计算出原始值

    // 立即恢复到原始状态
    row.status = originalStatus

    // 根据新状态确定操作文本
    const text = newStatus === '0' ? '启用' : '停用'
    const categoryName = row.categoryName || '该分类'

    ElMessageBox.confirm(`确认要"${text}""${categoryName}"分类吗？`, '系统提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        // 用户确认后，切换到新状态
        row.status = newStatus
        return ContractCategoryApi.updateCategory({
          id: row.id,
          status: newStatus
        })
      })
      .then(() => {
        ElMessage.success(text + '成功')
      })
      .catch(() => {
        // 用户取消或API失败时，保持原状态
        row.status = originalStatus
      })
  }

  // 删除分类
  async function handleDelete(row?: ContractCategory) {
    const categoryIds = row ? [row.id!] : ids.value
    const names = row ? row.categoryName : '选中的分类'

    try {
      await ElMessageBox.confirm(`是否确认删除"${names}"？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ContractCategoryApi.delCategory(categoryIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      console.error('删除失败:', error)
    }
  }

  // 提交表单
  function submitForm() {
    categoryRef.value?.validate(async (valid: boolean) => {
      if (!valid) return

      try {
        if (form.value.id) {
          await ContractCategoryApi.updateCategory(form.value)
          ElMessage.success('修改成功')
        } else {
          await ContractCategoryApi.addCategory(form.value)
          ElMessage.success('新增成功')
        }
        open.value = false
        refreshData()
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    })
  }

  // 取消按钮
  function cancel() {
    open.value = false
    resetForm()
  }

  // 重置表单
  function resetForm() {
    form.value = {
      id: undefined,
      categoryName: '',
      categoryDesc: '',
      sortOrder: 0,
      status: '0',
      remark: ''
    }
    categoryRef.value?.resetFields()
  }
</script>

<style lang="scss" scoped>
  .dialog-footer {
    text-align: right;
  }

  .tree-border {
    margin-top: 5px;
    border: 1px solid #e5e6e7;
    background: #ffffff none;
    border-radius: 4px;
    width: 100%;
  }
</style>
