<template>
  <div class="category-cards-container">
    <!-- 左侧分类列表 -->
    <div class="left-panel">
      <!-- 工具栏 -->
      <div class="toolbar">
        <ElInput
          v-model="searchKeyword"
          placeholder="搜索分类名称"
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          class="search-input"
        />
        <ElButton type="primary" @click="handleAdd" v-ripple>
          <ElIcon><Plus /></ElIcon>
          新增分类
        </ElButton>
      </div>

      <!-- 分类卡片列表 -->
      <div class="category-list" v-loading="loading">
        <div v-if="filteredCategoryList.length === 0 && !loading" class="empty-state">
          <ElEmpty description="暂无分类数据" />
        </div>
        <div
          v-for="category in filteredCategoryList"
          :key="category.id"
          class="category-card"
          :class="{ active: selectedCategory?.id === category.id }"
          @click="handleSelectCategory(category)"
        >
          <ElCard shadow="hover" :body-style="{ padding: '16px' }">
            <div class="card-header">
              <div class="category-name">{{ category.categoryName }}</div>
              <ElSwitch
                v-model="category.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(category)"
                @click.stop
                size="small"
              />
            </div>
            <div class="category-desc">
              {{ category.categoryDesc || '暂无描述' }}
            </div>
          </ElCard>
        </div>
      </div>
    </div>

    <!-- 右侧详情面板 -->
    <div class="right-panel">
      <div v-if="!selectedCategory" class="no-selection">
        <ElEmpty description="请选择一个分类查看详情" />
      </div>
      <div v-else class="category-detail">
        <!-- 详情头部 -->
        <div class="detail-header">
          <div class="detail-title">
            <h2>{{ selectedCategory.categoryName }}</h2>
          </div>
          <div class="detail-actions">
            <ElButton
              v-auth="'contract:contract:edit'"
              type="primary"
              @click="handleUpdate(selectedCategory)"
              v-ripple
            >
              <ElIcon><Edit /></ElIcon>
              编辑
            </ElButton>
            <ElButton
              v-auth="'contract:contract:remove'"
              type="danger"
              @click="handleDelete(selectedCategory)"
              v-ripple
            >
              <ElIcon><Delete /></ElIcon>
              删除
            </ElButton>
          </div>
        </div>

        <!-- 详情内容 -->
        <div class="detail-content">
          <!-- 基本信息卡片 -->
          <ElCard class="info-card" shadow="never">
            <template #header>
              <div class="card-title">
                <ElIcon><InfoFilled /></ElIcon>
                <span>基本信息</span>
              </div>
            </template>

            <div class="info-grid">
              <!-- 分类名称 -->
              <div class="info-item">
                <div class="info-label">分类名称</div>
                <div class="info-value">{{ selectedCategory.categoryName }}</div>
              </div>

              <!-- 状态 -->
              <div class="info-item">
                <div class="info-label">状态</div>
                <div class="info-value">
                  <ElTag
                    :type="selectedCategory.status === '0' ? 'success' : 'danger'"
                    size="large"
                  >
                    {{ selectedCategory.status === '0' ? '正常' : '停用' }}
                  </ElTag>
                </div>
              </div>
            </div>
          </ElCard>

          <!-- 描述信息卡片 -->
          <ElCard class="description-card" shadow="never">
            <template #header>
              <div class="card-title">
                <ElIcon><Document /></ElIcon>
                <span>分类描述</span>
              </div>
            </template>

            <div class="description-content">
              <p v-if="selectedCategory.categoryDesc" class="description-text">
                {{ selectedCategory.categoryDesc }}
              </p>
              <div v-else class="no-description">
                <ElIcon><DocumentRemove /></ElIcon>
                <span>暂无描述信息</span>
              </div>
            </div>
          </ElCard>
        </div>
      </div>
    </div>

    <!-- 添加或修改合同分类对话框 -->
    <ElDialog :title="title" v-model="open" width="500px" append-to-body>
      <ElForm ref="categoryRef" :model="form" :rules="rules" label-width="100px">
        <ElFormItem label="分类名称" prop="categoryName">
          <ElInput v-model="form.categoryName" placeholder="请输入分类名称" />
        </ElFormItem>
        <ElFormItem label="分类描述" prop="categoryDesc">
          <ElInput
            v-model="form.categoryDesc"
            type="textarea"
            :rows="4"
            placeholder="请输入分类描述"
          />
        </ElFormItem>
        <ElFormItem label="显示顺序" prop="sortOrder">
          <ElInputNumber
            v-model="form.sortOrder"
            controls-position="right"
            :min="0"
            placeholder="请输入显示顺序"
          />
        </ElFormItem>
        <ElFormItem label="状态">
          <ElRadioGroup v-model="form.status">
            <ElRadio v-for="dict in statusOptions" :key="dict.value" :value="dict.value">
              {{ dict.label }}
            </ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem label="备注" prop="remark">
          <ElInput v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="cancel">取 消</ElButton>
          <ElButton type="primary" @click="submitForm">确 定</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, computed } from 'vue'
  import type { FormInstance } from 'element-plus'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Search,
    Plus,
    Edit,
    Delete,
    InfoFilled,
    Document,
    DocumentRemove
  } from '@element-plus/icons-vue'
  import { ContractCategoryApi } from '@/api/contract/category'
  import type {
    ContractCategory,
    ContractCategoryForm,
    ContractCategoryQueryParams
  } from '@/types/contract/category'

  // 页面状态
  const loading = ref(false)
  const open = ref(false)
  const title = ref('')
  const categoryRef = ref<FormInstance>()
  const searchKeyword = ref('')
  const selectedCategory = ref<ContractCategory | null>(null)

  // 分类列表数据
  const categoryList = ref<ContractCategory[]>([])

  // 字典数据
  const statusOptions = ref([
    { label: '正常', value: '0' },
    { label: '停用', value: '1' }
  ])

  // 表单数据
  const form = reactive<ContractCategoryForm>({
    id: undefined,
    categoryName: '',
    categoryDesc: '',
    sortOrder: 0,
    status: '0',
    remark: ''
  })

  // 表单验证规则
  const rules = reactive({
    categoryName: [
      { required: true, message: '分类名称不能为空', trigger: 'blur' },
      { min: 2, max: 30, message: '分类名称长度必须介于 2 和 30 之间', trigger: 'blur' }
    ],
    categoryDesc: [{ max: 200, message: '分类描述长度不能超过 200 个字符', trigger: 'blur' }]
  })

  // 过滤后的分类列表
  const filteredCategoryList = computed(() => {
    if (!searchKeyword.value) {
      return categoryList.value
    }
    return categoryList.value.filter((category) =>
      category.categoryName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  })

  // 获取分类列表
  const getCategoryList = async () => {
    loading.value = true
    try {
      const params: ContractCategoryQueryParams = {
        pageNum: 1,
        pageSize: 1000, // 获取所有数据
        categoryName: '',
        status: ''
      }
      const response = await ContractCategoryApi.getCategoryList(params)

      // 修复：从response.data.rows获取数据，因为http返回的是axios响应对象
      categoryList.value = response.data?.rows || []

      // 如果有选中的分类，更新选中状态
      if (selectedCategory.value) {
        const updatedCategory = categoryList.value.find(
          (item) => item.id === selectedCategory.value?.id
        )
        if (updatedCategory) {
          selectedCategory.value = updatedCategory
        } else {
          selectedCategory.value = null
        }
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      ElMessage.error('获取分类列表失败')
    } finally {
      loading.value = false
    }
  }

  // 选择分类
  const handleSelectCategory = (category: ContractCategory) => {
    selectedCategory.value = category
  }

  // 搜索处理
  const handleSearch = () => {
    // 搜索时重置选中状态
    if (selectedCategory.value) {
      const isInFiltered = filteredCategoryList.value.some(
        (item) => item.id === selectedCategory.value?.id
      )
      if (!isInFiltered) {
        selectedCategory.value = null
      }
    }
  }

  // 重置表单
  const reset = () => {
    form.id = undefined
    form.categoryName = ''
    form.categoryDesc = ''
    form.sortOrder = 0
    form.status = '0'
    form.remark = ''
    categoryRef.value?.resetFields()
  }

  // 新增分类
  const handleAdd = () => {
    reset()
    open.value = true
    title.value = '添加合同分类'
  }

  // 修改分类
  const handleUpdate = (row?: ContractCategory) => {
    reset()
    const category = row || selectedCategory.value
    if (!category || !category.id) return

    ContractCategoryApi.getCategory(category.id).then((response) => {
      // 修复：从response.data.data获取数据，因为getCategory返回单个对象在data字段中
      const data = response.data?.data || response.data
      form.id = data.id
      form.categoryName = data.categoryName
      form.categoryDesc = data.categoryDesc || ''
      form.sortOrder = data.sortOrder || 0
      form.status = data.status || '0'
      form.remark = data.remark || ''
      open.value = true
      title.value = '修改合同分类'
    })
  }

  // 提交表单
  const submitForm = () => {
    categoryRef.value?.validate(async (valid: boolean) => {
      if (valid) {
        try {
          if (form.id) {
            await ContractCategoryApi.updateCategory(form)
            ElMessage.success('修改成功')
          } else {
            await ContractCategoryApi.addCategory(form)
            ElMessage.success('新增成功')
          }
          open.value = false
          getCategoryList()
        } catch (error) {
          console.error('操作失败:', error)
          ElMessage.error('操作失败')
        }
      }
    })
  }

  // 取消操作
  const cancel = () => {
    open.value = false
    reset()
  }

  // 删除分类
  const handleDelete = (row?: ContractCategory) => {
    const category = row || selectedCategory.value
    if (!category || !category.id) return

    const ids = [category.id]
    ElMessageBox.confirm('是否确认删除分类编号为"' + ids.join(',') + '"的数据项？')
      .then(async () => {
        try {
          await ContractCategoryApi.delCategory(ids)
          ElMessage.success('删除成功')
          // 如果删除的是当前选中的分类，清空选中状态
          if (selectedCategory.value?.id === category.id) {
            selectedCategory.value = null
          }
          getCategoryList()
        } catch (error) {
          console.error('删除失败:', error)
          ElMessage.error('删除失败')
        }
      })
      .catch(() => {})
  }

  // 状态切换
  const handleStatusChange = async (row: ContractCategory) => {
    try {
      const text = row.status === '0' ? '启用' : '停用'
      await ElMessageBox.confirm('确认要"' + text + '""' + row.categoryName + '"分类吗？')

      await ContractCategoryApi.updateCategory({
        id: row.id,
        categoryName: row.categoryName,
        categoryDesc: row.categoryDesc,
        sortOrder: row.sortOrder,
        status: row.status,
        remark: row.remark
      })

      ElMessage.success(text + '成功')

      // 更新选中分类的状态
      if (selectedCategory.value?.id === row.id) {
        selectedCategory.value.status = row.status
      }
    } catch (error) {
      // 恢复原状态
      row.status = row.status === '0' ? '1' : '0'
      console.error('状态切换失败:', error)
    }
  }

  // 页面初始化
  const init = () => {
    getCategoryList()
  }

  // 组件挂载时初始化
  init()
</script>

<style scoped lang="scss">
  .category-cards-container {
    display: flex;
    height: calc(100vh - 120px);
    gap: 20px;
    padding: 20px;
    background-color: var(--el-bg-color-page);

    .left-panel {
      width: 350px;
      display: flex;
      flex-direction: column;
      background: var(--el-bg-color);
      border-radius: 8px;
      padding: 16px;
      box-shadow: var(--el-box-shadow-light);

      .toolbar {
        display: flex;
        gap: 12px;
        margin-bottom: 16px;
        align-items: center;

        .search-input {
          flex: 1;
        }
      }

      .category-list {
        flex: 1;
        overflow-y: auto;

        .empty-state {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
        }

        .category-card {
          margin-bottom: 12px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }

          &.active {
            .el-card {
              border-color: var(--el-color-primary);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
            }
          }

          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .category-name {
              font-weight: 600;
              font-size: 16px;
              color: var(--el-text-color-primary);
            }
          }

          .category-desc {
            color: var(--el-text-color-regular);
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      background: var(--el-bg-color);
      border-radius: 8px;
      padding: 24px;
      box-shadow: var(--el-box-shadow-light);

      .no-selection {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }

      .category-detail {
        height: 100%;
        display: flex;
        flex-direction: column;

        .detail-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 24px;
          padding-bottom: 16px;
          border-bottom: 1px solid var(--el-border-color-light);

          .detail-title {
            h2 {
              margin: 0;
              font-size: 24px;
              font-weight: 600;
              color: var(--el-text-color-primary);
            }
          }

          .detail-actions {
            display: flex;
            gap: 12px;
          }
        }

        .detail-content {
          flex: 1;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 20px;

          .info-card,
          .description-card {
            border: 1px solid var(--el-border-color-lighter);

            .card-title {
              display: flex;
              align-items: center;
              gap: 8px;
              font-weight: 600;
              color: var(--el-text-color-primary);

              .el-icon {
                color: var(--el-color-primary);
              }
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;

            .info-item {
              .info-label {
                font-size: 14px;
                color: var(--el-text-color-secondary);
                margin-bottom: 8px;
                font-weight: 500;
              }

              .info-value {
                font-size: 16px;
                color: var(--el-text-color-primary);
                font-weight: 600;
              }
            }
          }

          .description-content {
            .description-text {
              font-size: 15px;
              line-height: 1.6;
              color: var(--el-text-color-primary);
              margin: 0;
              white-space: pre-wrap;
              word-break: break-word;
            }

            .no-description {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              padding: 40px 20px;
              color: var(--el-text-color-placeholder);
              font-size: 14px;

              .el-icon {
                font-size: 20px;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .category-cards-container {
      flex-direction: column;
      height: auto;

      .left-panel {
        width: 100%;
        height: 300px;
      }

      .right-panel {
        min-height: 400px;
      }
    }
  }

  @media (max-width: 768px) {
    .category-cards-container {
      padding: 12px;
      gap: 12px;

      .left-panel {
        padding: 12px;
        height: 250px;
      }

      .right-panel {
        padding: 16px;

        .detail-content {
          .info-grid {
            grid-template-columns: 1fr;
            gap: 16px;
          }

          .detail-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;

            .detail-actions {
              width: 100%;
              justify-content: flex-start;
            }
          }
        }
      }
    }
  }
</style>
