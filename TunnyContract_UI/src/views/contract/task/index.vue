<template>
  <div class="contract-task-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'contract:task:add'" type="primary" @click="handleAdd" v-ripple>
            新建审查任务
          </ElButton>
          <ElButton
            v-auth="'contract:task:remove'"
            type="danger"
            :disabled="multiple"
            @click="() => handleDelete()"
            v-ripple
          >
            批量删除
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="id"
        :loading="loading"
        :columns="columns"
        :data="taskList"
        :stripe="true"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @pagination:size-change="handleSizeChange"
        @pagination:current-change="handleCurrentChange"
      >
        <template #taskName="{ row }">
          <span>
            {{ row.taskName }}
          </span>
        </template>

        <template #taskStatus="{ row }">
          <ElTag :type="getStatusTagType(row.taskStatus)" size="small">
            {{ getStatusLabel(row.taskStatus) }}
          </ElTag>
        </template>

        <template #fileStatus="{ row }">
          <div v-if="row.totalFiles && row.totalFiles > 0" class="file-status-container">
            <ElTag :type="getFileStatusTagType(row.fileParseStatus)" size="small">
              {{ getFileStatusText(row) }}
            </ElTag>
            <div class="file-progress" v-if="row.totalFiles > 0">
              <span class="progress-text">{{ row.parsedFiles || 0 }}/{{ row.totalFiles }}</span>
              <ElProgress
                :percentage="getFileProgressPercentage(row)"
                :status="getFileProgressStatus(row.fileParseStatus)"
                :stroke-width="4"
                :show-text="false"
                style="margin-top: 2px"
              />
            </div>
          </div>
          <span v-else class="text-gray-400">无文件</span>
        </template>

        <template #action="{ row }">
          <el-tooltip content="查看详情" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:task:query'"
              link
              type="primary"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </el-tooltip>

          <el-tooltip v-if="row.taskStatus === '2'" content="查看报告" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:task:query'"
              link
              type="success"
              @click="handleViewReport(row)"
            >
              报告
            </el-button>
          </el-tooltip>

          <el-tooltip v-if="row.taskStatus === '3'" content="重新审查" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:task:edit'"
              link
              type="warning"
              @click="handleStartReview(row)"
            >
              重试
            </el-button>
          </el-tooltip>

          <el-tooltip
            v-if="row.taskStatus === '0'"
            :content="getStartReviewTooltip(row)"
            placement="top"
          >
            <el-button
              v-ripple
              v-auth="'contract:task:edit'"
              link
              type="success"
              :disabled="!canStartReview(row)"
              @click="handleStartReview(row)"
            >
              启动
            </el-button>
          </el-tooltip>

          <el-tooltip content="删除" placement="top">
            <el-button
              v-ripple
              v-auth="'contract:task:remove'"
              link
              type="danger"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-tooltip>
        </template>
      </ArtTable>
    </ElCard>

    <!-- 新建审查任务对话框 -->
    <ElDialog :title="dialogTitle" v-model="dialogVisible" width="800px" align-center>
      <ElForm ref="taskFormRef" :model="taskForm" :rules="taskFormRules" label-width="120px">
        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="任务名称" prop="taskName">
              <ElInput
                v-model="taskForm.taskName"
                placeholder="请输入任务名称"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <ElFormItem label="合同类别" prop="categoryId">
              <ElSelect
                v-model="taskForm.categoryId"
                placeholder="请选择合同类别"
                @change="handleCategoryChange"
                style="width: 100%"
              >
                <ElOption
                  v-for="category in categoryOptions"
                  :key="category.id"
                  :label="category.categoryName"
                  :value="category.id!"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="审查立场" prop="reviewPosition">
              <ElRadioGroup v-model="taskForm.reviewPosition" @change="handlePositionChange">
                <ElRadio
                  v-for="option in REVIEW_POSITION_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </ElRadio>
              </ElRadioGroup>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="审查策略" prop="strategyId">
              <ElSelect
                v-model="taskForm.strategyId"
                placeholder="请先选择合同类别和审查立场"
                :loading="strategyLoading"
                style="width: 100%"
              >
                <ElOption
                  v-for="strategy in strategyOptions"
                  :key="strategy.id"
                  :label="strategy.strategyName"
                  :value="strategy.id!"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElFormItem label="合同文件" prop="fileList">
              <ElUpload
                ref="fileUploadRef"
                v-model:file-list="taskForm.fileList"
                action="#"
                :auto-upload="false"
                :show-file-list="true"
                :limit="10"
                accept=".pdf,.doc,.docx"
                multiple
              >
                <ElButton type="primary">选择文件</ElButton>
                <template #tip>
                  <div class="el-upload__tip"> 支持 PDF、DOC、DOCX 格式，最多上传10个文件 </div>
                </template>
              </ElUpload>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>

      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="handleDialogCancel">取消</ElButton>
          <ElButton type="primary" @click="handleDialogSubmit" :loading="submitLoading">
            确定
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, h, onMounted } from 'vue'
  import { ElMessage, ElMessageBox, ElTag, ElProgress } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useTable } from '@/composables/useTable'
  import { ContractReviewTaskApi } from '@/api/contract/task'
  import { ContractCategoryApi } from '@/api/contract/category'
  import type { ContractReviewTask, ContractReviewTaskForm } from '@/types/contract/task'
  import type { ContractCategory } from '@/types/contract/category'
  import type { ContractReviewStrategy } from '@/types/contract/strategy'
  import type { FormInstance, FormRules } from 'element-plus'
  import {
    TASK_STATUS_LABELS,
    TASK_STATUS_TAG_TYPE,
    REVIEW_POSITION_OPTIONS
  } from '@/types/contract/task'

  defineOptions({ name: 'ContractTask' })

  // Vue Router实例
  const router = useRouter()

  // 定义表单搜索初始值
  const initialSearchState = {
    taskName: '',
    categoryId: undefined,
    reviewPosition: '',
    taskStatus: '',
    createBy: '',
    beginTime: '',
    endTime: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  const ids = ref<number[]>([])
  const single = ref(true)
  const multiple = ref(true)
  const categoryOptions = ref<ContractCategory[]>([])

  // 弹窗相关数据
  const dialogVisible = ref(false)
  const taskFormRef = ref<FormInstance>()
  const fileUploadRef = ref()
  const submitLoading = ref(false)
  const strategyLoading = ref(false)
  const strategyOptions = ref<ContractReviewStrategy[]>([])

  // 任务表单数据
  const taskForm = reactive<ContractReviewTaskForm & { fileList: any[] }>({
    taskName: '',
    categoryId: undefined,
    strategyId: undefined,
    reviewPosition: '',
    fileList: []
  })

  // 表单验证规则
  const taskFormRules: FormRules = {
    taskName: [
      { required: true, message: '请输入任务名称', trigger: 'blur' },
      { min: 2, max: 200, message: '任务名称长度在 2 到 200 个字符', trigger: 'blur' }
    ],
    categoryId: [{ required: true, message: '请选择合同类别', trigger: 'change' }],
    reviewPosition: [{ required: true, message: '请选择审查立场', trigger: 'change' }],
    strategyId: [{ required: true, message: '请选择审查策略', trigger: 'change' }]
  }

  const dialogTitle = computed(() => '新建审查任务')

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(searchParams, { ...initialSearchState, pageNum: 1 })
    refreshData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(searchParams, { ...formFilters, pageNum: 1 })
    refreshData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '任务名称',
      key: 'taskName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '合同类别',
      key: 'categoryId',
      type: 'select',
      props: { clearable: true },
      options:
        categoryOptions.value?.map((item) => ({
          label: item.categoryName,
          value: item.id!
        })) || []
    },
    {
      label: '审查状态',
      key: 'taskStatus',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '待处理', value: '0' },
        { label: '处理中', value: '1' },
        { label: '已完成', value: '2' },
        { label: '失败', value: '3' }
      ]
    },
    {
      label: '审查立场',
      key: 'reviewPosition',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '甲方', value: '1' },
        { label: '乙方', value: '2' }
      ]
    },
    {
      label: '创建人',
      key: 'createBy',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '创建时间',
      key: 'createTime',
      type: 'daterange',
      props: {
        clearable: true,
        'start-placeholder': '开始日期',
        'end-placeholder': '结束日期'
      }
    }
  ])

  // 使用useTable组合式函数
  const {
    data: taskList,
    columns,
    columnChecks,
    loading,
    pagination,
    refreshData,
    handleSizeChange,
    handleCurrentChange,
    searchParams
  } = useTable<ContractReviewTask>({
    core: {
      apiFn: (params: any) => ContractReviewTaskApi.getTaskList(params),
      apiParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: '',
        categoryId: undefined,
        reviewPosition: '',
        taskStatus: '',
        createBy: ''
      },
      paginationKey: {
        current: 'pageNum',
        size: 'pageSize'
      },
      immediate: true,
      columnsFactory: () => [
        {
          type: 'selection',
          width: 55,
          checked: true,
          disabled: true
        },
        {
          type: 'index',
          label: '序号',
          width: 80,
          checked: true,
          disabled: true
        },
        {
          prop: 'taskName',
          label: '任务名称',
          minWidth: 120,
          checked: true,
          useSlot: true,
          slotName: 'taskName'
        },
        {
          prop: 'categoryName',
          label: '合同类别',
          width: 120,
          checked: true,
          formatter: (row: ContractReviewTask) => {
            return row.categoryName || '未知类别'
          }
        },
        {
          prop: 'strategyName',
          label: '审查策略',
          minWidth: 180,
          checked: true,
          formatter: (row: ContractReviewTask) => {
            return row.strategyName || '未设置策略'
          }
        },
        {
          prop: 'reviewPosition',
          label: '审查立场',
          width: 100,
          checked: true,
          formatter: (row: ContractReviewTask) => {
            return h(
              ElTag,
              {
                type: row.reviewPosition === '1' ? 'primary' : 'success',
                size: 'small'
              },
              () => (row.reviewPosition === '1' ? '甲方' : '乙方')
            )
          }
        },
        {
          prop: 'taskStatus',
          label: '状态',
          width: 100,
          checked: true,
          useSlot: true,
          slotName: 'taskStatus'
        },
        {
          prop: 'fileParseStatus',
          label: '文件状态',
          width: 150,
          checked: true,
          useSlot: true,
          slotName: 'fileStatus'
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 160,
          checked: true
        },
        {
          prop: 'createBy',
          label: '创建人',
          width: 100,
          checked: true
        },
        {
          prop: 'action',
          label: '操作',
          width: 200,
          checked: true,
          disabled: true,
          useSlot: true,
          slotName: 'action'
        }
      ]
    }
  })

  // 获取状态标签类型
  const getStatusTagType = (status: string) => {
    return TASK_STATUS_TAG_TYPE[status as keyof typeof TASK_STATUS_TAG_TYPE] || 'info'
  }

  // 获取状态标签文本
  const getStatusLabel = (status: string) => {
    return TASK_STATUS_LABELS[status as keyof typeof TASK_STATUS_LABELS] || '未知状态'
  }

  // 获取合同分类列表
  const getCategoryList = async () => {
    try {
      console.log('开始获取合同分类列表...')
      const response = await ContractCategoryApi.getCategoryList({
        pageNum: 1,
        pageSize: 100,
        status: '0' // 只获取启用状态的分类
      })
      console.log('合同分类API响应:', response)

      // 使用统一的异常处理机制，依赖HTTP拦截器
      categoryOptions.value = (response.data as any)?.rows || []
      console.log('合同分类数据设置完成:', categoryOptions.value)
    } catch (error) {
      console.error('获取合同分类失败:', error)
      ElMessage.error('获取合同分类失败')
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    refreshData()
  }

  // 选择变化
  const handleSelectionChange = (selection: ContractReviewTask[]) => {
    ids.value = selection.map((item) => item.id!)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }

  // 新增任务
  const handleAdd = () => {
    resetTaskForm()
    dialogVisible.value = true
  }

  // 查看详情
  const handleViewDetail = (row: ContractReviewTask) => {
    router.push(`/contract/task/detail/${row.id}`)
  }

  // 查看报告
  const handleViewReport = (row: ContractReviewTask) => {
    router.push(`/contract/task/report/${row.id}`)
  }

  // 开始审查
  const handleStartReview = async (row: ContractReviewTask) => {
    try {
      await ElMessageBox.confirm(`确认开始审查任务"${row.taskName}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ContractReviewTaskApi.startReview(row.id!)
      ElMessage.success('审查任务已启动')
      refreshData()
    } catch (error) {
      console.error('启动审查失败:', error)
      ElMessage.error('启动失败')
    }
  }

  // 删除任务
  const handleDelete = async (row?: ContractReviewTask) => {
    const taskIds = row ? [row.id!] : ids.value
    const taskNames = row
      ? [row.taskName]
      : taskList.value.filter((item) => taskIds.includes(item.id!)).map((item) => item.taskName)

    try {
      await ElMessageBox.confirm(`确认删除任务"${taskNames.join('、')}"？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ContractReviewTaskApi.deleteTask(taskIds)
      ElMessage.success('删除成功')
      refreshData()
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }

  // 获取审查策略列表
  const getStrategyList = async () => {
    if (!taskForm.categoryId || !taskForm.reviewPosition) {
      strategyOptions.value = []
      return
    }

    strategyLoading.value = true
    try {
      const response = await ContractReviewTaskApi.getAvailableStrategies(
        taskForm.categoryId,
        taskForm.reviewPosition
      )
      // 处理数据结构：检查是否有data包装层
      const actualData = response.data || response
      if (actualData.code === 200) {
        strategyOptions.value = actualData.rows || []
      }
    } catch (error) {
      console.error('获取审查策略失败:', error)
      ElMessage.error('获取审查策略失败')
    } finally {
      strategyLoading.value = false
    }
  }

  // 处理合同类别变化
  const handleCategoryChange = () => {
    taskForm.strategyId = undefined
    strategyOptions.value = []
    getStrategyList()
  }

  // 处理审查立场变化
  const handlePositionChange = () => {
    taskForm.strategyId = undefined
    strategyOptions.value = []
    getStrategyList()
  }

  // 重置任务表单
  const resetTaskForm = () => {
    taskForm.taskName = ''
    taskForm.categoryId = undefined
    taskForm.strategyId = undefined
    taskForm.reviewPosition = ''
    taskForm.fileList = []
    strategyOptions.value = []

    // 重置表单验证状态
    taskFormRef.value?.resetFields()
  }

  // 弹窗取消
  const handleDialogCancel = () => {
    dialogVisible.value = false
    resetTaskForm()
  }

  // 弹窗提交
  const handleDialogSubmit = async () => {
    if (!taskFormRef.value) return

    try {
      await taskFormRef.value.validate()

      submitLoading.value = true

      // 提取文件列表
      const files = taskForm.fileList.map((item) => item.raw).filter(Boolean) as File[]

      // 准备任务数据（排除fileList）
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { fileList, ...taskData } = taskForm

      // 根据是否有文件选择不同的API
      if (files.length > 0) {
        await ContractReviewTaskApi.addTaskWithFiles(taskData, files)
        ElMessage.success(`任务创建成功，已上传${files.length}个文件`)
      } else {
        await ContractReviewTaskApi.addTask(taskData)
        ElMessage.success('任务创建成功')
      }

      dialogVisible.value = false
      resetTaskForm()
      refreshData()
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage.error('任务创建失败')
    } finally {
      submitLoading.value = false
    }
  }

  // 文件状态相关方法
  const getFileStatusTagType = (status: string) => {
    switch (status) {
      case '0':
        return 'info' // 未开始
      case '1':
        return 'warning' // 解析中
      case '2':
        return 'success' // 全部成功
      case '3':
        return 'danger' // 部分失败
      default:
        return 'info'
    }
  }

  const getFileStatusText = (row: ContractReviewTask) => {
    switch (row.fileParseStatus) {
      case '0':
        return '未开始'
      case '1':
        return '解析中'
      case '2':
        return '解析完成'
      case '3':
        return '部分失败'
      default:
        return '未知状态'
    }
  }

  const getFileProgressPercentage = (row: ContractReviewTask) => {
    if (!row.totalFiles || row.totalFiles === 0) return 0
    const parsedFiles = row.parsedFiles || 0
    return Math.round((parsedFiles / row.totalFiles) * 100)
  }

  const getFileProgressStatus = (status: string) => {
    switch (status) {
      case '2':
        return 'success' // 全部成功
      case '3':
        return 'exception' // 部分失败
      default:
        return undefined
    }
  }

  // 判断是否可以开始审查
  const canStartReview = (row: ContractReviewTask) => {
    return row.taskStatus === '0' && row.fileParseStatus === '2' && (row.totalFiles || 0) > 0
  }

  // 获取开始审查按钮的提示文本
  const getStartReviewTooltip = (row: ContractReviewTask) => {
    if (row.taskStatus !== '0') {
      return '任务状态不允许启动审查'
    }

    if (!row.totalFiles || row.totalFiles === 0) {
      return '请先上传合同文件'
    }

    switch (row.fileParseStatus) {
      case '0':
        return '文件尚未开始解析，请等待'
      case '1':
        return '文件正在解析中，请等待解析完成'
      case '2':
        return '开始审查'
      case '3':
        return '部分文件解析失败，建议检查后再启动'
      default:
        return '文件状态未知，请刷新后重试'
    }
  }

  // 页面初始化
  onMounted(() => {
    getCategoryList()
  })
</script>

<style scoped lang="scss">
  .contract-task-page {
    .task-name-link {
      color: #409eff;
      cursor: pointer;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .file-status-container {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .file-progress {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .progress-text {
        font-size: 12px;
        color: #666;
        text-align: center;
      }
    }
  }

  .polling-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;

    .last-update-time {
      color: #666;
      white-space: nowrap;
    }

    .el-icon-loading {
      animation: rotating 2s linear infinite;
    }

    @keyframes rotating {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
</style>
