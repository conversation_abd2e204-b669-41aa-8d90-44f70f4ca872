import { App, Directive, DirectiveBinding } from 'vue'
import { useAuth } from '@/composables/useAuth'

/**
 * 权限指令
 * 直接支持RuoYi权限标识格式
 * 用法：
 * <el-button v-auth="'system:user:add'">新增用户</el-button>
 * <el-button v-auth="['system:user:edit', 'system:user:remove']">批量操作</el-button>
 */

interface AuthBinding extends DirectiveBinding {
  value: string | string[]
}

function checkAuthPermission(el: HTMLElement, binding: AuthBinding): void {
  const { hasAuth, hasPermiOr } = useAuth()
  let hasPermission = false

  if (typeof binding.value === 'string') {
    // 单个权限验证
    hasPermission = hasAuth(binding.value)
  } else if (Array.isArray(binding.value)) {
    // 多个权限验证（只需要其中一个）
    hasPermission = hasPermiOr(binding.value)
  }

  // 如果没有权限，移除元素
  if (!hasPermission) {
    removeElement(el)
  }
}

function removeElement(el: HTMLElement): void {
  if (el.parentNode) {
    el.parentNode.removeChild(el)
  }
}

const authDirective: Directive = {
  mounted: checkAuthPermission,
  updated: checkAuthPermission
}

export function setupAuthDirective(app: App): void {
  app.directive('auth', authDirective)
}
