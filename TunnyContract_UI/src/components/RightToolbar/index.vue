<template>
  <div class="top-right-btn" :style="style">
    <el-row>
      <el-tooltip
        class="item"
        effect="dark"
        :content="showSearch ? '隐藏搜索' : '显示搜索'"
        placement="top"
        v-if="search"
      >
        <el-button circle icon="Search" @click="() => toggleSearch()" />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="刷新" placement="top">
        <el-button circle icon="Refresh" @click="() => refresh()" />
      </el-tooltip>
      <el-tooltip
        class="item"
        effect="dark"
        content="显隐列"
        placement="top"
        v-if="Object.keys(columns).length > 0"
      >
        <el-button
          circle
          icon="Menu"
          @click="() => showColumn()"
          v-if="showColumnsType == 'transfer'"
        />
        <el-dropdown
          trigger="click"
          :hide-on-click="false"
          style="padding-left: 12px"
          v-if="showColumnsType == 'checkbox'"
        >
          <el-button circle icon="Menu" />
          <template #dropdown>
            <el-dropdown-menu>
              <!-- 全选/反选 按钮 -->
              <el-dropdown-item>
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="isChecked"
                  @change="() => toggleCheckAll()"
                >
                  列展示
                </el-checkbox>
              </el-dropdown-item>
              <div class="check-line"></div>
              <template v-for="(item, key) in columns" :key="item.key">
                <el-dropdown-item>
                  <el-checkbox
                    v-model="(item as ColumnItem).visible"
                    @change="(event: any) => checkboxChange(event, key)"
                    :label="(item as ColumnItem).label"
                  />
                </el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-tooltip>
    </el-row>
    <el-dialog :title="title" v-model="open" append-to-body>
      <el-transfer
        :titles="['显示', '隐藏']"
        v-model="value"
        :data="transferData"
        @change="dataChange"
      ></el-transfer>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'

  interface ColumnItem {
    key: string
    label: string
    visible: boolean
  }

  // interface TransferData {
  //   key: number
  //   label: string
  // }

  // const { TransferData } = ElTransfer

  interface Props {
    /** 是否显示检索条件 */
    showSearch?: boolean
    /** 显隐列信息（数组格式、对象格式） */
    columns?: ColumnItem[] | Record<string, ColumnItem>
    /** 是否显示检索图标 */
    search?: boolean
    /** 显隐列类型（transfer穿梭框、checkbox复选框） */
    showColumnsType?: 'transfer' | 'checkbox'
    /** 右外边距 */
    gutter?: number
  }

  interface Emits {
    (e: 'update:showSearch', value: boolean): void
    (e: 'update:columns', value: any): void
    (e: 'queryTable'): void
  }

  const props = withDefaults(defineProps<Props>(), {
    showSearch: true,
    columns: () => ({}),
    search: true,
    showColumnsType: 'checkbox',
    gutter: 10
  })

  const emit = defineEmits<Emits>()

  // 显隐数据
  const value = ref<number[]>([])
  // 弹出层标题
  const title = ref('显示/隐藏')
  // 是否显示弹出层
  const open = ref(false)

  const style = computed(() => {
    const ret: Record<string, string> = {}
    if (props.gutter) {
      ret.marginRight = `${props.gutter / 2}px`
    }
    return ret
  })

  // 是否全选/半选 状态
  const isChecked = computed({
    get: () =>
      Array.isArray(props.columns)
        ? props.columns.every((col) => col.visible)
        : Object.values(props.columns).every((col) => col.visible),
    set: () => {}
  })

  const isIndeterminate = computed(() =>
    Array.isArray(props.columns)
      ? props.columns.some((col) => col.visible) && !isChecked.value
      : Object.values(props.columns).some((col) => col.visible) && !isChecked.value
  )

  const transferData = computed(() =>
    Array.isArray(props.columns)
      ? props.columns.map((item, index) => ({ key: index, label: item.label }))
      : Object.keys(props.columns).map((key, index) => ({
          key: index,
          label: (props.columns as Record<string, ColumnItem>)[key].label
        }))
  )

  // 搜索
  function toggleSearch(): void {
    emit('update:showSearch', !props.showSearch)
  }

  // 刷新
  function refresh(): void {
    emit('queryTable')
  }

  // 右侧列表元素变化
  function dataChange(data: any[]): void {
    const updateData: any = {}
    if (Array.isArray(props.columns)) {
      for (let item in props.columns) {
        const key = props.columns[item].key
        updateData[key] = !data.includes(parseInt(item))
      }
    } else {
      Object.keys(props.columns).forEach((key, index) => {
        updateData[key] = !data.includes(index)
      })
    }
    emit('update:columns', updateData)
  }

  // 打开显隐列dialog
  function showColumn(): void {
    open.value = true
  }

  if (props.showColumnsType == 'transfer') {
    // transfer穿梭显隐列初始默认隐藏列
    if (Array.isArray(props.columns)) {
      for (let item in props.columns) {
        if (props.columns[item].visible === false) {
          value.value.push(parseInt(item))
        }
      }
    } else {
      Object.keys(props.columns).forEach((_key, index) => {
        if ((props.columns as Record<string, ColumnItem>)[_key].visible === false) {
          value.value.push(index)
        }
      })
    }
  }

  // 单勾选
  function checkboxChange(event: any, key: string | number): void {
    const updateData: any = {}
    if (Array.isArray(props.columns)) {
      const targetColumn = props.columns.filter((item) => item.key == key)[0]
      if (targetColumn) {
        updateData[key] = event
      }
    } else {
      updateData[key] = event
    }
    emit('update:columns', updateData)
  }

  // 切换全选/反选
  function toggleCheckAll(): void {
    const newValue = !isChecked.value
    const updateData: any = {}
    if (Array.isArray(props.columns)) {
      props.columns.forEach((col) => {
        updateData[col.key] = newValue
      })
    } else {
      Object.keys(props.columns).forEach((key) => {
        updateData[key] = newValue
      })
    }
    emit('update:columns', updateData)
  }
</script>

<style lang="scss" scoped>
  :deep(.el-transfer__button) {
    border-radius: 50%;
    display: block;
    margin-left: 0px;
  }
  :deep(.el-transfer__button:first-child) {
    margin-bottom: 10px;
  }
  :deep(.el-dropdown-menu__item) {
    line-height: 30px;
    padding: 0 17px;
  }
  .check-line {
    width: 90%;
    height: 1px;
    background-color: #ccc;
    margin: 3px auto;
  }
</style>
