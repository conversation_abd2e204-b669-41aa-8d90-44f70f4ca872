<template>
  <div class="demo-route-settings">
    <SectionTitle :title="$t('setting.demoRoute.title')" />
    <div class="setting-item-wrapper">
      <SettingItem
        :config="demoRouteConfig"
        :model-value="showDemoRoutes"
        @change="handleDemoRouteChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import SectionTitle from './SectionTitle.vue'
  import SettingItem from './SettingItem.vue'
  import { useSettingStore } from '@/store/modules/setting'
  import { useMenuStore } from '@/store/modules/menu'
  import { storeToRefs } from 'pinia'
  import { useI18n } from 'vue-i18n'
  import { ElMessage } from 'element-plus'
  import { computed } from 'vue'

  const { t } = useI18n()
  const settingStore = useSettingStore()
  const menuStore = useMenuStore()
  const { showDemoRoutes } = storeToRefs(settingStore)

  // 演示路由设置配置
  const demoRouteConfig = computed(() => ({
    type: 'switch' as const,
    key: 'showDemoRoutes',
    label: t('setting.demoRoute.showDemo'),
    description: t('setting.demoRoute.description'),
    handler: 'setShowDemoRoutes'
  }))

  // 处理演示路由显示切换
  const handleDemoRouteChange = (value: boolean) => {
    settingStore.setShowDemoRoutes(value)

    // 重新设置菜单列表以应用过滤
    const currentMenuList = [...menuStore.menuList]
    menuStore.setMenuList(currentMenuList)

    // 显示切换提示
    ElMessage({
      type: 'success',
      message: value ? t('setting.demoRoute.showSuccess') : t('setting.demoRoute.hideSuccess'),
      duration: 2000
    })
  }
</script>

<style lang="scss" scoped>
  .demo-route-settings {
    margin-bottom: 30px;

    .setting-item-wrapper {
      background: transparent !important;
    }
  }
</style>
