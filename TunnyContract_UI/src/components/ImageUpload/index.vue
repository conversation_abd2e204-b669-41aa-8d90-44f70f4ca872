<template>
  <div class="component-upload-image">
    <el-upload
      multiple
      :disabled="disabled"
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :data="data"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUploadRef"
      :before-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && !disabled">
      请上传
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      的文件
    </div>

    <el-dialog v-model="dialogVisible" title="预览" width="800px" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch /*onMounted, nextTick, getCurrentInstance*/ } from 'vue'
  import { Plus } from '@element-plus/icons-vue'
  import { getToken } from '@/utils/auth'
  import { isExternal } from '@/utils/validation'
  import { ElMessage, ElLoading } from 'element-plus'
  import type { UploadFile } from 'element-plus'
  // import { FileApi } from '@/api/common/file'

  interface ImageItem {
    name: string
    url: string
  }

  interface Props {
    /** 绑定值 */
    modelValue?: string | ImageItem | ImageItem[]
    /** 上传接口地址 */
    action?: string
    /** 上传携带的参数 */
    data?: Record<string, any>
    /** 图片数量限制 */
    limit?: number
    /** 大小限制(MB) */
    fileSize?: number
    /** 文件类型, 例如['png', 'jpg', 'jpeg'] */
    fileType?: string[]
    /** 是否显示提示 */
    isShowTip?: boolean
    /** 禁用组件（仅查看图片） */
    disabled?: boolean
    /** 拖动排序 */
    drag?: boolean
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void
  }

  const props = withDefaults(defineProps<Props>(), {
    action: '/common/upload',
    limit: 5,
    fileSize: 5,
    fileType: () => ['png', 'jpg', 'jpeg'],
    isShowTip: true,
    disabled: false,
    drag: true
  })

  const emit = defineEmits<Emits>()

  // const { proxy } = getCurrentInstance()!
  const number = ref(0)
  const uploadList = ref<ImageItem[]>([])
  const dialogImageUrl = ref('')
  const dialogVisible = ref(false)
  const baseUrl = import.meta.env.VITE_APP_BASE_API
  const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + props.action)
  const headers = ref({ Authorization: 'Bearer ' + getToken() })
  const fileList = ref<ImageItem[]>([])
  const imageUploadRef = ref()

  const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize))

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        // 首先将值转为数组
        const list = Array.isArray(val) ? val : (props.modelValue as string).split(',')
        // 然后将数组转为对象数组
        fileList.value = list.map((item) => {
          if (typeof item === 'string') {
            if (item.indexOf(baseUrl) === -1 && !isExternal(item)) {
              item = { name: baseUrl + item, url: baseUrl + item }
            } else {
              item = { name: item, url: item }
            }
          }
          return item
        })
      } else {
        fileList.value = []
      }
    },
    { deep: true, immediate: true }
  )

  // 上传前loading加载
  function handleBeforeUpload(file: File): boolean {
    let isImg = false
    if (props.fileType && props.fileType.length) {
      let fileExtension = ''
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      isImg = props.fileType.some((type) => {
        if (file.type.indexOf(type) > -1) return true
        if (fileExtension && fileExtension.indexOf(type) > -1) return true
        return false
      })
    } else {
      isImg = file.type.indexOf('image') > -1
    }
    if (!isImg) {
      ElMessage.error(`文件格式不正确，请上传${props.fileType?.join('/')}图片格式文件!`)
      return false
    }
    if (file.name.includes(',')) {
      ElMessage.error('文件名不正确，不能包含英文逗号!')
      return false
    }
    if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize
      if (!isLt) {
        ElMessage.error(`上传头像图片大小不能超过 ${props.fileSize} MB!`)
        return false
      }
    }
    ElLoading.service({ text: '正在上传图片，请稍候...' })
    number.value++
    return true
  }

  // 文件个数超出
  function handleExceed(): void {
    ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`)
  }

  // 上传成功回调
  function handleUploadSuccess(res: any, file: UploadFile): void {
    if (res.code === 200) {
      uploadList.value.push({
        name: res.data?.originalFilename || res.fileName || file.name,
        url: res.data?.fileName || res.fileName
      })
      uploadedSuccessfully()
    } else {
      number.value--
      ElLoading.service().close()
      ElMessage.error(res.msg || '上传失败')
      imageUploadRef.value?.handleRemove(file)
      uploadedSuccessfully()
    }
  }

  // 删除图片
  function handleDelete(file: UploadFile): boolean {
    const findex = fileList.value.map((f) => f.name).indexOf(file.name!)
    if (findex > -1 && uploadList.value.length === number.value) {
      fileList.value.splice(findex, 1)
      emit('update:modelValue', listToString(fileList.value))
      return false
    }
    return true
  }

  // 上传结束处理
  function uploadedSuccessfully(): void {
    if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter((f) => f.url !== undefined).concat(uploadList.value)
      uploadList.value = []
      number.value = 0
      emit('update:modelValue', listToString(fileList.value))
      ElLoading.service().close()
    }
  }

  // 上传失败
  function handleUploadError(): void {
    ElMessage.error('上传图片失败')
    ElLoading.service().close()
  }

  // 预览
  function handlePictureCardPreview(file: UploadFile): void {
    dialogImageUrl.value = file.url!
    dialogVisible.value = true
  }

  // 对象转成指定字符串分隔
  function listToString(list: ImageItem[], separator = ','): string {
    let strs = ''
    for (let i in list) {
      if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0) {
        strs += list[i].url.replace(baseUrl, '') + separator
      }
    }
    return strs != '' ? strs.substring(0, strs.length - 1) : ''
  }

  // 初始化拖拽排序 - 暂时注释掉，需要安装sortablejs
  // onMounted(() => {
  //   if (props.drag && !props.disabled) {
  //     nextTick(() => {
  //       const element = imageUploadRef.value?.$el?.querySelector('.el-upload-list')
  //       // Sortable.create(element, {
  //       //   onEnd: (evt) => {
  //       //     const movedItem = fileList.value.splice(evt.oldIndex, 1)[0]
  //       //     fileList.value.splice(evt.newIndex, 0, movedItem)
  //       //     emit('update:modelValue', listToString(fileList.value))
  //       //   }
  //       // })
  //     })
  //   }
  // })
</script>

<style scoped lang="scss">
  // .el-upload--picture-card 控制加号部分
  :deep(.hide .el-upload--picture-card) {
    display: none;
  }

  :deep(.el-upload.el-upload--picture-card.is-disabled) {
    display: none !important;
  }
</style>
