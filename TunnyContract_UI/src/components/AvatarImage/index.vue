<template>
  <img
    :src="displaySrc"
    :alt="alt"
    :class="className"
    :style="style"
    @error="handleError"
    @load="handleLoad"
  />
</template>

<script setup lang="ts">
  // 导入默认头像
  import defaultAvatar from '@/assets/img/user/profile.jpg'

  interface Props {
    src?: string | null
    alt?: string
    className?: string
    style?: Record<string, any>
    defaultSrc?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    src: '',
    alt: 'avatar',
    className: '',
    style: () => ({}),
    defaultSrc: ''
  })

  const hasError = ref(false)

  // 计算显示的图片源
  const displaySrc = computed(() => {
    // 如果发生错误或者原始src为空，使用默认头像
    if (hasError.value || !props.src) {
      return props.defaultSrc || defaultAvatar
    }
    return props.src
  })

  // 处理图片加载错误
  const handleError = () => {
    console.warn('Avatar image failed to load:', props.src)
    hasError.value = true
  }

  // 处理图片加载成功
  const handleLoad = () => {
    hasError.value = false
  }

  // 监听 src 变化，重置错误状态
  watch(
    () => props.src,
    () => {
      hasError.value = false
    }
  )
</script>

<style scoped>
  img {
    transition: opacity 0.3s ease;
  }
</style>
