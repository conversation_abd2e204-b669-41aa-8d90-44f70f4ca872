<template>
  <div>
    <template v-for="(item, index) in options" :key="item.value">
      <template v-if="values.includes(item.value)">
        <span
          v-if="
            (!item.elTagType || item.elTagType === '') &&
            (!item.elTagClass || item.elTagClass === '')
          "
          :index="index"
          :class="item.elTagClass"
          >{{ item.label + ' ' }}</span
        >
        <el-tag
          v-else
          :disable-transitions="true"
          :index="index"
          :type="
            item.elTagType && item.elTagType !== '' && item.elTagType !== 'default'
              ? (item.elTagType as any)
              : undefined
          "
          :class="item.elTagClass"
          >{{ item.label + ' ' }}</el-tag
        >
      </template>
    </template>
    <template v-if="unmatch && showValue">
      {{ handleArray(unmatchArray) }}
    </template>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue'
  import type { DictOption } from '@/types/system/dict'

  interface Props {
    /** 字典选项数据 */
    options: DictOption[]
    /** 当前的值 */
    value: string | number | Array<string | number>
    /** 当未找到匹配的数据时，显示value */
    showValue?: boolean
    /** 分隔符 */
    separator?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    showValue: true,
    separator: ','
  })

  // 记录未匹配的项
  const unmatchArray = ref<Array<string | number>>([])
  const unmatch = ref(false)

  const values = computed(() => {
    if (props.value === null || typeof props.value === 'undefined' || props.value === '') return []
    return Array.isArray(props.value)
      ? props.value.map((item) => '' + item)
      : String(props.value).split(props.separator)
  })

  // 使用watch来处理副作用，避免在computed中修改ref
  watch(
    [() => props.value, () => props.options, values],
    () => {
      const tempUnmatchArray: Array<string | number> = []
      // 没有value不显示
      if (
        props.value === null ||
        typeof props.value === 'undefined' ||
        props.value === '' ||
        !Array.isArray(props.options) ||
        props.options.length === 0
      ) {
        unmatchArray.value = tempUnmatchArray
        unmatch.value = false
        return
      }
      // 传入值为数组
      let hasUnmatch = false // 添加一个标志来判断是否有未匹配项
      values.value.forEach((item) => {
        if (!props.options.some((v) => v.value === item)) {
          tempUnmatchArray.push(item)
          hasUnmatch = true // 如果有未匹配项，将标志设置为true
        }
      })
      unmatchArray.value = tempUnmatchArray
      unmatch.value = hasUnmatch // 返回标志的值
    },
    { immediate: true }
  )

  function handleArray(array: Array<string | number>): string {
    if (array.length === 0) return ''
    return array.reduce((pre: string, cur) => {
      return pre + ' ' + String(cur)
    }, '')
  }
</script>

<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>
