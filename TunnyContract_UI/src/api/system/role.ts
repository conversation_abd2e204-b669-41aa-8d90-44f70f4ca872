import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { Role, RoleQueryParams } from '@/types/system/role'
import type { User } from '@/types/system/user'
import type { Dept } from '@/types/system/dept'

/**
 * 角色管理API类
 * 完全保持RuoYi的角色接口格式
 */
export class RoleApi {
  /**
   * 查询角色列表
   * @param params 查询参数
   * @returns 角色列表响应
   */
  static async getRoleList(params: RoleQueryParams): Promise<RuoyiResponse<Role>> {
    return http.get('/system/role/list', { params })
  }

  /**
   * 查询角色详细信息
   * @param roleId 角色ID
   * @returns 角色详情响应
   */
  static async getRole(roleId: number): Promise<
    RuoyiResponse<{
      data: Role
      menuIds: number[]
      deptIds: number[]
    }>
  > {
    return http.get(`/system/role/${roleId}`)
  }

  /**
   * 新增角色
   * @param data 角色数据
   * @returns 操作结果
   */
  static async addRole(data: Partial<Role>): Promise<RuoyiResponse> {
    return http.post('/system/role', data)
  }

  /**
   * 修改角色
   * @param data 角色数据
   * @returns 操作结果
   */
  static async updateRole(data: Partial<Role>): Promise<RuoyiResponse> {
    return http.put('/system/role', data)
  }

  /**
   * 删除角色
   * @param roleIds 角色ID数组
   * @returns 操作结果
   */
  static async delRole(roleIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(roleIds) ? roleIds.join(',') : roleIds
    return http.delete(`/system/role/${ids}`)
  }

  /**
   * 修改角色状态
   * @param roleId 角色ID
   * @param status 状态（0正常 1停用）
   * @returns 操作结果
   */
  static async changeRoleStatus(roleId: number, status: string): Promise<RuoyiResponse> {
    return http.put('/system/role/changeStatus', { roleId, status })
  }

  /**
   * 角色数据权限设置
   * @param data 数据权限数据
   * @returns 操作结果
   */
  static async dataScope(data: {
    roleId: number
    roleName: string
    roleKey: string
    dataScope: string
    deptIds: number[]
  }): Promise<RuoyiResponse> {
    return http.put('/system/role/dataScope', data)
  }

  /**
   * 查询角色已授权用户列表
   * @param params 查询参数
   * @returns 已授权用户列表响应
   */
  static async allocatedUserList(params: {
    roleId: number
    userName?: string
    phonenumber?: string
    pageNum?: number
    pageSize?: number
  }): Promise<RuoyiResponse<User>> {
    return http.get('/system/role/authUser/allocatedList', { params })
  }

  /**
   * 查询角色未授权用户列表
   * @param params 查询参数
   * @returns 未授权用户列表响应
   */
  static async unallocatedUserList(params: {
    roleId: number
    userName?: string
    phonenumber?: string
    pageNum?: number
    pageSize?: number
  }): Promise<RuoyiResponse<User>> {
    return http.get('/system/role/authUser/unallocatedList', { params })
  }

  /**
   * 取消用户授权角色
   * @param data 取消授权数据
   * @returns 操作结果
   */
  static async authUserCancel(data: { roleId: number; userId: number }): Promise<RuoyiResponse> {
    return http.put('/system/role/authUser/cancel', data)
  }

  /**
   * 批量取消用户授权角色
   * @param data 批量取消授权数据
   * @returns 操作结果
   */
  static async authUserCancelAll(data: {
    roleId: number
    userIds: string
  }): Promise<RuoyiResponse> {
    return http.put('/system/role/authUser/cancelAll', null, { params: data })
  }

  /**
   * 批量选择用户授权
   * @param data 批量授权数据
   * @returns 操作结果
   */
  static async authUserSelectAll(data: {
    roleId: number
    userIds: string
  }): Promise<RuoyiResponse> {
    return http.put('/system/role/authUser/selectAll', null, { params: data })
  }

  /**
   * 根据角色ID查询菜单树结构
   * @param roleId 角色ID
   * @returns 菜单树响应
   */
  static async roleMenuTreeselect(roleId: number): Promise<
    RuoyiResponse<{
      menus: any[]
      checkedKeys: number[]
    }>
  > {
    return http.get(`/system/menu/roleMenuTreeselect/${roleId}`)
  }

  /**
   * 根据角色ID查询部门树结构
   * @param roleId 角色ID
   * @returns 部门树响应
   */
  static async deptTreeSelect(roleId: number): Promise<RuoyiResponse<Dept[]>> {
    return http.get(`/system/role/deptTree/${roleId}`)
  }

  /**
   * 导出角色数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async exportRole(params: RoleQueryParams): Promise<Blob> {
    const response = await http.post('/system/role/export', params, { responseType: 'blob' })
    return response.data
  }
}
