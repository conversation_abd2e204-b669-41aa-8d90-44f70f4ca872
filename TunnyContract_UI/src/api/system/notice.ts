import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { Notice, NoticeQueryParams } from '@/types/system/notice'

/**
 * 通知公告管理API类
 * 完全保持RuoYi的通知公告接口格式
 */
export class NoticeApi {
  /**
   * 查询通知公告列表
   * @param params 查询参数
   * @returns 通知公告列表响应
   */
  static async getNoticeList(params?: NoticeQueryParams): Promise<RuoyiResponse<Notice>> {
    return http.get('/system/notice/list', { params })
  }

  /**
   * 查询通知公告详细信息
   * @param noticeId 通知公告ID
   * @returns 通知公告详情响应
   */
  static async getNotice(noticeId: number): Promise<RuoyiResponse<Notice>> {
    return http.get(`/system/notice/${noticeId}`)
  }

  /**
   * 新增通知公告
   * @param data 通知公告数据
   * @returns 操作结果
   */
  static async addNotice(data: Partial<Notice>): Promise<RuoyiResponse> {
    return http.post('/system/notice', data)
  }

  /**
   * 修改通知公告
   * @param data 通知公告数据
   * @returns 操作结果
   */
  static async updateNotice(data: Partial<Notice>): Promise<RuoyiResponse> {
    return http.put('/system/notice', data)
  }

  /**
   * 删除通知公告
   * @param noticeIds 通知公告ID数组
   * @returns 操作结果
   */
  static async deleteNotice(noticeIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(noticeIds) ? noticeIds.join(',') : noticeIds
    return http.delete(`/system/notice/${ids}`)
  }

  /**
   * 导出通知公告数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async exportNotice(params?: NoticeQueryParams): Promise<Blob> {
    const response = await http.post('/system/notice/export', params, { responseType: 'blob' })
    return response.data
  }
}
