/**
 * 字典管理API
 * 基于RuoYi接口格式，支持字典类型和字典数据的完整CRUD操作
 */

import { http } from '@/utils/http'
import type {
  DictType,
  DictData,
  DictTypeQueryParams,
  DictDataQueryParams
} from '@/types/system/dict'
import type { RuoyiResponse } from '@/types/http'

export class DictApi {
  // ========== 字典类型管理 ==========

  /**
   * 查询字典类型列表
   */
  static getDictTypeList(params?: DictTypeQueryParams): Promise<RuoyiResponse<DictType>> {
    return http.get('/system/dict/type/list', { params })
  }

  /**
   * 查询字典类型详细信息
   */
  static getDictTypeDetail(dictId: number): Promise<RuoyiResponse<DictType>> {
    return http.get(`/system/dict/type/${dictId}`)
  }

  /**
   * 新增字典类型
   */
  static addDictType(data: DictType): Promise<RuoyiResponse<any>> {
    return http.post('/system/dict/type', data)
  }

  /**
   * 修改字典类型
   */
  static updateDictType(data: DictType): Promise<RuoyiResponse<any>> {
    return http.put('/system/dict/type', data)
  }

  /**
   * 删除字典类型
   */
  static deleteDictType(dictIds: number | number[]): Promise<RuoyiResponse<any>> {
    return http.delete(`/system/dict/type/${dictIds}`)
  }

  /**
   * 导出字典类型
   */
  static exportDictType(params?: DictTypeQueryParams): Promise<any> {
    return http.post('/system/dict/type/export', params, {
      responseType: 'blob'
    })
  }

  /**
   * 获取字典选择框列表
   */
  static getDictTypeOptions(): Promise<RuoyiResponse<DictType[]>> {
    return http.get('/system/dict/type/optionselect')
  }

  // ========== 字典数据管理 ==========

  /**
   * 查询字典数据列表
   */
  static getDictDataList(params?: DictDataQueryParams): Promise<RuoyiResponse<DictData>> {
    return http.get('/system/dict/data/list', { params })
  }

  /**
   * 查询字典数据详细信息
   */
  static getDictDataDetail(dictCode: number): Promise<RuoyiResponse<DictData>> {
    return http.get(`/system/dict/data/${dictCode}`)
  }

  /**
   * 根据字典类型查询字典数据信息
   */
  static getDictDataByType(dictType: string): Promise<RuoyiResponse<DictData[]>> {
    return http.get(`/system/dict/data/type/${dictType}`)
  }

  /**
   * 新增字典数据
   */
  static addDictData(data: DictData): Promise<RuoyiResponse<any>> {
    return http.post('/system/dict/data', data)
  }

  /**
   * 修改字典数据
   */
  static updateDictData(data: DictData): Promise<RuoyiResponse<any>> {
    return http.put('/system/dict/data', data)
  }

  /**
   * 删除字典数据
   */
  static deleteDictData(dictCodes: number | number[]): Promise<RuoyiResponse<any>> {
    return http.delete(`/system/dict/data/${dictCodes}`)
  }

  /**
   * 导出字典数据
   */
  static exportDictData(params?: DictDataQueryParams): Promise<any> {
    return http.post('/system/dict/data/export', params, {
      responseType: 'blob'
    })
  }

  // ========== 缓存管理 ==========

  /**
   * 刷新字典缓存
   */
  static refreshDictCache(): Promise<RuoyiResponse<any>> {
    return http.delete('/system/dict/type/refreshCache')
  }
}

// 导出默认实例（兼容旧的调用方式）
export const {
  getDictTypeList,
  getDictTypeDetail,
  addDictType,
  updateDictType,
  deleteDictType,
  exportDictType,
  getDictTypeOptions,
  getDictDataList,
  getDictDataDetail,
  getDictDataByType,
  addDictData,
  updateDictData,
  deleteDictData,
  exportDictData,
  refreshDictCache
} = DictApi

// 兼容RuoYi原有的函数名
export const listType = getDictTypeList
export const getType = getDictTypeDetail
export const delType = deleteDictType
export const optionselect = getDictTypeOptions
export const listData = getDictDataList
export const getData = getDictDataDetail
export const delData = deleteDictData
export const refreshCache = refreshDictCache
