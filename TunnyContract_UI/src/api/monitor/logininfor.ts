import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { LoginInfor, LoginInforQueryParams } from '@/types/system/logininfor'

/**
 * 登录日志管理API类
 * 完全保持RuoYi的登录日志接口格式
 */
export class LoginInforApi {
  /**
   * 查询登录日志列表
   * @param params 查询参数
   * @returns 登录日志列表响应
   */
  static async getLoginInforList(
    params?: LoginInforQueryParams
  ): Promise<RuoyiResponse<LoginInfor>> {
    return http.get('/monitor/logininfor/list', { params })
  }

  /**
   * 查询登录日志详细信息
   * @param infoId 登录日志ID
   * @returns 登录日志详情响应
   */
  static async getLoginInfor(infoId: number): Promise<RuoyiResponse<LoginInfor>> {
    return http.get(`/monitor/logininfor/${infoId}`)
  }

  /**
   * 删除登录日志
   * @param infoIds 登录日志ID数组
   * @returns 操作结果
   */
  static async deleteLoginInfor(infoIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(infoIds) ? infoIds.join(',') : infoIds
    return http.delete(`/monitor/logininfor/${ids}`)
  }

  /**
   * 解锁用户登录状态
   * @param userName 用户名
   * @returns 操作结果
   */
  static async unlockLoginInfor(userName: string): Promise<RuoyiResponse> {
    return http.get(`/monitor/logininfor/unlock/${userName}`)
  }

  /**
   * 清空登录日志
   * @returns 操作结果
   */
  static async cleanLoginInfor(): Promise<RuoyiResponse> {
    return http.delete('/monitor/logininfor/clean')
  }

  /**
   * 导出登录日志数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async exportLoginInfor(params?: LoginInforQueryParams): Promise<Blob> {
    const response = await http.post('/monitor/logininfor/export', params, { responseType: 'blob' })
    return response.data
  }
}
