import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { Cache, CacheName, CacheKeyValue } from '@/types/monitor/cache'

/**
 * 缓存监控API类
 * 完全保持RuoYi的缓存监控接口格式
 */
export class CacheApi {
  /**
   * 查询缓存详细信息
   * @returns 缓存信息响应
   */
  static async getCache(): Promise<RuoyiResponse<Cache>> {
    return http.get('/monitor/cache')
  }

  /**
   * 查询缓存名称列表
   * @returns 缓存名称列表响应
   */
  static async listCacheName(): Promise<RuoyiResponse<CacheName[]>> {
    return http.get('/monitor/cache/getNames')
  }

  /**
   * 查询缓存键名列表
   * @param cacheName 缓存名称
   * @returns 缓存键名列表响应
   */
  static async listCacheKey(cacheName: string): Promise<RuoyiResponse<string[]>> {
    return http.get(`/monitor/cache/getKeys/${cacheName}`)
  }

  /**
   * 查询缓存内容
   * @param cacheName 缓存名称
   * @param cacheKey 缓存键名
   * @returns 缓存值响应
   */
  static async getCacheValue(
    cacheName: string,
    cacheKey: string
  ): Promise<RuoyiResponse<CacheKeyValue>> {
    return http.get(`/monitor/cache/getValue/${cacheName}/${cacheKey}`)
  }

  /**
   * 清理指定名称缓存
   * @param cacheName 缓存名称
   * @returns 操作结果
   */
  static async clearCacheName(cacheName: string): Promise<RuoyiResponse> {
    return http.delete(`/monitor/cache/clearCacheName/${cacheName}`)
  }

  /**
   * 清理指定键名缓存
   * @param cacheKey 缓存键名
   * @returns 操作结果
   */
  static async clearCacheKey(cacheKey: string): Promise<RuoyiResponse> {
    return http.delete(`/monitor/cache/clearCacheKey/${cacheKey}`)
  }

  /**
   * 清理全部缓存
   * @returns 操作结果
   */
  static async clearCacheAll(): Promise<RuoyiResponse> {
    return http.delete('/monitor/cache/clearCacheAll')
  }
}
