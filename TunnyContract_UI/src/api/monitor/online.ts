import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { OnlineUser, OnlineUserQueryParams } from '@/types/monitor/online'

/**
 * 在线用户管理API类
 * 完全保持RuoYi的在线用户接口格式
 */
export class OnlineUserApi {
  /**
   * 查询在线用户列表
   * @param params 查询参数
   * @returns 在线用户列表响应
   */
  static async getOnlineUserList(
    params?: OnlineUserQueryParams
  ): Promise<RuoyiResponse<OnlineUser>> {
    return http.get('/monitor/online/list', { params })
  }

  /**
   * 强退在线用户
   * @param tokenId 会话Token ID
   * @returns 操作结果
   */
  static async forceLogout(tokenId: string): Promise<RuoyiResponse> {
    return http.delete(`/monitor/online/${tokenId}`)
  }

  /**
   * 导出在线用户数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async exportOnlineUser(params?: OnlineUserQueryParams): Promise<Blob> {
    const response = await http.post('/monitor/online/export', params, { responseType: 'blob' })
    return response.data
  }
}
