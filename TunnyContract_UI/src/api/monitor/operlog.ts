import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { OperLog, OperLogQueryParams } from '@/types/system/operlog'

/**
 * 操作日志管理API类
 * 完全保持RuoYi的操作日志接口格式
 */
export class OperLogApi {
  /**
   * 查询操作日志列表
   * @param params 查询参数
   * @returns 操作日志列表响应
   */
  static async getOperLogList(params?: OperLogQueryParams): Promise<RuoyiResponse<OperLog>> {
    return http.get('/monitor/operlog/list', { params })
  }

  /**
   * 删除操作日志
   * @param operIds 操作日志ID数组
   * @returns 操作结果
   */
  static async deleteOperLog(operIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(operIds) ? operIds.join(',') : operIds
    return http.delete(`/monitor/operlog/${ids}`)
  }

  /**
   * 清空操作日志
   * @returns 操作结果
   */
  static async cleanOperLog(): Promise<RuoyiResponse> {
    return http.delete('/monitor/operlog/clean')
  }

  /**
   * 导出操作日志数据
   * @param params 查询参数
   * @returns 文件流
   */
  static async exportOperLog(params?: OperLogQueryParams): Promise<Blob> {
    const response = await http.post('/monitor/operlog/export', params, { responseType: 'blob' })
    return response.data
  }
}
