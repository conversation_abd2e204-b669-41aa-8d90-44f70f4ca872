import { http } from '@/utils/http'
import type { JobQueryParams, JobForm } from '@/types/monitor/job'

/**
 * 定时任务API类
 */
export class JobApi {
  /**
   * 查询定时任务调度列表
   */
  static async getJobList(params: JobQueryParams) {
    const response = await http.get('/monitor/job/list', { params })
    return (response.data as any)?.data || response.data
  }

  /**
   * 查询定时任务调度详细
   */
  static async getJob(jobId: number) {
    const response = await http.get(`/monitor/job/${jobId}`)
    return (response.data as any)?.data || response.data
  }

  /**
   * 新增定时任务调度
   */
  static async addJob(data: JobForm) {
    const response = await http.post('/monitor/job', data)
    return (response.data as any)?.data || response.data
  }

  /**
   * 修改定时任务调度
   */
  static async updateJob(data: JobForm) {
    const response = await http.put('/monitor/job', data)
    return (response.data as any)?.data || response.data
  }

  /**
   * 删除定时任务调度
   */
  static async delJob(jobId: number | string) {
    const response = await http.delete(`/monitor/job/${jobId}`)
    return (response.data as any)?.data || response.data
  }

  /**
   * 任务状态修改
   */
  static async changeJobStatus(jobId: number, status: string) {
    const data = { jobId, status }
    const response = await http.put('/monitor/job/changeStatus', data)
    return (response.data as any)?.data || response.data
  }

  /**
   * 定时任务立即执行一次
   */
  static async runJob(jobId: number, jobGroup: string) {
    const data = { jobId, jobGroup }
    const response = await http.put('/monitor/job/run', data)
    return (response.data as any)?.data || response.data
  }

  /**
   * 导出定时任务
   */
  static async exportJob(params: JobQueryParams) {
    return await http.post('/monitor/job/export', params, {
      responseType: 'blob'
    })
  }
}
