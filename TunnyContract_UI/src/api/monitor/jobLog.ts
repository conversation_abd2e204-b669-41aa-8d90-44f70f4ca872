import { http } from '@/utils/http'
import type { JobLogQueryParams } from '@/types/monitor/jobLog'

/**
 * 定时任务调度日志API类
 */
export class JobLogApi {
  /**
   * 查询调度日志列表
   */
  static async getJobLogList(params: JobLogQueryParams) {
    const response = await http.get('/monitor/jobLog/list', { params })
    return (response.data as any)?.data || response.data
  }

  /**
   * 删除调度日志
   */
  static async delJobLog(jobLogId: number | string) {
    const response = await http.delete(`/monitor/jobLog/${jobLogId}`)
    return (response.data as any)?.data || response.data
  }

  /**
   * 清空调度日志
   */
  static async cleanJobLog() {
    const response = await http.delete('/monitor/jobLog/clean')
    return (response.data as any)?.data || response.data
  }

  /**
   * 导出调度日志
   */
  static async exportJobLog(params: JobLogQueryParams) {
    return await http.post('/monitor/jobLog/export', params, {
      responseType: 'blob'
    })
  }
}
