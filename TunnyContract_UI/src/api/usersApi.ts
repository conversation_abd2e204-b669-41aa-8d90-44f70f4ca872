import { http } from '@/utils/http'
import { LoginApi } from '@/api/auth/login'
import { UserMockService } from '@/mock/services/userMockService'

// Mock模式配置 - 用于演示功能
const DEMO_CONFIG = {
  // 基础表格使用mock模式
  useBasicTableMock: true,
  // 高级表格使用mock模式（扩展数据）
  useAdvancedTableMock: true
}

export class UserService {
  // 登录 - 使用RuoYi格式
  static login(params: Api.Auth.LoginParams) {
    return LoginApi.login({
      username: params.userName,
      password: params.password,
      code: params.code,
      uuid: params.uuid
    })
  }

  // 获取用户信息 - 使用RuoYi格式
  static getUserInfo() {
    return LoginApi.getUserInfo()
  }

  // 获取用户列表
  static getUserList(params: Api.Common.PaginatingSearchParams) {
    // 演示模式：使用mock数据
    if (DEMO_CONFIG.useBasicTableMock) {
      return UserMockService.getUserList(params)
    }

    // 生产模式：使用真实API
    return http.get<Api.User.UserListData>('/api/user/list', { params })
  }

  // 获取扩展用户列表（用于高级表格演示）
  static getExtendedUserList(params: Api.Common.PaginatingSearchParams) {
    // 演示模式：使用mock数据（大数据量）
    if (DEMO_CONFIG.useAdvancedTableMock) {
      return UserMockService.getExtendedUserList(params)
    }

    // 生产模式：使用真实API
    return http.get<Api.User.UserListData>('/api/user/list', { params })
  }

  // 切换mock模式的工具方法（用于调试）
  static toggleMockMode(type: 'basic' | 'advanced', enabled: boolean) {
    if (type === 'basic') {
      DEMO_CONFIG.useBasicTableMock = enabled
    } else if (type === 'advanced') {
      DEMO_CONFIG.useAdvancedTableMock = enabled
    }
    console.log(`${type} table mock mode ${enabled ? 'enabled' : 'disabled'}`)
  }

  // 获取当前mock模式状态
  static getMockStatus() {
    return {
      basicTableMock: DEMO_CONFIG.useBasicTableMock,
      advancedTableMock: DEMO_CONFIG.useAdvancedTableMock
    }
  }
}
