/**
 * 文件处理API
 * 保持RuoYi接口格式，支持文件上传、下载、预览等功能
 */

import { http } from '@/utils/http'
import { downloadBlob, handleExportResponse } from '@/utils/download'
import { ElLoading, ElMessage } from 'element-plus'
import type { RuoyiResponse } from '@/types/http'

/** 文件上传结果 */
export interface UploadResult {
  /** 文件名 */
  fileName: string
  /** 文件路径 */
  url: string
  /** 原始文件名 */
  originalFilename: string
  /** 文件大小 */
  size: number
}

/** 文件下载参数 */
export interface DownloadParams {
  /** 文件名 */
  fileName: string
  /** 是否删除源文件 */
  delete?: boolean
}

/** Excel导出参数 */
export interface ExcelExportParams {
  /** 导出数据 */
  data?: any[]
  /** 文件名 */
  filename?: string
  /** 工作表名 */
  sheetName?: string
}

/**
 * 文件处理API类
 */
export class FileApi {
  /**
   * 上传文件
   * @param file 文件对象
   * @param onProgress 上传进度回调
   * @returns 上传结果
   */
  static async uploadFile(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<RuoyiResponse<UploadResult>> {
    const formData = new FormData()
    formData.append('file', file)

    return http.post('/common/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
  }

  /**
   * 批量上传文件
   * @param files 文件列表
   * @param onProgress 上传进度回调
   * @returns 上传结果列表
   */
  static async uploadFiles(
    files: File[],
    onProgress?: (progress: number) => void
  ): Promise<RuoyiResponse<UploadResult[]>> {
    const formData = new FormData()
    files.forEach((file) => {
      formData.append('files', file)
    })

    return http.post('/common/uploads', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
  }

  /**
   * 下载文件
   * @param fileName 文件名
   * @param originalName 原始文件名（用于保存）
   */
  static async downloadFile(fileName: string, originalName?: string): Promise<void> {
    const loading = ElLoading.service({
      text: '正在下载文件，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const response = await http.get('/common/download', {
        params: { fileName },
        responseType: 'blob'
      })

      if (response.data instanceof Blob) {
        const downloadName = originalName || fileName.split('/').pop() || 'download'
        downloadBlob(response.data, downloadName)
        ElMessage.success('文件下载成功')
      } else {
        ElMessage.error('下载文件格式错误')
      }
    } catch (error) {
      console.error('下载文件失败:', error)
      ElMessage.error('下载文件失败')
    } finally {
      loading.close()
    }
  }

  /**
   * 删除文件
   * @param fileName 文件名
   */
  static async deleteFile(fileName: string): Promise<RuoyiResponse<void>> {
    return http.delete('/common/delete', {
      params: { fileName }
    })
  }

  /**
   * 获取文件信息
   * @param fileName 文件名
   */
  static async getFileInfo(fileName: string): Promise<RuoyiResponse<any>> {
    return http.get('/common/info', {
      params: { fileName }
    })
  }

  /**
   * 导出Excel文件
   * @param url 导出接口地址
   * @param params 导出参数
   * @param filename 文件名
   */
  static async exportExcel(
    url: string,
    params: Record<string, any> = {},
    filename?: string
  ): Promise<void> {
    const loading = ElLoading.service({
      text: '正在导出数据，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const response = await http.post(url, params, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      handleExportResponse(response, filename)
      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    } finally {
      loading.close()
    }
  }

  /**
   * 导入Excel文件
   * @param url 导入接口地址
   * @param file Excel文件
   * @param updateSupport 是否支持更新
   */
  static async importExcel(
    url: string,
    file: File,
    updateSupport = false
  ): Promise<RuoyiResponse<any>> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('updateSupport', updateSupport.toString())

    return http.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * 下载导入模板
   * @param url 模板下载地址
   * @param filename 文件名
   */
  static async downloadTemplate(url: string, filename?: string): Promise<void> {
    const loading = ElLoading.service({
      text: '正在下载模板，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const response = await http.get(url, {
        responseType: 'blob'
      })

      handleExportResponse(response, filename || 'template.xlsx')
      ElMessage.success('模板下载成功')
    } catch (error) {
      console.error('下载模板失败:', error)
      ElMessage.error('下载模板失败')
    } finally {
      loading.close()
    }
  }

  /**
   * 预览文件
   * @param fileName 文件名
   * @returns 预览URL
   */
  static getPreviewUrl(fileName: string): string {
    return `${import.meta.env.VITE_APP_BASE_API}/common/preview?fileName=${encodeURIComponent(fileName)}`
  }

  /**
   * 获取文件访问URL
   * @param fileName 文件名
   * @returns 文件访问URL
   */
  static getFileUrl(fileName: string): string {
    if (!fileName) return ''

    // 如果是完整URL，直接返回
    if (fileName.startsWith('http://') || fileName.startsWith('https://')) {
      return fileName
    }

    // 拼接基础URL
    return `${import.meta.env.VITE_APP_BASE_API}${fileName}`
  }

  /**
   * 验证文件类型
   * @param file 文件对象
   * @param allowedTypes 允许的文件类型
   * @returns 是否通过验证
   */
  static validateFileType(file: File, allowedTypes: string[]): boolean {
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    return allowedTypes.includes(fileExtension || '')
  }

  /**
   * 验证文件大小
   * @param file 文件对象
   * @param maxSize 最大大小（MB）
   * @returns 是否通过验证
   */
  static validateFileSize(file: File, maxSize: number): boolean {
    const fileSizeMB = file.size / 1024 / 1024
    return fileSizeMB <= maxSize
  }
}
