import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type {
  ContractReviewTask,
  ContractReviewTaskQueryParams,
  ContractReviewTaskForm
} from '@/types/contract/task'

/**
 * 合同审查任务管理API类
 * 遵循若依框架的API接口格式和命名规范
 */
export class ContractReviewTaskApi {
  /**
   * 查询合同审查任务列表
   * @param params 查询参数
   * @returns 任务列表响应
   */
  static async getTaskList(
    params?: ContractReviewTaskQueryParams
  ): Promise<RuoyiResponse<ContractReviewTask>> {
    return http.get('/contract/task/list', { params })
  }

  /**
   * 查询合同审查任务详细信息
   * @param taskId 任务ID
   * @returns 任务详情响应
   */
  static async getTask(taskId: number): Promise<RuoyiResponse<ContractReviewTask>> {
    return http.get(`/contract/task/${taskId}`)
  }

  /**
   * 新增合同审查任务
   * @param data 任务数据
   * @returns 操作结果
   */
  static async addTask(data: ContractReviewTaskForm): Promise<RuoyiResponse> {
    return http.post('/contract/task', data)
  }

  /**
   * 新增合同审查任务（支持文件上传）
   * @param taskData 任务数据
   * @param files 文件列表
   * @returns 操作结果
   */
  static async addTaskWithFiles(
    taskData: ContractReviewTaskForm,
    files: File[]
  ): Promise<RuoyiResponse> {
    const formData = new FormData()
    formData.append('taskData', JSON.stringify(taskData))

    files.forEach((file) => {
      formData.append('files', file)
    })

    return http.post('/contract/task/addWithFiles', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }

  /**
   * 修改合同审查任务
   * @param data 任务数据
   * @returns 操作结果
   */
  static async updateTask(data: ContractReviewTaskForm): Promise<RuoyiResponse> {
    return http.put('/contract/task', data)
  }

  /**
   * 删除合同审查任务
   * @param taskIds 任务ID数组
   * @returns 操作结果
   */
  static async deleteTask(taskIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(taskIds) ? taskIds.join(',') : taskIds
    return http.delete(`/contract/task/${ids}`)
  }

  /**
   * 开始审查任务
   * @param taskId 任务ID
   * @returns 操作结果
   */
  static async startReview(taskId: number): Promise<RuoyiResponse> {
    return http.post(`/contract/task/start/${taskId}`)
  }

  /**
   * 获取任务审查状态
   * @param taskId 任务ID
   * @returns 任务状态信息
   */
  static async getTaskStatus(taskId: number): Promise<RuoyiResponse<ContractReviewTask>> {
    return http.get(`/contract/task/status/${taskId}`)
  }

  /**
   * 获取任务审查结果
   * @param taskId 任务ID
   * @returns 审查结果详情
   */
  static async getReviewResult(taskId: number): Promise<RuoyiResponse> {
    return http.get(`/contract/task/result/${taskId}`)
  }

  /**
   * 取消审查任务
   * @param taskId 任务ID
   * @returns 操作结果
   */
  static async cancelReview(taskId: number): Promise<RuoyiResponse> {
    return http.post(`/contract/task/cancel/${taskId}`)
  }

  /**
   * 根据合同分类和审查立场获取可用策略列表
   * @param categoryId 合同分类ID
   * @param reviewPosition 审查立场
   * @returns 策略列表
   */
  static async getAvailableStrategies(
    categoryId: number,
    reviewPosition: string
  ): Promise<RuoyiResponse> {
    return http.get('/contract/task/strategies', {
      params: { categoryId, reviewPosition }
    })
  }

  /**
   * 获取任务统计信息
   * @param params 查询参数
   * @returns 统计信息
   */
  static async getTaskStatistics(params?: {
    beginTime?: string
    endTime?: string
    categoryId?: number
  }): Promise<RuoyiResponse> {
    return http.get('/contract/task/statistics', { params })
  }

  /**
   * 批量开始审查任务
   * @param taskIds 任务ID数组
   * @returns 操作结果
   */
  static async batchStartReview(taskIds: number[]): Promise<RuoyiResponse> {
    return http.post('/contract/task/batch/start', { taskIds })
  }

  /**
   * 批量取消审查任务
   * @param taskIds 任务ID数组
   * @returns 操作结果
   */
  static async batchCancelReview(taskIds: number[]): Promise<RuoyiResponse> {
    return http.post('/contract/task/batch/cancel', { taskIds })
  }
}
