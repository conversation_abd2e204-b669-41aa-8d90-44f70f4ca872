import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type {
  ContractRiskPoint,
  ContractRiskPointQueryParams,
  ContractRiskPointForm
} from '@/types/contract/riskPoint'

/**
 * 合同风险点管理API类
 * 遵循若依框架的API接口格式和命名规范
 */
export class ContractRiskPointApi {
  /**
   * 查询合同风险点列表
   * @param params 查询参数
   * @returns 风险点列表响应
   */
  static async getRiskPointList(
    params?: ContractRiskPointQueryParams
  ): Promise<RuoyiResponse<ContractRiskPoint>> {
    return http.get('/contract/point/list', { params })
  }

  /**
   * 根据条款ID查询风险点列表
   * @param clauseId 条款ID
   * @returns 风险点列表响应
   */
  static async getRiskPointListByClause(
    clauseId: number
  ): Promise<RuoyiResponse<ContractRiskPoint>> {
    return http.get('/contract/point/list', {
      params: { clauseId, pageNum: 1, pageSize: 1000 }
    })
  }

  /**
   * 查询合同风险点详细信息
   * @param riskPointId 风险点ID
   * @returns 风险点详情响应
   */
  static async getRiskPoint(riskPointId: number): Promise<RuoyiResponse<ContractRiskPoint>> {
    return http.get(`/contract/point/${riskPointId}`)
  }

  /**
   * 新增合同风险点
   * @param data 风险点数据
   * @returns 操作结果
   */
  static async addRiskPoint(data: ContractRiskPointForm): Promise<RuoyiResponse> {
    return http.post('/contract/point', data)
  }

  /**
   * 修改合同风险点
   * @param data 风险点数据
   * @returns 操作结果
   */
  static async updateRiskPoint(data: ContractRiskPointForm): Promise<RuoyiResponse> {
    return http.put('/contract/point', data)
  }

  /**
   * 删除合同风险点
   * @param riskPointIds 风险点ID数组
   * @returns 操作结果
   */
  static async delRiskPoint(riskPointIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(riskPointIds) ? riskPointIds.join(',') : riskPointIds
    return http.delete(`/contract/point/${ids}`)
  }

  /**
   * 启用/禁用风险点
   * @param riskPointId 风险点ID
   * @param status 状态 (1-启用，0-禁用)
   * @returns 操作结果
   */
  static async toggleRiskPointStatus(riskPointId: number, status: string): Promise<RuoyiResponse> {
    return http.put(`/contract/point/status/${riskPointId}`, { riskStatus: status })
  }

  /**
   * 复制风险点
   * @param riskPointId 源风险点ID
   * @param newRiskName 新风险点名称
   * @returns 操作结果
   */
  static async copyRiskPoint(riskPointId: number, newRiskName: string): Promise<RuoyiResponse> {
    return http.post(`/contract/point/copy/${riskPointId}`, { riskName: newRiskName })
  }

  /**
   * 根据风险等级统计风险点数量
   * @param clauseId 条款ID（可选）
   * @returns 统计数据
   */
  static async getRiskPointStats(clauseId?: number): Promise<
    RuoyiResponse<{
      totalCount: number
      majorRiskCount: number
      generalRiskCount: number
      enabledCount: number
      disabledCount: number
    }>
  > {
    const params = clauseId ? { clauseId } : {}
    return http.get('/contract/point/stats', { params })
  }

  /**
   * 批量设置风险点等级
   * @param riskPointIds 风险点ID数组
   * @param riskLevel 风险等级 (1-重大风险，2-一般风险)
   * @returns 操作结果
   */
  static async batchSetRiskLevel(
    riskPointIds: number[],
    riskLevel: string
  ): Promise<RuoyiResponse> {
    return http.put('/contract/point/batch-level', { riskPointIds, riskLevel })
  }
}
