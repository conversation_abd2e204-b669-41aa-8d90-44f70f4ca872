import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type {
  ContractReviewClause,
  ContractReviewClauseQueryParams,
  ContractReviewClauseForm
} from '@/types/contract/clause'

/**
 * 合同审查条款管理API类
 * 遵循若依框架的API接口格式和命名规范
 */
export class ContractReviewClauseApi {
  /**
   * 查询合同审查条款列表
   * @param params 查询参数
   * @returns 条款列表响应
   */
  static async getClauseList(
    params?: ContractReviewClauseQueryParams
  ): Promise<RuoyiResponse<ContractReviewClause>> {
    return http.get('/contract/clause/list', { params })
  }

  /**
   * 根据策略ID查询条款列表
   * @param strategyId 策略ID
   * @returns 条款列表响应
   */
  static async getClauseListByStrategy(
    strategyId: number
  ): Promise<RuoyiResponse<ContractReviewClause>> {
    return http.get('/contract/clause/list', {
      params: { strategyId, pageNum: 1, pageSize: 1000 }
    })
  }

  /**
   * 查询合同审查条款详细信息
   * @param clauseId 条款ID
   * @returns 条款详情响应
   */
  static async getClause(clauseId: number): Promise<RuoyiResponse<ContractReviewClause>> {
    return http.get(`/contract/clause/${clauseId}`)
  }

  /**
   * 新增合同审查条款
   * @param data 条款数据
   * @returns 操作结果
   */
  static async addClause(data: ContractReviewClauseForm): Promise<RuoyiResponse> {
    return http.post('/contract/clause', data)
  }

  /**
   * 修改合同审查条款
   * @param data 条款数据
   * @returns 操作结果
   */
  static async updateClause(data: ContractReviewClauseForm): Promise<RuoyiResponse> {
    return http.put('/contract/clause', data)
  }

  /**
   * 删除合同审查条款
   * @param clauseIds 条款ID数组
   * @returns 操作结果
   */
  static async delClause(clauseIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(clauseIds) ? clauseIds.join(',') : clauseIds
    return http.delete(`/contract/clause/${ids}`)
  }

  /**
   * 启用/禁用条款
   * @param clauseId 条款ID
   * @param status 状态 (1-启用，0-禁用)
   * @returns 操作结果
   */
  static async toggleClauseStatus(clauseId: number, status: string): Promise<RuoyiResponse> {
    return http.put(`/contract/clause/status/${clauseId}`, { clauseStatus: status })
  }

  /**
   * 复制条款
   * @param clauseId 源条款ID
   * @param newClauseName 新条款名称
   * @returns 操作结果
   */
  static async copyClause(clauseId: number, newClauseName: string): Promise<RuoyiResponse> {
    return http.post(`/contract/clause/copy/${clauseId}`, { clauseName: newClauseName })
  }
}
