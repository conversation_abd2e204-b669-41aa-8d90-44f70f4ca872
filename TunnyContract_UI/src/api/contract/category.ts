import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type { ContractCategory, ContractCategoryQueryParams } from '@/types/contract/category'

/**
 * 合同分类管理API类
 * 遵循若依框架的API接口格式和命名规范
 */
export class ContractCategoryApi {
  /**
   * 查询合同分类列表
   * @param params 查询参数
   * @returns 合同分类列表响应
   */
  static async getCategoryList(
    params?: ContractCategoryQueryParams
  ): Promise<RuoyiResponse<ContractCategory>> {
    return http.get('/contract/category/list', { params })
  }

  /**
   * 查询合同分类详细信息
   * @param categoryId 分类ID
   * @returns 合同分类详情响应
   */
  static async getCategory(categoryId: number): Promise<RuoyiResponse<ContractCategory>> {
    return http.get(`/contract/category/${categoryId}`)
  }

  /**
   * 新增合同分类
   * @param data 分类数据
   * @returns 操作结果
   */
  static async addCategory(data: Partial<ContractCategory>): Promise<RuoyiResponse> {
    return http.post('/contract/category', data)
  }

  /**
   * 修改合同分类
   * @param data 分类数据
   * @returns 操作结果
   */
  static async updateCategory(data: Partial<ContractCategory>): Promise<RuoyiResponse> {
    return http.put('/contract/category', data)
  }

  /**
   * 删除合同分类
   * @param categoryIds 分类ID数组
   * @returns 操作结果
   */
  static async delCategory(categoryIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(categoryIds) ? categoryIds.join(',') : categoryIds
    return http.delete(`/contract/category/${ids}`)
  }
}
