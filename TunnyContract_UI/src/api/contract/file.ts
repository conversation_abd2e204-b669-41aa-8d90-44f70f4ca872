import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'

/**
 * 文件解析状态信息
 */
export interface FileParseStatus {
  /** 总文件数 */
  totalFiles: number
  /** 已解析文件数 */
  parsedFiles: number
  /** 解析失败文件数 */
  failedFiles: number
  /** 解析中文件数 */
  parsingFiles: number
  /** 待解析文件数 */
  pendingFiles: number
  /** 文件解析汇总状态 (0-未开始，1-解析中，2-全部成功，3-部分失败) */
  fileParseStatus: string
  /** 文件列表 */
  files?: any[]
}

/**
 * 合同文件管理API类
 */
export class ContractFileApi {
  /**
   * 获取任务的文件解析状态
   * @param taskId 任务ID
   * @returns 文件解析状态信息
   */
  static async getTaskFileStatus(taskId: number): Promise<RuoyiResponse<FileParseStatus>> {
    return http.get(`/contract/file/taskStatus/${taskId}`)
  }

  /**
   * 根据任务ID查询合同文件列表
   * @param taskId 任务ID
   * @returns 文件列表和统计信息
   */
  static async getFilesByTaskId(taskId: number): Promise<RuoyiResponse> {
    return http.get(`/contract/file/task/${taskId}`)
  }

  /**
   * 获取文件统计信息
   * @param taskId 任务ID（可选）
   * @returns 统计信息
   */
  static async getStatistics(taskId?: number): Promise<RuoyiResponse> {
    const params = taskId ? { taskId } : {}
    return http.get('/contract/file/statistics', { params })
  }
}
