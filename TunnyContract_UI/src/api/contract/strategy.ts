import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type {
  ContractReviewStrategy,
  ContractReviewStrategyQueryParams,
  ContractReviewStrategyForm
} from '@/types/contract/strategy'

/**
 * 合同审查策略管理API类
 * 遵循若依框架的API接口格式和命名规范
 */
export class ContractReviewStrategyApi {
  /**
   * 查询合同审查策略列表
   * @param params 查询参数
   * @returns 策略列表响应
   */
  static async getStrategyList(
    params?: ContractReviewStrategyQueryParams
  ): Promise<RuoyiResponse<ContractReviewStrategy>> {
    return http.get('/contract/strategy/list', { params })
  }

  /**
   * 查询合同审查策略详细信息
   * @param strategyId 策略ID
   * @returns 策略详情响应
   */
  static async getStrategy(strategyId: number): Promise<RuoyiResponse<ContractReviewStrategy>> {
    return http.get(`/contract/strategy/${strategyId}`)
  }

  /**
   * 新增合同审查策略
   * @param data 策略数据
   * @returns 操作结果
   */
  static async addStrategy(data: ContractReviewStrategyForm): Promise<RuoyiResponse> {
    return http.post('/contract/strategy', data)
  }

  /**
   * 修改合同审查策略
   * @param data 策略数据
   * @returns 操作结果
   */
  static async updateStrategy(data: ContractReviewStrategyForm): Promise<RuoyiResponse> {
    return http.put('/contract/strategy', data)
  }

  /**
   * 删除合同审查策略
   * @param strategyIds 策略ID数组
   * @returns 操作结果
   */
  static async delStrategy(strategyIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(strategyIds) ? strategyIds.join(',') : strategyIds
    return http.delete(`/contract/strategy/${ids}`)
  }

  /**
   * 发布策略
   * @param strategyId 策略ID
   * @returns 操作结果
   */
  static async publishStrategy(strategyId: number): Promise<RuoyiResponse> {
    return http.put(`/contract/strategy/publish/${strategyId}`)
  }

  /**
   * 取消发布策略（设为草稿）
   * @param strategyId 策略ID
   * @returns 操作结果
   */
  static async unpublishStrategy(strategyId: number): Promise<RuoyiResponse> {
    return http.put(`/contract/strategy/unpublish/${strategyId}`)
  }

  /**
   * 复制策略
   * @param strategyId 源策略ID
   * @param newStrategyName 新策略名称
   * @returns 操作结果
   */
  static async copyStrategy(strategyId: number, newStrategyName: string): Promise<RuoyiResponse> {
    return http.post(`/contract/strategy/copy/${strategyId}`, { strategyName: newStrategyName })
  }

  /**
   * 获取策略统计信息
   * @returns 统计数据
   */
  static async getStrategyStats(): Promise<
    RuoyiResponse<{
      totalCount: number
      draftCount: number
      publishedCount: number
      partyACount: number
      partyBCount: number
    }>
  > {
    return http.get('/contract/strategy/stats')
  }
}
