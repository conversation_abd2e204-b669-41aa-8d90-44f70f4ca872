/**
 * HTTP封装测试API
 * 用于验证HTTP封装是否正常工作
 */

import { HttpClient } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'

/**
 * 测试用户接口
 */
export interface TestUser {
  userId: number
  userName: string
  nickName: string
  email: string
  phonenumber: string
  status: string
}

/**
 * 测试用户查询参数
 */
export interface TestUserQueryParams {
  pageNum?: number
  pageSize?: number
  userName?: string
  status?: string
}

/**
 * 测试API类
 */
export class TestApi {
  /**
   * 测试获取用户列表 - 验证RuoYi响应格式
   */
  static async getUserList(params: TestUserQueryParams): Promise<RuoyiResponse<TestUser>> {
    return HttpClient.get('/system/user/list', params)
  }

  /**
   * 测试获取用户详情
   */
  static async getUser(userId: number): Promise<RuoyiResponse<TestUser>> {
    return HttpClient.get(`/system/user/${userId}`)
  }

  /**
   * 测试新增用户
   */
  static async addUser(data: Partial<TestUser>): Promise<RuoyiResponse> {
    return HttpClient.post('/system/user', data)
  }

  /**
   * 测试更新用户
   */
  static async updateUser(data: Partial<TestUser>): Promise<RuoyiResponse> {
    return HttpClient.put('/system/user', data)
  }

  /**
   * 测试删除用户
   */
  static async deleteUser(userIds: number[]): Promise<RuoyiResponse> {
    return HttpClient.delete(`/system/user/${userIds.join(',')}`)
  }
}
