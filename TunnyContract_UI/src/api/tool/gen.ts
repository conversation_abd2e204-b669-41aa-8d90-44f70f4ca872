import { http } from '@/utils/http'
import type { RuoyiResponse } from '@/types/http'
import type {
  GenTable,
  DbTable,
  GenTableQueryParams,
  DbTableQueryParams,
  ImportTableParams
} from '@/types/tool/gen'

/**
 * 代码生成管理API类
 * 完全保持RuoYi的代码生成接口格式
 */
export class GenApi {
  /**
   * 查询生成表列表
   */
  static async getGenTableList(params?: GenTableQueryParams): Promise<RuoyiResponse<GenTable>> {
    return http.get('/tool/gen/list', { params })
  }

  /**
   * 查询数据库表列表
   */
  static async getDbTableList(params?: DbTableQueryParams): Promise<RuoyiResponse<DbTable>> {
    return http.get('/tool/gen/db/list', { params })
  }

  /**
   * 查询表详细信息
   */
  static async getGenTable(tableId: number): Promise<RuoyiResponse<GenTable>> {
    return http.get(`/tool/gen/${tableId}`)
  }

  /**
   * 修改代码生成信息
   */
  static async updateGenTable(data: GenTable): Promise<RuoyiResponse> {
    return http.put('/tool/gen', data)
  }

  /**
   * 导入表
   */
  static async importTable(params: ImportTableParams): Promise<RuoyiResponse> {
    return http.post('/tool/gen/importTable', null, { params })
  }

  /**
   * 预览生成代码
   */
  static async previewTable(tableId: number): Promise<RuoyiResponse<Record<string, string>>> {
    return http.get(`/tool/gen/preview/${tableId}`)
  }

  /**
   * 删除表数据
   */
  static async deleteGenTable(tableIds: number | number[]): Promise<RuoyiResponse> {
    const ids = Array.isArray(tableIds) ? tableIds.join(',') : tableIds
    return http.delete(`/tool/gen/${ids}`)
  }

  /**
   * 生成代码（自定义路径）
   */
  static async genCode(tableName: string): Promise<RuoyiResponse> {
    return http.get(`/tool/gen/genCode/${tableName}`)
  }

  /**
   * 批量生成代码
   */
  static async batchGenCode(tableNames: string[]): Promise<Blob> {
    const names = tableNames.join(',')
    const response = await http.request({
      url: '/tool/gen/batchGenCode',
      method: 'get',
      params: { tables: names },
      responseType: 'blob'
    })
    return response.data
  }

  /**
   * 同步数据库
   */
  static async synchDb(tableName: string): Promise<RuoyiResponse> {
    return http.get(`/tool/gen/synchDb/${tableName}`)
  }
}
