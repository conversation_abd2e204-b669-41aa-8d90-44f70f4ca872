<template>
  <div class="user-store-test">
    <h2>用户状态管理测试</h2>

    <div class="test-section">
      <h3>登录状态</h3>
      <p>是否已登录: {{ userStore.isLogin }}</p>
      <p>Token: {{ userStore.token ? '已设置' : '未设置' }}</p>
    </div>

    <div class="test-section">
      <h3>用户信息</h3>
      <p>用户ID: {{ userStore.userInfo.userId || '未设置' }}</p>
      <p>用户名: {{ userStore.userInfo.userName || '未设置' }}</p>
      <p>昵称: {{ userStore.userInfo.nickName || '未设置' }}</p>
      <p>邮箱: {{ userStore.userInfo.email || '未设置' }}</p>
    </div>

    <div class="test-section">
      <h3>角色和权限</h3>
      <p>角色列表: {{ userStore.roles.join(', ') || '无' }}</p>
      <p>权限数量: {{ userStore.permissions.length }}</p>
      <p>权限示例: {{ userStore.permissions.slice(0, 3).join(', ') || '无' }}</p>
    </div>

    <div class="test-section">
      <h3>操作测试</h3>
      <button @click="testSetUserInfo" class="test-btn">测试设置用户信息</button>
      <button @click="testSetPermissions" class="test-btn">测试设置权限</button>
      <button @click="testSetRoles" class="test-btn">测试设置角色</button>
      <button @click="testClearData" class="test-btn">清空测试数据</button>
    </div>

    <div class="test-section">
      <h3>权限验证测试</h3>
      <p>system:user:add 权限: {{ hasUserAddPermission ? '有' : '无' }}</p>
      <p>system:user:edit 权限: {{ hasUserEditPermission ? '有' : '无' }}</p>
      <p>admin 角色: {{ hasAdminRole ? '有' : '无' }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { useUserStore } from '@/store/modules/user'
  import { hasPermi, hasRole } from '@/utils/auth'
  import type { UserInfo } from '@/types/system/auth'

  const userStore = useUserStore()

  // 权限验证计算属性
  const hasUserAddPermission = computed(() => hasPermi('system:user:add'))
  const hasUserEditPermission = computed(() => hasPermi('system:user:edit'))
  const hasAdminRole = computed(() => hasRole('admin'))

  // 测试设置用户信息
  const testSetUserInfo = () => {
    const testUserInfo: UserInfo = {
      userId: 1,
      userName: 'testuser',
      nickName: '测试用户',
      email: '<EMAIL>',
      phonenumber: '13800138000',
      sex: '0'
    }

    userStore.setUserInfo(testUserInfo)
    console.log('用户信息已设置:', testUserInfo)
  }

  // 测试设置权限
  const testSetPermissions = () => {
    const testPermissions = [
      'system:user:add',
      'system:user:edit',
      'system:user:remove',
      'system:role:list',
      'system:menu:list'
    ]

    userStore.setPermissions(testPermissions)
    console.log('权限已设置:', testPermissions)
  }

  // 测试设置角色
  const testSetRoles = () => {
    const testRoles = ['admin', 'common']

    userStore.setRoles(testRoles)
    console.log('角色已设置:', testRoles)
  }

  // 清空测试数据
  const testClearData = () => {
    userStore.setUserInfo({} as UserInfo)
    userStore.setPermissions([])
    userStore.setRoles([])
    console.log('测试数据已清空')
  }
</script>

<style scoped>
  .user-store-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .test-section h3 {
    margin-top: 0;
    color: #333;
  }

  .test-section p {
    margin: 8px 0;
    color: #666;
  }

  .test-btn {
    margin: 5px;
    padding: 8px 16px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .test-btn:hover {
    background-color: #40a9ff;
  }

  .test-btn:active {
    background-color: #096dd9;
  }
</style>
