/**
 * 用户状态管理使用示例
 * 演示如何使用重构后的用户Store
 */

import { useUserStore } from '@/store/modules/user'
import { LoginApi } from '@/api/auth/login'
// import { setToken } from '@/utils/auth'

/**
 * 用户状态管理使用示例
 */
export class UserStoreUsageExample {
  /**
   * 登录流程示例
   */
  static async loginExample() {
    const userStore = useUserStore()

    try {
      // 1. 用户登录
      const loginForm = {
        username: 'admin',
        password: 'admin123',
        code: '1234',
        uuid: 'test-uuid'
      }

      const loginResponse = await LoginApi.login(loginForm)

      // 2. 设置token
      userStore.setTokenValue(loginResponse.token)

      // 3. 获取用户信息
      await userStore.getUserInfoFromServer()

      console.log('登录成功')
      console.log('用户信息:', userStore.userInfo)
      console.log('用户角色:', userStore.roles)
      console.log('用户权限:', userStore.permissions)
      console.log('登录状态:', userStore.isLogin)
    } catch (error) {
      console.error('登录失败:', error)
    }
  }

  /**
   * 权限验证示例
   */
  static permissionExample() {
    const userStore = useUserStore()

    // 检查是否有特定权限
    const hasUserAdd = userStore.permissions.includes('system:user:add')
    const hasUserEdit = userStore.permissions.includes('system:user:edit')
    const hasUserDelete = userStore.permissions.includes('system:user:remove')

    console.log('用户管理权限检查:')
    console.log('新增权限:', hasUserAdd)
    console.log('编辑权限:', hasUserEdit)
    console.log('删除权限:', hasUserDelete)

    // 检查角色
    const isAdmin = userStore.roles.includes('admin')
    const isCommon = userStore.roles.includes('common')

    console.log('角色检查:')
    console.log('管理员角色:', isAdmin)
    console.log('普通用户角色:', isCommon)
  }

  /**
   * 用户信息更新示例
   */
  static updateUserInfoExample() {
    const userStore = useUserStore()

    // 更新用户信息
    const newUserInfo = {
      ...userStore.userInfo,
      nickName: '新昵称',
      email: '<EMAIL>'
    }

    userStore.setUserInfo(newUserInfo)

    console.log('用户信息已更新:', userStore.userInfo)
  }

  /**
   * 退出登录示例
   */
  static async logoutExample() {
    const userStore = useUserStore()

    try {
      await userStore.logOut()
      console.log('退出登录成功')
      console.log('登录状态:', userStore.isLogin)
      console.log('用户信息:', userStore.userInfo)
      console.log('角色列表:', userStore.roles)
      console.log('权限列表:', userStore.permissions)
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }

  /**
   * 状态持久化示例
   */
  static persistenceExample() {
    const userStore = useUserStore()

    console.log('持久化状态检查:')
    console.log('用户信息:', userStore.userInfo)
    console.log('角色列表:', userStore.roles)
    console.log('权限列表:', userStore.permissions)
    console.log('语言设置:', userStore.language)
    console.log('搜索历史:', userStore.searchHistory)

    // 注意：token通过Cookie管理，不在localStorage中持久化
    console.log('Token状态:', userStore.token)
  }
}

/**
 * 使用示例
 */
export function runUserStoreExamples() {
  console.log('=== 用户状态管理示例 ===')

  // 1. 登录示例
  // UserStoreUsageExample.loginExample()

  // 2. 权限验证示例
  UserStoreUsageExample.permissionExample()

  // 3. 用户信息更新示例
  UserStoreUsageExample.updateUserInfoExample()

  // 4. 状态持久化示例
  UserStoreUsageExample.persistenceExample()

  // 5. 退出登录示例
  // UserStoreUsageExample.logoutExample()
}
