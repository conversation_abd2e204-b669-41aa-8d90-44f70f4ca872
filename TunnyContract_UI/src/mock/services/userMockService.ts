import { ACCOUNT_TABLE_DATA } from '@/mock/temp/formData'

interface ExtendedPaginatingSearchParams extends Api.Common.PaginatingSearchParams {
  name?: string
  phone?: string
}

/**
 * 用户Mock服务
 * 模拟后端API的响应格式和行为
 */
export class UserMockService {
  /**
   * 模拟获取用户列表
   * @param params 查询参数
   * @returns Promise<Api.User.UserListData>
   */
  static async getUserList(params: ExtendedPaginatingSearchParams): Promise<Api.User.UserListData> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 500 + 200))

    const { current = 1, size = 20, name = '', phone = '' } = params

    // 将mock数据转换为API期望的格式
    let filteredData = ACCOUNT_TABLE_DATA.map((user) => ({
      id: user.id,
      nickName: user.username,
      userGender: user.gender === 1 ? '男' : '女',
      userPhone: user.mobile,
      userEmail: user.email,
      department: user.dep,
      status: user.status as '1' | '2' | '3' | '4',
      createTime: user.create_time,
      avatar: user.avatar,
      // 添加缺失的字段
      createBy: 'admin',
      updateBy: 'admin',
      updateTime: user.create_time,
      userName: user.username,
      userRoles: []
    }))

    // 根据搜索条件过滤数据
    if (name) {
      filteredData = filteredData.filter((user) =>
        user.nickName.toLowerCase().includes(name.toLowerCase())
      )
    }

    if (phone) {
      filteredData = filteredData.filter((user) => user.userPhone.includes(phone))
    }

    // 计算分页
    const total = filteredData.length
    const startIndex = (current - 1) * size
    const endIndex = startIndex + size
    const records = filteredData.slice(startIndex, endIndex)

    // 返回符合API格式的数据
    return {
      records,
      total,
      current,
      size
    }
  }

  /**
   * 生成更多mock数据（用于演示大数据量）
   * @param count 生成数据条数
   * @returns 扩展的用户数据
   */
  static generateMoreMockData(count: number = 100) {
    const departments = [
      '研发部',
      '产品部',
      '设计部',
      '运营部',
      '市场部',
      '人事部',
      '财务部',
      '客服部'
    ]
    const statuses = ['1', '2', '3', '4']
    const genders = ['男', '女']

    const additionalData = []

    for (let i = 1; i <= count; i++) {
      const id = ACCOUNT_TABLE_DATA.length + i
      additionalData.push({
        id,
        nickName: `用户${id}`,
        userGender: genders[Math.floor(Math.random() * genders.length)],
        userPhone: `1${Math.floor(Math.random() * 9) + 3}${Math.floor(Math.random() * *********)
          .toString()
          .padStart(8, '0')}`,
        userEmail: `user${id}@company.com`,
        department: departments[Math.floor(Math.random() * departments.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)] as '1' | '2' | '3' | '4',
        createTime: new Date(
          2020 + Math.floor(Math.random() * 5),
          Math.floor(Math.random() * 12),
          Math.floor(Math.random() * 28) + 1
        )
          .toISOString()
          .replace('T', ' ')
          .split('.')[0],
        avatar: `/src/assets/img/avatar/avatar${Math.floor(Math.random() * 10) + 1}.webp`,
        // 添加缺失的字段
        createBy: 'admin',
        updateBy: 'admin',
        updateTime: new Date(
          2020 + Math.floor(Math.random() * 5),
          Math.floor(Math.random() * 12),
          Math.floor(Math.random() * 28) + 1
        )
          .toISOString()
          .replace('T', ' ')
          .split('.')[0],
        userName: `user${id}`,
        userRoles: []
      })
    }

    return additionalData
  }

  /**
   * 获取扩展的用户列表（包含更多数据）
   * @param params 查询参数
   * @returns Promise<Api.User.UserListData>
   */
  static async getExtendedUserList(
    params: ExtendedPaginatingSearchParams
  ): Promise<Api.User.UserListData> {
    // 模拟网络延迟
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 800 + 300))

    const { current = 1, size = 20, name = '', phone = '' } = params

    // 合并原始数据和生成的数据
    const originalData = ACCOUNT_TABLE_DATA.map((user) => ({
      id: user.id,
      nickName: user.username,
      userGender: user.gender === 1 ? '男' : '女',
      userPhone: user.mobile,
      userEmail: user.email,
      department: user.dep,
      status: user.status as '1' | '2' | '3' | '4',
      createTime: user.create_time,
      avatar: user.avatar,
      // 添加缺失的字段
      createBy: 'admin',
      updateBy: 'admin',
      updateTime: user.create_time,
      userName: user.username,
      userRoles: []
    }))

    const additionalData = this.generateMoreMockData(200) // 生成200条额外数据
    let allData = [...originalData, ...additionalData]

    // 根据搜索条件过滤数据
    if (name) {
      allData = allData.filter((user) => user.nickName.toLowerCase().includes(name.toLowerCase()))
    }

    if (phone) {
      allData = allData.filter((user) => user.userPhone.includes(phone))
    }

    // 计算分页
    const total = allData.length
    const startIndex = (current - 1) * size
    const endIndex = startIndex + size
    const records = allData.slice(startIndex, endIndex)

    // 返回符合API格式的数据
    return {
      records,
      total,
      current,
      size
    }
  }
}
