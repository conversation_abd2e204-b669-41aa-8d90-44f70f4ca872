export const commentList = reactive([
  {
    id: 1,
    date: '2024-9-3',
    content: '发现了一个超级好用的工具，开心',
    collection: 5,
    comment: 8,
    userName: '匿名'
  },
  {
    id: 2,
    date: '2024-9-3',
    content: '今天的代码写得很顺利！',
    collection: 3,
    comment: 2,
    userName: 'Coder123'
  },
  {
    id: 3,
    date: '2024-9-4',
    content: '遇到个bug，调试了一整天',
    collection: 7,
    comment: 10,
    userName: 'DebugMaster'
  },
  {
    id: 4,
    date: '2024-9-4',
    content: '学Node真的是一件很有趣的事',
    collection: 9,
    comment: 4,
    userName: 'NodeLover'
  },
  {
    id: 5,
    date: '2024-9-5',
    content: '今天的进度有点慢，需要加把劲了',
    collection: 2,
    comment: 3,
    userName: '努力中的小白'
  },
  {
    id: 6,
    date: '2024-9-5',
    content: '太好了，终于解决了一个难题！',
    collection: 11,
    comment: 5,
    userName: '匿名'
  },
  {
    id: 7,
    date: '2024-9-6',
    content: '学会了新的Node技巧，开心！',
    collection: 4,
    comment: 7,
    userName: '开心每一天'
  },
  {
    id: 8,
    date: '2024-9-6',
    content: '代码优化真的是一个细致活',
    collection: 6,
    comment: 4,
    userName: '精益求精'
  },
  {
    id: 9,
    date: '2024-9-7',
    content: '今天的工作太顺利了，完美！',
    collection: 10,
    comment: 9,
    userName: '完美主义者'
  },
  {
    id: 10,
    date: '2024-9-7',
    content: '需要多练习，才能掌握更多技能',
    collection: 5,
    comment: 6,
    userName: '匿名'
  },
  {
    id: 11,
    date: '2024-9-8',
    content: '每天进步一点点，终会成功',
    collection: 8,
    comment: 7,
    userName: '逐梦者'
  },
  {
    id: 12,
    date: '2024-9-8',
    content: '与其抱怨，不如努力改变',
    collection: 12,
    comment: 10,
    userName: '改变命运'
  },
  {
    id: 13,
    date: '2024-9-9',
    content: '今天尝试了新的库，感觉不错',
    collection: 9,
    comment: 8,
    userName: '新手尝试'
  },
  {
    id: 14,
    date: '2024-9-9',
    content: '写代码也需要灵感，今天灵感不错',
    collection: 6,
    comment: 5,
    userName: '灵感源泉'
  },
  {
    id: 15,
    date: '2024-9-10',
    content: '感谢社区的帮助，让我解决了问题',
    collection: 7,
    comment: 4,
    userName: '受益匪浅'
  },
  {
    id: 16,
    date: '2024-9-10',
    content: '学习的路上要保持耐心和恒心',
    collection: 3,
    comment: 2,
    userName: '匿名'
  },
  {
    id: 17,
    date: '2024-9-11',
    content: '今天学习了异步编程的知识，受益匪浅',
    collection: 10,
    comment: 9,
    userName: '异步学习者'
  },
  {
    id: 18,
    date: '2024-9-11',
    content: '今天的代码质量提升了不少',
    collection: 11,
    comment: 6,
    userName: '代码匠人'
  },
  {
    id: 19,
    date: '2024-9-12',
    content: '感觉学习编程真的很有成就感',
    collection: 8,
    comment: 7,
    userName: '成就感满满'
  },
  {
    id: 20,
    date: '2024-9-12',
    content: '要加倍努力，才能超越昨天的自己',
    collection: 5,
    comment: 4,
    userName: '努力超越'
  },
  {
    id: 21,
    date: '2024-9-13',
    content: '今天的代码写得很顺手，继续保持',
    collection: 9,
    comment: 8,
    userName: '顺风顺水'
  },
  {
    id: 22,
    date: '2024-9-13',
    content: '写代码也需要创意，今天很有创意',
    collection: 7,
    comment: 5,
    userName: '创意无限'
  },
  {
    id: 23,
    date: '2024-9-14',
    content: '遇到的难题解决了，感觉很有成就感',
    collection: 10,
    comment: 9,
    userName: '匿名'
  },
  {
    id: 24,
    date: '2024-9-14',
    content: '今天的编程练习很有收获',
    collection: 8,
    comment: 7,
    userName: '收获满满'
  },
  {
    id: 25,
    date: '2024-9-15',
    content: '学习编程的路上，有苦有甜',
    collection: 6,
    comment: 4,
    userName: '苦乐编程'
  },
  {
    id: 26,
    date: '2024-9-15',
    content: '今天的代码写得特别流畅，开心！',
    collection: 11,
    comment: 6,
    userName: '流畅编程'
  },
  {
    id: 27,
    date: '2024-9-16',
    content: '今天的编程练习让我更有信心',
    collection: 9,
    comment: 8,
    userName: '信心满满'
  },
  {
    id: 28,
    date: '2024-9-16',
    content: '今天的编程学习让我收获很多',
    collection: 7,
    comment: 5,
    userName: '匿名'
  },
  {
    id: 29,
    date: '2024-9-17',
    content: '编程是一门艺术，今天体会到了',
    collection: 12,
    comment: 10,
    userName: '编程艺术家'
  },
  {
    id: 30,
    date: '2024-9-17',
    content: '今天的代码写得很顺利，继续加油！',
    collection: 10,
    comment: 9,
    userName: '匿名'
  }
])
