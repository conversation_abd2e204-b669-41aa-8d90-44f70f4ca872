/**
 * 路由分类管理工具函数
 */

import { AppRouteRecord } from '@/types/router'

/**
 * 路由类型枚举
 */
export enum RouteTypeEnum {
  /** 演示路由 */
  DEMO = 'demo',
  /** 业务路由 */
  BUSINESS = 'business',
  /** 系统路由 */
  SYSTEM = 'system'
}

/**
 * 判断路由是否为指定类型
 * @param route 路由记录
 * @param type 路由类型
 * @returns 是否为指定类型的路由
 */
export const isRouteType = (route: AppRouteRecord, type: RouteTypeEnum): boolean => {
  return route.meta?.routeType === type
}

/**
 * 判断路由是否为演示路由
 * @param route 路由记录
 * @returns 是否为演示路由
 */
export const isDemoRoute = (route: AppRouteRecord): boolean => {
  return isRouteType(route, RouteTypeEnum.DEMO)
}

/**
 * 判断路由是否为业务路由
 * @param route 路由记录
 * @returns 是否为业务路由
 */
export const isBusinessRoute = (route: AppRouteRecord): boolean => {
  return isRouteType(route, RouteTypeEnum.BUSINESS)
}

/**
 * 判断路由是否为系统路由
 * @param route 路由记录
 * @returns 是否为系统路由
 */
export const isSystemRoute = (route: AppRouteRecord): boolean => {
  return isRouteType(route, RouteTypeEnum.SYSTEM)
}

/**
 * 根据类型过滤路由
 * @param routes 路由列表
 * @param type 要过滤的路由类型
 * @param include 是否包含该类型（true为包含，false为排除）
 * @returns 过滤后的路由列表
 */
export const filterRoutesByType = (
  routes: AppRouteRecord[],
  type: RouteTypeEnum,
  include: boolean = true
): AppRouteRecord[] => {
  return routes.filter((route) => {
    const isTargetType = isRouteType(route, type)

    // 处理子路由
    if (route.children && route.children.length > 0) {
      const filteredChildren = filterRoutesByType(route.children, type, include)
      route.children = filteredChildren

      // 如果父路由不是目标类型，但有符合条件的子路由，则保留
      if (!isTargetType && filteredChildren.length > 0) {
        return true
      }

      // 如果父路由是目标类型，根据include参数决定
      if (isTargetType) {
        return include
      }

      // 如果所有子路由都被过滤掉，且父路由也不符合条件，则过滤掉父路由
      return filteredChildren.length > 0
    }

    // 对于没有子路由的路由，直接根据类型判断
    return include ? isTargetType || !route.meta?.routeType : !isTargetType
  })
}

/**
 * 过滤演示路由
 * @param routes 路由列表
 * @param showDemo 是否显示演示路由
 * @returns 过滤后的路由列表
 */
export const filterDemoRoutes = (routes: AppRouteRecord[], showDemo: boolean): AppRouteRecord[] => {
  return filterRoutesByType(routes, RouteTypeEnum.DEMO, showDemo)
}

/**
 * 获取指定类型的路由列表
 * @param routes 路由列表
 * @param type 路由类型
 * @returns 指定类型的路由列表
 */
export const getRoutesByType = (
  routes: AppRouteRecord[],
  type: RouteTypeEnum
): AppRouteRecord[] => {
  const result: AppRouteRecord[] = []

  const collectRoutes = (routeList: AppRouteRecord[]) => {
    routeList.forEach((route) => {
      if (isRouteType(route, type)) {
        result.push(route)
      }

      if (route.children && route.children.length > 0) {
        collectRoutes(route.children)
      }
    })
  }

  collectRoutes(routes)
  return result
}

/**
 * 获取路由统计信息
 * @param routes 路由列表
 * @returns 路由统计信息
 */
export const getRouteStatistics = (routes: AppRouteRecord[]) => {
  const stats = {
    total: 0,
    demo: 0,
    business: 0,
    system: 0,
    untyped: 0
  }

  const countRoutes = (routeList: AppRouteRecord[]) => {
    routeList.forEach((route) => {
      stats.total++

      switch (route.meta?.routeType) {
        case RouteTypeEnum.DEMO:
          stats.demo++
          break
        case RouteTypeEnum.BUSINESS:
          stats.business++
          break
        case RouteTypeEnum.SYSTEM:
          stats.system++
          break
        default:
          stats.untyped++
      }

      if (route.children && route.children.length > 0) {
        countRoutes(route.children)
      }
    })
  }

  countRoutes(routes)
  return stats
}
