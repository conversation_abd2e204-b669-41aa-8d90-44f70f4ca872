/**
 * 日期时间相关工具函数
 */

/**
 * 格式化时间
 * @param time 时间字符串或时间戳
 * @param format 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatTime(time?: string | number | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) return ''

  const date = new Date(time)
  if (isNaN(date.getTime())) return ''

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 解析时间字符串
 * @param timeStr 时间字符串
 * @returns Date对象
 */
export function parseTime(timeStr?: string | number | Date): Date | null {
  if (!timeStr) return null

  const date = new Date(timeStr)
  return isNaN(date.getTime()) ? null : date
}

/**
 * 获取日期范围
 * @param days 天数
 * @returns [开始日期, 结束日期]
 */
export function getDateRange(days: number): [string, string] {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - days)

  return [formatTime(start, 'YYYY-MM-DD'), formatTime(end, 'YYYY-MM-DD')]
}

/**
 * 判断是否为今天
 * @param date 日期
 * @returns 是否为今天
 */
export function isToday(date: string | number | Date): boolean {
  const today = new Date()
  const targetDate = new Date(date)

  return today.toDateString() === targetDate.toDateString()
}

/**
 * 获取相对时间描述
 * @param time 时间
 * @returns 相对时间描述
 */
export function getRelativeTime(time: string | number | Date): string {
  const now = Date.now()
  const targetTime = new Date(time).getTime()
  const diff = now - targetTime

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else if (diff < 2592000000) {
    // 30天内
    return `${Math.floor(diff / 86400000)}天前`
  } else {
    return formatTime(time, 'YYYY-MM-DD')
  }
}
