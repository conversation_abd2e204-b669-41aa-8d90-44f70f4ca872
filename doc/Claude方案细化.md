# 合同审查报告查看功能 - Claude 实现方案细化

## 📊 项目概述

**功能名称**: 合同审查任务报告查看功能  
**实现方案**: PDF.js + Vue3 组合方案  
**总预计工时**: 74工时（约10个工作日）  
**开发模式**: 分阶段递进式开发  

## 🎯 任务概览表

| 阶段 | 任务数 | 预计工时 | 优先级 | 依赖关系 |
|------|--------|----------|--------|----------|
| 后端API开发 | 8个任务 | 16工时 | P0 | 无依赖 |
| 前端基础开发 | 10个任务 | 20工时 | P0 | 依赖后端API |
| PDF渲染核心 | 6个任务 | 18工时 | P0 | 依赖前端基础 |
| 交互联动 | 5个任务 | 12工时 | P1 | 依赖PDF渲染 |
| 优化完善 | 4个任务 | 8工时 | P2 | 依赖交互联动 |

## 🔧 阶段一：后端API开发（16工时）

### 任务1.1：新增报告查看API接口
**文件位置**: `ContractReviewTaskController.java`  
**预计工时**: 3小时  

**具体代码实现**:
```java
/**
 * 获取合同审查报告详情
 * @param taskId 任务ID
 * @return 报告详情数据
 */
@PreAuthorize("@ss.hasPermi('contract:task:query')")
@GetMapping("/report/{taskId}")
public AjaxResult getTaskReport(@PathVariable Long taskId) {
    try {
        // 1. 获取任务基本信息
        ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
        if (task == null) {
            return error("审查任务不存在");
        }
        
        // 2. 检查任务状态
        if (!"2".equals(task.getTaskStatus())) {
            return error("任务尚未完成，无法查看报告");
        }
        
        // 3. 获取任务关联的合同文件
        ContractFile queryFile = new ContractFile();
        queryFile.setTaskId(taskId);
        List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
        if (contractFiles.isEmpty()) {
            return error("未找到关联的合同文件");
        }
        
        // 4. 构建报告数据
        Map<String, Object> reportData = new HashMap<>();
        reportData.put("taskInfo", task);
        reportData.put("contractFiles", contractFiles);
        
        // 5. 解析审查结果
        String reviewResult = task.getReviewResult();
        if (StringUtils.isNotBlank(reviewResult)) {
            try {
                Object parsedResult = JSON.parseObject(reviewResult);
                reportData.put("reviewResults", parsedResult);
            } catch (Exception e) {
                logger.warn("解析审查结果失败: {}", e.getMessage());
                reportData.put("reviewResults", null);
            }
        }
        
        return success(reportData);
        
    } catch (Exception e) {
        logger.error("获取任务报告失败", e);
        return error("获取报告失败：" + e.getMessage());
    }
}
```

**验收标准**:
- ✅ API返回任务基本信息
- ✅ API返回合同文件列表
- ✅ API返回解析后的审查结果JSON
- ✅ 正确处理任务不存在/未完成状态
- ✅ 单元测试覆盖率>90%

### 任务1.2：新增文档访问API
**文件位置**: `ContractReviewTaskController.java`  
**预计工时**: 2小时

**具体代码实现**:
```java
/**
 * 获取合同文档访问URL（支持在线预览）
 * @param taskId 任务ID
 * @param fileId 文件ID
 * @return 文档访问URL
 */
@PreAuthorize("@ss.hasPermi('contract:task:query')")
@GetMapping("/document/{taskId}/{fileId}")
public AjaxResult getDocumentUrl(@PathVariable Long taskId, @PathVariable Long fileId) {
    try {
        // 1. 验证任务和文件关联关系
        ContractFile contractFile = contractFileService.selectContractFileById(fileId);
        if (contractFile == null || !taskId.equals(contractFile.getTaskId())) {
            return error("文件不存在或无权访问");
        }
        
        // 2. 获取文件访问URL
        String fileUrl = contractFile.getFilePath();
        
        // 3. 判断文件类型，如果是Word文档需要转换为PDF
        String fileName = contractFile.getFileName();
        String fileExtension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        
        Map<String, Object> result = new HashMap<>();
        
        if (".pdf".equals(fileExtension)) {
            // PDF文件直接返回URL
            result.put("documentUrl", fileUrl);
            result.put("documentType", "pdf");
        } else if (".doc".equals(fileExtension) || ".docx".equals(fileExtension)) {
            // Word文件需要转换
            String pdfUrl = documentRenderService.convertWordToPdf(contractFile);
            result.put("documentUrl", pdfUrl);
            result.put("documentType", "pdf");
            result.put("originalType", "word");
        } else {
            return error("不支持的文件格式");
        }
        
        result.put("fileName", fileName);
        result.put("fileSize", contractFile.getFileSize());
        
        return success(result);
        
    } catch (Exception e) {
        logger.error("获取文档URL失败", e);
        return error("获取文档失败：" + e.getMessage());
    }
}
```

**验收标准**:
- ✅ 正确返回PDF文档访问URL
- ✅ Word文档能转换为PDF并返回URL
- ✅ 权限验证正确
- ✅ 错误处理完善

### 任务1.3：创建文档渲染服务
**文件位置**: `DocumentRenderService.java`（新建）  
**预计工时**: 4小时

**具体代码实现**:
```java
@Service
public class DocumentRenderService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentRenderService.class);
    
    @Value("${file.temp-dir:/tmp/contract-render}")
    private String tempDir;
    
    @Value("${file.pdf-cache-dir:/tmp/contract-pdf-cache}")
    private String pdfCacheDir;
    
    /**
     * 将Word文档转换为PDF
     * @param contractFile 合同文件信息
     * @return PDF文件的访问URL
     */
    public String convertWordToPdf(ContractFile contractFile) throws Exception {
        String fileName = contractFile.getFileName();
        String fileMd5 = contractFile.getFileMd5();
        
        // 1. 检查缓存
        String cachedPdfPath = checkPdfCache(fileMd5);
        if (cachedPdfPath != null) {
            return cachedPdfPath;
        }
        
        // 2. 下载Word文档到临时目录
        String wordFilePath = downloadToTemp(contractFile.getFilePath(), fileName);
        
        // 3. 转换Word为PDF
        String pdfFileName = fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";
        String pdfFilePath = Paths.get(pdfCacheDir, fileMd5 + ".pdf").toString();
        
        convertWordToPdfInternal(wordFilePath, pdfFilePath);
        
        // 4. 上传PDF到MinIO并返回访问URL
        String pdfUrl = uploadPdfToMinio(pdfFilePath, pdfFileName);
        
        // 5. 清理临时文件
        cleanupTempFiles(wordFilePath, pdfFilePath);
        
        return pdfUrl;
    }
    
    /**
     * 使用Apache POI + iText进行Word转PDF
     */
    private void convertWordToPdfInternal(String wordPath, String pdfPath) throws Exception {
        File wordFile = new File(wordPath);
        File pdfFile = new File(pdfPath);
        
        // 确保输出目录存在
        pdfFile.getParentFile().mkdirs();
        
        try (FileInputStream fis = new FileInputStream(wordFile);
             FileOutputStream fos = new FileOutputStream(pdfFile)) {
            
            if (wordPath.endsWith(".docx")) {
                // 处理.docx文件
                XWPFDocument document = new XWPFDocument(fis);
                PdfOptions options = PdfOptions.create();
                PdfConverter.getInstance().convert(document, fos, options);
                document.close();
            } else {
                // 处理.doc文件
                HWPFDocument document = new HWPFDocument(fis);
                PdfOptions options = PdfOptions.create();
                PdfConverter.getInstance().convert(document, fos, options);
                document.close();
            }
        }
    }
    
    /**
     * 检查PDF缓存是否存在
     */
    private String checkPdfCache(String fileMd5) {
        String cachedPath = Paths.get(pdfCacheDir, fileMd5 + ".pdf").toString();
        File cachedFile = new File(cachedPath);
        if (cachedFile.exists()) {
            // 返回缓存文件的MinIO URL
            return getCachedPdfUrl(fileMd5);
        }
        return null;
    }
    
    /**
     * 下载文件到临时目录
     */
    private String downloadToTemp(String sourceUrl, String fileName) throws Exception {
        String tempFilePath = Paths.get(tempDir, fileName).toString();
        File tempFile = new File(tempFilePath);
        tempFile.getParentFile().mkdirs();
        
        URL url = new URL(sourceUrl);
        URLConnection connection = url.openConnection();
        try (InputStream inputStream = connection.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        
        return tempFilePath;
    }
    
    /**
     * 上传PDF到MinIO
     */
    private String uploadPdfToMinio(String pdfPath, String fileName) throws Exception {
        // 使用现有的MinIO服务上传PDF文件
        // 返回可访问的URL
        return "";
    }
    
    /**
     * 获取缓存PDF的URL
     */
    private String getCachedPdfUrl(String fileMd5) {
        // 返回缓存文件的访问URL
        return "";
    }
    
    /**
     * 清理临时文件
     */
    private void cleanupTempFiles(String... filePaths) {
        for (String filePath : filePaths) {
            try {
                Files.deleteIfExists(Paths.get(filePath));
            } catch (IOException e) {
                logger.warn("删除临时文件失败: {}", filePath);
            }
        }
    }
}
```

**验收标准**:
- ✅ Word文档成功转换为PDF
- ✅ 转换质量保持原文档格式
- ✅ 转换缓存机制正常工作
- ✅ 临时文件正确清理
- ✅ 异常处理完善

### 任务1.4：新增坐标解析服务
**文件位置**: `RiskPointCoordinateService.java`（新建）  
**预计工时**: 3小时

**具体代码实现**:
```java
@Service
public class RiskPointCoordinateService {
    
    private static final Logger logger = LoggerFactory.getLogger(RiskPointCoordinateService.class);
    
    /**
     * 解析审查结果中的风险点坐标信息
     * @param reviewResultJson 审查结果JSON
     * @return 结构化的坐标数据
     */
    public List<RiskPointHighlight> parseRiskPointCoordinates(String reviewResultJson) {
        List<RiskPointHighlight> highlights = new ArrayList<>();
        
        try {
            JSONObject reviewData = JSON.parseObject(reviewResultJson);
            JSONArray dataArray = reviewData.getJSONArray("data");
            
            if (dataArray != null) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject clause = dataArray.getJSONObject(i);
                    String clauseName = clause.getString("clauseName");
                    JSONArray riskPoints = clause.getJSONArray("riskPoints");
                    
                    if (riskPoints != null) {
                        for (int j = 0; j < riskPoints.size(); j++) {
                            JSONObject riskPoint = riskPoints.getJSONObject(j);
                            highlights.addAll(parseRiskHits(riskPoint, clauseName));
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("解析风险点坐标失败", e);
        }
        
        return highlights;
    }
    
    /**
     * 解析单个风险点的命中信息
     */
    private List<RiskPointHighlight> parseRiskHits(JSONObject riskPoint, String clauseName) {
        List<RiskPointHighlight> highlights = new ArrayList<>();
        
        String riskName = riskPoint.getString("riskName");
        JSONArray riskHits = riskPoint.getJSONArray("riskHits");
        
        if (riskHits != null) {
            for (int i = 0; i < riskHits.size(); i++) {
                JSONObject hit = riskHits.getJSONObject(i);
                
                RiskPointHighlight highlight = new RiskPointHighlight();
                highlight.setClauseName(clauseName);
                highlight.setRiskName(riskName);
                highlight.setText(hit.getString("text"));
                highlight.setMatchType(hit.getString("matchType"));
                highlight.setExplain(hit.getString("explain"));
                
                // 解析坐标信息（如果存在）
                if (hit.containsKey("coordinates")) {
                    JSONObject coords = hit.getJSONObject("coordinates");
                    highlight.setPageNumber(coords.getIntValue("pageNumber"));
                    highlight.setX(coords.getDoubleValue("x"));
                    highlight.setY(coords.getDoubleValue("y"));
                    highlight.setWidth(coords.getDoubleValue("width"));
                    highlight.setHeight(coords.getDoubleValue("height"));
                }
                
                highlights.add(highlight);
            }
        }
        
        return highlights;
    }
}

/**
 * 风险点高亮数据结构
 */
@Data
public class RiskPointHighlight {
    private String clauseName;      // 条款名称
    private String riskName;        // 风险点名称
    private String text;            // 匹配文本
    private String matchType;       // 匹配类型
    private String explain;         // 解释说明
    private Integer pageNumber;     // 页码
    private Double x;               // X坐标
    private Double y;               // Y坐标
    private Double width;           // 宽度
    private Double height;          // 高度
    private String riskLevel;       // 风险级别
    private String highlightId;     // 高亮唯一ID
}
```

**验收标准**:
- ✅ 正确解析JSON中的坐标信息
- ✅ 数据结构化处理完善
- ✅ 支持多种匹配类型
- ✅ 异常处理健壮

### 任务1.5：添加Maven依赖配置
**文件位置**: `ruoyi-common/pom.xml`  
**预计工时**: 1小时

**具体代码实现**:
```xml
<!-- Word转PDF依赖 -->
<dependency>
    <groupId>fr.opensagres.xdocreport</groupId>
    <artifactId>fr.opensagres.xdocreport.converter.pdf</artifactId>
    <version>2.0.4</version>
</dependency>

<dependency>
    <groupId>fr.opensagres.xdocreport</groupId>
    <artifactId>fr.opensagres.xdocreport.document.docx</artifactId>
    <version>2.0.4</version>
</dependency>

<dependency>
    <groupId>fr.opensagres.xdocreport</groupId>
    <artifactId>fr.opensagres.xdocreport.template.freemarker</artifactId>
    <version>2.0.4</version>
</dependency>

<!-- PDF处理增强 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.30</version>
</dependency>

<!-- 文档转换工具 -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-converter</artifactId>
    <version>2.0.2</version>
</dependency>
```

### 任务1.6：完善文件权限控制
**文件位置**: `ContractReviewTaskController.java`  
**预计工时**: 2小时

**具体代码实现**:
```java
/**
 * 验证用户对任务的访问权限
 */
private boolean validateTaskAccess(Long taskId, String userId) {
    ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
    if (task == null) {
        return false;
    }
    
    // 检查是否是任务创建者
    if (userId.equals(task.getCreateBy())) {
        return true;
    }
    
    // 检查是否有查看权限（可扩展为部门权限等）
    return SecurityUtils.hasPermi("contract:task:query");
}

/**
 * 生成带权限验证的文档访问令牌
 */
private String generateDocumentAccessToken(Long taskId, Long fileId, String userId) {
    Map<String, Object> claims = new HashMap<>();
    claims.put("taskId", taskId);
    claims.put("fileId", fileId);
    claims.put("userId", userId);
    claims.put("expire", System.currentTimeMillis() + 3600000); // 1小时过期
    
    return JwtUtils.createToken(claims);
}
```

### 任务1.7：添加审计日志记录
**文件位置**: `ContractReviewTaskController.java`  
**预计工时**: 2小时

**具体代码实现**:
```java
/**
 * 记录报告查看日志
 */
@Log(title = "合同审查报告", businessType = BusinessType.OTHER)
private void logReportAccess(Long taskId, String action) {
    try {
        ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
        String logMsg = String.format("用户查看合同审查报告，任务：%s，操作：%s", 
                                    task.getTaskName(), action);
        
        // 使用若依的日志记录机制
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(
            SecurityUtils.getUsername(), Constants.LOGIN_SUCCESS, logMsg));
            
    } catch (Exception e) {
        logger.warn("记录审计日志失败", e);
    }
}
```

### 任务1.8：编写单元测试
**文件位置**: `src/test/java/com/ruoyi/web/controller/contract/ContractReviewTaskControllerTest.java`  
**预计工时**: 1小时

**具体代码实现**:
```java
@RunWith(SpringRunner.class)
@WebMvcTest(ContractReviewTaskController.class)
public class ContractReviewTaskControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private IContractReviewTaskService contractReviewTaskService;
    
    @MockBean  
    private IContractFileService contractFileService;
    
    @Test
    public void testGetTaskReport_Success() throws Exception {
        // Mock数据
        ContractReviewTask mockTask = new ContractReviewTask();
        mockTask.setId(1L);
        mockTask.setTaskName("测试任务");
        mockTask.setTaskStatus("2");
        mockTask.setReviewResult("{\"data\":[]}");
        
        when(contractReviewTaskService.selectContractReviewTaskById(1L))
            .thenReturn(mockTask);
        when(contractFileService.selectContractFileList(any()))
            .thenReturn(Arrays.asList(new ContractFile()));
        
        // 执行测试
        mockMvc.perform(get("/contract/task/report/1")
                .with(SecurityMockMvcRequestPostProcessors.user("admin").roles("ADMIN")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.taskInfo").exists())
                .andExpect(jsonPath("$.data.contractFiles").exists());
    }
    
    @Test
    public void testGetTaskReport_TaskNotFound() throws Exception {
        when(contractReviewTaskService.selectContractReviewTaskById(1L))
            .thenReturn(null);
        
        mockMvc.perform(get("/contract/task/report/1")
                .with(SecurityMockMvcRequestPostProcessors.user("admin").roles("ADMIN")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("审查任务不存在"));
    }
}
```

---

## 🎨 阶段二：前端基础开发（20工时）

### 任务2.1：创建报告详情页面路由
**文件位置**: `router/index.ts`  
**预计工时**: 1小时

**具体代码实现**:
```typescript
// 在 contract 路由组下添加
{
  path: '/contract/report/detail/:taskId',
  name: 'ContractReportDetail',
  component: () => import('@/views/contract/report/detail/index.vue'),
  meta: {
    title: '审查报告详情',
    requireAuth: true,
    permissions: ['contract:task:query']
  }
}
```

**验收标准**:
- ✅ 路由正确注册
- ✅ 权限验证正常
- ✅ 页面标题显示正确

### 任务2.2：创建报告详情页面主框架
**文件位置**: `views/contract/report/detail/index.vue`（新建）  
**预计工时**: 3小时

**具体代码实现**:
```vue
<template>
  <div class="contract-report-detail art-full-height">
    <!-- 页面头部 -->
    <div class="report-header">
      <ElPageHeader @back="handleBack" :content="`审查报告 - ${taskInfo?.taskName || ''}`">
        <template #extra>
          <ElSpace>
            <ElButton @click="handleExportReport" :loading="exportLoading">
              <ElIcon><Download /></ElIcon>
              导出报告
            </ElButton>
            <ElButton @click="handlePrintReport">
              <ElIcon><Printer /></ElIcon>
              打印报告
            </ElButton>
          </ElSpace>
        </template>
      </ElPageHeader>
    </div>

    <!-- 主体内容 -->
    <div class="report-content" v-loading="loading">
      <div class="viewer-layout" v-if="!loading && reportData">
        <!-- 左侧任务信息面板 -->
        <div class="task-info-panel" :style="{ width: panelWidths.left + 'px' }">
          <TaskInfoPanel :task-info="taskInfo" />
        </div>

        <!-- 左侧分割线 -->
        <div 
          class="resize-handle left-handle" 
          @mousedown="startResize('left', $event)"
        />

        <!-- 中间文档查看器 -->
        <div class="document-viewer-panel" :style="{ flex: 1 }">
          <PDFDocumentViewer
            ref="pdfViewerRef"
            :document-url="documentUrl"
            :highlight-data="highlightData"
            :loading="documentLoading"
            @document-loaded="handleDocumentLoaded"
            @highlight-clicked="handleHighlightClicked"
            @page-changed="handlePageChanged"
          />
        </div>

        <!-- 右侧分割线 -->
        <div 
          class="resize-handle right-handle" 
          @mousedown="startResize('right', $event)"
        />

        <!-- 右侧审查结果面板 -->
        <div class="review-results-panel" :style="{ width: panelWidths.right + 'px' }">
          <ReviewResultsPanel
            :review-results="reviewResults"
            :active-risk-id="activeRiskId"
            @risk-item-click="handleRiskItemClick"
            @clause-toggle="handleClauseToggle"
          />
        </div>
      </div>

      <!-- 错误状态 -->
      <div class="error-state" v-else-if="error">
        <ElEmpty :description="error" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Printer } from '@element-plus/icons-vue'
import { ContractReviewTaskApi } from '@/api/contract/task'

// 导入子组件
import TaskInfoPanel from './components/TaskInfoPanel.vue'
import PDFDocumentViewer from './components/PDFDocumentViewer.vue'
import ReviewResultsPanel from './components/ReviewResultsPanel.vue'

defineOptions({ name: 'ContractReportDetail' })

// 路由相关
const route = useRoute()
const router = useRouter()
const taskId = Number(route.params.taskId)

// 响应式数据
const loading = ref(true)
const documentLoading = ref(false)
const exportLoading = ref(false)
const error = ref('')

const reportData = ref<any>(null)
const taskInfo = ref<any>(null)
const documentUrl = ref('')
const reviewResults = ref<any[]>([])
const highlightData = ref<any[]>([])

const activeRiskId = ref('')
const currentPage = ref(1)

// 面板宽度控制
const panelWidths = reactive({
  left: 300,   // 左侧任务信息面板宽度
  right: 400   // 右侧审查结果面板宽度
})

// 组件引用
const pdfViewerRef = ref()

// 页面初始化
onMounted(() => {
  loadReportData()
})

// 加载报告数据
const loadReportData = async () => {
  try {
    loading.value = true
    
    // 1. 获取报告基本数据
    const response = await ContractReviewTaskApi.getTaskReport(taskId)
    if (response.code === 200) {
      reportData.value = response.data
      taskInfo.value = response.data.taskInfo
      reviewResults.value = parseReviewResults(response.data.reviewResults)
      
      // 2. 获取文档访问URL
      await loadDocumentUrl()
      
    } else {
      throw new Error(response.msg || '获取报告数据失败')
    }
    
  } catch (err: any) {
    console.error('加载报告数据失败:', err)
    error.value = err.message || '加载报告数据失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 获取文档URL
const loadDocumentUrl = async () => {
  try {
    documentLoading.value = true
    
    const contractFiles = reportData.value?.contractFiles || []
    if (contractFiles.length === 0) {
      throw new Error('未找到关联的合同文件')
    }
    
    // 使用第一个文件
    const firstFile = contractFiles[0]
    const response = await ContractReviewTaskApi.getDocumentUrl(taskId, firstFile.id)
    
    if (response.code === 200) {
      documentUrl.value = response.data.documentUrl
      
      // 处理高亮数据
      highlightData.value = processHighlightData(reviewResults.value)
    } else {
      throw new Error(response.msg || '获取文档URL失败')
    }
    
  } catch (err: any) {
    console.error('获取文档URL失败:', err)
    ElMessage.error(err.message || '获取文档URL失败')
  } finally {
    documentLoading.value = false
  }
}

// 解析审查结果数据
const parseReviewResults = (rawResults: any) => {
  if (!rawResults || !rawResults.data) {
    return []
  }
  
  return rawResults.data.map((clause: any, clauseIndex: number) => {
    return {
      clauseId: `clause_${clauseIndex}`,
      clauseName: clause.clauseName,
      clauseContent: clause.clauseContent,
      riskPoints: clause.riskPoints?.map((risk: any, riskIndex: number) => ({
        riskId: `risk_${clauseIndex}_${riskIndex}`,
        riskName: risk.riskName,
        riskLevel: risk.riskLevel || 'normal',
        riskAnalysis: risk.riskAnalysis,
        suggestModify: risk.suggestModify,
        riskHits: risk.riskHits?.map((hit: any, hitIndex: number) => ({
          hitId: `hit_${clauseIndex}_${riskIndex}_${hitIndex}`,
          text: hit.text,
          matchType: hit.matchType,
          explain: hit.explain,
          coordinates: hit.coordinates
        })) || []
      })) || []
    }
  })
}

// 处理高亮数据
const processHighlightData = (reviewResults: any[]) => {
  const highlights: any[] = []
  
  reviewResults.forEach(clause => {
    clause.riskPoints?.forEach((risk: any) => {
      risk.riskHits?.forEach((hit: any) => {
        if (hit.coordinates) {
          highlights.push({
            id: hit.hitId,
            pageNumber: hit.coordinates.pageNumber || 1,
            x: hit.coordinates.x || 0,
            y: hit.coordinates.y || 0,
            width: hit.coordinates.width || 0,
            height: hit.coordinates.height || 0,
            text: hit.text,
            riskLevel: risk.riskLevel,
            riskName: risk.riskName,
            explain: hit.explain
          })
        }
      })
    })
  })
  
  return highlights
}

// 事件处理函数
const handleBack = () => {
  router.back()
}

const handleDocumentLoaded = () => {
  console.log('PDF文档加载完成')
}

const handleHighlightClicked = (highlightId: string) => {
  activeRiskId.value = highlightId
  console.log('点击高亮区域:', highlightId)
}

const handleRiskItemClick = (riskId: string) => {
  activeRiskId.value = riskId
  // 通知PDF查看器滚动到对应位置
  pdfViewerRef.value?.scrollToHighlight(riskId)
}

const handleClauseToggle = (clauseId: string) => {
  console.log('切换条款展开状态:', clauseId)
}

const handlePageChanged = (pageNumber: number) => {
  currentPage.value = pageNumber
}

const handleExportReport = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现报告导出功能
    ElMessage.success('报告导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handlePrintReport = () => {
  window.print()
}

// 拖拽调整面板宽度
const startResize = (side: 'left' | 'right', event: MouseEvent) => {
  // 实现拖拽调整面板宽度逻辑
  console.log('开始调整面板宽度:', side, event)
}
</script>

<style scoped lang="scss">
.contract-report-detail {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .report-header {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .report-content {
    flex: 1;
    overflow: hidden;
    padding: 16px;

    .viewer-layout {
      display: flex;
      height: 100%;
      gap: 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .task-info-panel {
        min-width: 200px;
        max-width: 500px;
        border-right: 1px solid #e5e7eb;
        background: #fafafa;
      }

      .document-viewer-panel {
        min-width: 400px;
        display: flex;
        flex-direction: column;
        background: white;
      }

      .review-results-panel {
        min-width: 300px;
        max-width: 600px;
        border-left: 1px solid #e5e7eb;
        background: #fafafa;
      }

      .resize-handle {
        width: 4px;
        background: #e5e7eb;
        cursor: col-resize;
        transition: background-color 0.2s;

        &:hover {
          background: #3b82f6;
        }
      }
    }

    .error-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: white;
      border-radius: 8px;
    }
  }
}
</style>
```

**验收标准**:
- ✅ 页面布局正确（三栏布局）
- ✅ 路由跳转正常
- ✅ 数据加载逻辑完整
- ✅ 错误状态处理
- ✅ 响应式设计

### 任务2.3：任务信息面板组件
**文件位置**: `views/contract/report/detail/components/TaskInfoPanel.vue`（新建）  
**预计工时**: 2小时

**具体代码实现**:
```vue
<template>
  <div class="task-info-panel">
    <div class="panel-header">
      <h3>任务信息</h3>
    </div>
    <div class="panel-content">
      <div class="info-group">
        <div class="info-item">
          <span class="label">任务名称:</span>
          <span class="value">{{ taskInfo?.taskName }}</span>
        </div>
        <div class="info-item">
          <span class="label">任务编号:</span>
          <span class="value">{{ taskInfo?.taskNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">合同类别:</span>
          <span class="value">{{ taskInfo?.categoryName }}</span>
        </div>
        <div class="info-item">
          <span class="label">审查立场:</span>
          <ElTag :type="taskInfo?.reviewPosition === '1' ? 'primary' : 'success'" size="small">
            {{ taskInfo?.reviewPosition === '1' ? '甲方' : '乙方' }}
          </ElTag>
        </div>
        <div class="info-item">
          <span class="label">审查策略:</span>
          <span class="value">{{ taskInfo?.strategyName }}</span>
        </div>
        <div class="info-item">
          <span class="label">任务状态:</span>
          <ElTag :type="getStatusTagType(taskInfo?.taskStatus)" size="small">
            {{ getStatusLabel(taskInfo?.taskStatus) }}
          </ElTag>
        </div>
      </div>

      <ElDivider />

      <div class="info-group">
        <div class="info-item">
          <span class="label">创建时间:</span>
          <span class="value">{{ taskInfo?.createTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">开始时间:</span>
          <span class="value">{{ taskInfo?.startTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">完成时间:</span>
          <span class="value">{{ taskInfo?.endTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建人:</span>
          <span class="value">{{ taskInfo?.createBy }}</span>
        </div>
      </div>

      <ElDivider />

      <div class="info-group">
        <div class="info-item">
          <span class="label">风险总数:</span>
          <span class="value risk-count">{{ taskInfo?.totalRiskCount || 0 }}</span>
        </div>
        <div class="info-item">
          <span class="label">重大风险:</span>
          <span class="value high-risk">{{ taskInfo?.highRiskCount || 0 }}</span>
        </div>
        <div class="info-item">
          <span class="label">一般风险:</span>
          <span class="value normal-risk">{{ taskInfo?.normalRiskCount || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElTag, ElDivider } from 'element-plus'

interface Props {
  taskInfo: any
}

defineProps<Props>()

const getStatusTagType = (status: string) => {
  const statusMap = {
    '0': 'info',
    '1': 'warning', 
    '2': 'success',
    '3': 'danger'
  }
  return statusMap[status as keyof typeof statusMap] || 'info'
}

const getStatusLabel = (status: string) => {
  const statusMap = {
    '0': '待处理',
    '1': '处理中',
    '2': '已完成', 
    '3': '失败'
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}
</script>

<style scoped lang="scss">
.task-info-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .panel-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .info-group {
      margin-bottom: 16px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-size: 14px;
          color: #6b7280;
          white-space: nowrap;
        }

        .value {
          font-size: 14px;
          color: #1f2937;
          text-align: right;
          max-width: 60%;
          word-break: break-word;

          &.risk-count {
            font-weight: 600;
            color: #3b82f6;
          }

          &.high-risk {
            font-weight: 600;
            color: #ef4444;
          }

          &.normal-risk {
            font-weight: 600;
            color: #f59e0b;
          }
        }
      }
    }

    .el-divider {
      margin: 16px 0;
    }
  }
}
</style>
```

### 任务2.4：审查结果面板组件
**文件位置**: `views/contract/report/detail/components/ReviewResultsPanel.vue`（新建）  
**预计工时**: 3小时

**具体代码实现**:
```vue
<template>
  <div class="review-results-panel">
    <div class="panel-header">
      <h3>审查结果</h3>
      <div class="header-actions">
        <ElButton size="small" @click="expandAll">
          {{ allExpanded ? '全部折叠' : '全部展开' }}
        </ElButton>
      </div>
    </div>
    
    <div class="panel-content">
      <div class="results-summary">
        <div class="summary-item">
          <span class="label">条款总数:</span>
          <span class="value">{{ reviewResults.length }}</span>
        </div>
        <div class="summary-item">
          <span class="label">风险总数:</span>
          <span class="value risk-total">{{ totalRisks }}</span>
        </div>
      </div>

      <ElDivider />

      <div class="results-list">
        <div 
          v-for="clause in reviewResults" 
          :key="clause.clauseId"
          class="clause-item"
        >
          <div 
            class="clause-header"
            @click="toggleClause(clause.clauseId)"
          >
            <div class="clause-title">
              <ElIcon class="expand-icon" :class="{ expanded: expandedClauses.has(clause.clauseId) }">
                <ArrowRight />
              </ElIcon>
              <span>{{ clause.clauseName }}</span>
              <ElBadge 
                v-if="clause.riskPoints?.length" 
                :value="clause.riskPoints.length" 
                class="risk-badge"
              />
            </div>
          </div>

          <ElCollapse v-model="expandedClauses" accordion>
            <ElCollapseItem :name="clause.clauseId">
              <div class="clause-content">
                <div class="clause-text">
                  <h5>条款内容：</h5>
                  <p class="content-text">{{ clause.clauseContent }}</p>
                </div>

                <div v-if="clause.riskPoints?.length" class="risk-points">
                  <div 
                    v-for="risk in clause.riskPoints" 
                    :key="risk.riskId"
                    class="risk-item"
                    :class="{ 
                      active: activeRiskId === risk.riskId,
                      [`risk-${risk.riskLevel}`]: true 
                    }"
                  >
                    <div class="risk-header" @click="handleRiskClick(risk.riskId)">
                      <div class="risk-title">
                        <ElIcon class="risk-level-icon">
                          <WarningFilled v-if="risk.riskLevel === 'high'" />
                          <Warning v-else />
                        </ElIcon>
                        <span>{{ risk.riskName }}</span>
                      </div>
                      <div class="risk-hits-count">
                        {{ risk.riskHits?.length || 0 }} 处
                      </div>
                    </div>

                    <div class="risk-details">
                      <div class="risk-analysis">
                        <strong>风险分析：</strong>
                        <p>{{ risk.riskAnalysis }}</p>
                      </div>

                      <div class="risk-suggestion">
                        <strong>修改建议：</strong>
                        <p>{{ risk.suggestModify }}</p>
                      </div>

                      <div v-if="risk.riskHits?.length" class="risk-hits">
                        <strong>命中位置：</strong>
                        <div 
                          v-for="hit in risk.riskHits" 
                          :key="hit.hitId"
                          class="hit-item"
                          :class="{ active: activeRiskId === hit.hitId }"
                          @click="handleHitClick(hit.hitId)"
                        >
                          <div class="hit-text">
                            <ElIcon><Location /></ElIcon>
                            "{{ hit.text }}"
                          </div>
                          <div class="hit-explain">{{ hit.explain }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ElCollapseItem>
          </ElCollapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { 
  ElButton, ElDivider, ElBadge, ElCollapse, ElCollapseItem, ElIcon 
} from 'element-plus'
import { 
  ArrowRight, WarningFilled, Warning, Location 
} from '@element-plus/icons-vue'

interface Props {
  reviewResults: any[]
  activeRiskId: string
}

interface Emits {
  (e: 'risk-item-click', riskId: string): void
  (e: 'clause-toggle', clauseId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const expandedClauses = ref(new Set<string>())
const allExpanded = ref(false)

// 计算属性
const totalRisks = computed(() => {
  return props.reviewResults.reduce((total, clause) => {
    return total + (clause.riskPoints?.length || 0)
  }, 0)
})

// 方法
const toggleClause = (clauseId: string) => {
  if (expandedClauses.value.has(clauseId)) {
    expandedClauses.value.delete(clauseId)
  } else {
    expandedClauses.value.add(clauseId)
  }
  emit('clause-toggle', clauseId)
}

const expandAll = () => {
  if (allExpanded.value) {
    expandedClauses.value.clear()
  } else {
    props.reviewResults.forEach(clause => {
      expandedClauses.value.add(clause.clauseId)
    })
  }
  allExpanded.value = !allExpanded.value
}

const handleRiskClick = (riskId: string) => {
  emit('risk-item-click', riskId)
}

const handleHitClick = (hitId: string) => {
  emit('risk-item-click', hitId)
}
</script>

<style scoped lang="scss">
.review-results-panel {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;

    .results-summary {
      padding: 16px;

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;

        .label {
          font-size: 14px;
          color: #6b7280;
        }

        .value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;

          &.risk-total {
            color: #ef4444;
          }
        }
      }
    }

    .results-list {
      .clause-item {
        border-bottom: 1px solid #f3f4f6;

        .clause-header {
          padding: 12px 16px;
          cursor: pointer;
          background: #fafafa;
          border-bottom: 1px solid #e5e7eb;

          &:hover {
            background: #f3f4f6;
          }

          .clause-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .expand-icon {
              transition: transform 0.3s ease;
              color: #6b7280;

              &.expanded {
                transform: rotate(90deg);
              }
            }

            span {
              font-weight: 500;
              color: #1f2937;
            }

            .risk-badge {
              margin-left: auto;
            }
          }
        }

        .clause-content {
          padding: 16px;

          .clause-text {
            margin-bottom: 16px;

            h5 {
              margin: 0 0 8px 0;
              font-size: 14px;
              color: #6b7280;
            }

            .content-text {
              margin: 0;
              padding: 12px;
              background: #f9fafb;
              border-radius: 6px;
              font-size: 14px;
              line-height: 1.6;
              color: #374151;
            }
          }

          .risk-points {
            .risk-item {
              margin-bottom: 16px;
              border: 1px solid #e5e7eb;
              border-radius: 8px;
              overflow: hidden;

              &.active {
                border-color: #3b82f6;
                box-shadow: 0 0 0 1px #3b82f6;
              }

              &.risk-high {
                border-left: 4px solid #ef4444;
              }

              &.risk-normal {
                border-left: 4px solid #f59e0b;
              }

              .risk-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px;
                background: #fafafa;
                cursor: pointer;

                &:hover {
                  background: #f3f4f6;
                }

                .risk-title {
                  display: flex;
                  align-items: center;
                  gap: 8px;

                  .risk-level-icon {
                    color: #ef4444;
                  }

                  span {
                    font-weight: 500;
                    color: #1f2937;
                  }
                }

                .risk-hits-count {
                  font-size: 12px;
                  color: #6b7280;
                  padding: 2px 8px;
                  background: #e5e7eb;
                  border-radius: 12px;
                }
              }

              .risk-details {
                padding: 12px;

                .risk-analysis,
                .risk-suggestion {
                  margin-bottom: 12px;

                  strong {
                    display: block;
                    margin-bottom: 4px;
                    font-size: 13px;
                    color: #374151;
                  }

                  p {
                    margin: 0;
                    padding: 8px;
                    background: #f9fafb;
                    border-radius: 4px;
                    font-size: 13px;
                    line-height: 1.5;
                    color: #6b7280;
                  }
                }

                .risk-hits {
                  strong {
                    display: block;
                    margin-bottom: 8px;
                    font-size: 13px;
                    color: #374151;
                  }

                  .hit-item {
                    padding: 8px;
                    margin-bottom: 8px;
                    border: 1px solid #e5e7eb;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &:hover {
                      background: #f9fafb;
                      border-color: #d1d5db;
                    }

                    &.active {
                      background: #dbeafe;
                      border-color: #3b82f6;
                    }

                    .hit-text {
                      display: flex;
                      align-items: center;
                      gap: 6px;
                      margin-bottom: 4px;
                      font-size: 13px;
                      color: #1f2937;
                      font-weight: 500;

                      .el-icon {
                        color: #3b82f6;
                      }
                    }

                    .hit-explain {
                      font-size: 12px;
                      color: #6b7280;
                      padding-left: 20px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-results-panel {
    .panel-header {
      padding: 12px;
    }

    .results-summary {
      padding: 12px;
    }

    .clause-header {
      padding: 10px 12px;
    }

    .clause-content {
      padding: 12px;
    }
  }
}
</style>
```

### 任务2.5：API接口类型定义
**文件位置**: `types/contract/report.ts`（新建）  
**预计工时**: 1小时

**具体代码实现**:
```typescript
import type { ContractReviewTask } from './task'
import type { ContractFile } from './file'

/**
 * 合同审查报告数据结构
 */
export interface ContractReportData {
  taskInfo: ContractReviewTask
  contractFiles: ContractFile[]
  reviewResults: ReviewResultData | null
}

/**
 * 审查结果数据结构
 */
export interface ReviewResultData {
  data: ClauseResult[]
}

/**
 * 条款审查结果
 */
export interface ClauseResult {
  clauseName: string
  clauseContent: string
  riskPoints: RiskPoint[]
}

/**
 * 风险点
 */
export interface RiskPoint {
  riskName: string
  riskLevel: 'high' | 'normal' | 'low'
  riskAnalysis: string
  suggestModify: string
  riskHits: RiskHit[]
}

/**
 * 风险命中
 */
export interface RiskHit {
  text: string
  matchType: string
  pattern?: string
  explain: string
  coordinates?: {
    pageNumber: number
    x: number
    y: number
    width: number
    height: number
  }
}

/**
 * 高亮数据结构
 */
export interface HighlightData {
  id: string
  pageNumber: number
  x: number
  y: number
  width: number
  height: number
  text: string
  riskLevel: 'high' | 'normal' | 'low'
  riskName: string
  explain: string
}

/**
 * 文档信息
 */
export interface DocumentInfo {
  documentUrl: string
  documentType: 'pdf' | 'word'
  originalType?: 'word'
  fileName: string
  fileSize: number
}

/**
 * 报告查看API响应类型
 */
export interface ReportApiResponse {
  taskInfo: ContractReviewTask
  contractFiles: ContractFile[]
  reviewResults: ReviewResultData
}
```

### 任务2.6：前端API调用函数
**文件位置**: `api/contract/task.ts`（扩展现有文件）  
**预计工时**: 2小时

**具体代码实现**:
```typescript
// 在现有的 ContractReviewTaskApi 类中添加以下方法

/**
 * 获取合同审查报告详情
 * @param taskId 任务ID
 * @returns 报告详情数据
 */
static async getTaskReport(taskId: number): Promise<RuoyiResponse<ReportApiResponse>> {
  return http.get(`/contract/task/report/${taskId}`)
}

/**
 * 获取合同文档访问URL
 * @param taskId 任务ID
 * @param fileId 文件ID
 * @returns 文档信息
 */
static async getDocumentUrl(
  taskId: number, 
  fileId: number
): Promise<RuoyiResponse<DocumentInfo>> {
  return http.get(`/contract/task/document/${taskId}/${fileId}`)
}

/**
 * 导出审查报告
 * @param taskId 任务ID
 * @param format 导出格式
 * @returns 导出结果
 */
static async exportReport(
  taskId: number, 
  format: 'pdf' | 'word' | 'excel' = 'pdf'
): Promise<RuoyiResponse> {
  return http.post(`/contract/task/export/${taskId}`, { format }, {
    responseType: 'blob'
  })
}

/**
 * 获取报告访问权限令牌
 * @param taskId 任务ID
 * @returns 访问令牌
 */
static async getReportAccessToken(taskId: number): Promise<RuoyiResponse<{ token: string }>> {
  return http.post(`/contract/task/report/${taskId}/token`)
}

/**
 * 记录报告查看日志
 * @param taskId 任务ID
 * @param action 操作类型
 * @returns 操作结果
 */
static async logReportAccess(
  taskId: number, 
  action: string
): Promise<RuoyiResponse> {
  return http.post(`/contract/task/report/${taskId}/log`, { action })
}
```

### 任务2.7：响应式布局优化
**文件位置**: `views/contract/report/detail/index.vue`（扩展现有文件）  
**预计工时**: 2小时

**具体代码实现**:
```vue
<!-- 在现有模板中添加响应式处理 -->
<template>
  <div class="contract-report-detail art-full-height" :class="{ mobile: isMobile }">
    <!-- 移动端头部 -->
    <div v-if="isMobile" class="mobile-header">
      <ElButton 
        text 
        @click="showMobileSidebar = true"
        :disabled="!reviewResults.length"
      >
        <ElIcon><Menu /></ElIcon>
        审查结果
      </ElButton>
      
      <ElButton text @click="showPDFToolbar = !showPDFToolbar">
        <ElIcon><Setting /></ElIcon>
        PDF工具
      </ElButton>
    </div>

    <!-- 移动端侧边栏 -->
    <ElDrawer
      v-if="isMobile"
      v-model="showMobileSidebar"
      title="审查结果"
      size="80%"
      direction="rtl"
    >
      <ReviewResultsPanel
        :review-results="reviewResults"
        :active-risk-id="activeRiskId"
        @risk-item-click="handleMobileRiskClick"
      />
    </ElDrawer>

    <!-- 桌面端布局保持不变 -->
    <!-- ... 现有内容 ... -->
  </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'

// 响应式相关
const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)
const showMobileSidebar = ref(false)
const showPDFToolbar = ref(false)

// 移动端特殊处理
const handleMobileRiskClick = (riskId: string) => {
  handleRiskItemClick(riskId)
  showMobileSidebar.value = false // 选择后关闭侧边栏
}

// 监听屏幕尺寸变化
watch(isMobile, (mobile) => {
  if (!mobile) {
    showMobileSidebar.value = false
  }
})
</script>

<style scoped lang="scss">
// 响应式样式
@media (max-width: 768px) {
  .contract-report-detail {
    &.mobile {
      .viewer-layout {
        flex-direction: column;
        
        .task-info-panel,
        .review-results-panel {
          display: none;
        }

        .document-viewer-panel {
          min-width: auto;
        }

        .resize-handle {
          display: none;
        }
      }
    }

    .mobile-header {
      display: flex;
      justify-content: space-between;
      padding: 8px 16px;
      background: white;
      border-bottom: 1px solid #e5e7eb;
    }
  }
}

@media (max-width: 480px) {
  .contract-report-detail {
    .report-header {
      padding: 8px 16px;

      :deep(.el-page-header__content) {
        font-size: 14px;
      }

      .header-actions {
        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }

    .report-content {
      padding: 8px;
    }
  }
}
</style>
```

### 任务2.8-2.10：其他前端基础任务
- **任务2.8**: 错误处理机制（1小时）
- **任务2.9**: 加载状态管理（2小时）
- **任务2.10**: 路由守卫配置（3小时）

---

## 📄 阶段三：PDF渲染核心（18工时）

### 任务3.1：集成PDF.js依赖
**文件位置**: `package.json` + 相关配置  
**预计工时**: 2小时

**具体代码实现**:
```bash
# 安装依赖
pnpm add pdfjs-dist@3.11.174
pnpm add -D @types/pdfjs-dist
```

```typescript
// vite.config.ts 配置
export default defineConfig({
  // ... 其他配置
  optimizeDeps: {
    include: ['pdfjs-dist']
  },
  define: {
    // 修复PDF.js在Vite中的问题
    global: 'globalThis'
  }
})
```

```typescript
// src/utils/pdf.ts - PDF.js 配置
import * as pdfjsLib from 'pdfjs-dist'

// 配置Worker路径
if (import.meta.env.DEV) {
  pdfjsLib.GlobalWorkerOptions.workerSrc = '/node_modules/pdfjs-dist/build/pdf.worker.js'
} else {
  pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`
}

export { pdfjsLib }
```

**验收标准**:
- ✅ PDF.js 正确安装和配置
- ✅ Worker 路径配置正确
- ✅ TypeScript 类型支持正常
- ✅ 开发和生产环境都能正常工作

### 任务3.2：创建PDF查看器核心组件
**文件位置**: `components/PDFDocumentViewer.vue`（新建）  
**预计工时**: 8小时

**具体代码实现**:
```vue
<template>
  <div class="pdf-document-viewer">
    <!-- 工具栏 -->
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <ElButtonGroup>
          <ElButton size="small" @click="zoomOut" :disabled="scale <= 0.5">
            <ElIcon><ZoomOut /></ElIcon>
          </ElButton>
          <ElButton size="small" @click="resetZoom">
            {{ Math.round(scale * 100) }}%
          </ElButton>
          <ElButton size="small" @click="zoomIn" :disabled="scale >= 3">
            <ElIcon><ZoomIn /></ElIcon>
          </ElButton>
        </ElButtonGroup>

        <ElDivider direction="vertical" />

        <ElButtonGroup>
          <ElButton size="small" @click="prevPage" :disabled="currentPage <= 1">
            <ElIcon><ArrowLeft /></ElIcon>
          </ElButton>
          <span class="page-indicator">
            <ElInput
              v-model="pageInputValue"
              size="small"
              style="width: 50px"
              @keyup.enter="goToPage"
              @blur="goToPage"
            />
            / {{ totalPages }}
          </span>
          <ElButton size="small" @click="nextPage" :disabled="currentPage >= totalPages">
            <ElIcon><ArrowRight /></ElIcon>
          </ElButton>
        </ElButtonGroup>
      </div>

      <div class="toolbar-right">
        <ElButton size="small" @click="highlightAll" :type="showAllHighlights ? 'primary' : 'default'">
          <ElIcon><View /></ElIcon>
          {{ showAllHighlights ? '隐藏高亮' : '显示高亮' }}
        </ElButton>
        
        <ElButton size="small" @click="downloadPDF">
          <ElIcon><Download /></ElIcon>
          下载PDF
        </ElButton>
      </div>
    </div>

    <!-- PDF内容区域 -->
    <div class="pdf-content" ref="pdfContentRef" v-loading="loading">
      <div class="pdf-container" ref="pdfContainerRef">
        <!-- PDF页面将动态插入这里 -->
      </div>
    </div>

    <!-- 页面导航侧边栏 -->
    <div class="pdf-sidebar" v-if="showSidebar">
      <div class="sidebar-header">
        <span>页面导航</span>
        <ElButton text @click="showSidebar = false">
          <ElIcon><Close /></ElIcon>
        </ElButton>
      </div>
      <div class="sidebar-content">
        <div 
          class="page-thumbnail"
          v-for="page in thumbnailPages"
          :key="page.pageNumber"
          :class="{ active: page.pageNumber === currentPage }"
          @click="goToPage(page.pageNumber)"
        >
          <canvas :ref="'thumb-' + page.pageNumber" />
          <span class="page-number">{{ page.pageNumber }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue'
import { pdfjsLib } from '@/utils/pdf'
import { ElMessage } from 'element-plus'
import { ZoomIn, ZoomOut, ArrowLeft, ArrowRight, View, Download, Close } from '@element-plus/icons-vue'

// 组件属性
interface Props {
  documentUrl: string
  highlightData: HighlightItem[]
  loading?: boolean
}

interface HighlightItem {
  id: string
  pageNumber: number
  x: number
  y: number
  width: number
  height: number
  text: string
  riskLevel: 'high' | 'normal' | 'low'
  riskName: string
  explain: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 组件事件
const emit = defineEmits<{
  documentLoaded: []
  highlightClicked: [highlightId: string]
  pageChanged: [pageNumber: number]
}>()

// 响应式数据
const pdfContentRef = ref<HTMLElement>()
const pdfContainerRef = ref<HTMLElement>()
const loading = ref(false)

const pdfDocument = ref<pdfjsLib.PDFDocumentProxy>()
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1.2)
const pageInputValue = ref('1')

const showAllHighlights = ref(false)
const showSidebar = ref(false)
const thumbnailPages = ref<any[]>([])

const renderedPages = new Map()
const highlightLayers = new Map()

// 监听器
watch(() => props.documentUrl, (newUrl) => {
  if (newUrl) {
    loadPdfDocument(newUrl)
  }
})

watch(() => props.highlightData, (newHighlights) => {
  if (newHighlights && pdfDocument.value) {
    updateHighlights()
  }
})

watch(currentPage, (newPage) => {
  pageInputValue.value = String(newPage)
  emit('pageChanged', newPage)
})

// 组件挂载
onMounted(() => {
  if (props.documentUrl) {
    loadPdfDocument(props.documentUrl)
  }
})

// PDF加载函数
const loadPdfDocument = async (url: string) => {
  try {
    loading.value = true
    
    const loadingTask = pdfjsLib.getDocument({
      url: url,
      cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
      cMapPacked: true
    })
    
    const pdf = await loadingTask.promise
    pdfDocument.value = pdf
    totalPages.value = pdf.numPages
    
    // 清空之前的内容
    if (pdfContainerRef.value) {
      pdfContainerRef.value.innerHTML = ''
    }
    renderedPages.clear()
    highlightLayers.clear()
    
    // 渲染所有页面
    await renderAllPages()
    
    emit('documentLoaded')
    
  } catch (error) {
    console.error('PDF加载失败:', error)
    ElMessage.error('PDF文档加载失败')
  } finally {
    loading.value = false
  }
}

// 渲染所有PDF页面
const renderAllPages = async () => {
  if (!pdfDocument.value || !pdfContainerRef.value) return
  
  for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
    await renderPage(pageNum)
  }
  
  // 渲染完成后处理高亮
  nextTick(() => {
    updateHighlights()
  })
}

// 渲染单个页面
const renderPage = async (pageNumber: number) => {
  if (!pdfDocument.value || !pdfContainerRef.value) return
  
  try {
    const page = await pdfDocument.value.getPage(pageNumber)
    const viewport = page.getViewport({ scale: scale.value })
    
    // 创建页面容器
    const pageContainer = document.createElement('div')
    pageContainer.className = 'pdf-page-container'
    pageContainer.dataset.pageNumber = String(pageNumber)
    
    // 创建Canvas
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')!
    canvas.width = viewport.width
    canvas.height = viewport.height
    canvas.className = 'pdf-page-canvas'
    
    // 渲染PDF页面
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise
    
    // 创建文本层
    const textLayerDiv = document.createElement('div')
    textLayerDiv.className = 'pdf-text-layer'
    textLayerDiv.style.width = viewport.width + 'px'
    textLayerDiv.style.height = viewport.height + 'px'
    
    // 渲染文本层
    const textContent = await page.getTextContent()
    pdfjsLib.renderTextLayer({
      textContent: textContent,
      container: textLayerDiv,
      viewport: viewport,
      textDivs: []
    })
    
    // 创建高亮层
    const highlightLayerDiv = document.createElement('div')
    highlightLayerDiv.className = 'pdf-highlight-layer'
    highlightLayerDiv.style.width = viewport.width + 'px'
    highlightLayerDiv.style.height = viewport.height + 'px'
    
    // 组装页面
    pageContainer.appendChild(canvas)
    pageContainer.appendChild(textLayerDiv)
    pageContainer.appendChild(highlightLayerDiv)
    pdfContainerRef.value.appendChild(pageContainer)
    
    // 记录渲染的页面
    renderedPages.set(pageNumber, {
      page,
      viewport,
      canvas,
      textLayer: textLayerDiv,
      highlightLayer: highlightLayerDiv,
      container: pageContainer
    })
    
  } catch (error) {
    console.error(`渲染第${pageNumber}页失败:`, error)
  }
}

// 更新高亮显示
const updateHighlights = () => {
  // 先清除所有现有高亮
  highlightLayers.forEach((highlights, pageNum) => {
    const pageData = renderedPages.get(pageNum)
    if (pageData && pageData.highlightLayer) {
      pageData.highlightLayer.innerHTML = ''
    }
  })
  highlightLayers.clear()
  
  if (!showAllHighlights.value) return
  
  // 添加新高亮
  props.highlightData.forEach(highlight => {
    addHighlight(highlight)
  })
}

// 添加单个高亮
const addHighlight = (highlight: HighlightItem) => {
  const pageData = renderedPages.get(highlight.pageNumber)
  if (!pageData) return
  
  const highlightElement = document.createElement('div')
  highlightElement.className = `pdf-highlight risk-${highlight.riskLevel}`
  highlightElement.dataset.highlightId = highlight.id
  highlightElement.title = `${highlight.riskName}: ${highlight.explain}`
  
  // 计算在当前缩放下的坐标
  const scaledX = highlight.x * scale.value
  const scaledY = highlight.y * scale.value
  const scaledWidth = highlight.width * scale.value
  const scaledHeight = highlight.height * scale.value
  
  highlightElement.style.position = 'absolute'
  highlightElement.style.left = scaledX + 'px'
  highlightElement.style.top = scaledY + 'px'
  highlightElement.style.width = scaledWidth + 'px'
  highlightElement.style.height = scaledHeight + 'px'
  
  // 添加点击事件
  highlightElement.addEventListener('click', () => {
    emit('highlightClicked', highlight.id)
  })
  
  pageData.highlightLayer.appendChild(highlightElement)
  
  // 记录高亮信息
  if (!highlightLayers.has(highlight.pageNumber)) {
    highlightLayers.set(highlight.pageNumber, [])
  }
  highlightLayers.get(highlight.pageNumber)!.push({
    element: highlightElement,
    data: highlight
  })
}

// 缩放控制
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.25)
    rerenderAllPages()
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.25)
    rerenderAllPages()
  }
}

const resetZoom = () => {
  scale.value = 1.2
  rerenderAllPages()
}

// 页面导航
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
    scrollToPage(currentPage.value)
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
    scrollToPage(currentPage.value)
  }
}

const goToPage = (pageNumber?: number | string) => {
  let targetPage: number
  
  if (typeof pageNumber === 'number') {
    targetPage = pageNumber
  } else {
    targetPage = parseInt(pageInputValue.value) || currentPage.value
  }
  
  if (targetPage >= 1 && targetPage <= totalPages.value) {
    currentPage.value = targetPage
    scrollToPage(targetPage)
  } else {
    pageInputValue.value = String(currentPage.value)
  }
}

// 滚动到指定页面
const scrollToPage = (pageNumber: number) => {
  const pageContainer = pdfContainerRef.value?.querySelector(`[data-page-number="${pageNumber}"]`)
  if (pageContainer && pdfContentRef.value) {
    pageContainer.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 重新渲染所有页面（用于缩放后）
const rerenderAllPages = async () => {
  loading.value = true
  try {
    await renderAllPages()
  } finally {
    loading.value = false
  }
}

// 高亮控制
const highlightAll = () => {
  showAllHighlights.value = !showAllHighlights.value
  updateHighlights()
}

// 下载PDF
const downloadPDF = () => {
  if (props.documentUrl) {
    const link = document.createElement('a')
    link.href = props.documentUrl
    link.download = '合同审查报告.pdf'
    link.click()
  }
}

// 滚动到指定高亮（外部调用）
const scrollToHighlight = (highlightId: string) => {
  const highlight = props.highlightData.find(h => h.id === highlightId)
  if (highlight) {
    // 先滚动到对应页面
    scrollToPage(highlight.pageNumber)
    
    // 然后高亮闪烁
    nextTick(() => {
      const highlightElement = pdfContainerRef.value?.querySelector(`[data-highlight-id="${highlightId}"]`)
      if (highlightElement) {
        highlightElement.classList.add('flash-highlight')
        setTimeout(() => {
          highlightElement.classList.remove('flash-highlight')
        }, 2000)
      }
    })
  }
}

// 暴露方法给父组件
defineExpose({
  scrollToHighlight
})
</script>

<style scoped lang="scss">
.pdf-document-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  .pdf-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .page-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: #6b7280;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .pdf-content {
    flex: 1;
    overflow: auto;
    background: #f3f4f6;
    padding: 20px;

    .pdf-container {
      max-width: 100%;
      margin: 0 auto;

      .pdf-page-container {
        position: relative;
        margin-bottom: 20px;
        background: white;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        overflow: hidden;

        .pdf-page-canvas {
          display: block;
          width: 100%;
          height: auto;
        }

        .pdf-text-layer {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          overflow: hidden;
          opacity: 0; // 隐藏文本层，仅用于文本选择
        }

        .pdf-highlight-layer {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;

          .pdf-highlight {
            pointer-events: all;
            cursor: pointer;
            border-radius: 2px;
            transition: all 0.2s ease;

            &.risk-high {
              background-color: rgba(239, 68, 68, 0.3);
              border: 1px solid rgba(239, 68, 68, 0.6);
            }

            &.risk-normal {
              background-color: rgba(245, 158, 11, 0.3);
              border: 1px solid rgba(245, 158, 11, 0.6);
            }

            &.risk-low {
              background-color: rgba(34, 197, 94, 0.3);
              border: 1px solid rgba(34, 197, 94, 0.6);
            }

            &:hover {
              opacity: 0.8;
              transform: scale(1.02);
            }

            &.flash-highlight {
              animation: flashHighlight 2s ease-in-out;
            }
          }
        }
      }
    }
  }

  .pdf-sidebar {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 200px;
    background: white;
    border-left: 1px solid #e5e7eb;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 10;

    .sidebar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e5e7eb;
      font-weight: 500;
    }

    .sidebar-content {
      overflow-y: auto;
      padding: 8px;

      .page-thumbnail {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        margin-bottom: 8px;
        border: 1px solid transparent;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #f3f4f6;
          border-color: #d1d5db;
        }

        &.active {
          background: #dbeafe;
          border-color: #3b82f6;
        }

        canvas {
          max-width: 100%;
          border: 1px solid #e5e7eb;
          border-radius: 2px;
        }

        .page-number {
          margin-top: 4px;
          font-size: 12px;
          color: #6b7280;
        }
      }
    }
  }
}

@keyframes flashHighlight {
  0%, 100% { background-color: rgba(59, 130, 246, 0.3); }
  50% { background-color: rgba(59, 130, 246, 0.6); }
}

// 响应式设计
@media (max-width: 768px) {
  .pdf-document-viewer {
    .pdf-toolbar {
      flex-direction: column;
      gap: 8px;
      padding: 8px;

      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }
    }

    .pdf-content {
      padding: 10px;
    }

    .pdf-sidebar {
      width: 150px;
    }
  }
}
</style>
```

**验收标准**:
- ✅ PDF文档正确加载和显示
- ✅ 缩放功能正常工作
- ✅ 页面导航功能完整
- ✅ 高亮区域正确显示
- ✅ 高亮点击事件正确触发
- ✅ 响应式布局适配

### 任务3.3-3.6：其他PDF渲染任务
- **任务3.3**: 文本层渲染和选择（3小时）
- **任务3.4**: 高亮层管理和渲染（3小时）
- **任务3.5**: 页面缓存和性能优化（2小时）
- **任务3.6**: PDF工具栏功能完善（2小时）

---

## 🔗 阶段四：交互联动（12工时）

### 任务4.1：实现风险点联动功能
**文件位置**: `components/ReviewResultsPanel.vue`（已在任务2.4中实现基础功能）  
**预计工时**: 4小时

**扩展功能实现**:
```vue
<script setup lang="ts">
// 添加高级交互功能

// 风险点搜索
const searchKeyword = ref('')
const filteredResults = computed(() => {
  if (!searchKeyword.value) return props.reviewResults
  
  return props.reviewResults.map(clause => ({
    ...clause,
    riskPoints: clause.riskPoints?.filter(risk => 
      risk.riskName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      risk.riskAnalysis.toLowerCase().includes(searchKeyword.value.toLowerCase())
    ) || []
  })).filter(clause => clause.riskPoints.length > 0)
})

// 风险点统计
const riskStats = computed(() => {
  const stats = { high: 0, normal: 0, low: 0 }
  props.reviewResults.forEach(clause => {
    clause.riskPoints?.forEach(risk => {
      stats[risk.riskLevel as keyof typeof stats]++
    })
  })
  return stats
})

// 批量操作
const selectedRisks = ref(new Set<string>())
const selectAllRisks = () => {
  const allRiskIds = props.reviewResults.flatMap(clause => 
    clause.riskPoints?.map(risk => risk.riskId) || []
  )
  selectedRisks.value = new Set(allRiskIds)
}

// 导航到风险点
const navigateToRisk = (riskId: string, smooth = true) => {
  emit('risk-item-click', riskId)
  
  if (smooth) {
    // 添加视觉反馈
    const riskElement = document.querySelector(`[data-risk-id="${riskId}"]`)
    if (riskElement) {
      riskElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      riskElement.classList.add('highlight-flash')
      setTimeout(() => {
        riskElement.classList.remove('highlight-flash')
      }, 1500)
    }
  }
}

// 键盘导航支持
const handleKeyNavigation = (event: KeyboardEvent) => {
  if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
    event.preventDefault()
    // 实现上下键导航风险点
    navigateRisksByKeyboard(event.key === 'ArrowDown' ? 1 : -1)
  }
}

const navigateRisksByKeyboard = (direction: number) => {
  const allRiskIds = props.reviewResults.flatMap(clause => 
    clause.riskPoints?.map(risk => risk.riskId) || []
  )
  
  const currentIndex = allRiskIds.findIndex(id => id === props.activeRiskId)
  const nextIndex = Math.max(0, Math.min(allRiskIds.length - 1, currentIndex + direction))
  
  if (nextIndex !== currentIndex) {
    navigateToRisk(allRiskIds[nextIndex])
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyNavigation)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyNavigation)
})
</script>

<template>
  <!-- 在原有模板基础上添加搜索和统计功能 -->
  <div class="review-results-panel">
    <div class="panel-header">
      <h3>审查结果</h3>
      
      <!-- 搜索框 -->
      <div class="search-container">
        <ElInput
          v-model="searchKeyword"
          size="small"
          placeholder="搜索风险点..."
          prefix-icon="Search"
          clearable
        />
      </div>
      
      <!-- 风险统计 -->
      <div class="risk-stats">
        <ElTag type="danger" size="small">
          重大风险: {{ riskStats.high }}
        </ElTag>
        <ElTag type="warning" size="small">
          一般风险: {{ riskStats.normal }}
        </ElTag>
        <ElTag type="success" size="small">
          低风险: {{ riskStats.low }}
        </ElTag>
      </div>
    </div>
    
    <!-- 原有内容... -->
  </div>
</template>
```

### 任务4.2：侧边栏拖拽调整
**文件位置**: `views/contract/report/detail/index.vue`  
**预计工时**: 2小时

**具体代码实现**:
```vue
<script setup lang="ts">
// 拖拽调整逻辑
const isDragging = ref(false)
const dragSide = ref<'left' | 'right'>('left')
const dragStartX = ref(0)
const dragStartWidth = ref(0)

const startResize = (side: 'left' | 'right', event: MouseEvent) => {
  event.preventDefault()
  
  isDragging.value = true
  dragSide.value = side
  dragStartX.value = event.clientX
  dragStartWidth.value = panelWidths[side]
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

const handleResize = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = event.clientX - dragStartX.value
  const newWidth = dragSide.value === 'left' 
    ? dragStartWidth.value + deltaX
    : dragStartWidth.value - deltaX
  
  // 限制最小最大宽度
  const minWidth = 200
  const maxWidth = 600
  const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, newWidth))
  
  panelWidths[dragSide.value] = constrainedWidth
}

const stopResize = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
  
  // 保存用户偏好到本地存储
  localStorage.setItem('contractReportPanelWidths', JSON.stringify(panelWidths))
}

// 加载用户偏好
onMounted(() => {
  const savedWidths = localStorage.getItem('contractReportPanelWidths')
  if (savedWidths) {
    const parsed = JSON.parse(savedWidths)
    Object.assign(panelWidths, parsed)
  }
})
</script>

<style scoped lang="scss">
.resize-handle {
  position: relative;
  
  &:hover::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 20px;
    background: #3b82f6;
    border-radius: 1px;
  }
  
  &:active {
    background: #3b82f6 !important;
  }
}

.viewer-layout {
  &.dragging {
    .task-info-panel,
    .document-viewer-panel,
    .review-results-panel {
      pointer-events: none;
    }
  }
}
</style>
```

### 任务4.3-4.5：其他交互功能
- **任务4.3**: 高亮闪烁动画（2小时）
- **任务4.4**: 搜索定位功能（2小时）
- **任务4.5**: 键盘快捷键（2小时）

---

## ⚡ 阶段五：优化完善（8工时）

### 任务5.1：懒加载和虚拟滚动
**预计工时**: 3小时

### 任务5.2：缓存机制优化
**预计工时**: 2小时

### 任务5.3：错误处理完善
**预计工时**: 2小时

### 任务5.4：单元测试编写
**预计工时**: 1小时

---

## 📋 开发优先级和依赖关系

### 关键路径分析:
```
后端API开发 → 前端基础开发 → PDF渲染核心 → 交互联动 → 优化完善
     ↓              ↓              ↓            ↓          ↓
  任务1.1-1.8    任务2.1-2.10    任务3.1-3.6   任务4.1-4.5  任务5.1-5.4
  (16工时)       (20工时)        (18工时)      (12工时)     (8工时)
```

### 并行开发建议:
- **前端基础开发**可以在后端API完成70%后开始
- **PDF渲染核心**可以独立开发和测试
- **单元测试**可以与功能开发并行进行

## 🎯 里程碑节点:

### MVP版本（54工时 - 约7个工作日）:
- ✅ 基础报告查看功能
- ✅ PDF文档显示  
- ✅ 简单高亮功能
- ✅ 基础交互联动

### 完整版本（74工时 - 约10个工作日）:
- ✅ 所有功能完整实现
- ✅ 性能优化完成
- ✅ 测试覆盖完整

## 🛠️ 技术栈确认:

### 后端新增依赖:
```xml
<!-- Word转PDF -->
<dependency>
    <groupId>fr.opensagres.xdocreport</groupId>
    <artifactId>fr.opensagres.xdocreport.converter.pdf</artifactId>
    <version>2.0.4</version>
</dependency>
<!-- PDF处理 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.30</version>
</dependency>
```

### 前端新增依赖:
```json
{
  "pdfjs-dist": "^3.11.174",
  "@types/pdfjs-dist": "^2.10.378",
  "@vueuse/core": "^10.0.0"
}
```

## 📝 注意事项

1. **防简化原则**: 严格遵循所有现有功能的完整性，在此基础上进行功能增强
2. **安全考虑**: 所有文件访问都需要权限验证和访问令牌
3. **性能优化**: PDF渲染采用分页加载，避免大文件导致的性能问题
4. **兼容性**: 确保多浏览器兼容性，特别是PDF.js的支持情况
5. **响应式设计**: 移动端和桌面端都要有良好的用户体验

## 🎉 验收标准

每个任务完成后都需要通过相应的验收标准，包括：
- 功能正确性测试
- 性能基准测试
- 安全性检查
- 用户体验评估
- 代码质量审查

---

**文档版本**: V1.0  
**创建时间**: 2025-09-12  
**创建者**: Claude 终极编程大魔王  
**预计完成时间**: 10个工作日