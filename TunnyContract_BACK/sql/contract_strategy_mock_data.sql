-- ===========================
-- 买卖合同审查策略模拟数据
-- 创建日期: 2025-01-09
-- 说明: 为买卖合同分类创建完整的审查策略、条款和风险点数据
-- ===========================

-- 1. 插入买卖合同审查策略
INSERT INTO `contract_review_strategy` (
    `strategy_name`, `strategy_desc`, `category_id`, `review_position`, 
    `strategy_status`, `version`, `is_default`, `create_by` 
) VALUES 
-- 甲方策略
(
    '买卖合同甲方审查策略', 
    '针对买卖合同甲方立场的标准审查策略，重点关注付款条件、交付风险、质量保证等甲方核心利益',
    1, -- 买卖合同分类ID
    '1', -- 甲方立场
    '1', -- 已发布
    '1.0',
    '1', -- 默认策略
    'system'
),
-- 乙方策略
(
    '买卖合同乙方审查策略',
    '针对买卖合同乙方立场的标准审查策略，重点关注收款保障、交付义务、违约责任等乙方核心关切',
    1, -- 买卖合同分类ID
    '2', -- 乙方立场
    '1', -- 已发布
    '1.0',
    '1', -- 默认策略
    'system'
),
-- 甲方高风险策略
(
    '买卖合同甲方高风险审查策略',
    '针对高价值、高风险买卖合同的甲方审查策略，审查标准更加严格，风险控制更加全面',
    1, -- 买卖合同分类ID
    '1', -- 甲方立场
    '1', -- 已发布
    '2.0',
    '0', -- 非默认策略
    'system'
);

-- 2. 插入审查条款 (针对甲方策略)
INSERT INTO `contract_review_clause` (
    `strategy_id`, `clause_name`, `clause_desc`, `sort_order`, `clause_status`, `create_by`
) VALUES 
-- 甲方策略条款
(1, '合同主体条款', '审查合同当事人的主体资格、经营范围、授权代表等基本信息', 1, '1', 'system'),
(1, '标的物条款', '审查商品名称、规格、型号、数量、质量标准等标的物相关条款', 2, '1', 'system'),
(1, '价款条款', '审查商品价格、计价方式、价格调整机制、税费承担等价款相关条款', 3, '1', 'system'),
(1, '交付条款', '审查交付时间、地点、方式、验收标准、风险转移等交付相关条款', 4, '1', 'system'),
(1, '付款条款', '审查付款方式、付款期限、付款条件、逾期责任等付款相关条款', 5, '1', 'system'),
(1, '质量保证条款', '审查质量标准、质保期限、质保责任、售后服务等质量保证条款', 6, '1', 'system'),
(1, '违约责任条款', '审查违约情形、违约责任、损害赔偿、合同解除等违约责任条款', 7, '1', 'system'),
(1, '争议解决条款', '审查管辖法院、仲裁机构、适用法律等争议解决条款', 8, '1', 'system'),

-- 乙方策略条款
(2, '合同主体条款', '审查合同当事人的主体资格、付款能力、信用状况等基本信息', 1, '1', 'system'),
(2, '标的物条款', '审查商品描述准确性、技术要求可行性、交付标准合理性', 2, '1', 'system'),
(2, '价款条款', '审查价格合理性、付款保障、价格风险分担等价款相关条款', 3, '1', 'system'),
(2, '交付条款', '审查交付义务明确性、不可抗力免责、延期交付责任等', 4, '1', 'system'),
(2, '收款条款', '审查收款方式、收款保障、逾期付款利息等收款相关条款', 5, '1', 'system'),
(2, '质量责任条款', '审查质量责任范围、质保期限合理性、免责条件等', 6, '1', 'system'),
(2, '违约责任条款', '审查违约责任对等性、免责条件、责任限制等条款', 7, '1', 'system'),
(2, '争议解决条款', '审查争议解决方式、管辖约定、法律适用等条款', 8, '1', 'system');

-- 3. 插入风险点 (针对甲方策略的主要条款)
INSERT INTO `contract_risk_point` (
    `clause_id`, `risk_name`, `risk_desc`, `risk_level`, `risk_analysis`, 
    `suggest_modify`, `keyword_pattern`, `sort_order`, `risk_status`, `create_by`
) VALUES 
-- 合同主体条款风险点
(1, '主体资格不明确', '合同当事人主体资格描述不清晰或缺失', '1', 
 '主体资格不明确可能导致合同无效或难以执行，影响合同履行和权利救济', 
 '建议补充完整的当事人信息：1.完整的公司名称和注册地址 2.法定代表人姓名和联系方式 3.营业执照统一社会信用代码', 
 '主体|当事人|甲方|乙方|公司|企业', 1, '1', 'system'),

(1, '授权代表信息缺失', '签约人授权范围不明确或授权文件缺失', '2', 
 '授权不明确可能导致合同效力争议，影响合同的法律约束力', 
 '建议明确授权代表信息：1.授权代表姓名和职务 2.授权范围和期限 3.提供授权委托书', 
 '授权|代表|委托|签约人|法定代表人', 2, '1', 'system'),

-- 标的物条款风险点  
(2, '商品描述不准确', '商品名称、规格、型号等描述不够准确或存在歧义', '1',
 '商品描述不准确可能导致交付争议，影响合同履行和验收', 
 '建议完善商品描述：1.准确的商品名称和型号 2.详细的技术规格和参数 3.明确的质量标准', 
 '商品|货物|产品|规格|型号|技术参数|质量标准', 1, '1', 'system'),

(2, '数量计量单位不明', '商品数量或计量单位表述不清晰', '2',
 '数量不明确可能导致交付数量争议和结算纠纷', 
 '建议明确数量信息：1.准确的数量和计量单位 2.计量方式和标准 3.允许的误差范围', 
 '数量|计量|单位|吨|件|套|台|误差', 2, '1', 'system'),

-- 价款条款风险点
(3, '价格调整机制缺失', '缺乏价格调整机制或调整条件不明确', '1',
 '价格固定可能面临原材料涨价风险，缺乏调整机制可能导致损失', 
 '建议建立价格调整机制：1.明确调价触发条件 2.调价计算方式 3.调价通知程序', 
 '价格|调价|涨价|原材料|成本|调整', 1, '1', 'system'),

(3, '税费承担约定不清', '税费承担责任约定不明确或缺失', '2',
 '税费承担不明确可能导致额外成本和争议', 
 '建议明确税费承担：1.各项税费的承担方 2.税率变化的处理 3.发票开具要求', 
 '税费|税收|发票|增值税|承担|负担', 2, '1', 'system'),

-- 交付条款风险点
(4, '交付时间约定模糊', '交付时间表述不够明确或缺乏具体日期', '1',
 '交付时间不明确可能导致延期交付争议和违约风险',
 '建议明确交付时间：1.具体的交付日期或期限 2.交付进度安排 3.延期交付的处理',
 '交付|交货|时间|日期|期限|延期|逾期', 1, '1', 'system'),

(4, '验收标准不明确', '货物验收标准和程序约定不清晰', '1',
 '验收标准不明确可能导致验收争议和质量纠纷',
 '建议完善验收条款：1.明确的验收标准和方法 2.验收期限和程序 3.不合格品的处理',
 '验收|检验|标准|合格|不合格|质量检测', 2, '1', 'system'),

(4, '风险转移时点不清', '货物风险转移时点约定不明确', '2',
 '风险转移不明确可能导致损失承担争议',
 '建议明确风险转移：1.风险转移的具体时点 2.运输保险安排 3.货损责任划分',
 '风险|转移|运输|保险|货损|责任', 3, '1', 'system'),

-- 付款条款风险点
(5, '付款方式风险高', '付款方式存在较高的资金风险', '1',
 '高风险付款方式可能导致资金损失和回款困难',
 '建议采用安全付款方式：1.银行转账或承兑汇票 2.避免现金交易 3.建立付款保障机制',
 '付款|支付|现金|转账|汇票|保证金', 1, '1', 'system'),

(5, '付款期限过长', '付款期限设置不合理，存在回款风险', '1',
 '付款期限过长增加资金占用成本和坏账风险',
 '建议合理设置付款期限：1.根据行业惯例确定期限 2.设置分期付款 3.建立逾期付款责任',
 '付款期限|账期|分期|逾期|利息|违约金', 2, '1', 'system'),

-- 质量保证条款风险点
(6, '质保期限不合理', '质保期限设置过短或过长', '2',
 '质保期限不合理可能影响质量保障或增加成本',
 '建议合理设置质保期：1.参考行业标准 2.考虑产品特性 3.明确质保范围',
 '质保|保修|质量保证|保证期|质保期', 1, '1', 'system'),

(6, '售后服务约定缺失', '缺乏明确的售后服务条款', '2',
 '售后服务不明确可能影响后续维护和客户满意度',
 '建议完善售后服务：1.明确服务内容和标准 2.服务响应时间 3.服务费用承担',
 '售后|服务|维护|维修|技术支持|响应时间', 2, '1', 'system'),

-- 违约责任条款风险点
(7, '违约责任不对等', '双方违约责任设置不对等或过于严苛', '1',
 '违约责任不对等可能导致合同履行困难和法律风险',
 '建议平衡违约责任：1.确保责任对等 2.设置合理的违约金 3.明确免责条件',
 '违约|责任|违约金|赔偿|免责|不可抗力', 1, '1', 'system'),

(7, '损害赔偿范围过宽', '损害赔偿范围约定过于宽泛', '2',
 '赔偿范围过宽可能导致过度的赔偿责任',
 '建议限制赔偿范围：1.明确直接损失和间接损失 2.设置赔偿上限 3.排除特殊损失',
 '赔偿|损失|直接损失|间接损失|上限|特殊损失', 2, '1', 'system'),

-- 争议解决条款风险点
(8, '管辖约定不利', '争议解决的管辖约定对己方不利', '2',
 '不利的管辖约定可能增加诉讼成本和执行难度',
 '建议争取有利管辖：1.选择己方所在地法院 2.考虑仲裁方式 3.明确适用法律',
 '管辖|法院|仲裁|诉讼|适用法律|争议解决', 1, '1', 'system'),

-- 乙方策略风险点 (针对乙方策略的条款)
-- 合同主体条款风险点 (乙方视角)
(9, '买方资信状况不明', '买方的资信状况和付款能力未充分了解', '1',
 '买方资信不明可能导致回款风险和坏账损失',
 '建议核实买方资信：1.查询企业信用报告 2.了解财务状况 3.要求提供担保',
 '资信|信用|财务|担保|付款能力|信用报告', 1, '1', 'system'),

-- 标的物条款风险点 (乙方视角)
(10, '技术要求过于严苛', '买方提出的技术要求超出合理范围或行业标准', '1',
 '过于严苛的技术要求可能导致履约困难和成本增加',
 '建议合理化技术要求：1.参考行业标准 2.评估技术可行性 3.协商合理要求',
 '技术要求|技术标准|规格|参数|可行性|行业标准', 1, '1', 'system'),

-- 价款条款风险点 (乙方视角)
(11, '价格过低无利润', '合同价格过低，无法覆盖成本或获得合理利润', '1',
 '价格过低可能导致亏损经营和资金链风险',
 '建议重新评估价格：1.核算完整成本 2.确保合理利润 3.考虑价格调整机制',
 '价格|成本|利润|亏损|调价|盈利', 1, '1', 'system'),

-- 交付条款风险点 (乙方视角)
(12, '交付期限过紧', '交付期限设置过于紧张，存在延期风险', '1',
 '交付期限过紧可能导致延期交付和违约责任',
 '建议合理安排交付期：1.评估生产周期 2.考虑供应链风险 3.预留缓冲时间',
 '交付期限|生产周期|供应链|延期|缓冲时间', 1, '1', 'system'),

-- 收款条款风险点 (乙方视角)
(13, '收款保障不足', '缺乏有效的收款保障措施', '1',
 '收款保障不足可能导致回款困难和资金风险',
 '建议加强收款保障：1.要求预付款或保证金 2.设置收款担保 3.明确逾期付款责任',
 '收款|保障|预付款|保证金|担保|逾期付款', 1, '1', 'system'),

-- 质量责任条款风险点 (乙方视角)
(14, '质保责任过重', '质量保证责任设置过重，超出合理范围', '1',
 '过重的质保责任可能导致后续成本过高',
 '建议合理化质保责任：1.参考行业惯例 2.明确质保范围 3.设置责任上限',
 '质保责任|质量保证|保修|责任范围|上限', 1, '1', 'system'),

-- 违约责任条款风险点 (乙方视角)
(15, '己方违约金过高', '己方违约金设置过高，风险过大', '1',
 '过高的违约金可能导致过度的经济损失',
 '建议降低违约金：1.协商合理的违约金标准 2.设置违约金上限 3.明确减免条件',
 '违约金|过高|上限|减免|合理|标准', 1, '1', 'system'),

-- 争议解决条款风险点 (乙方视角)
(16, '仲裁费用承担不利', '仲裁费用承担约定对己方不利', '2',
 '不利的费用承担可能增加争议解决成本',
 '建议优化费用承担：1.协商合理的费用分担 2.考虑败诉方承担 3.明确费用范围',
 '仲裁费用|费用承担|败诉方|费用分担|争议成本', 1, '1', 'system'),

-- 高风险策略条款 (策略ID=3)
-- 先插入高风险策略的条款
INSERT INTO `contract_review_clause` (
    `strategy_id`, `clause_name`, `clause_desc`, `sort_order`, `clause_status`, `create_by`
) VALUES
(3, '主体资格深度审查', '深度审查合同主体的资质、信用、经营状况等', 1, '1', 'system'),
(3, '标的物详细规范', '详细审查标的物的技术规格、质量标准、检测要求', 2, '1', 'system'),
(3, '价款风险控制', '严格审查价格机制、支付保障、汇率风险等', 3, '1', 'system'),
(3, '交付风险管控', '全面审查交付条件、物流安排、保险配置等', 4, '1', 'system'),
(3, '付款安全保障', '强化付款方式、资金监管、担保措施等安全保障', 5, '1', 'system'),
(3, '质量全程管控', '建立全程质量管控体系和责任追溯机制', 6, '1', 'system'),
(3, '违约防范机制', '建立违约预警和防范机制，强化违约责任', 7, '1', 'system'),
(3, '争议预防处理', '建立争议预防机制和高效处理程序', 8, '1', 'system'),
(3, '合规性审查', '全面审查合同的法律合规性和监管要求', 9, '1', 'system'),
(3, '风险评估报告', '要求提供全面的风险评估和应对预案', 10, '1', 'system');

-- 高风险策略的关键风险点
INSERT INTO `contract_risk_point` (
    `clause_id`, `risk_name`, `risk_desc`, `risk_level`, `risk_analysis`,
    `suggest_modify`, `keyword_pattern`, `sort_order`, `risk_status`, `create_by`
) VALUES
-- 主体资格深度审查风险点
(17, '关联交易未披露', '存在关联交易但未充分披露相关风险', '1',
 '隐瞒关联交易可能导致利益冲突和决策偏差',
 '要求披露关联交易：1.详细说明关联关系 2.披露交易背景和必要性 3.提供独立性声明',
 '关联交易|关联方|利益冲突|独立性|披露', 1, '1', 'system'),

(17, '实际控制人风险', '实际控制人存在重大风险或不稳定因素', '1',
 '实际控制人风险可能影响合同履行的稳定性',
 '深度调查实际控制人：1.核实控制权结构 2.了解个人信用状况 3.评估稳定性风险',
 '实际控制人|控制权|股权结构|信用状况|稳定性', 2, '1', 'system'),

-- 价款风险控制风险点
(19, '汇率风险敞口大', '涉及外币交易但汇率风险防范不足', '1',
 '汇率波动可能导致重大经济损失',
 '建立汇率风险防范：1.使用汇率锁定工具 2.设置汇率调整机制 3.分散币种风险',
 '汇率|外币|汇率风险|锁定|对冲|币种', 1, '1', 'system'),

(19, '价格操纵风险', '存在价格操纵或不正当竞争的风险', '1',
 '价格操纵可能导致法律风险和声誉损失',
 '防范价格操纵：1.建立公平定价机制 2.避免垄断行为 3.遵守反垄断法规',
 '价格操纵|垄断|不正当竞争|反垄断|公平定价', 2, '1', 'system'),

-- 合规性审查风险点
(25, '反腐败合规风险', '存在商业贿赂或腐败风险', '1',
 '腐败行为可能导致严重的法律后果和声誉损失',
 '建立反腐败机制：1.明确禁止贿赂条款 2.建立举报机制 3.定期合规培训',
 '贿赂|腐败|回扣|不正当利益|合规|反腐败', 1, '1', 'system'),

(25, '数据安全合规', '涉及数据处理但数据安全保护不足', '1',
 '数据安全问题可能导致监管处罚和信息泄露风险',
 '加强数据安全：1.建立数据保护机制 2.遵守数据保护法规 3.明确数据处理责任',
 '数据安全|个人信息|数据保护|隐私|信息安全|合规', 2, '1', 'system');

-- ===========================
-- 数据验证查询
-- ===========================

-- 查询插入的策略数据
SELECT
    s.id,
    s.strategy_name,
    s.strategy_desc,
    c.category_name,
    CASE s.review_position
        WHEN '1' THEN '甲方'
        WHEN '2' THEN '乙方'
        ELSE '未知'
    END as review_position_name,
    CASE s.strategy_status
        WHEN '0' THEN '草稿'
        WHEN '1' THEN '已发布'
        ELSE '未知'
    END as strategy_status_name,
    s.version,
    CASE s.is_default
        WHEN '1' THEN '是'
        WHEN '0' THEN '否'
        ELSE '未知'
    END as is_default_name
FROM contract_review_strategy s
LEFT JOIN contract_category c ON s.category_id = c.id
WHERE c.category_name = '买卖合同'
ORDER BY s.id;

-- 查询策略对应的条款数量
SELECT
    s.strategy_name,
    COUNT(cl.id) as clause_count
FROM contract_review_strategy s
LEFT JOIN contract_review_clause cl ON s.id = cl.strategy_id
LEFT JOIN contract_category c ON s.category_id = c.id
WHERE c.category_name = '买卖合同'
GROUP BY s.id, s.strategy_name
ORDER BY s.id;

-- 查询条款对应的风险点数量
SELECT
    s.strategy_name,
    cl.clause_name,
    COUNT(rp.id) as risk_point_count,
    SUM(CASE WHEN rp.risk_level = '1' THEN 1 ELSE 0 END) as high_risk_count,
    SUM(CASE WHEN rp.risk_level = '2' THEN 1 ELSE 0 END) as normal_risk_count
FROM contract_review_strategy s
LEFT JOIN contract_review_clause cl ON s.id = cl.strategy_id
LEFT JOIN contract_risk_point rp ON cl.id = rp.clause_id
LEFT JOIN contract_category c ON s.category_id = c.id
WHERE c.category_name = '买卖合同'
GROUP BY s.id, s.strategy_name, cl.id, cl.clause_name
ORDER BY s.id, cl.sort_order;

-- 统计总体数据
SELECT
    '买卖合同策略统计' as summary,
    COUNT(DISTINCT s.id) as strategy_count,
    COUNT(DISTINCT cl.id) as clause_count,
    COUNT(DISTINCT rp.id) as risk_point_count,
    SUM(CASE WHEN rp.risk_level = '1' THEN 1 ELSE 0 END) as total_high_risk,
    SUM(CASE WHEN rp.risk_level = '2' THEN 1 ELSE 0 END) as total_normal_risk
FROM contract_review_strategy s
LEFT JOIN contract_review_clause cl ON s.id = cl.strategy_id
LEFT JOIN contract_risk_point rp ON cl.id = rp.clause_id
LEFT JOIN contract_category c ON s.category_id = c.id
WHERE c.category_name = '买卖合同';

-- ===========================
-- 执行说明
-- ===========================
/*
执行此SQL脚本后，将为买卖合同分类创建：

1. 3个审查策略：
   - 买卖合同甲方审查策略（默认策略）
   - 买卖合同乙方审查策略（默认策略）
   - 买卖合同甲方高风险审查策略（专业策略）

2. 26个审查条款：
   - 甲方策略：8个标准条款
   - 乙方策略：8个标准条款
   - 高风险策略：10个深度条款

3. 约30个风险点：
   - 涵盖重大风险和一般风险
   - 包含详细的风险分析和修改建议
   - 提供关键词匹配模式

数据特点：
- 真实性：基于实际买卖合同审查经验
- 完整性：覆盖合同审查的主要方面
- 实用性：提供具体的风险识别和建议
- 可扩展：便于后续添加更多策略和风险点
*/
