-- ===========================
-- 智能合同审查系统数据库表结构
-- 基于PRD文档v2.0设计
-- 创建日期: 2025-09-02
-- ===========================

-- 1. 合同分类表
DROP TABLE IF EXISTS `contract_category`;
CREATE TABLE `contract_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '分类名称',
  `category_desc` text COLLATE utf8mb4_bin COMMENT '分类描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
  `status` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '状态(0-正常，1-停用)',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_name` (`category_name`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同分类表';

-- 插入测试数据
INSERT INTO `contract_category` (`category_name`, `category_desc`, `sort_order`, `status`, `create_time`, `create_by`, `del_flag`) VALUES
('采购合同', '用于采购商品或服务的合同', 1, '0', NOW(), 'admin', '0'),
('销售合同', '用于销售商品或服务的合同', 2, '0', NOW(), 'admin', '0'),
('服务合同', '用于提供各类服务的合同', 3, '0', NOW(), 'admin', '0'),
('租赁合同', '用于租赁房屋、设备等的合同', 4, '0', NOW(), 'admin', '0'),
('劳动合同', '用于雇佣关系的合同', 5, '1', NOW(), 'admin', '0');

-- 2. 合同审查策略表
DROP TABLE IF EXISTS `contract_review_strategy`;
CREATE TABLE `contract_review_strategy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '策略ID',
  `strategy_name` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '策略名称',
  `strategy_desc` text COLLATE utf8mb4_bin COMMENT '策略描述',
  `category_id` bigint(20) NOT NULL COMMENT '关联合同分类ID',
  `review_position` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '审查立场(1-甲方，2-乙方)',
  `strategy_status` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '策略状态(0-草稿，1-已发布)',
  `version` varchar(20) COLLATE utf8mb4_bin DEFAULT '1.0' COMMENT '策略版本号',
  `is_default` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '是否默认策略(1-是，0-否)',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_position_status` (`review_position`, `strategy_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_strategy_category` FOREIGN KEY (`category_id`) REFERENCES `contract_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同审查策略表';

-- 3. 合同审查条款表
DROP TABLE IF EXISTS `contract_review_clause`;
CREATE TABLE `contract_review_clause` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '条款ID',
  `strategy_id` bigint(20) NOT NULL COMMENT '关联策略ID',
  `clause_name` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '条款名称',
  `clause_desc` text COLLATE utf8mb4_bin COMMENT '条款说明',
  `clause_content` text COLLATE utf8mb4_bin COMMENT '条款详细内容',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
  `clause_status` char(1) COLLATE utf8mb4_bin DEFAULT '1' COMMENT '条款状态(1-启用，0-禁用)',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_clause_strategy` FOREIGN KEY (`strategy_id`) REFERENCES `contract_review_strategy` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同审查条款表';

-- 4. 合同风险点表
DROP TABLE IF EXISTS `contract_risk_point`;
CREATE TABLE `contract_risk_point` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '风险点ID',
  `clause_id` bigint(20) NOT NULL COMMENT '关联条款ID',
  `risk_name` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '风险点名称',
  `risk_desc` text COLLATE utf8mb4_bin COMMENT '风险点描述',
  `risk_level` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '风险等级(1-重大风险，2-一般风险)',
  `risk_analysis` text COLLATE utf8mb4_bin COMMENT '风险分析',
  `suggest_modify` text COLLATE utf8mb4_bin COMMENT '修改建议',
  `keyword_pattern` text COLLATE utf8mb4_bin COMMENT '关键词匹配模式',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
  `risk_status` char(1) COLLATE utf8mb4_bin DEFAULT '1' COMMENT '风险点状态(1-启用，0-禁用)',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_clause_id` (`clause_id`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_risk_clause` FOREIGN KEY (`clause_id`) REFERENCES `contract_review_clause` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同风险点表';

-- 5. 合同审查任务表
DROP TABLE IF EXISTS `contract_review_task`;
CREATE TABLE `contract_review_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_no` varchar(50) COLLATE utf8mb4_bin NOT NULL COMMENT '任务编号',
  `task_name` varchar(200) COLLATE utf8mb4_bin NOT NULL COMMENT '任务名称',
  `category_id` bigint(20) NOT NULL COMMENT '合同分类ID',
  `strategy_id` bigint(20) NOT NULL COMMENT '审查策略ID',
  `review_position` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '审查立场(1-甲方，2-乙方)',
  `task_status` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '任务状态(0-待处理，1-处理中，2-已完成，3-失败)',
  `start_time` timestamp NULL COMMENT '开始处理时间',
  `end_time` timestamp NULL COMMENT '完成时间',
  `total_risk_count` int(11) DEFAULT 0 COMMENT '识别风险总数',
  `high_risk_count` int(11) DEFAULT 0 COMMENT '重大风险数量',
  `normal_risk_count` int(11) DEFAULT 0 COMMENT '一般风险数量',
  `error_message` text COLLATE utf8mb4_bin COMMENT '错误信息',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_category_strategy` (`category_id`, `strategy_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_task_category` FOREIGN KEY (`category_id`) REFERENCES `contract_category` (`id`),
  CONSTRAINT `fk_task_strategy` FOREIGN KEY (`strategy_id`) REFERENCES `contract_review_strategy` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同审查任务表';

-- 6. 合同文件表
DROP TABLE IF EXISTS `contract_file`;
CREATE TABLE `contract_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `task_id` bigint(20) NOT NULL COMMENT '关联审查任务ID',
  `file_name` varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) COLLATE utf8mb4_bin NOT NULL COMMENT '文件存储路径',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小(字节)',
  `file_type` varchar(50) COLLATE utf8mb4_bin COMMENT '文件类型(pdf/doc/docx)',
  `file_md5` varchar(32) COLLATE utf8mb4_bin COMMENT '文件MD5值',
  `upload_status` char(1) COLLATE utf8mb4_bin DEFAULT '1' COMMENT '上传状态(1-成功，0-失败)',
  `parse_status` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '解析状态(0-未解析，1-解析中，2-解析成功，3-解析失败)',
  `content_text` longtext COLLATE utf8mb4_bin COMMENT '解析后的文本内容',
  `page_count` int(11) DEFAULT 0 COMMENT '文档页数',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_file_md5` (`file_md5`),
  KEY `idx_parse_status` (`parse_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_file_task` FOREIGN KEY (`task_id`) REFERENCES `contract_review_task` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同文件表';

-- 7. 合同审查结果表
DROP TABLE IF EXISTS `contract_review_result`;
CREATE TABLE `contract_review_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` bigint(20) NOT NULL COMMENT '关联审查任务ID',
  `file_id` bigint(20) NOT NULL COMMENT '关联文件ID',
  `review_summary` text COLLATE utf8mb4_bin COMMENT '审查总结',
  `total_score` decimal(5,2) DEFAULT 0.00 COMMENT '总体得分',
  `risk_level` char(1) COLLATE utf8mb4_bin COMMENT '整体风险等级(1-高风险，2-中风险，3-低风险)',
  `report_json` longtext COLLATE utf8mb4_bin COMMENT '完整报告JSON数据',
  `report_html` longtext COLLATE utf8mb4_bin COMMENT '报告HTML格式',
  `export_count` int(11) DEFAULT 0 COMMENT '导出次数',
  `last_export_time` timestamp NULL COMMENT '最后导出时间',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_file` (`task_id`, `file_id`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_result_task` FOREIGN KEY (`task_id`) REFERENCES `contract_review_task` (`id`),
  CONSTRAINT `fk_result_file` FOREIGN KEY (`file_id`) REFERENCES `contract_file` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同审查结果表';

-- 8. 合同风险识别结果表
DROP TABLE IF EXISTS `contract_risk_result`;
CREATE TABLE `contract_risk_result` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '风险结果ID',
  `result_id` bigint(20) NOT NULL COMMENT '关联审查结果ID',
  `risk_point_id` bigint(20) NOT NULL COMMENT '关联风险点ID',
  `matched_text` text COLLATE utf8mb4_bin COMMENT '匹配到的文本内容',
  `page_number` int(11) COMMENT '所在页码',
  `position_start` int(11) COMMENT '文本开始位置',
  `position_end` int(11) COMMENT '文本结束位置',
  `confidence_score` decimal(5,2) DEFAULT 0.00 COMMENT '匹配置信度得分',
  `risk_level` char(1) COLLATE utf8mb4_bin NOT NULL COMMENT '风险等级(1-重大风险，2-一般风险)',
  `suggestion` text COLLATE utf8mb4_bin COMMENT '修改建议',
  `is_confirmed` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '是否已确认(1-已确认，0-待确认)',
  `confirm_time` timestamp NULL COMMENT '确认时间',
  `confirm_user` varchar(50) COLLATE utf8mb4_bin COMMENT '确认用户',
  
  -- 标准审计字段
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_result_id` (`result_id`),
  KEY `idx_risk_point_id` (`risk_point_id`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_page_number` (`page_number`),
  KEY `idx_is_confirmed` (`is_confirmed`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`),
  CONSTRAINT `fk_risk_result_review` FOREIGN KEY (`result_id`) REFERENCES `contract_review_result` (`id`),
  CONSTRAINT `fk_risk_result_point` FOREIGN KEY (`risk_point_id`) REFERENCES `contract_risk_point` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='合同风险识别结果表';

-- ===========================
-- 初始化数据
-- ===========================

-- 插入默认合同分类
INSERT INTO `contract_category` (`category_name`, `category_desc`, `sort_order`, `create_by`) VALUES
('买卖合同', '用于规范商品或服务的买卖行为，明确双方权利义务', 1, 'system'),
('租赁合同', '用于规范资产或不动产的租赁行为，确定租赁条件', 2, 'system'),
('劳动合同', '用于规范用人单位与劳动者之间的劳动关系', 3, 'system'),
('服务合同', '用于规范服务提供方与接受方之间的服务关系', 4, 'system'),
('合作合同', '用于规范多方合作的权利义务和合作条件', 5, 'system');

-- ===========================
-- 索引优化建议
-- ===========================

-- 为了提高查询性能，建议根据实际使用情况添加以下复合索引：

-- 审查任务查询优化
-- ALTER TABLE contract_review_task ADD INDEX idx_status_create (`task_status`, `create_time` DESC);
-- ALTER TABLE contract_review_task ADD INDEX idx_user_status (`create_by`, `task_status`);

-- 风险结果查询优化  
-- ALTER TABLE contract_risk_result ADD INDEX idx_result_risk_level (`result_id`, `risk_level`);
-- ALTER TABLE contract_risk_result ADD INDEX idx_result_page (`result_id`, `page_number`);

-- 文件解析状态查询优化
-- ALTER TABLE contract_file ADD INDEX idx_parse_create (`parse_status`, `create_time` DESC);

-- ===========================
-- 表结构设计说明
-- ===========================

/*
1. 所有表都遵循项目数据库规范，包含完整的审计字段和扩展字段
2. 使用utf8mb4_bin排序规则，确保中文内容的正确处理
3. 建立了完整的外键约束，保证数据一致性
4. 风险点表支持关键词匹配模式，便于AI算法集成
5. 审查结果表支持JSON和HTML格式存储，满足不同展示需求
6. 文件表记录了解析状态和文本内容，支持增量处理
7. 预留了评分机制和确认机制，支持人工干预和质量控制
8. 所有状态字段都使用char(1)类型，便于扩展和维护
*/