package com.ruoyi.common.utils.contract;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.contract.PdfParseUtil.PdfParseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * PDF解析服务类
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Component
public class PdfParseService
{
    private static final Logger log = LoggerFactory.getLogger(PdfParseService.class);

    private static final Executor PARSE_EXECUTOR = Executors.newFixedThreadPool(5);

    /**
     * 同步解析PDF文档
     *
     * @param filePath 文件路径
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public PdfParseResult parsePdfSync(String filePath) throws ServiceException
    {
        if (filePath == null || filePath.trim().isEmpty())
        {
            throw new ServiceException("文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists())
        {
            throw new ServiceException("文件不存在：" + filePath);
        }

        if (!file.canRead())
        {
            throw new ServiceException("文件无法读取：" + filePath);
        }

        log.info("开始解析PDF文档：{}", filePath);
        long startTime = System.currentTimeMillis();

        try
        {
            PdfParseResult result = PdfParseUtil.parsePdf(file);
            long endTime = System.currentTimeMillis();
            
            log.info("PDF文档解析完成：{}，耗时：{}ms，页数：{}，文本长度：{}", 
                    filePath, endTime - startTime, result.getPageCount(), 
                    result.getFullText() != null ? result.getFullText().length() : 0);
            
            return result;
        }
        catch (ServiceException e)
        {
            log.error("PDF文档解析失败：{}", filePath, e);
            throw e;
        }
        catch (Exception e)
        {
            log.error("PDF文档解析发生未知错误：{}", filePath, e);
            throw new ServiceException("PDF文档解析失败：" + e.getMessage());
        }
    }

    /**
     * 同步解析PDF文档（输入流）
     *
     * @param inputStream 输入流
     * @param fileName 文件名（用于日志）
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public PdfParseResult parsePdfSync(InputStream inputStream, String fileName) throws ServiceException
    {
        if (inputStream == null)
        {
            throw new ServiceException("输入流不能为空");
        }

        log.info("开始解析PDF文档：{}", fileName);
        long startTime = System.currentTimeMillis();

        try
        {
            PdfParseResult result = PdfParseUtil.parsePdf(inputStream);
            long endTime = System.currentTimeMillis();
            
            log.info("PDF文档解析完成：{}，耗时：{}ms，页数：{}，文本长度：{}", 
                    fileName, endTime - startTime, result.getPageCount(), 
                    result.getFullText() != null ? result.getFullText().length() : 0);
            
            return result;
        }
        catch (ServiceException e)
        {
            log.error("PDF文档解析失败：{}", fileName, e);
            throw e;
        }
        catch (Exception e)
        {
            log.error("PDF文档解析发生未知错误：{}", fileName, e);
            throw new ServiceException("PDF文档解析失败：" + e.getMessage());
        }
    }

    /**
     * 异步解析PDF文档
     *
     * @param filePath 文件路径
     * @return 异步解析结果
     */
    public CompletableFuture<PdfParseResult> parsePdfAsync(String filePath)
    {
        return CompletableFuture.supplyAsync(() -> {
            try
            {
                return parsePdfSync(filePath);
            }
            catch (ServiceException e)
            {
                throw new RuntimeException(e);
            }
        }, PARSE_EXECUTOR);
    }

    /**
     * 异步解析PDF文档（输入流）
     *
     * @param inputStream 输入流
     * @param fileName 文件名
     * @return 异步解析结果
     */
    public CompletableFuture<PdfParseResult> parsePdfAsync(InputStream inputStream, String fileName)
    {
        return CompletableFuture.supplyAsync(() -> {
            try
            {
                return parsePdfSync(inputStream, fileName);
            }
            catch (ServiceException e)
            {
                throw new RuntimeException(e);
            }
        }, PARSE_EXECUTOR);
    }

    /**
     * 验证PDF文档
     *
     * @param filePath 文件路径
     * @return 验证结果
     */
    public boolean validatePdf(String filePath)
    {
        try
        {
            File file = new File(filePath);
            if (!file.exists() || !file.canRead())
            {
                return false;
            }

            try (FileInputStream fis = new FileInputStream(file))
            {
                return PdfParseUtil.validatePdf(fis);
            }
        }
        catch (Exception e)
        {
            log.warn("PDF验证失败：{}", filePath, e);
            return false;
        }
    }

    /**
     * 验证PDF文档（输入流）
     *
     * @param inputStream 输入流
     * @return 验证结果
     */
    public boolean validatePdf(InputStream inputStream)
    {
        try
        {
            return PdfParseUtil.validatePdf(inputStream);
        }
        catch (Exception e)
        {
            log.warn("PDF验证失败", e);
            return false;
        }
    }

    /**
     * 搜索关键词
     *
     * @param parseResult 解析结果
     * @param keywords 关键词列表
     * @return 搜索结果
     */
    public Map<String, List<Map<String, Object>>> searchKeywords(PdfParseResult parseResult, List<String> keywords)
    {
        if (parseResult == null || keywords == null || keywords.isEmpty())
        {
            throw new ServiceException("解析结果和关键词不能为空");
        }

        log.info("开始搜索关键词，关键词数量：{}", keywords.size());
        long startTime = System.currentTimeMillis();

        try
        {
            Map<String, List<Map<String, Object>>> searchResults = PdfParseUtil.searchKeywords(parseResult, keywords);
            long endTime = System.currentTimeMillis();
            
            int totalMatches = searchResults.values().stream()
                    .mapToInt(List::size)
                    .sum();
                    
            log.info("关键词搜索完成，耗时：{}ms，匹配数量：{}", endTime - startTime, totalMatches);
            
            return searchResults;
        }
        catch (Exception e)
        {
            log.error("关键词搜索失败", e);
            throw new ServiceException("关键词搜索失败：" + e.getMessage());
        }
    }

    /**
     * 获取PDF统计信息
     *
     * @param parseResult 解析结果
     * @return 统计信息
     */
    public Map<String, Object> getStatistics(PdfParseResult parseResult)
    {
        if (parseResult == null)
        {
            throw new ServiceException("解析结果不能为空");
        }

        try
        {
            return PdfParseUtil.getStatistics(parseResult);
        }
        catch (Exception e)
        {
            log.error("获取PDF统计信息失败", e);
            throw new ServiceException("获取PDF统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 批量解析PDF文档
     *
     * @param filePaths 文件路径列表
     * @return 批量解析结果
     */
    public CompletableFuture<Map<String, PdfParseResult>> batchParsePdf(List<String> filePaths)
    {
        if (filePaths == null || filePaths.isEmpty())
        {
            throw new ServiceException("文件路径列表不能为空");
        }

        log.info("开始批量解析PDF文档，文件数量：{}", filePaths.size());
        
        List<CompletableFuture<Map.Entry<String, PdfParseResult>>> futures = filePaths.stream()
                .map(filePath -> CompletableFuture.<Map.Entry<String, PdfParseResult>>supplyAsync(() -> {
                    try
                    {
                        PdfParseResult result = parsePdfSync(filePath);
                        return new AbstractMap.SimpleEntry<String, PdfParseResult>(filePath, result);
                    }
                    catch (Exception e)
                    {
                        log.error("批量解析失败：{}", filePath, e);
                        return new AbstractMap.SimpleEntry<String, PdfParseResult>(filePath, null);
                    }
                }, PARSE_EXECUTOR))
                .collect(java.util.stream.Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .filter(entry -> entry.getValue() != null)
                        .collect(java.util.stream.Collectors.toMap(
                                Map.Entry::getKey, 
                                Map.Entry::getValue
                        )));
    }

    /**
     * 计算文档相似度（基于文本内容）
     *
     * @param result1 第一个解析结果
     * @param result2 第二个解析结果
     * @return 相似度（0-1之间）
     */
    public double calculateSimilarity(PdfParseResult result1, PdfParseResult result2)
    {
        if (result1 == null || result2 == null)
        {
            return 0.0;
        }

        String text1 = result1.getFullText();
        String text2 = result2.getFullText();

        if (text1 == null || text2 == null)
        {
            return 0.0;
        }

        // 简单的文本相似度计算（基于共同词汇）
        String[] words1 = text1.toLowerCase().split("\\s+");
        String[] words2 = text2.toLowerCase().split("\\s+");

        java.util.Set<String> set1 = new java.util.HashSet<>(java.util.Arrays.asList(words1));
        java.util.Set<String> set2 = new java.util.HashSet<>(java.util.Arrays.asList(words2));

        java.util.Set<String> intersection = new java.util.HashSet<>(set1);
        intersection.retainAll(set2);

        java.util.Set<String> union = new java.util.HashSet<>(set1);
        union.addAll(set2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    /**
     * 检查PDF文档健康状况
     *
     * @param filePath 文件路径
     * @return 健康检查结果
     */
    public Map<String, Object> healthCheck(String filePath)
    {
        Map<String, Object> health = new java.util.HashMap<>();
        
        try
        {
            // 基本文件检查
            File file = new File(filePath);
            health.put("fileExists", file.exists());
            health.put("fileReadable", file.canRead());
            health.put("fileSize", file.length());
            
            if (file.exists() && file.canRead())
            {
                // PDF格式验证
                boolean validPdf = validatePdf(filePath);
                health.put("validPdf", validPdf);
                
                if (validPdf)
                {
                    // 尝试解析获取基本信息
                    PdfParseResult result = parsePdfSync(filePath);
                    health.put("pageCount", result.getPageCount());
                    health.put("hasText", result.getFullText() != null && !result.getFullText().trim().isEmpty());
                    health.put("textLength", result.getFullText() != null ? result.getFullText().length() : 0);
                    health.put("textDensity", PdfParseUtil.calculateTextDensity(result));
                    health.put("metadata", result.getMetadata());
                }
            }
            
            health.put("status", "success");
        }
        catch (Exception e)
        {
            log.error("PDF健康检查失败：{}", filePath, e);
            health.put("status", "error");
            health.put("error", e.getMessage());
        }
        
        return health;
    }
}