package com.ruoyi.common.exception.dify;

/**
 * Dify异常类
 * 
 * <AUTHOR>
 */
public class DifyException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

    /** 错误码 */
    private String errorCode;

    /** 错误详情 */
    private String errorDetail;

    public DifyException()
    {
        super();
    }

    public DifyException(String message)
    {
        super(message);
    }

    public DifyException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public DifyException(String errorCode, String message)
    {
        super(message);
        this.errorCode = errorCode;
    }

    public DifyException(String errorCode, String message, String errorDetail)
    {
        super(message);
        this.errorCode = errorCode;
        this.errorDetail = errorDetail;
    }

    public DifyException(String errorCode, String message, Throwable cause)
    {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public DifyException(String errorCode, String message, String errorDetail, Throwable cause)
    {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorDetail = errorDetail;
    }

    public String getErrorCode()
    {
        return errorCode;
    }

    public void setErrorCode(String errorCode)
    {
        this.errorCode = errorCode;
    }

    public String getErrorDetail()
    {
        return errorDetail;
    }

    public void setErrorDetail(String errorDetail)
    {
        this.errorDetail = errorDetail;
    }

    @Override
    public String toString()
    {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        if (errorCode != null)
        {
            sb.append("[").append(errorCode).append("]");
        }
        sb.append(": ").append(getMessage());
        if (errorDetail != null)
        {
            sb.append(" (").append(errorDetail).append(")");
        }
        return sb.toString();
    }
}
