package com.ruoyi.common.utils.contract;

import com.ruoyi.common.exception.ServiceException;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Word文档解析工具类
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class WordParseUtil
{
    private static final Logger log = LoggerFactory.getLogger(WordParseUtil.class);

    /**
     * Word解析结果实体
     */
    public static class WordParseResult
    {
        private String fullText;
        private int paragraphCount;
        private List<ParagraphInfo> paragraphs;
        private long fileSize;
        private String docType; // docx, doc
        private Map<String, Object> metadata;

        public WordParseResult()
        {
            this.paragraphs = new ArrayList<>();
            this.metadata = new HashMap<>();
        }

        public String getFullText() { return fullText; }
        public void setFullText(String fullText) { this.fullText = fullText; }
        public int getParagraphCount() { return paragraphCount; }
        public void setParagraphCount(int paragraphCount) { this.paragraphCount = paragraphCount; }
        public List<ParagraphInfo> getParagraphs() { return paragraphs; }
        public void setParagraphs(List<ParagraphInfo> paragraphs) { this.paragraphs = paragraphs; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public String getDocType() { return docType; }
        public void setDocType(String docType) { this.docType = docType; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 段落信息
     */
    public static class ParagraphInfo
    {
        private int paragraphIndex;
        private String text;
        private int startIndex;
        private int endIndex;
        private List<RunInfo> runs;
        private String alignment;

        public ParagraphInfo()
        {
            this.runs = new ArrayList<>();
        }

        public int getParagraphIndex() { return paragraphIndex; }
        public void setParagraphIndex(int paragraphIndex) { this.paragraphIndex = paragraphIndex; }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public int getStartIndex() { return startIndex; }
        public void setStartIndex(int startIndex) { this.startIndex = startIndex; }
        public int getEndIndex() { return endIndex; }
        public void setEndIndex(int endIndex) { this.endIndex = endIndex; }
        public List<RunInfo> getRuns() { return runs; }
        public void setRuns(List<RunInfo> runs) { this.runs = runs; }
        public String getAlignment() { return alignment; }
        public void setAlignment(String alignment) { this.alignment = alignment; }
    }

    /**
     * 文本片段信息（用于前端高亮定位）
     */
    public static class RunInfo
    {
        private String text;
        private boolean bold;
        private boolean italic;
        private boolean underline;
        private String fontFamily;
        private Integer fontSize;
        private String color;
        private int startIndex;
        private int endIndex;

        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public boolean isBold() { return bold; }
        public void setBold(boolean bold) { this.bold = bold; }
        public boolean isItalic() { return italic; }
        public void setItalic(boolean italic) { this.italic = italic; }
        public boolean isUnderline() { return underline; }
        public void setUnderline(boolean underline) { this.underline = underline; }
        public String getFontFamily() { return fontFamily; }
        public void setFontFamily(String fontFamily) { this.fontFamily = fontFamily; }
        public Integer getFontSize() { return fontSize; }
        public void setFontSize(Integer fontSize) { this.fontSize = fontSize; }
        public String getColor() { return color; }
        public void setColor(String color) { this.color = color; }
        public int getStartIndex() { return startIndex; }
        public void setStartIndex(int startIndex) { this.startIndex = startIndex; }
        public int getEndIndex() { return endIndex; }
        public void setEndIndex(int endIndex) { this.endIndex = endIndex; }
    }

    /**
     * 解析Word文档
     *
     * @param inputStream Word文件输入流
     * @param fileName 文件名（用于判断文档类型）
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public static WordParseResult parseWord(InputStream inputStream, String fileName) throws ServiceException
    {
        try
        {
            if (fileName != null && fileName.toLowerCase().endsWith(".docx"))
            {
                return parseDocx(inputStream);
            }
            else if (fileName != null && fileName.toLowerCase().endsWith(".doc"))
            {
                return parseDoc(inputStream);
            }
            else
            {
                throw new ServiceException("不支持的Word文档格式，仅支持.doc和.docx格式");
            }
        }
        catch (IOException e)
        {
            log.error("解析Word文档失败：{}", fileName, e);
            throw new ServiceException("Word文档解析失败：" + e.getMessage());
        }
    }

    /**
     * 解析Word文档
     *
     * @param file Word文件
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public static WordParseResult parseWord(File file) throws ServiceException
    {
        try (FileInputStream fis = new FileInputStream(file))
        {
            WordParseResult result = parseWord(fis, file.getName());
            result.setFileSize(file.length());
            return result;
        }
        catch (IOException e)
        {
            log.error("解析Word文档失败：{}", file.getAbsolutePath(), e);
            throw new ServiceException("Word文档解析失败：" + e.getMessage());
        }
    }

    /**
     * 解析DOCX文档
     *
     * @param inputStream 输入流
     * @return 解析结果
     * @throws IOException IO异常
     */
    private static WordParseResult parseDocx(InputStream inputStream) throws IOException
    {
        WordParseResult result = new WordParseResult();
        result.setDocType("docx");

        try (XWPFDocument document = new XWPFDocument(inputStream))
        {
            // 提取全文
            try (XWPFWordExtractor extractor = new XWPFWordExtractor(document))
            {
                String fullText = extractor.getText();
                result.setFullText(fullText);
            }

            // 解析段落信息
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            result.setParagraphCount(paragraphs.size());
            
            List<ParagraphInfo> paragraphInfos = new ArrayList<>();
            int textIndex = 0;

            for (int i = 0; i < paragraphs.size(); i++)
            {
                XWPFParagraph paragraph = paragraphs.get(i);
                ParagraphInfo paragraphInfo = parseDocxParagraph(paragraph, i, textIndex);
                paragraphInfos.add(paragraphInfo);
                textIndex = paragraphInfo.getEndIndex() + 1; // 包含换行符
            }

            result.setParagraphs(paragraphInfos);

            // 提取元数据
            extractDocxMetadata(document, result);

            log.info("DOCX文档解析完成，段落数：{}，文本长度：{}", 
                    paragraphs.size(), result.getFullText() != null ? result.getFullText().length() : 0);
        }

        return result;
    }

    /**
     * 解析DOC文档
     *
     * @param inputStream 输入流
     * @return 解析结果
     * @throws IOException IO异常
     */
    private static WordParseResult parseDoc(InputStream inputStream) throws IOException
    {
        WordParseResult result = new WordParseResult();
        result.setDocType("doc");

        try (HWPFDocument document = new HWPFDocument(inputStream))
        {
            // 提取全文
            WordExtractor extractor = new WordExtractor(document);
            String fullText = extractor.getText();
            result.setFullText(fullText);

            // 解析文档范围
            Range range = document.getRange();
            
            // 简化的段落解析（DOC格式限制）
            String[] paragraphTexts = result.getFullText().split("\\r\\n|\\n|\\r");
            List<ParagraphInfo> paragraphInfos = new ArrayList<>();
            int textIndex = 0;

            for (int i = 0; i < paragraphTexts.length; i++)
            {
                String paragraphText = paragraphTexts[i];
                ParagraphInfo paragraphInfo = new ParagraphInfo();
                paragraphInfo.setParagraphIndex(i);
                paragraphInfo.setText(paragraphText);
                paragraphInfo.setStartIndex(textIndex);
                paragraphInfo.setEndIndex(textIndex + paragraphText.length());
                
                // DOC格式的Run信息简化处理
                RunInfo runInfo = new RunInfo();
                runInfo.setText(paragraphText);
                runInfo.setStartIndex(0);
                runInfo.setEndIndex(paragraphText.length());
                paragraphInfo.getRuns().add(runInfo);
                
                paragraphInfos.add(paragraphInfo);
                textIndex += paragraphText.length() + 1; // 包含换行符
            }

            result.setParagraphs(paragraphInfos);
            result.setParagraphCount(paragraphInfos.size());

            // 提取元数据
            extractDocMetadata(document, result);

            log.info("DOC文档解析完成，段落数：{}，文本长度：{}", 
                    paragraphInfos.size(), result.getFullText() != null ? result.getFullText().length() : 0);
        }

        return result;
    }

    /**
     * 解析DOCX段落
     *
     * @param paragraph 段落对象
     * @param paragraphIndex 段落索引
     * @param startTextIndex 开始文本位置
     * @return 段落信息
     */
    private static ParagraphInfo parseDocxParagraph(XWPFParagraph paragraph, int paragraphIndex, int startTextIndex)
    {
        ParagraphInfo paragraphInfo = new ParagraphInfo();
        paragraphInfo.setParagraphIndex(paragraphIndex);

        String paragraphText = paragraph.getText();
        paragraphInfo.setText(paragraphText);
        paragraphInfo.setStartIndex(startTextIndex);
        paragraphInfo.setEndIndex(startTextIndex + paragraphText.length());

        // 设置段落对齐方式
        if (paragraph.getAlignment() != null)
        {
            paragraphInfo.setAlignment(paragraph.getAlignment().toString());
        }

        // 解析Run信息
        List<XWPFRun> runs = paragraph.getRuns();
        List<RunInfo> runInfos = new ArrayList<>();
        int runTextIndex = 0;

        for (XWPFRun run : runs)
        {
            RunInfo runInfo = new RunInfo();
            String runText = run.getText(0);
            if (runText == null) runText = "";

            runInfo.setText(runText);
            runInfo.setBold(run.isBold());
            runInfo.setItalic(run.isItalic());
            runInfo.setUnderline(run.getUnderline() != null);
            runInfo.setFontFamily(run.getFontFamily());
            runInfo.setFontSize(run.getFontSize());
            runInfo.setStartIndex(runTextIndex);
            runInfo.setEndIndex(runTextIndex + runText.length());

            // 提取颜色信息
            if (run.getColor() != null)
            {
                runInfo.setColor(run.getColor());
            }

            runInfos.add(runInfo);
            runTextIndex += runText.length();
        }

        paragraphInfo.setRuns(runInfos);
        return paragraphInfo;
    }

    /**
     * 提取DOCX元数据
     *
     * @param document DOCX文档
     * @param result 解析结果
     */
    private static void extractDocxMetadata(XWPFDocument document, WordParseResult result)
    {
        try
        {
            Map<String, Object> metadata = result.getMetadata();
            
            if (document.getProperties() != null && document.getProperties().getCoreProperties() != null)
            {
                org.apache.poi.ooxml.POIXMLProperties.CoreProperties coreProps = document.getProperties().getCoreProperties();
                metadata.put("title", coreProps.getTitle());
                metadata.put("creator", coreProps.getCreator());
                metadata.put("description", coreProps.getDescription());
                metadata.put("subject", coreProps.getSubject());
                metadata.put("created", coreProps.getCreated());
                metadata.put("modified", coreProps.getModified());
                metadata.put("lastModifiedBy", coreProps.getLastModifiedByUser());
            }

            metadata.put("paragraphCount", document.getParagraphs().size());
            metadata.put("tableCount", document.getTables().size());
            
            log.debug("DOCX元数据提取完成：{}", metadata);
        }
        catch (Exception e)
        {
            log.warn("提取DOCX元数据失败", e);
        }
    }

    /**
     * 提取DOC元数据
     *
     * @param document DOC文档
     * @param result 解析结果
     */
    private static void extractDocMetadata(HWPFDocument document, WordParseResult result)
    {
        try
        {
            Map<String, Object> metadata = result.getMetadata();
            
            if (document.getDocumentSummaryInformation() != null)
            {
                org.apache.poi.hpsf.DocumentSummaryInformation summaryInfo = document.getDocumentSummaryInformation();
                metadata.put("company", summaryInfo.getCompany());
                metadata.put("category", summaryInfo.getCategory());
            }

            if (document.getSummaryInformation() != null)
            {
                org.apache.poi.hpsf.SummaryInformation summaryInfo = document.getSummaryInformation();
                metadata.put("title", summaryInfo.getTitle());
                metadata.put("author", summaryInfo.getAuthor());
                metadata.put("subject", summaryInfo.getSubject());
                metadata.put("comments", summaryInfo.getComments());
                metadata.put("createDateTime", summaryInfo.getCreateDateTime());
                metadata.put("lastSaveDateTime", summaryInfo.getLastSaveDateTime());
                metadata.put("lastAuthor", summaryInfo.getLastAuthor());
            }

            Range range = document.getRange();
            // 使用文本长度作为字符数统计
            String rangeText = range.text();
            metadata.put("characterCount", rangeText != null ? rangeText.length() : 0);
            metadata.put("sectionCount", document.getSectionTable().getSections().size());
            
            log.debug("DOC元数据提取完成：{}", metadata);
        }
        catch (Exception e)
        {
            log.warn("提取DOC元数据失败", e);
        }
    }

    /**
     * 验证Word文档是否可解析
     *
     * @param inputStream Word文件输入流
     * @param fileName 文件名
     * @return 是否可解析
     */
    public static boolean validateWord(InputStream inputStream, String fileName)
    {
        try
        {
            if (fileName != null && fileName.toLowerCase().endsWith(".docx"))
            {
                try (XWPFDocument document = new XWPFDocument(inputStream))
                {
                    return document.getParagraphs() != null;
                }
            }
            else if (fileName != null && fileName.toLowerCase().endsWith(".doc"))
            {
                try (HWPFDocument document = new HWPFDocument(inputStream))
                {
                    return document.getRange() != null;
                }
            }
            else
            {
                return false;
            }
        }
        catch (Exception e)
        {
            log.warn("Word文档验证失败：{}", fileName, e);
            return false;
        }
    }

    /**
     * 在文本中搜索关键词并返回位置信息
     *
     * @param parseResult 解析结果
     * @param keywords 关键词列表
     * @return 关键词位置信息
     */
    public static Map<String, List<Map<String, Object>>> searchKeywords(WordParseResult parseResult, List<String> keywords)
    {
        Map<String, List<Map<String, Object>>> searchResults = new HashMap<>();
        
        for (String keyword : keywords)
        {
            List<Map<String, Object>> keywordPositions = new ArrayList<>();
            
            // 在每个段落中搜索关键词
            for (ParagraphInfo paragraph : parseResult.getParagraphs())
            {
                Pattern pattern = Pattern.compile(Pattern.quote(keyword), Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(paragraph.getText());
                
                while (matcher.find())
                {
                    Map<String, Object> position = new HashMap<>();
                    position.put("paragraphIndex", paragraph.getParagraphIndex());
                    position.put("startIndex", matcher.start());
                    position.put("endIndex", matcher.end());
                    position.put("matchText", matcher.group());
                    position.put("contextText", getContextText(paragraph.getText(), matcher.start(), matcher.end()));
                    
                    // 尝试找到对应的Run
                    for (RunInfo run : paragraph.getRuns())
                    {
                        if (matcher.start() >= run.getStartIndex() && matcher.end() <= run.getEndIndex())
                        {
                            position.put("bold", run.isBold());
                            position.put("italic", run.isItalic());
                            position.put("underline", run.isUnderline());
                            position.put("fontFamily", run.getFontFamily());
                            position.put("fontSize", run.getFontSize());
                            position.put("color", run.getColor());
                            break;
                        }
                    }
                    
                    keywordPositions.add(position);
                }
            }
            
            searchResults.put(keyword, keywordPositions);
        }
        
        return searchResults;
    }

    /**
     * 获取关键词上下文文本
     *
     * @param text 原文本
     * @param startIndex 开始位置
     * @param endIndex 结束位置
     * @return 上下文文本
     */
    private static String getContextText(String text, int startIndex, int endIndex)
    {
        int contextLength = 50; // 上下文长度
        int contextStart = Math.max(0, startIndex - contextLength);
        int contextEnd = Math.min(text.length(), endIndex + contextLength);
        
        String context = text.substring(contextStart, contextEnd);
        
        // 添加省略号
        if (contextStart > 0) context = "..." + context;
        if (contextEnd < text.length()) context = context + "...";
        
        return context;
    }

    /**
     * 计算Word文档的文本密度
     *
     * @param parseResult 解析结果
     * @return 文本密度（字符数/段落数）
     */
    public static double calculateTextDensity(WordParseResult parseResult)
    {
        if (parseResult.getParagraphCount() == 0) return 0.0;
        
        int totalChars = parseResult.getFullText() != null ? parseResult.getFullText().length() : 0;
        return (double) totalChars / parseResult.getParagraphCount();
    }

    /**
     * 获取Word文档统计信息
     *
     * @param parseResult 解析结果
     * @return 统计信息
     */
    public static Map<String, Object> getStatistics(WordParseResult parseResult)
    {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("paragraphCount", parseResult.getParagraphCount());
        stats.put("totalChars", parseResult.getFullText() != null ? parseResult.getFullText().length() : 0);
        stats.put("textDensity", calculateTextDensity(parseResult));
        stats.put("averageWordsPerParagraph", 
                parseResult.getParagraphCount() > 0 ? 
                    (parseResult.getFullText() != null ? parseResult.getFullText().split("\\s+").length : 0) / parseResult.getParagraphCount() : 0);
        stats.put("fileSize", parseResult.getFileSize());
        stats.put("docType", parseResult.getDocType());
        stats.put("metadata", parseResult.getMetadata());
        
        // 计算格式统计
        int boldCount = 0;
        int italicCount = 0;
        int underlineCount = 0;
        
        for (ParagraphInfo paragraph : parseResult.getParagraphs())
        {
            for (RunInfo run : paragraph.getRuns())
            {
                if (run.isBold()) boldCount++;
                if (run.isItalic()) italicCount++;
                if (run.isUnderline()) underlineCount++;
            }
        }
        
        stats.put("boldCount", boldCount);
        stats.put("italicCount", italicCount);
        stats.put("underlineCount", underlineCount);
        
        return stats;
    }

    /**
     * 提取Word文档中的表格信息（仅DOCX支持）
     *
     * @param parseResult 解析结果
     * @param inputStream 输入流
     * @return 表格信息列表
     */
    public static List<Map<String, Object>> extractTables(WordParseResult parseResult, InputStream inputStream)
    {
        List<Map<String, Object>> tables = new ArrayList<>();
        
        if (!"docx".equals(parseResult.getDocType()))
        {
            log.warn("表格提取仅支持DOCX格式");
            return tables;
        }
        
        try (XWPFDocument document = new XWPFDocument(inputStream))
        {
            java.util.List<org.apache.poi.xwpf.usermodel.XWPFTable> docTables = document.getTables();

            for (int i = 0; i < docTables.size(); i++)
            {
                org.apache.poi.xwpf.usermodel.XWPFTable table = docTables.get(i);
                Map<String, Object> tableInfo = new HashMap<>();
                
                tableInfo.put("tableIndex", i);
                tableInfo.put("rowCount", table.getRows().size());
                tableInfo.put("columnCount", table.getRows().isEmpty() ? 0 : table.getRow(0).getTableCells().size());
                
                // 提取表格内容
                List<List<String>> tableData = new ArrayList<>();
                table.getRows().forEach(row -> {
                    List<String> rowData = new ArrayList<>();
                    row.getTableCells().forEach(cell -> {
                        rowData.add(cell.getText());
                    });
                    tableData.add(rowData);
                });
                
                tableInfo.put("data", tableData);
                tables.add(tableInfo);
            }
            
            log.info("提取表格信息完成，表格数量：{}", tables.size());
        }
        catch (Exception e)
        {
            log.error("提取表格信息失败", e);
        }
        
        return tables;
    }
}