package com.ruoyi.common.utils.contract;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.exception.ServiceException;

import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * PDF文档解析工具类
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class PdfParseUtil
{
    private static final Logger log = LoggerFactory.getLogger(PdfParseUtil.class);

    /**
     * PDF解析结果实体
     */
    public static class PdfParseResult
    {
        private String fullText;
        private int pageCount;
        private List<PageInfo> pages;
        private long fileSize;
        private Map<String, Object> metadata;

        public PdfParseResult()
        {
            this.pages = new ArrayList<>();
            this.metadata = new HashMap<>();
        }

        public String getFullText() { return fullText; }
        public void setFullText(String fullText) { this.fullText = fullText; }
        public int getPageCount() { return pageCount; }
        public void setPageCount(int pageCount) { this.pageCount = pageCount; }
        public List<PageInfo> getPages() { return pages; }
        public void setPages(List<PageInfo> pages) { this.pages = pages; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 页面信息
     */
    public static class PageInfo
    {
        private int pageNumber;
        private String text;
        private float width;
        private float height;
        private List<TextBlock> textBlocks;

        public PageInfo()
        {
            this.textBlocks = new ArrayList<>();
        }

        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public float getWidth() { return width; }
        public void setWidth(float width) { this.width = width; }
        public float getHeight() { return height; }
        public void setHeight(float height) { this.height = height; }
        public List<TextBlock> getTextBlocks() { return textBlocks; }
        public void setTextBlocks(List<TextBlock> textBlocks) { this.textBlocks = textBlocks; }
    }

    /**
     * 文本块信息（用于前端高亮定位）
     */
    public static class TextBlock
    {
        private String text;
        private float x;
        private float y;
        private float width;
        private float height;
        private int startIndex;
        private int endIndex;

        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public float getX() { return x; }
        public void setX(float x) { this.x = x; }
        public float getY() { return y; }
        public void setY(float y) { this.y = y; }
        public float getWidth() { return width; }
        public void setWidth(float width) { this.width = width; }
        public float getHeight() { return height; }
        public void setHeight(float height) { this.height = height; }
        public int getStartIndex() { return startIndex; }
        public void setStartIndex(int startIndex) { this.startIndex = startIndex; }
        public int getEndIndex() { return endIndex; }
        public void setEndIndex(int endIndex) { this.endIndex = endIndex; }
    }

    /**
     * 解析PDF文档
     *
     * @param inputStream PDF文件输入流
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public static PdfParseResult parsePdf(InputStream inputStream) throws ServiceException
    {
        PDDocument document = null;
        try
        {
            document = PDDocument.load(inputStream);
            return parsePdfDocument(document);
        }
        catch (IOException e)
        {
            log.error("解析PDF文档失败", e);
            throw new ServiceException("PDF文档解析失败：" + e.getMessage());
        }
        finally
        {
            if (document != null)
            {
                try
                {
                    document.close();
                }
                catch (IOException e)
                {
                    log.warn("关闭PDF文档失败", e);
                }
            }
        }
    }

    /**
     * 解析PDF文档
     *
     * @param file PDF文件
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public static PdfParseResult parsePdf(File file) throws ServiceException
    {
        PDDocument document = null;
        try
        {
            document = PDDocument.load(file);
            PdfParseResult result = parsePdfDocument(document);
            result.setFileSize(file.length());
            return result;
        }
        catch (IOException e)
        {
            log.error("解析PDF文档失败：{}", file.getAbsolutePath(), e);
            throw new ServiceException("PDF文档解析失败：" + e.getMessage());
        }
        finally
        {
            if (document != null)
            {
                try
                {
                    document.close();
                }
                catch (IOException e)
                {
                    log.warn("关闭PDF文档失败", e);
                }
            }
        }
    }

    /**
     * 解析PDF文档核心逻辑
     *
     * @param document PDF文档对象
     * @return 解析结果
     * @throws IOException IO异常
     */
    private static PdfParseResult parsePdfDocument(PDDocument document) throws IOException
    {
        PdfParseResult result = new PdfParseResult();
        
        // 基本信息
        int pageCount = document.getNumberOfPages();
        result.setPageCount(pageCount);
        log.info("PDF文档总页数：{}", pageCount);

        // 提取文档元数据
        extractMetadata(document, result);

        // 提取全文内容
        PDFTextStripper textStripper = new PDFTextStripper();
        textStripper.setSortByPosition(true);
        String fullText = textStripper.getText(document);
        result.setFullText(fullText);

        // 逐页解析
        List<PageInfo> pages = new ArrayList<>();
        for (int i = 0; i < pageCount; i++)
        {
            PageInfo pageInfo = parsePageInfo(document, i + 1);
            pages.add(pageInfo);
        }
        result.setPages(pages);

        log.info("PDF文档解析完成，总页数：{}，文本长度：{}", pageCount, fullText.length());
        return result;
    }

    /**
     * 解析单页信息
     *
     * @param document PDF文档
     * @param pageNumber 页码（从1开始）
     * @return 页面信息
     * @throws IOException IO异常
     */
    private static PageInfo parsePageInfo(PDDocument document, int pageNumber) throws IOException
    {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNumber(pageNumber);

        PDPage page = document.getPage(pageNumber - 1);
        PDRectangle pageSize = page.getMediaBox();
        pageInfo.setWidth(pageSize.getWidth());
        pageInfo.setHeight(pageSize.getHeight());

        // 提取页面文本
        PDFTextStripper textStripper = new PDFTextStripper();
        textStripper.setStartPage(pageNumber);
        textStripper.setEndPage(pageNumber);
        textStripper.setSortByPosition(true);
        String pageText = textStripper.getText(document);
        pageInfo.setText(pageText);

        // 解析文本块位置信息（用于前端高亮）
        List<TextBlock> textBlocks = parseTextBlocks(page, pageText);
        pageInfo.setTextBlocks(textBlocks);

        return pageInfo;
    }

    /**
     * 解析文本块位置信息
     * 注：这是简化实现，实际项目中可能需要更复杂的文本位置解析
     *
     * @param page PDF页面
     * @param pageText 页面文本
     * @return 文本块列表
     */
    private static List<TextBlock> parseTextBlocks(PDPage page, String pageText)
    {
        List<TextBlock> textBlocks = new ArrayList<>();
        
        try
        {
            // 按行分割文本，模拟文本块
            String[] lines = pageText.split("\\n");
            float lineHeight = 12.0f; // 默认行高
            float yPosition = page.getMediaBox().getHeight() - 50; // 从页面顶部开始
            int textIndex = 0;

            for (String line : lines)
            {
                if (line.trim().isEmpty()) continue;

                TextBlock textBlock = new TextBlock();
                textBlock.setText(line.trim());
                textBlock.setX(50.0f); // 默认左边距
                textBlock.setY(yPosition);
                textBlock.setWidth(page.getMediaBox().getWidth() - 100); // 默认宽度
                textBlock.setHeight(lineHeight);
                textBlock.setStartIndex(textIndex);
                textBlock.setEndIndex(textIndex + line.length());

                textBlocks.add(textBlock);
                
                yPosition -= lineHeight + 2; // 下一行位置
                textIndex += line.length() + 1; // 包含换行符
            }
        }
        catch (Exception e)
        {
            log.warn("解析文本块位置失败", e);
        }

        return textBlocks;
    }

    /**
     * 提取PDF元数据
     *
     * @param document PDF文档
     * @param result 解析结果
     */
    private static void extractMetadata(PDDocument document, PdfParseResult result)
    {
        try
        {
            Map<String, Object> metadata = result.getMetadata();
            
            if (document.getDocumentInformation() != null)
            {
                metadata.put("title", document.getDocumentInformation().getTitle());
                metadata.put("author", document.getDocumentInformation().getAuthor());
                metadata.put("subject", document.getDocumentInformation().getSubject());
                metadata.put("creator", document.getDocumentInformation().getCreator());
                metadata.put("producer", document.getDocumentInformation().getProducer());
                metadata.put("creationDate", document.getDocumentInformation().getCreationDate());
                metadata.put("modificationDate", document.getDocumentInformation().getModificationDate());
            }

            metadata.put("version", document.getVersion());
            metadata.put("encrypted", document.isEncrypted());
            metadata.put("pageCount", document.getNumberOfPages());

            log.debug("PDF元数据提取完成：{}", metadata);
        }
        catch (Exception e)
        {
            log.warn("提取PDF元数据失败", e);
        }
    }

    /**
     * 验证PDF文档是否可解析
     *
     * @param inputStream PDF文件输入流
     * @return 是否可解析
     */
    public static boolean validatePdf(InputStream inputStream)
    {
        PDDocument document = null;
        try
        {
            document = PDDocument.load(inputStream);
            return !document.isEncrypted() && document.getNumberOfPages() > 0;
        }
        catch (IOException e)
        {
            log.warn("PDF文档验证失败", e);
            return false;
        }
        finally
        {
            if (document != null)
            {
                try
                {
                    document.close();
                }
                catch (IOException e)
                {
                    log.warn("关闭PDF文档失败", e);
                }
            }
        }
    }

    /**
     * 在文本中搜索关键词并返回位置信息
     *
     * @param parseResult 解析结果
     * @param keywords 关键词列表
     * @return 关键词位置信息
     */
    public static Map<String, List<Map<String, Object>>> searchKeywords(PdfParseResult parseResult, List<String> keywords)
    {
        Map<String, List<Map<String, Object>>> searchResults = new HashMap<>();
        
        for (String keyword : keywords)
        {
            List<Map<String, Object>> keywordPositions = new ArrayList<>();
            
            // 在每页中搜索关键词
            for (PageInfo page : parseResult.getPages())
            {
                Pattern pattern = Pattern.compile(Pattern.quote(keyword), Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(page.getText());
                
                while (matcher.find())
                {
                    Map<String, Object> position = new HashMap<>();
                    position.put("pageNumber", page.getPageNumber());
                    position.put("startIndex", matcher.start());
                    position.put("endIndex", matcher.end());
                    position.put("matchText", matcher.group());
                    
                    // 尝试找到对应的文本块
                    for (TextBlock textBlock : page.getTextBlocks())
                    {
                        if (matcher.start() >= textBlock.getStartIndex() && matcher.end() <= textBlock.getEndIndex())
                        {
                            position.put("x", textBlock.getX());
                            position.put("y", textBlock.getY());
                            position.put("width", textBlock.getWidth());
                            position.put("height", textBlock.getHeight());
                            break;
                        }
                    }
                    
                    keywordPositions.add(position);
                }
            }
            
            searchResults.put(keyword, keywordPositions);
        }
        
        return searchResults;
    }

    /**
     * 计算PDF文档的文本密度
     *
     * @param parseResult 解析结果
     * @return 文本密度（字符数/页数）
     */
    public static double calculateTextDensity(PdfParseResult parseResult)
    {
        if (parseResult.getPageCount() == 0) return 0.0;
        
        int totalChars = parseResult.getFullText() != null ? parseResult.getFullText().length() : 0;
        return (double) totalChars / parseResult.getPageCount();
    }

    /**
     * 获取PDF文档统计信息
     *
     * @param parseResult 解析结果
     * @return 统计信息
     */
    public static Map<String, Object> getStatistics(PdfParseResult parseResult)
    {
        Map<String, Object> stats = new HashMap<>();
        
        stats.put("pageCount", parseResult.getPageCount());
        stats.put("totalChars", parseResult.getFullText() != null ? parseResult.getFullText().length() : 0);
        stats.put("textDensity", calculateTextDensity(parseResult));
        stats.put("averageWordsPerPage", 
                parseResult.getPageCount() > 0 ? 
                    (parseResult.getFullText() != null ? parseResult.getFullText().split("\\s+").length : 0) / parseResult.getPageCount() : 0);
        stats.put("fileSize", parseResult.getFileSize());
        stats.put("metadata", parseResult.getMetadata());
        
        return stats;
    }
}