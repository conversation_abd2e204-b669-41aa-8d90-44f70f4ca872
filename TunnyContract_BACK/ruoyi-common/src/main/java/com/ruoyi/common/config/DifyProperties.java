package com.ruoyi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * Dify智能体配置属性（优化版）
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "dify")
public class DifyProperties
{
    /** 是否启用Dify功能 */
    private boolean enabled;

    /** Dify服务基础URL */
    private String baseUrl;

    /** 连接配置 */
    private ConnectionConfig connection = new ConnectionConfig();

    /** 默认用户标识 */
    private String defaultUser;

    /** 智能体配置映射 - 动态配置 */
    private Map<String, AgentConfig> agents = new HashMap<>();

    /**
     * 连接配置类
     */
    public static class ConnectionConfig
    {
        /** 连接超时时间（毫秒） */
        private int connectTimeout;

        /** 读取超时时间（毫秒） */
        private int readTimeout;

        /** 写入超时时间（毫秒） */
        private int writeTimeout;

        // Getters and Setters
        public int getConnectTimeout()
        {
            return connectTimeout;
        }

        public void setConnectTimeout(int connectTimeout)
        {
            this.connectTimeout = connectTimeout;
        }

        public int getReadTimeout()
        {
            return readTimeout;
        }

        public void setReadTimeout(int readTimeout)
        {
            this.readTimeout = readTimeout;
        }

        public int getWriteTimeout()
        {
            return writeTimeout;
        }

        public void setWriteTimeout(int writeTimeout)
        {
            this.writeTimeout = writeTimeout;
        }
    }

    /**
     * 智能体配置类
     */
    public static class AgentConfig
    {
        /** API配置 */
        private AgentApiConfig api = new AgentApiConfig();

        /** 智能体描述 */
        private String description;

        /** 智能体类型 */
        private String type;

        /** 是否启用 */
        private boolean enabled = true;

        // Getters and Setters
        public AgentApiConfig getApi()
        {
            return api;
        }

        public void setApi(AgentApiConfig api)
        {
            this.api = api;
        }

        public String getDescription()
        {
            return description;
        }

        public void setDescription(String description)
        {
            this.description = description;
        }

        public String getType()
        {
            return type;
        }

        public void setType(String type)
        {
            this.type = type;
        }

        public boolean isEnabled()
        {
            return enabled;
        }

        public void setEnabled(boolean enabled)
        {
            this.enabled = enabled;
        }
    }

    /**
     * 智能体API配置类
     */
    public static class AgentApiConfig
    {
        /** API密钥 */
        private String key;

        // Getters and Setters
        public String getKey()
        {
            return key;
        }

        public void setKey(String key)
        {
            this.key = key;
        }
    }

    /**
     * 配置验证
     */
    @PostConstruct
    public void validate()
    {
        if (enabled)
        {
            if (!StringUtils.hasText(baseUrl))
            {
                throw new IllegalArgumentException("dify.base-url 不能为空");
            }

            if (connection.getConnectTimeout() <= 0)
            {
                throw new IllegalArgumentException("dify.connection.connect-timeout 必须大于0");
            }

            if (connection.getReadTimeout() <= 0)
            {
                throw new IllegalArgumentException("dify.connection.read-timeout 必须大于0");
            }

            if (connection.getWriteTimeout() <= 0)
            {
                throw new IllegalArgumentException("dify.connection.write-timeout 必须大于0");
            }

            if (!StringUtils.hasText(defaultUser))
            {
                throw new IllegalArgumentException("dify.default-user 不能为空");
            }

            // 验证智能体配置
            agents.forEach((name, config) -> {
                if (config.isEnabled())
                {
                    if (!StringUtils.hasText(config.getApi().getKey()))
                    {
                        throw new IllegalArgumentException("智能体 " + name + " 的 API Key 不能为空");
                    }
                    if (!StringUtils.hasText(config.getType()))
                    {
                        throw new IllegalArgumentException("智能体 " + name + " 的类型不能为空");
                    }
                }
            });
        }
    }

    // Main class Getters and Setters
    public boolean isEnabled()
    {
        return enabled;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public String getBaseUrl()
    {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl)
    {
        this.baseUrl = baseUrl;
    }

    public ConnectionConfig getConnection()
    {
        return connection;
    }

    public void setConnection(ConnectionConfig connection)
    {
        this.connection = connection;
    }

    public String getDefaultUser()
    {
        return defaultUser;
    }

    public void setDefaultUser(String defaultUser)
    {
        this.defaultUser = defaultUser;
    }

    public Map<String, AgentConfig> getAgents()
    {
        return agents;
    }

    public void setAgents(Map<String, AgentConfig> agents)
    {
        this.agents = agents;
    }

    /**
     * 获取所有启用的智能体配置
     */
    public Map<String, AgentConfig> getEnabledAgents()
    {
        Map<String, AgentConfig> enabledAgents = new HashMap<>();
        agents.forEach((name, config) -> {
            if (config.isEnabled())
            {
                enabledAgents.put(name, config);
            }
        });
        return enabledAgents;
    }

    /**
     * 根据名称获取智能体配置
     */
    public AgentConfig getAgentConfig(String agentName)
    {
        return agents.get(agentName);
    }

    /**
     * 检查智能体是否存在且启用
     */
    public boolean isAgentEnabled(String agentName)
    {
        AgentConfig config = agents.get(agentName);
        return config != null && config.isEnabled();
    }
}
