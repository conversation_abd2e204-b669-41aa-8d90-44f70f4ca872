package com.ruoyi.common.utils.contract;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.contract.WordParseUtil.WordParseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Word解析服务类
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Component
public class WordParseService
{
    private static final Logger log = LoggerFactory.getLogger(WordParseService.class);

    private static final Executor PARSE_EXECUTOR = Executors.newFixedThreadPool(5);

    /**
     * 同步解析Word文档
     *
     * @param filePath 文件路径
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public WordParseResult parseWordSync(String filePath) throws ServiceException
    {
        if (filePath == null || filePath.trim().isEmpty())
        {
            throw new ServiceException("文件路径不能为空");
        }

        File file = new File(filePath);
        if (!file.exists())
        {
            throw new ServiceException("文件不存在：" + filePath);
        }

        if (!file.canRead())
        {
            throw new ServiceException("文件无法读取：" + filePath);
        }

        log.info("开始解析Word文档：{}", filePath);
        long startTime = System.currentTimeMillis();

        try
        {
            WordParseResult result = WordParseUtil.parseWord(file);
            long endTime = System.currentTimeMillis();
            
            log.info("Word文档解析完成：{}，耗时：{}ms，段落数：{}，文本长度：{}", 
                    filePath, endTime - startTime, result.getParagraphCount(), 
                    result.getFullText() != null ? result.getFullText().length() : 0);
            
            return result;
        }
        catch (ServiceException e)
        {
            log.error("Word文档解析失败：{}", filePath, e);
            throw e;
        }
        catch (Exception e)
        {
            log.error("Word文档解析发生未知错误：{}", filePath, e);
            throw new ServiceException("Word文档解析失败：" + e.getMessage());
        }
    }

    /**
     * 同步解析Word文档（输入流）
     *
     * @param inputStream 输入流
     * @param fileName 文件名（用于日志和类型判断）
     * @return 解析结果
     * @throws ServiceException 解析异常
     */
    public WordParseResult parseWordSync(InputStream inputStream, String fileName) throws ServiceException
    {
        if (inputStream == null)
        {
            throw new ServiceException("输入流不能为空");
        }

        if (fileName == null || fileName.trim().isEmpty())
        {
            throw new ServiceException("文件名不能为空");
        }

        log.info("开始解析Word文档：{}", fileName);
        long startTime = System.currentTimeMillis();

        try
        {
            WordParseResult result = WordParseUtil.parseWord(inputStream, fileName);
            long endTime = System.currentTimeMillis();
            
            log.info("Word文档解析完成：{}，耗时：{}ms，段落数：{}，文本长度：{}", 
                    fileName, endTime - startTime, result.getParagraphCount(), 
                    result.getFullText() != null ? result.getFullText().length() : 0);
            
            return result;
        }
        catch (ServiceException e)
        {
            log.error("Word文档解析失败：{}", fileName, e);
            throw e;
        }
        catch (Exception e)
        {
            log.error("Word文档解析发生未知错误：{}", fileName, e);
            throw new ServiceException("Word文档解析失败：" + e.getMessage());
        }
    }

    /**
     * 异步解析Word文档
     *
     * @param filePath 文件路径
     * @return 异步解析结果
     */
    public CompletableFuture<WordParseResult> parseWordAsync(String filePath)
    {
        return CompletableFuture.supplyAsync(() -> {
            try
            {
                return parseWordSync(filePath);
            }
            catch (ServiceException e)
            {
                throw new RuntimeException(e);
            }
        }, PARSE_EXECUTOR);
    }

    /**
     * 异步解析Word文档（输入流）
     *
     * @param inputStream 输入流
     * @param fileName 文件名
     * @return 异步解析结果
     */
    public CompletableFuture<WordParseResult> parseWordAsync(InputStream inputStream, String fileName)
    {
        return CompletableFuture.supplyAsync(() -> {
            try
            {
                return parseWordSync(inputStream, fileName);
            }
            catch (ServiceException e)
            {
                throw new RuntimeException(e);
            }
        }, PARSE_EXECUTOR);
    }

    /**
     * 验证Word文档
     *
     * @param filePath 文件路径
     * @return 验证结果
     */
    public boolean validateWord(String filePath)
    {
        try
        {
            File file = new File(filePath);
            if (!file.exists() || !file.canRead())
            {
                return false;
            }

            String fileName = file.getName();
            if (!fileName.toLowerCase().endsWith(".doc") && !fileName.toLowerCase().endsWith(".docx"))
            {
                return false;
            }

            try (FileInputStream fis = new FileInputStream(file))
            {
                return WordParseUtil.validateWord(fis, fileName);
            }
        }
        catch (Exception e)
        {
            log.warn("Word验证失败：{}", filePath, e);
            return false;
        }
    }

    /**
     * 验证Word文档（输入流）
     *
     * @param inputStream 输入流
     * @param fileName 文件名
     * @return 验证结果
     */
    public boolean validateWord(InputStream inputStream, String fileName)
    {
        try
        {
            if (fileName == null || (!fileName.toLowerCase().endsWith(".doc") && !fileName.toLowerCase().endsWith(".docx")))
            {
                return false;
            }

            return WordParseUtil.validateWord(inputStream, fileName);
        }
        catch (Exception e)
        {
            log.warn("Word验证失败：{}", fileName, e);
            return false;
        }
    }

    /**
     * 搜索关键词
     *
     * @param parseResult 解析结果
     * @param keywords 关键词列表
     * @return 搜索结果
     */
    public Map<String, List<Map<String, Object>>> searchKeywords(WordParseResult parseResult, List<String> keywords)
    {
        if (parseResult == null || keywords == null || keywords.isEmpty())
        {
            throw new ServiceException("解析结果和关键词不能为空");
        }

        log.info("开始搜索关键词，关键词数量：{}", keywords.size());
        long startTime = System.currentTimeMillis();

        try
        {
            Map<String, List<Map<String, Object>>> searchResults = WordParseUtil.searchKeywords(parseResult, keywords);
            long endTime = System.currentTimeMillis();
            
            int totalMatches = searchResults.values().stream()
                    .mapToInt(List::size)
                    .sum();
                    
            log.info("关键词搜索完成，耗时：{}ms，匹配数量：{}", endTime - startTime, totalMatches);
            
            return searchResults;
        }
        catch (Exception e)
        {
            log.error("关键词搜索失败", e);
            throw new ServiceException("关键词搜索失败：" + e.getMessage());
        }
    }

    /**
     * 获取Word统计信息
     *
     * @param parseResult 解析结果
     * @return 统计信息
     */
    public Map<String, Object> getStatistics(WordParseResult parseResult)
    {
        if (parseResult == null)
        {
            throw new ServiceException("解析结果不能为空");
        }

        try
        {
            return WordParseUtil.getStatistics(parseResult);
        }
        catch (Exception e)
        {
            log.error("获取Word统计信息失败", e);
            throw new ServiceException("获取Word统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 批量解析Word文档
     *
     * @param filePaths 文件路径列表
     * @return 批量解析结果
     */
    public CompletableFuture<Map<String, WordParseResult>> batchParseWord(List<String> filePaths)
    {
        if (filePaths == null || filePaths.isEmpty())
        {
            throw new ServiceException("文件路径列表不能为空");
        }

        log.info("开始批量解析Word文档，文件数量：{}", filePaths.size());
        
        List<CompletableFuture<Map.Entry<String, WordParseResult>>> futures = filePaths.stream()
                .map(filePath -> CompletableFuture.<Map.Entry<String, WordParseResult>>supplyAsync(() -> {
                    try
                    {
                        WordParseResult result = parseWordSync(filePath);
                        return new AbstractMap.SimpleEntry<String, WordParseResult>(filePath, result);
                    }
                    catch (Exception e)
                    {
                        log.error("批量解析失败：{}", filePath, e);
                        return new AbstractMap.SimpleEntry<String, WordParseResult>(filePath, null);
                    }
                }, PARSE_EXECUTOR))
                .collect(java.util.stream.Collectors.toList());

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .filter(entry -> entry.getValue() != null)
                        .collect(java.util.stream.Collectors.toMap(
                                Map.Entry::getKey, 
                                Map.Entry::getValue
                        )));
    }

    /**
     * 计算文档相似度（基于文本内容）
     *
     * @param result1 第一个解析结果
     * @param result2 第二个解析结果
     * @return 相似度（0-1之间）
     */
    public double calculateSimilarity(WordParseResult result1, WordParseResult result2)
    {
        if (result1 == null || result2 == null)
        {
            return 0.0;
        }

        String text1 = result1.getFullText();
        String text2 = result2.getFullText();

        if (text1 == null || text2 == null)
        {
            return 0.0;
        }

        // 简单的文本相似度计算（基于共同词汇）
        String[] words1 = text1.toLowerCase().split("\\s+");
        String[] words2 = text2.toLowerCase().split("\\s+");

        java.util.Set<String> set1 = new java.util.HashSet<>(java.util.Arrays.asList(words1));
        java.util.Set<String> set2 = new java.util.HashSet<>(java.util.Arrays.asList(words2));

        java.util.Set<String> intersection = new java.util.HashSet<>(set1);
        intersection.retainAll(set2);

        java.util.Set<String> union = new java.util.HashSet<>(set1);
        union.addAll(set2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    /**
     * 提取表格信息（仅支持DOCX）
     *
     * @param filePath 文件路径
     * @return 表格信息列表
     */
    public List<Map<String, Object>> extractTables(String filePath)
    {
        try
        {
            File file = new File(filePath);
            if (!file.exists() || !file.canRead())
            {
                throw new ServiceException("文件不存在或无法读取：" + filePath);
            }

            if (!file.getName().toLowerCase().endsWith(".docx"))
            {
                throw new ServiceException("表格提取仅支持DOCX格式");
            }

            WordParseResult parseResult = parseWordSync(filePath);
            
            try (FileInputStream fis = new FileInputStream(file))
            {
                return WordParseUtil.extractTables(parseResult, fis);
            }
        }
        catch (Exception e)
        {
            log.error("提取表格信息失败：{}", filePath, e);
            throw new ServiceException("提取表格信息失败：" + e.getMessage());
        }
    }

    /**
     * 检查Word文档健康状况
     *
     * @param filePath 文件路径
     * @return 健康检查结果
     */
    public Map<String, Object> healthCheck(String filePath)
    {
        Map<String, Object> health = new java.util.HashMap<>();
        
        try
        {
            // 基本文件检查
            File file = new File(filePath);
            health.put("fileExists", file.exists());
            health.put("fileReadable", file.canRead());
            health.put("fileSize", file.length());
            health.put("fileName", file.getName());
            
            String fileName = file.getName();
            boolean isDoc = fileName.toLowerCase().endsWith(".doc");
            boolean isDocx = fileName.toLowerCase().endsWith(".docx");
            health.put("supportedFormat", isDoc || isDocx);
            health.put("docType", isDocx ? "docx" : (isDoc ? "doc" : "unknown"));
            
            if (file.exists() && file.canRead() && (isDoc || isDocx))
            {
                // Word格式验证
                boolean validWord = validateWord(filePath);
                health.put("validWord", validWord);
                
                if (validWord)
                {
                    // 尝试解析获取基本信息
                    WordParseResult result = parseWordSync(filePath);
                    health.put("paragraphCount", result.getParagraphCount());
                    health.put("hasText", result.getFullText() != null && !result.getFullText().trim().isEmpty());
                    health.put("textLength", result.getFullText() != null ? result.getFullText().length() : 0);
                    health.put("textDensity", WordParseUtil.calculateTextDensity(result));
                    health.put("metadata", result.getMetadata());
                    health.put("docType", result.getDocType());
                    
                    // 表格检查（仅DOCX）
                    if ("docx".equals(result.getDocType()))
                    {
                        List<Map<String, Object>> tables = extractTables(filePath);
                        health.put("tableCount", tables.size());
                    }
                }
            }
            
            health.put("status", "success");
        }
        catch (Exception e)
        {
            log.error("Word健康检查失败：{}", filePath, e);
            health.put("status", "error");
            health.put("error", e.getMessage());
        }
        
        return health;
    }

    /**
     * 比较两个Word文档的差异
     *
     * @param filePath1 第一个文档路径
     * @param filePath2 第二个文档路径
     * @return 差异分析结果
     */
    public Map<String, Object> compareDocuments(String filePath1, String filePath2)
    {
        Map<String, Object> comparison = new java.util.HashMap<>();
        
        try
        {
            WordParseResult result1 = parseWordSync(filePath1);
            WordParseResult result2 = parseWordSync(filePath2);
            
            comparison.put("file1", filePath1);
            comparison.put("file2", filePath2);
            comparison.put("similarity", calculateSimilarity(result1, result2));
            
            // 基本统计对比
            Map<String, Object> stats1 = getStatistics(result1);
            Map<String, Object> stats2 = getStatistics(result2);
            
            Map<String, Object> statsDiff = new java.util.HashMap<>();
            statsDiff.put("paragraphCountDiff", (Integer)stats2.get("paragraphCount") - (Integer)stats1.get("paragraphCount"));
            statsDiff.put("textLengthDiff", (Integer)stats2.get("totalChars") - (Integer)stats1.get("totalChars"));
            statsDiff.put("fileSizeDiff", result2.getFileSize() - result1.getFileSize());
            
            comparison.put("statisticsDiff", statsDiff);
            comparison.put("file1Stats", stats1);
            comparison.put("file2Stats", stats2);
            
            log.info("文档比较完成：{} vs {}，相似度：{}", filePath1, filePath2, comparison.get("similarity"));
        }
        catch (Exception e)
        {
            log.error("文档比较失败：{} vs {}", filePath1, filePath2, e);
            comparison.put("error", e.getMessage());
        }
        
        return comparison;
    }
}