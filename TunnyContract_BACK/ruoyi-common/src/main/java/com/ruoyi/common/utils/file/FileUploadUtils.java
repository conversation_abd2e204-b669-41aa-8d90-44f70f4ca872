package com.ruoyi.common.utils.file;


import com.ruoyi.common.config.MinioConfig;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException;
import com.ruoyi.common.exception.file.FileSizeLimitExceededException;
import com.ruoyi.common.exception.file.InvalidExtensionException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.Seq;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 文件上传工具类
 * 提供本地存储和MinIO对象存储的文件上传功能，支持文件类型校验和大小限制
 * 增加了文件重复上传检测功能，可以避免重复存储相同内容的文件
 * 
 * <AUTHOR>
 */
public class FileUploadUtils
{
    private static final Logger logger = LoggerFactory.getLogger(FileUploadUtils.class);
    
    /**
     * Redis缓存KEY前缀
     */
    private static final String REDIS_FILE_MD5_KEY = "file:md5:";
    
    /**
     * Redis缓存过期时间（天）
     */
    private static final long REDIS_CACHE_EXPIRE_DAYS = 30;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 本地默认上传的地址
     */
    private static String defaultBaseDir = RuoYiConfig.getProfile();
    
    /**
     * Minio默认上传的地址
     */
    private static String bucketName = MinioConfig.getBucketName();
    /**
     * 配置中的文件上传大小限制，引用spring.servlet.multipart.max-file-size
     */
    private static long maxFileSize = 1024 * 1024 * 1024; // 默认1GB

    public static void setDefaultBaseDir(String defaultBaseDir)
    {
        FileUploadUtils.defaultBaseDir = defaultBaseDir;
    }

    public static String getDefaultBaseDir()
    {
        return defaultBaseDir;
    }
    
    public static String getBucketName()
    {
        return bucketName;
    }

    /**
     * 以默认配置进行文件上传
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws Exception
     */
    public static final String upload(MultipartFile file) throws IOException
    {
        try
        {
            return upload(getDefaultBaseDir(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static final String upload(String baseDir, MultipartFile file) throws IOException
    {
        try
        {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 获取Redis缓存实例
     *
     * @return RedisCache实例
     */
    private static RedisCache getRedisCache() {
        return SpringUtils.getBean(RedisCache.class);
    }
    
    /**
     * 文件上传
     * 处理文件验证、重命名和保存，并支持重复文件检测
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @param allowedExtension 上传文件类型
     * @return 返回上传成功的文件路径
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException 比如读写文件出错时
     * @throws InvalidExtensionException 文件校验异常
     */
    public static final String upload(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException
    {
        int fileNamelength = Objects.requireNonNull(file.getOriginalFilename()).length();
        if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH)
        {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        assertAllowed(file, allowedExtension);

        // 计算文件MD5，检查是否为重复文件
        try {
            String fileMd5 = calculateMD5(file);
            String redisKey = REDIS_FILE_MD5_KEY + fileMd5;
            String existingPath = getRedisCache().getCacheObject(redisKey);

            // 如果文件已存在，直接返回已有文件路径
            if (StringUtils.isNotEmpty(existingPath)) {
                logger.info("检测到重复文件 {} (MD5: {}), 返回已存在的文件路径", file.getOriginalFilename(), fileMd5);
                return existingPath;
            }

            String fileName = extractFilename(file);
            String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
            file.transferTo(Paths.get(absPath));
            String pathFileName = getPathFileName(baseDir, fileName);

            // 将新文件的MD5和路径添加到Redis缓存
            getRedisCache().setCacheObject(REDIS_FILE_MD5_KEY + fileMd5, pathFileName, (int)REDIS_CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
            return pathFileName;

        } catch (NoSuchAlgorithmException e) {
            // 如果MD5计算失败，回退到旧的上传方式
            logger.warn("计算文件MD5失败，无法检测重复文件: {}", e.getMessage());
            String fileName = extractFilename(file);
            String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
            file.transferTo(Paths.get(absPath));
            return getPathFileName(baseDir, fileName);
        }
    }
    
    /**
     * MinIO文件上传处理
     * 处理文件验证、重命名，并上传到MinIO对象存储，支持重复文件检测
     *
     * @param bucketName 存储桶名称
     * @param file 上传的文件
     * @param allowedExtension 允许的文件类型数组
     * @return 返回上传成功的文件访问路径
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws IOException IO异常
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws InvalidExtensionException 不允许的文件类型
     */
    private static final String uploadMinino(String bucketName, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException
    {
        int fileNamelength = file.getOriginalFilename().length();
        if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH)
        {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }
        assertAllowed(file, allowedExtension);
        try
        {
            // 计算文件MD5，检查是否为重复文件
            try {
                String fileMd5 = calculateMD5(file);
                String redisKey = REDIS_FILE_MD5_KEY + fileMd5;
                String existingPath = getRedisCache().getCacheObject(redisKey);

                // 如果文件已存在，直接返回已有文件路径
                if (StringUtils.isNotEmpty(existingPath)) {
                    logger.info("检测到重复文件 {} (MD5: {}), 返回已存在的MinIO文件路径", file.getOriginalFilename(), fileMd5);
                    return existingPath;
                }

                String fileName = extractFilename(file);//处理文件名称，避免文件名冲突
                String pathFileName = MinioUtil.uploadFile(bucketName, fileName, file);

                // 将新文件的MD5和路径添加到Redis缓存 30天过期
                //getRedisCache().setCacheObject(redisKey, pathFileName, (int)REDIS_CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
                return pathFileName;

            } catch (NoSuchAlgorithmException e) {
                // 如果MD5计算失败，回退到旧的上传方式
                logger.warn("计算文件MD5失败，无法检测重复文件: {}", e.getMessage());
                String fileName = extractFilename(file);
                return MinioUtil.uploadFile(bucketName, fileName, file);
            }
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 以默认BucketName配置上传到Minio服务器
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws Exception
     */
    public static final String uploadMinio(MultipartFile file) throws IOException
    {
        try
        {
            return uploadMinino(getBucketName(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }
    
    /**
     * 自定义bucketName配置上传到Minio服务器
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws Exception
     */
    public static final String uploadMinio(MultipartFile file, String bucketName) throws IOException
    {
        try
        {
            return uploadMinino(bucketName, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 编码文件名
     * 生成基于日期路径和唯一序列号的文件名，避免文件名冲突
     *
     * @param file 上传的文件
     * @return 编码后的文件名（包含相对路径）
     */
    public static final String extractFilename(MultipartFile file)
    {
        return StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getId(Seq.uploadSeqType), getExtension(file));
    }


    /**
     * 编码文件名
     * 生成基于日期路径和唯一序列号的文件名，避免文件名冲突
     *
     * @param fileName 上传的文件名
     * * @param fileExtension 上传的文件后缀
     * @return 编码后的文件名（包含相对路径）
     */
    public static final String extractFilename(String  fileName ,String fileExtension)
    {
        return StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                FilenameUtils.getBaseName(fileName), Seq.getId(Seq.uploadSeqType), fileExtension);
    }
    /**
     * 获取文件的绝对路径
     * 如果目录不存在，会自动创建目录
     *
     * @param uploadDir 上传目录
     * @param fileName 文件名
     * @return 绝对路径文件对象
     * @throws IOException IO异常
     */
    public static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException
    {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.exists())
        {
            if (!desc.getParentFile().exists())
            {
                desc.getParentFile().mkdirs();
            }
        }
        return desc;
    }

    /**
     * 获取文件的访问路径
     * 根据上传目录和文件名，生成可访问的资源路径
     *
     * @param uploadDir 上传目录
     * @param fileName 文件名
     * @return 资源访问路径
     * @throws IOException IO异常
     */
    public static final String getPathFileName(String uploadDir, String fileName) throws IOException
    {
        int dirLastIndex = RuoYiConfig.getProfile().length() + 1;
        String currentDir = StringUtils.substring(uploadDir, dirLastIndex);
        return Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
    }

    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     * @return
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException
     */
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, InvalidExtensionException
    {
        long size = file.getSize();
        if (size > maxFileSize)
        {
            throw new FileSizeLimitExceededException(maxFileSize / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension))
        {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.VIDEO_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidVideoExtensionException(allowedExtension, extension,
                        fileName);
            }
            else
            {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file)
    {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension))
        {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension
     * @param allowedExtension
     * @return
     */
    public static final boolean isAllowedExtension(String extension, String[] allowedExtension)
    {
        for (String str : allowedExtension)
        {
            if (str.equalsIgnoreCase(extension))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算文件的MD5哈希值
     * 用于检测重复文件，相同内容的文件会有相同的MD5值
     *
     * @param file 待计算的文件
     * @return 文件的MD5哈希值
     * @throws IOException IO异常
     * @throws NoSuchAlgorithmException 加密算法不可用异常
     */
    private static String calculateMD5(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        try (InputStream is = file.getInputStream()) {
            byte[] buffer = new byte[8192];
            int read;
            while ((read = is.read(buffer)) > 0) {
                md.update(buffer, 0, read);
            }

            byte[] md5Bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }
    }
    
    /**
     * 根据文件路径检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public static boolean checkFileExists(String filePath) {
        // 本地文件系统检查
        if (filePath.startsWith(Constants.RESOURCE_PREFIX)) {
            // 去除资源前缀
            String realPath = filePath.substring(Constants.RESOURCE_PREFIX.length());
            String fullPath = RuoYiConfig.getProfile() + realPath;
            return Files.exists(Paths.get(fullPath));
        }
        // MinIO文件检查可以通过MinioUtil实现，根据需要扩展
        return false;
    }
    
    /**
     * 清除文件缓存
     * 当文件存储空间不足或需要清理时调用
     */
    public static void clearFileCache() {
        logger.info("清除Redis文件MD5缓存");
        // 获取所有以REDIS_FILE_MD5_KEY开头的缓存键并删除
        Collection<String> keys = getRedisCache().keys(REDIS_FILE_MD5_KEY + "*");
        if (keys != null && !keys.isEmpty()) {
            getRedisCache().deleteObject(keys);
        }
    }



    /**
     * 更新文件上传大小限制
     *
     * @param maxSize 最大文件大小（字节）
     */
    @Value("${spring.servlet.multipart.max-file-size}")
    public void setMaxFileSize(DataSize maxSize) {
        FileUploadUtils.maxFileSize = maxSize.toBytes();
        logger.info("设置文件上传大小限制为: {}MB", maxFileSize / 1024 / 1024);
    }
}