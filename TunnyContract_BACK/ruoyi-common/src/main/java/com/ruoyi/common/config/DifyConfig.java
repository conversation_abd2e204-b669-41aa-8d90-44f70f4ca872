package com.ruoyi.common.config;

import com.ruoyi.common.utils.dify.DifyClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Dify智能体配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "dify", name = "enabled", havingValue = "true", matchIfMissing = true)
public class DifyConfig
{
    private static final Logger log = LoggerFactory.getLogger(DifyConfig.class);

    @Autowired
    private DifyProperties difyProperties;

    /**
     * 创建智能体客户端管理器
     */
    @Bean
    public DifyClientUtil.DifyAgentClientManager difyAgentClientManager()
    {
        if (!com.ruoyi.common.utils.StringUtils.hasText(difyProperties.getBaseUrl()))
        {
            log.warn("Dify Base URL未配置，请在application.yml中配置dify.base-url");
            throw new IllegalArgumentException("Dify Base URL未配置");
        }

        DifyClientUtil.DifyAgentClientManager manager = new DifyClientUtil.DifyAgentClientManager(difyProperties);

        // 打印详细的初始化信息
        printDifyInitializationInfo();

        return manager;
    }

    /**
     * 打印Dify初始化信息
     */
    private void printDifyInitializationInfo()
    {
        log.info("=== Dify智能体服务初始化信息 ===");

        // 基础配置信息
        log.info("服务状态: {}", difyProperties.isEnabled() ? "已启用" : "已禁用");
        log.info("服务地址: {}", difyProperties.getBaseUrl());
        log.info("默认用户: {}", difyProperties.getDefaultUser());

        // 连接配置信息
        DifyProperties.ConnectionConfig connection = difyProperties.getConnection();
        log.info("连接配置:");
        log.info("  - 连接超时: {}ms", connection.getConnectTimeout());
        log.info("  - 读取超时: {}ms", connection.getReadTimeout());
        log.info("  - 写入超时: {}ms", connection.getWriteTimeout());

        // 智能体配置信息
        java.util.Map<String, DifyProperties.AgentConfig> allAgents = difyProperties.getAgents();
        java.util.Map<String, DifyProperties.AgentConfig> enabledAgents = difyProperties.getEnabledAgents();

        log.info("智能体配置:");
        log.info("  - 总数量: {}", allAgents.size());
        log.info("  - 启用数量: {}", enabledAgents.size());
        log.info("  - 禁用数量: {}", allAgents.size() - enabledAgents.size());

        if (enabledAgents.isEmpty())
        {
            log.warn("  ⚠️  未配置任何启用的智能体！");
        }
        else
        {
            log.info("启用的智能体详情:");
            enabledAgents.forEach((name, config) -> {
                log.info("  📱 智能体: {}", name);
                log.info("    - 类型: {}", config.getType());
                log.info("    - 描述: {}", config.getDescription() != null ? config.getDescription() : "无描述");
                log.info("    - API Key: {}***",
                    config.getApi().getKey() != null && config.getApi().getKey().length() > 8
                        ? config.getApi().getKey().substring(0, 8)
                        : "未配置");
                log.info("    - 状态: 已启用");
            });
        }

        // 禁用的智能体信息
        if (allAgents.size() > enabledAgents.size())
        {
            log.info("禁用的智能体:");
            allAgents.forEach((name, config) -> {
                if (!config.isEnabled())
                {
                    log.info("  🚫 智能体: {} (类型: {}, 状态: 已禁用)", name, config.getType());
                }
            });
        }

        log.info("=== Dify智能体服务初始化完成 ===");
    }
}
