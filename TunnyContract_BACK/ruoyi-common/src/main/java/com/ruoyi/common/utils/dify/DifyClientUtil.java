package com.ruoyi.common.utils.dify;

import com.ruoyi.common.constant.DifyConstants;
import com.ruoyi.common.exception.dify.DifyException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.config.DifyProperties;
import io.github.imfangs.dify.client.DifyClient;
import io.github.imfangs.dify.client.DifyChatClient;
import io.github.imfangs.dify.client.DifyCompletionClient;
import io.github.imfangs.dify.client.DifyWorkflowClient;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.DifyClientFactory;
import io.github.imfangs.dify.client.model.chat.ChatMessage;
import io.github.imfangs.dify.client.model.chat.ChatMessageResponse;
import io.github.imfangs.dify.client.model.completion.CompletionRequest;
import io.github.imfangs.dify.client.model.completion.CompletionResponse;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunRequest;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import io.github.imfangs.dify.client.model.datasets.RetrieveRequest;
import io.github.imfangs.dify.client.model.datasets.RetrieveResponse;
import io.github.imfangs.dify.client.model.datasets.RetrievalModel;
import io.github.imfangs.dify.client.enums.ResponseMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import com.alibaba.fastjson2.JSON;

/**
 * Dify智能体客户端工具类
 * 
 * <AUTHOR>
 */
public class DifyClientUtil
{
    private static final Logger log = LoggerFactory.getLogger(DifyClientUtil.class);

    /**
     * 获取智能体客户端管理器
     */
    private static DifyAgentClientManager getClientManager()
    {
        try
        {
            return SpringUtils.getBean(DifyAgentClientManager.class);
        }
        catch (Exception e)
        {
            log.error("获取Dify智能体客户端管理器失败", e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "Dify服务不可用", e);
        }
    }

    /**
     * 获取指定智能体的完整客户端
     */
    public static DifyClient getClient(String agentName)
    {
        try
        {
            return getClientManager().getClient(agentName);
        }
        catch (Exception e)
        {
            log.error("获取智能体客户端失败，智能体: {}", agentName, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "智能体服务不可用: " + agentName, e);
        }
    }

    /**
     * 获取指定智能体的对话客户端
     */
    public static DifyChatClient getChatClient(String agentName)
    {
        try
        {
            return getClientManager().getChatClient(agentName);
        }
        catch (Exception e)
        {
            log.error("获取智能体对话客户端失败，智能体: {}", agentName, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "智能体对话服务不可用: " + agentName, e);
        }
    }

    /**
     * 获取指定智能体的文本生成客户端
     */
    public static DifyCompletionClient getCompletionClient(String agentName)
    {
        try
        {
            return getClientManager().getCompletionClient(agentName);
        }
        catch (Exception e)
        {
            log.error("获取智能体文本生成客户端失败，智能体: {}", agentName, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "智能体文本生成服务不可用: " + agentName, e);
        }
    }

    /**
     * 获取指定智能体的工作流客户端
     */
    public static DifyWorkflowClient getWorkflowClient(String agentName)
    {
        try
        {
            return getClientManager().getWorkflowClient(agentName);
        }
        catch (Exception e)
        {
            log.error("获取智能体工作流客户端失败，智能体: {}", agentName, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "智能体工作流服务不可用: " + agentName, e);
        }
    }

    /**
     * 获取指定智能体的知识库客户端
     */
    public static DifyDatasetsClient getDatasetsClient(String agentName)
    {
        try
        {
            return getClientManager().getDatasetsClient(agentName);
        }
        catch (Exception e)
        {
            log.error("获取智能体知识库客户端失败，智能体: {}", agentName, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "智能体知识库服务不可用: " + agentName, e);
        }
    }

    /**
     * 发送对话消息（阻塞模式）
     * 
     * @param agentName 智能体名称
     * @param query 查询内容
     * @param user 用户标识
     * @return 对话响应
     */
    public static ChatMessageResponse sendChatMessage(String agentName, String query, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(query))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "查询内容不能为空");
        }

        try
        {
            DifyChatClient chatClient = getChatClient(agentName);
            
            ChatMessage message = ChatMessage.builder()
                    .query(query)
                    .user(StringUtils.isNotEmpty(user) ? user : DifyConstants.DefaultValues.DEFAULT_USER)
                    .responseMode(ResponseMode.BLOCKING)
                    .build();

            ChatMessageResponse response = chatClient.sendChatMessage(message);
            log.info("发送对话消息成功，智能体: {}, 用户: {}, 查询: {}, 消息ID: {}", agentName, user, query, response.getMessageId());
            return response;
        }
        catch (Exception e)
        {
            log.error("发送对话消息失败，智能体: {}, 用户: {}, 查询: {}", agentName, user, query, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "发送对话消息失败", e);
        }
    }

    /**
     * 发送文本生成请求（阻塞模式）
     * 
     * @param agentName 智能体名称
     * @param inputs 输入参数
     * @param user 用户标识
     * @return 文本生成响应
     */
    public static CompletionResponse sendCompletionMessage(String agentName, Map<String, Object> inputs, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (inputs == null || inputs.isEmpty())
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "输入参数不能为空");
        }

        try
        {
            DifyCompletionClient completionClient = getCompletionClient(agentName);
            
            CompletionRequest request = CompletionRequest.builder()
                    .inputs(inputs)
                    .responseMode(ResponseMode.BLOCKING)
                    .user(StringUtils.isNotEmpty(user) ? user : DifyConstants.DefaultValues.DEFAULT_USER)
                    .build();

            CompletionResponse response = completionClient.sendCompletionMessage(request);
            log.info("发送文本生成请求成功，智能体: {}, 用户: {}, 消息ID: {}", agentName, user, response.getMessageId());
            return response;
        }
        catch (Exception e)
        {
            log.error("发送文本生成请求失败，智能体: {}, 用户: {}, 输入: {}", agentName, user, inputs, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "发送文本生成请求失败", e);
        }
    }

    /**
     * 执行工作流（阻塞模式）
     * 
     * @param agentName 智能体名称
     * @param inputs 输入参数
     * @param user 用户标识
     * @return 工作流执行响应
     */
    public static WorkflowRunResponse runWorkflow(String agentName, Map<String, Object> inputs, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (inputs == null || inputs.isEmpty())
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "输入参数不能为空");
        }

        try
        {
            DifyWorkflowClient workflowClient = getWorkflowClient(agentName);
            
            WorkflowRunRequest request = WorkflowRunRequest.builder()
                    .inputs(inputs)
                    .responseMode(ResponseMode.BLOCKING)
                    .user(StringUtils.isNotEmpty(user) ? user : DifyConstants.DefaultValues.DEFAULT_USER)
                    .build();

            WorkflowRunResponse response = workflowClient.runWorkflow(request);
            log.info("执行工作流成功，智能体: {}, 用户: {}, 任务ID: {}", agentName, user, response.getTaskId());
            return response;
        }
        catch (Exception e)
        {
            log.error("执行工作流失败，智能体: {}, 用户: {}, 输入: {}", agentName, user, inputs, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "执行工作流失败", e);
        }
    }

    /**
     * 检索知识库内容
     *
     * @param agentName 智能体名称
     * @param query 检索查询
     * @param user 用户标识
     * @return 检索响应
     */
    public static RetrieveResponse retrieveDatasets(String agentName, String query, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(query))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "检索查询不能为空");
        }

        try
        {
            DifyDatasetsClient datasetsClient = getDatasetsClient(agentName);

            RetrievalModel retrievalModel = new RetrievalModel();
            retrievalModel.setTopK(DifyConstants.DefaultValues.DEFAULT_TOP_K);
            retrievalModel.setScoreThreshold(DifyConstants.DefaultValues.DEFAULT_SCORE_THRESHOLD);

            RetrieveRequest request = RetrieveRequest.builder()
                    .query(query)
                    .retrievalModel(retrievalModel)
                    .build();

            RetrieveResponse response = datasetsClient.retrieveDataset("default", request);
            log.info("检索知识库成功，智能体: {}, 用户: {}, 查询: {}, 结果数量: {}",
                    agentName, user, query, response.getRecords().size());
            return response;
        }
        catch (Exception e)
        {
            log.error("检索知识库失败，智能体: {}, 用户: {}, 查询: {}", agentName, user, query, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "检索知识库失败", e);
        }
    }

    /**
     * 获取所有已启用的智能体信息
     *
     * @return 智能体信息映射
     */
    public static Map<String, Object> getEnabledAgents()
    {
        try
        {
            return getClientManager().getEnabledAgents();
        }
        catch (Exception e)
        {
            log.error("获取已启用智能体信息失败", e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "获取智能体信息失败", e);
        }
    }

    /**
     * 检查指定智能体是否可用
     *
     * @param agentName 智能体名称
     * @return 是否可用
     */
    public static boolean isAgentAvailable(String agentName)
    {
        if (StringUtils.isEmpty(agentName))
        {
            return false;
        }

        try
        {
            DifyClient client = getClient(agentName);
            return client != null;
        }
        catch (Exception e)
        {
            log.warn("智能体不可用: {}", agentName, e);
            return false;
        }
    }

    /**
     * 验证智能体配置
     *
     * @param agentName 智能体名称
     * @throws DifyException 如果配置无效
     */
    public static void validateAgentConfig(String agentName)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }

        if (!isAgentAvailable(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.AGENT_NOT_FOUND, "智能体不存在或未启用: " + agentName);
        }
    }

    /**
     * 获取管理器状态信息
     *
     * @return 状态信息
     */
    public static Map<String, Object> getManagerStatus()
    {
        try
        {
            DifyAgentClientManager manager = getClientManager();
            return manager.getCacheStatistics();
        }
        catch (Exception e)
        {
            log.error("获取管理器状态失败", e);
            return new java.util.HashMap<>();
        }
    }

    /**
     * 打印管理器状态
     */
    public static void printManagerStatus()
    {
        try
        {
            DifyAgentClientManager manager = getClientManager();
            manager.printManagerStatus();
        }
        catch (Exception e)
        {
            log.error("打印管理器状态失败", e);
        }
    }

    /**
     * 清理指定智能体的缓存
     *
     * @param agentName 智能体名称
     */
    public static void clearAgentCache(String agentName)
    {
        try
        {
            DifyAgentClientManager manager = getClientManager();
            manager.clearAgentCache(agentName);
        }
        catch (Exception e)
        {
            log.error("清理智能体缓存失败: {}", agentName, e);
        }
    }

    /**
     * 清理所有缓存
     */
    public static void clearAllCache()
    {
        try
        {
            DifyAgentClientManager manager = getClientManager();
            manager.clearAllCache();
        }
        catch (Exception e)
        {
            log.error("清理所有缓存失败", e);
        }
    }

    /**
     * 从工作流响应数据中提取outputs.text内容
     *
     * @param data 工作流响应数据
     * @return outputs.text内容，如果不存在则返回整个响应数据的JSON字符串
     */
    public static String extractOutputText(WorkflowRunResponse.WorkflowRunData data)
    {
        if (data == null)
        {
            return null;
        }

        try
        {
            String responseDataJson = JSON.toJSONString(data);
            Map<String, Object> responseDataMap = JSON.parseObject(responseDataJson, Map.class);
            
            if (responseDataMap != null && responseDataMap.containsKey("outputs"))
            {
                Map<String, Object> outputs = (Map<String, Object>) responseDataMap.get("outputs");
                if (outputs != null && outputs.containsKey("text"))
                {
                    return (String) outputs.get("text");
                }
            }
            
            // 如果没有找到outputs.text，返回整个响应数据
            return responseDataJson;
        }
        catch (Exception e)
        {
            log.error("提取outputs.text失败", e);
            return JSON.toJSONString(data);
        }
    }

    /**
     * 智能处理JSON转义字符，保留内容格式
     *
     * @param jsonText 可能包含转义字符的JSON文本
     * @return 处理后的JSON文本
     */
    public static String processJsonEscape(String jsonText)
    {
        if (StringUtils.isEmpty(jsonText))
        {
            return jsonText;
        }

        try
        {
            // 首先尝试直接解析，如果成功则无需处理转义
            JSON.parseObject(jsonText);
            return jsonText;
        }
        catch (Exception e)
        {
            // JSON解析失败时才进行转义处理
            String cleanedJson = jsonText
                .replace("\\\"", "\"")
                .replace("\\\\", "\\");
            
            // 验证处理后的JSON格式
            try
            {
                JSON.parseObject(cleanedJson);
                return cleanedJson;
            }
            catch (Exception jsonEx)
            {
                // 如果处理后仍无效，返回原始数据
                log.warn("JSON转义处理失败，返回原始数据: {}", jsonEx.getMessage());
                return jsonText;
            }
        }
    }

    /**
     * Dify智能体客户端管理器
     */
    public static class DifyAgentClientManager
    {
        private final DifyProperties difyProperties;
        private final Map<String, DifyClient> clientCache = new java.util.HashMap<>();
        private final Map<String, DifyChatClient> chatClientCache = new java.util.HashMap<>();
        private final Map<String, DifyCompletionClient> completionClientCache = new java.util.HashMap<>();
        private final Map<String, DifyWorkflowClient> workflowClientCache = new java.util.HashMap<>();
        private final Map<String, DifyDatasetsClient> datasetsClientCache = new java.util.HashMap<>();
        private final Logger log = LoggerFactory.getLogger(DifyAgentClientManager.class);

        public DifyAgentClientManager(DifyProperties difyProperties)
        {
            this.difyProperties = difyProperties;
            log.info("Dify智能体客户端管理器创建完成，配置的智能体数量: {}", difyProperties.getAgents().size());
        }

        /**
         * 获取智能体的完整客户端
         */
        public DifyClient getClient(String agentName)
        {
            return clientCache.computeIfAbsent(agentName, name -> {
                DifyProperties.AgentConfig agentConfig = difyProperties.getAgentConfig(name);
                if (agentConfig == null || !agentConfig.isEnabled())
                {
                    throw new IllegalArgumentException("智能体不存在或未启用: " + name);
                }

                io.github.imfangs.dify.client.model.DifyConfig config = createClientConfig(agentConfig);
                return DifyClientFactory.createClient(config);
            });
        }

        /**
         * 获取智能体的对话客户端
         */
        public DifyChatClient getChatClient(String agentName)
        {
            return chatClientCache.computeIfAbsent(agentName, name -> {
                DifyProperties.AgentConfig agentConfig = difyProperties.getAgentConfig(name);
                if (agentConfig == null || !agentConfig.isEnabled())
                {
                    throw new IllegalArgumentException("智能体不存在或未启用: " + name);
                }

                io.github.imfangs.dify.client.model.DifyConfig config = createClientConfig(agentConfig);
                return DifyClientFactory.createChatClient(config);
            });
        }

        /**
         * 获取智能体的文本生成客户端
         */
        public DifyCompletionClient getCompletionClient(String agentName)
        {
            return completionClientCache.computeIfAbsent(agentName, name -> {
                DifyProperties.AgentConfig agentConfig = difyProperties.getAgentConfig(name);
                if (agentConfig == null || !agentConfig.isEnabled())
                {
                    throw new IllegalArgumentException("智能体不存在或未启用: " + name);
                }

                io.github.imfangs.dify.client.model.DifyConfig config = createClientConfig(agentConfig);
                return DifyClientFactory.createCompletionClient(config);
            });
        }

        /**
         * 获取智能体的工作流客户端
         */
        public DifyWorkflowClient getWorkflowClient(String agentName)
        {
            return workflowClientCache.computeIfAbsent(agentName, name -> {
                DifyProperties.AgentConfig agentConfig = difyProperties.getAgentConfig(name);
                if (agentConfig == null || !agentConfig.isEnabled())
                {
                    throw new IllegalArgumentException("智能体不存在或未启用: " + name);
                }

                io.github.imfangs.dify.client.model.DifyConfig config = createClientConfig(agentConfig);
                return DifyClientFactory.createWorkflowClient(config);
            });
        }

        /**
         * 获取智能体的知识库客户端
         */
        public DifyDatasetsClient getDatasetsClient(String agentName)
        {
            return datasetsClientCache.computeIfAbsent(agentName, name -> {
                DifyProperties.AgentConfig agentConfig = difyProperties.getAgentConfig(name);
                if (agentConfig == null || !agentConfig.isEnabled())
                {
                    throw new IllegalArgumentException("智能体不存在或未启用: " + name);
                }

                io.github.imfangs.dify.client.model.DifyConfig config = createClientConfig(agentConfig);
                return DifyClientFactory.createDatasetsClient(config);
            });
        }

        /**
         * 创建客户端配置
         */
        private io.github.imfangs.dify.client.model.DifyConfig createClientConfig(DifyProperties.AgentConfig agentConfig)
        {
            if (!StringUtils.hasText(agentConfig.getApi().getKey()))
            {
                throw new IllegalArgumentException("智能体API Key未配置");
            }

            return io.github.imfangs.dify.client.model.DifyConfig.builder()
                    .baseUrl(difyProperties.getBaseUrl())
                    .apiKey(agentConfig.getApi().getKey())
                    .connectTimeout(difyProperties.getConnection().getConnectTimeout())
                    .readTimeout(difyProperties.getConnection().getReadTimeout())
                    .writeTimeout(difyProperties.getConnection().getWriteTimeout())
                    .build();
        }

        /**
         * 获取所有启用的智能体信息
         */
        public Map<String, Object> getEnabledAgents()
        {
            return new java.util.HashMap<>(difyProperties.getEnabledAgents());
        }

        /**
         * 获取客户端缓存统计信息
         */
        public Map<String, Object> getCacheStatistics()
        {
            Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("totalClients", clientCache.size());
            stats.put("chatClients", chatClientCache.size());
            stats.put("completionClients", completionClientCache.size());
            stats.put("workflowClients", workflowClientCache.size());
            stats.put("datasetsClients", datasetsClientCache.size());
            stats.put("cachedAgents", new java.util.HashSet<String>() {{
                addAll(clientCache.keySet());
                addAll(chatClientCache.keySet());
                addAll(completionClientCache.keySet());
                addAll(workflowClientCache.keySet());
                addAll(datasetsClientCache.keySet());
            }});
            return stats;
        }

        /**
         * 打印当前管理器状态
         */
        public void printManagerStatus()
        {
            log.info("=== Dify智能体客户端管理器状态 ===");
            log.info("配置的智能体总数: {}", difyProperties.getAgents().size());
            log.info("启用的智能体数量: {}", difyProperties.getEnabledAgents().size());

            Map<String, Object> cacheStats = getCacheStatistics();
            log.info("客户端缓存统计:");
            log.info("  - 完整客户端: {}", cacheStats.get("totalClients"));
            log.info("  - 对话客户端: {}", cacheStats.get("chatClients"));
            log.info("  - 文本生成客户端: {}", cacheStats.get("completionClients"));
            log.info("  - 工作流客户端: {}", cacheStats.get("workflowClients"));
            log.info("  - 知识库客户端: {}", cacheStats.get("datasetsClients"));

            @SuppressWarnings("unchecked")
            java.util.Set<String> cachedAgents = (java.util.Set<String>) cacheStats.get("cachedAgents");
            log.info("已缓存的智能体: {}", cachedAgents);
            log.info("=== 状态打印完成 ===");
        }

        /**
         * 清理指定智能体的所有客户端缓存
         */
        public void clearAgentCache(String agentName)
        {
            boolean cleared = false;
            if (clientCache.remove(agentName) != null) cleared = true;
            if (chatClientCache.remove(agentName) != null) cleared = true;
            if (completionClientCache.remove(agentName) != null) cleared = true;
            if (workflowClientCache.remove(agentName) != null) cleared = true;
            if (datasetsClientCache.remove(agentName) != null) cleared = true;

            if (cleared)
            {
                log.info("已清理智能体 {} 的所有客户端缓存", agentName);
            }
            else
            {
                log.debug("智能体 {} 没有缓存的客户端", agentName);
            }
        }

        /**
         * 清理所有客户端缓存
         */
        public void clearAllCache()
        {
            int totalCleared = clientCache.size() + chatClientCache.size() +
                              completionClientCache.size() + workflowClientCache.size() +
                              datasetsClientCache.size();

            clientCache.clear();
            chatClientCache.clear();
            completionClientCache.clear();
            workflowClientCache.clear();
            datasetsClientCache.clear();

            log.info("已清理所有客户端缓存，共清理 {} 个客户端", totalCleared);
        }
    }
}
