package com.ruoyi.common.utils.file;

import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.http.Method;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * Minio 文件存储工具类
 * 
 * <AUTHOR>
 */
public class MinioUtil
{
    private static final Logger log = LoggerFactory.getLogger(MinioUtil.class);
    
    /**
     * 上传文件
     * 
     * @param bucketName 桶名称
     * @param fileName 对象名称
     * @param multipartFile 文件对象
     * @return 文件路径
     * @throws IOException IO异常
     */
    public static String uploadFile(String bucketName, String fileName, MultipartFile multipartFile) throws IOException
    {
        //log.info("MinioUtil.uploadFile开始：bucket={}, fileName={}, fileSize={}", bucketName, fileName, multipartFile.getSize());

        String url = "";
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);

        if (minioClient == null) {
            //log.error("无法获取MinioClient实例");
            throw new IOException("MinioClient未正确配置");
        }

        try (InputStream inputStream = multipartFile.getInputStream())
        {
            //log.info("开始上传文件到MinIO：bucket={}, object={}", bucketName, fileName);

            minioClient.putObject(PutObjectArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .stream(inputStream, multipartFile.getSize(), -1)
                .contentType(multipartFile.getContentType())
                .build());

            //log.info("文件上传成功，开始生成访问URL");

            url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .method(Method.GET)
                .build());

            // 移除查询参数，保留完整的HTTP URL
            url = url.substring(0, url.indexOf('?'));

            //log.info("文件上传完成，返回完整URL：{}", url);
            return url;
        }
        catch (Exception e)
        {
            //log.error("MinIO文件上传失败：bucket={}, fileName={}, 错误：{}", bucketName, fileName, e.getMessage(), e);
            throw new IOException("MinIO上传失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 从File对象上传文件到MinIO
     * 
     * @param bucketName 桶名称
     * @param objectName 对象名称（MinIO中的完整路径）
     * @param file 文件对象
     * @return 文件路径
     */
    public static String uploadFileFromFile(String bucketName, String objectName, File file)throws IOException {
        if (file == null || !file.exists()) {
            //log.error("文件不存在，无法上传");
            return null;
        }
        
        String pathFileName = null;
        
        try {
            // 读取文件内容
            byte[] fileBytes = FileUtils.readFileToByteArray(file);
            
            // 获取MinioClient实例并上传
            MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
            //log.info("开始上传文件到MinIO，大小：{} 字节，对象名：{}", fileBytes.length, objectName);
            
            // 使用字节流上传文件
            try (ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes)) {
                minioClient.putObject(
                    PutObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .stream(inputStream, fileBytes.length, -1)
                        .contentType("text/plain")
                        .build()
                );
                
                // 返回相同格式的结果
                pathFileName = bucketName + "/" + objectName;
                //log.info("文件上传成功，路径：{}", pathFileName);
            }
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
            //log.error("上传文件到MinIO时出错: {}", e.getMessage(), e);
        }
        
        return pathFileName;
    }
}
