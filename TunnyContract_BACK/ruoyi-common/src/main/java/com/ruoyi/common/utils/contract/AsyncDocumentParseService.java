package com.ruoyi.common.utils.contract;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.contract.PdfParseUtil.PdfParseResult;
import com.ruoyi.common.utils.contract.WordParseUtil.WordParseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 异步文档解析服务
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Component
public class AsyncDocumentParseService
{
    private static final Logger log = LoggerFactory.getLogger(AsyncDocumentParseService.class);

    @Autowired
    private PdfParseService pdfParseService;

    @Autowired
    private WordParseService wordParseService;

    // 使用线程池处理异步解析任务
    private final ExecutorService parseExecutor = Executors.newFixedThreadPool(10);

    // 存储解析任务状态和结果
    private final Map<String, ParseTask> taskMap = new ConcurrentHashMap<>();

    // 解析完成回调接口
    public interface ParseCompletionCallback
    {
        void onParseSuccess(String taskId, Object parseResult, ParseTask task);
        void onParseFailure(String taskId, String errorMessage, ParseTask task);
    }

    // 回调函数映射
    private final Map<String, ParseCompletionCallback> callbackMap = new ConcurrentHashMap<>();

    /**
     * 解析任务状态枚举
     */
    public enum ParseStatus
    {
        PENDING("待解析"),
        RUNNING("解析中"),
        SUCCESS("解析成功"),
        FAILED("解析失败"),
        CANCELLED("已取消");

        private final String description;

        ParseStatus(String description)
        {
            this.description = description;
        }

        public String getDescription()
        {
            return description;
        }
    }

    /**
     * 解析任务实体
     */
    public static class ParseTask
    {
        private String taskId;
        private String filePath;
        private String fileName;
        private String fileType;
        private ParseStatus status;
        private long startTime;
        private long endTime;
        private Object parseResult;
        private String errorMessage;
        private CompletableFuture<Object> future;
        private double progress;

        public ParseTask()
        {
            this.status = ParseStatus.PENDING;
            this.startTime = System.currentTimeMillis();
            this.progress = 0.0;
        }

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        public ParseStatus getStatus() { return status; }
        public void setStatus(ParseStatus status) { this.status = status; }
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public Object getParseResult() { return parseResult; }
        public void setParseResult(Object parseResult) { this.parseResult = parseResult; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public CompletableFuture<Object> getFuture() { return future; }
        public void setFuture(CompletableFuture<Object> future) { this.future = future; }
        public double getProgress() { return progress; }
        public void setProgress(double progress) { this.progress = progress; }

        public long getDuration()
        {
            if (endTime > 0)
            {
                return endTime - startTime;
            }
            else if (status == ParseStatus.RUNNING)
            {
                return System.currentTimeMillis() - startTime;
            }
            return 0;
        }
    }

    /**
     * 提交异步解析任务
     *
     * @param filePath 文件路径
     * @return 任务ID
     * @throws ServiceException 服务异常
     */
    public String submitParseTask(String filePath) throws ServiceException
    {
        if (filePath == null || filePath.trim().isEmpty())
        {
            throw new ServiceException("文件路径不能为空");
        }

        File file = getFileFromPath(filePath);
        if (!file.exists())
        {
            throw new ServiceException("文件不存在：" + filePath);
        }

        if (!file.canRead())
        {
            throw new ServiceException("文件无法读取：" + filePath);
        }

        String taskId = generateTaskId();
        String fileName = file.getName();
        String fileType = determineFileType(fileName);

        if (!"pdf".equals(fileType) && !"doc".equals(fileType) && !"docx".equals(fileType))
        {
            throw new ServiceException("不支持的文件类型，仅支持PDF、DOC、DOCX格式");
        }

        ParseTask task = new ParseTask();
        task.setTaskId(taskId);
        // 重要：保存本地文件路径而不是原始MinIO URL
        task.setFilePath(file.getAbsolutePath());
        task.setFileName(fileName);
        task.setFileType(fileType);

        ////log.info("创建解析任务，原始路径：{}，本地文件路径：{}", filePath, file.getAbsolutePath());

        // 创建异步任务
        CompletableFuture<Object> future = CompletableFuture.supplyAsync(() -> {
            return executeParseTask(task);
        }, parseExecutor);

        task.setFuture(future);
        taskMap.put(taskId, task);

        ////log.info("提交异步解析任务：{}，文件：{}", taskId, fileName);
        return taskId;
    }

    /**
     * 提交异步解析任务（带回调）
     *
     * @param filePath 文件路径
     * @param callback 解析完成回调
     * @return 任务ID
     * @throws ServiceException 服务异常
     */
    public String submitParseTask(String filePath, ParseCompletionCallback callback) throws ServiceException
    {
        String taskId = submitParseTask(filePath);
        if (callback != null)
        {
            callbackMap.put(taskId, callback);
            //log.debug("为任务 {} 注册回调函数", taskId);
        }
        return taskId;
    }

    /**
     * 批量提交解析任务
     *
     * @param filePaths 文件路径列表
     * @return 任务ID列表
     */
    public List<String> submitBatchParseTasks(List<String> filePaths)
    {
        if (filePaths == null || filePaths.isEmpty())
        {
            throw new ServiceException("文件路径列表不能为空");
        }

        List<String> taskIds = filePaths.stream()
                .map(this::submitParseTask)
                .collect(java.util.stream.Collectors.toList());

        ////log.info("提交批量解析任务，任务数量：{}", taskIds.size());
        return taskIds;
    }

    /**
     * 从路径获取文件对象，支持本地路径和MinIO URL
     *
     * @param filePath 文件路径或MinIO URL
     * @return 文件对象
     */
    private File getFileFromPath(String filePath) throws ServiceException
    {
        try
        {
            // 判断是否为HTTP URL（MinIO）
            if (filePath.startsWith("http://") || filePath.startsWith("https://"))
            {
                ////log.info("检测到MinIO URL，开始下载文件：{}", filePath);
                return downloadFileFromMinIO(filePath);
            }
            else
            {
                // 本地文件路径
                return new File(filePath);
            }
        }
        catch (Exception e)
        {
            //log.error("获取文件失败：{}", e.getMessage(), e);
            throw new ServiceException("获取文件失败：" + e.getMessage());
        }
    }

    /**
     * 从MinIO下载文件到临时目录
     *
     * @param minioUrl MinIO文件URL
     * @return 临时文件对象
     */
    private File downloadFileFromMinIO(String minioUrl) throws Exception
    {
        URL url = new URL(minioUrl);
        String fileName = Paths.get(url.getPath()).getFileName().toString();

        // 创建临时文件
        Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "contract-parse");
        Files.createDirectories(tempDir);

        Path tempFile = tempDir.resolve(System.currentTimeMillis() + "_" + fileName);

        ////log.info("开始从MinIO下载文件：{} -> {}", minioUrl, tempFile);

        try (InputStream inputStream = url.openStream())
        {
            Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
            ////log.info("文件下载成功：{}", tempFile);

            File file = tempFile.toFile();
            // 设置文件在JVM退出时自动删除
            file.deleteOnExit();
            return file;
        }
    }

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    public ParseTask getTaskStatus(String taskId)
    {
        if (taskId == null || taskId.trim().isEmpty())
        {
            throw new ServiceException("任务ID不能为空");
        }

        ParseTask task = taskMap.get(taskId);
        if (task == null)
        {
            throw new ServiceException("任务不存在：" + taskId);
        }

        return task;
    }

    /**
     * 获取所有任务状态
     *
     * @return 任务状态列表
     */
    public Map<String, ParseTask> getAllTaskStatus()
    {
        return new HashMap<>(taskMap);
    }

    /**
     * 等待任务完成并获取结果
     *
     * @param taskId 任务ID
     * @param timeoutSeconds 超时时间（秒）
     * @return 解析结果
     * @throws ServiceException 服务异常
     */
    public Object waitForTaskCompletion(String taskId, long timeoutSeconds) throws ServiceException
    {
        ParseTask task = getTaskStatus(taskId);
        
        if (task.getStatus() == ParseStatus.SUCCESS)
        {
            return task.getParseResult();
        }
        
        if (task.getStatus() == ParseStatus.FAILED)
        {
            throw new ServiceException("解析任务失败：" + task.getErrorMessage());
        }
        
        if (task.getStatus() == ParseStatus.CANCELLED)
        {
            throw new ServiceException("解析任务已被取消");
        }

        try
        {
            CompletableFuture<Object> future = task.getFuture();
            if (future == null)
            {
                throw new ServiceException("任务Future为空");
            }

            Object result = future.get(timeoutSeconds, TimeUnit.SECONDS);
            return result;
        }
        catch (TimeoutException e)
        {
            throw new ServiceException("任务执行超时：" + timeoutSeconds + "秒");
        }
        catch (InterruptedException e)
        {
            Thread.currentThread().interrupt();
            throw new ServiceException("任务被中断");
        }
        catch (ExecutionException e)
        {
            throw new ServiceException("任务执行异常：" + e.getCause().getMessage());
        }
    }

    /**
     * 取消解析任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    public boolean cancelTask(String taskId)
    {
        ParseTask task = taskMap.get(taskId);
        if (task == null)
        {
            log.warn("尝试取消不存在的任务：{}", taskId);
            return false;
        }

        if (task.getStatus() == ParseStatus.SUCCESS || task.getStatus() == ParseStatus.FAILED)
        {
            log.warn("任务已完成，无法取消：{}，状态：{}", taskId, task.getStatus());
            return false;
        }

        CompletableFuture<Object> future = task.getFuture();
        if (future != null)
        {
            boolean cancelled = future.cancel(true);
            if (cancelled)
            {
                task.setStatus(ParseStatus.CANCELLED);
                task.setEndTime(System.currentTimeMillis());
                //log.info("任务取消成功：{}", taskId);
                return true;
            }
        }

        log.warn("任务取消失败：{}", taskId);
        return false;
    }

    /**
     * 清理已完成的任务
     *
     * @param olderThanMinutes 清理多少分钟前的任务
     * @return 清理的任务数量
     */
    public int cleanupCompletedTasks(int olderThanMinutes)
    {
        long cutoffTime = System.currentTimeMillis() - (olderThanMinutes * 60 * 1000L);
        
        int cleanedCount = 0;
        java.util.Iterator<java.util.Map.Entry<String, ParseTask>> iterator = taskMap.entrySet().iterator();

        while (iterator.hasNext())
        {
            java.util.Map.Entry<String, ParseTask> entry = iterator.next();
            ParseTask task = entry.getValue();
            
            if ((task.getStatus() == ParseStatus.SUCCESS ||
                 task.getStatus() == ParseStatus.FAILED ||
                 task.getStatus() == ParseStatus.CANCELLED) &&
                task.getEndTime() > 0 &&
                task.getEndTime() < cutoffTime)
            {
                // 清理对应的临时文件
                if (isTemporaryFile(task.getFilePath()))
                {
                    File tempFile = new File(task.getFilePath());
                    if (tempFile.exists())
                    {
                        if (tempFile.delete())
                        {
                            log.debug("清理任务关联的临时文件：{}", tempFile.getAbsolutePath());
                        }
                        else
                        {
                            log.warn("清理任务关联的临时文件失败：{}", tempFile.getAbsolutePath());
                        }
                    }
                }

                iterator.remove();
                cleanedCount++;
                log.debug("清理已完成任务：{}，状态：{}", entry.getKey(), task.getStatus());
            }
        }
        
        if (cleanedCount > 0)
        {
            //log.info("清理已完成任务数量：{}", cleanedCount);
        }
        
        return cleanedCount;
    }

    /**
     * 获取任务统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getTaskStatistics()
    {
        Map<String, Object> stats = new HashMap<>();
        
        int pendingCount = 0;
        int runningCount = 0;
        int successCount = 0;
        int failedCount = 0;
        int cancelledCount = 0;
        
        for (ParseTask task : taskMap.values())
        {
            switch (task.getStatus())
            {
                case PENDING:
                    pendingCount++;
                    break;
                case RUNNING:
                    runningCount++;
                    break;
                case SUCCESS:
                    successCount++;
                    break;
                case FAILED:
                    failedCount++;
                    break;
                case CANCELLED:
                    cancelledCount++;
                    break;
            }
        }
        
        stats.put("totalTasks", taskMap.size());
        stats.put("pendingTasks", pendingCount);
        stats.put("runningTasks", runningCount);
        stats.put("successTasks", successCount);
        stats.put("failedTasks", failedCount);
        stats.put("cancelledTasks", cancelledCount);
        stats.put("activeThreads", parseExecutor instanceof ThreadPoolExecutor ? 
                ((ThreadPoolExecutor) parseExecutor).getActiveCount() : -1);
        
        return stats;
    }

    /**
     * 执行解析任务的核心逻辑
     *
     * @param task 解析任务
     * @return 解析结果
     */
    private Object executeParseTask(ParseTask task)
    {
        File tempFileToCleanup = null;

        try
        {
            task.setStatus(ParseStatus.RUNNING);
            task.setProgress(0.1);

            ////log.info("开始执行解析任务：{}，文件：{}", task.getTaskId(), task.getFileName());

            // 检查是否为临时文件，如果是则记录以便后续清理
            String filePath = task.getFilePath();
            if (isTemporaryFile(filePath))
            {
                tempFileToCleanup = new File(filePath);
                //log.debug("检测到临时文件，将在解析完成后清理：{}", filePath);
            }

            Object result = null;

            switch (task.getFileType())
            {
                case "pdf":
                    task.setProgress(0.3);
                    result = pdfParseService.parsePdfSync(task.getFilePath());
                    break;
                case "doc":
                case "docx":
                    task.setProgress(0.3);
                    result = wordParseService.parseWordSync(task.getFilePath());
                    break;
                default:
                    throw new ServiceException("不支持的文件类型：" + task.getFileType());
            }

            task.setProgress(0.9);
            task.setParseResult(result);
            task.setStatus(ParseStatus.SUCCESS);
            task.setProgress(1.0);
            task.setEndTime(System.currentTimeMillis());

            ////log.info("解析任务完成：{}，耗时：{}ms", task.getTaskId(), task.getDuration());

            // 调用成功回调
            invokeSuccessCallback(task.getTaskId(), result, task);

            return result;
        }
        catch (Exception e)
        {
            task.setStatus(ParseStatus.FAILED);
            task.setErrorMessage(e.getMessage());
            task.setEndTime(System.currentTimeMillis());

            //log.error("解析任务失败：{}，错误：{}", task.getTaskId(), e.getMessage(), e);

            // 调用失败回调
            invokeFailureCallback(task.getTaskId(), e.getMessage(), task);

            throw new RuntimeException(e);
        }
        finally
        {
            // 清理临时文件
            cleanupTemporaryFile(tempFileToCleanup, task.getTaskId());
        }
    }

    /**
     * 判断是否为临时文件
     *
     * @param filePath 文件路径
     * @return 是否为临时文件
     */
    private boolean isTemporaryFile(String filePath)
    {
        if (filePath == null)
        {
            return false;
        }

        // 检查是否为我们创建的临时文件
        return filePath.contains("contract-parse") &&
               (filePath.contains(System.getProperty("java.io.tmpdir")) ||
                filePath.startsWith("/tmp/") ||
                filePath.startsWith("/var/folders/"));
    }

    /**
     * 安全地清理临时文件
     *
     * @param tempFile 临时文件对象
     * @param taskId 任务ID（用于日志）
     */
    private void cleanupTemporaryFile(File tempFile, String taskId)
    {
        if (tempFile == null)
        {
            return;
        }

        try
        {
            if (tempFile.exists())
            {
                boolean deleted = tempFile.delete();
                if (deleted)
                {
                    ////log.info("临时文件清理成功，任务：{}，文件：{}", taskId, tempFile.getAbsolutePath());
                }
                else
                {
                    //log.warn("临时文件删除失败，任务：{}，文件：{}，文件可能被其他进程占用",
                            //taskId, tempFile.getAbsolutePath());

                    // 如果直接删除失败，尝试标记为删除
                    tempFile.deleteOnExit();
                    ////log.info("已标记临时文件在JVM退出时删除：{}", tempFile.getAbsolutePath());
                }
            }
            else
            {
                log.debug("临时文件已不存在，无需清理，任务：{}，文件：{}", taskId, tempFile.getAbsolutePath());
            }
        }
        catch (SecurityException e)
        {
            log.error("清理临时文件时发生安全异常，任务：{}，文件：{}，错误：{}",
                     taskId, tempFile.getAbsolutePath(), e.getMessage());
            // 发生异常时仍然尝试标记删除
            tempFile.deleteOnExit();
        }
        catch (Exception e)
        {
            log.error("清理临时文件时发生未知异常，任务：{}，文件：{}，错误：{}",
                     taskId, tempFile.getAbsolutePath(), e.getMessage(), e);
        }
    }

    /**
     * 调用成功回调
     *
     * @param taskId 任务ID
     * @param result 解析结果
     * @param task 任务对象
     */
    private void invokeSuccessCallback(String taskId, Object result, ParseTask task)
    {
        ParseCompletionCallback callback = callbackMap.remove(taskId);
        if (callback != null)
        {
            try
            {
                log.debug("调用解析成功回调，任务：{}", taskId);
                callback.onParseSuccess(taskId, result, task);
            }
            catch (Exception e)
            {
                log.error("执行解析成功回调时发生异常，任务：{}，错误：{}", taskId, e.getMessage(), e);
            }
        }
    }

    /**
     * 调用失败回调
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @param task 任务对象
     */
    private void invokeFailureCallback(String taskId, String errorMessage, ParseTask task)
    {
        ParseCompletionCallback callback = callbackMap.remove(taskId);
        if (callback != null)
        {
            try
            {
                log.debug("调用解析失败回调，任务：{}", taskId);
                callback.onParseFailure(taskId, errorMessage, task);
            }
            catch (Exception e)
            {
                log.error("执行解析失败回调时发生异常，任务：{}，错误：{}", taskId, e.getMessage(), e);
            }
        }
    }

    /**
     * 生成任务ID
     *
     * @return 任务ID
     */
    private String generateTaskId()
    {
        return "PARSE_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int) (Math.random() * 65536));
    }

    /**
     * 根据文件名确定文件类型
     *
     * @param fileName 文件名
     * @return 文件类型
     */
    private String determineFileType(String fileName)
    {
        if (fileName == null || fileName.isEmpty())
        {
            return "unknown";
        }
        
        String lowerFileName = fileName.toLowerCase();
        
        if (lowerFileName.endsWith(".pdf"))
        {
            return "pdf";
        }
        else if (lowerFileName.endsWith(".doc"))
        {
            return "doc";
        }
        else if (lowerFileName.endsWith(".docx"))
        {
            return "docx";
        }
        
        return "unknown";
    }

    /**
     * 检查系统健康状态
     *
     * @return 健康状态信息
     */
    public Map<String, Object> healthCheck()
    {
        Map<String, Object> health = new HashMap<>();
        
        try
        {
            health.put("serviceStatus", "healthy");
            health.put("taskMapSize", taskMap.size());
            
            if (parseExecutor instanceof ThreadPoolExecutor)
            {
                ThreadPoolExecutor tpe = (ThreadPoolExecutor) parseExecutor;
                health.put("corePoolSize", tpe.getCorePoolSize());
                health.put("maximumPoolSize", tpe.getMaximumPoolSize());
                health.put("activeCount", tpe.getActiveCount());
                health.put("taskCount", tpe.getTaskCount());
                health.put("completedTaskCount", tpe.getCompletedTaskCount());
                health.put("queueSize", tpe.getQueue().size());
            }
            
            Map<String, Object> stats = getTaskStatistics();
            health.put("taskStatistics", stats);
            
            health.put("memoryUsage", getMemoryUsage());
        }
        catch (Exception e)
        {
            health.put("serviceStatus", "unhealthy");
            health.put("error", e.getMessage());
        }
        
        return health;
    }

    /**
     * 获取内存使用情况
     *
     * @return 内存使用信息
     */
    private Map<String, Object> getMemoryUsage()
    {
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> memory = new HashMap<>();
        
        memory.put("totalMemory", runtime.totalMemory());
        memory.put("freeMemory", runtime.freeMemory());
        memory.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        memory.put("maxMemory", runtime.maxMemory());
        
        return memory;
    }

    /**
     * 关闭服务（应用关闭时调用）
     */
    public void shutdown()
    {
        //log.info("开始关闭异步文档解析服务...");
        
        // 取消所有未完成的任务
        for (ParseTask task : taskMap.values())
        {
            if (task.getStatus() == ParseStatus.PENDING || task.getStatus() == ParseStatus.RUNNING)
            {
                cancelTask(task.getTaskId());
            }
        }
        
        // 关闭线程池
        parseExecutor.shutdown();
        try
        {
            if (!parseExecutor.awaitTermination(30, TimeUnit.SECONDS))
            {
                parseExecutor.shutdownNow();
                if (!parseExecutor.awaitTermination(10, TimeUnit.SECONDS))
                {
                    log.warn("线程池无法正常关闭");
                }
            }
        }
        catch (InterruptedException e)
        {
            parseExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        //log.info("异步文档解析服务已关闭");
    }
}