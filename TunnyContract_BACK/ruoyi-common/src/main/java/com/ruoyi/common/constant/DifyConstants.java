package com.ruoyi.common.constant;

/**
 * Dify相关常量
 * 
 * <AUTHOR>
 */
public class DifyConstants
{
    /**
     * 响应模式
     */
    public static class ResponseMode
    {
        /** 阻塞模式 */
        public static final String BLOCKING = "blocking";
        
        /** 流式模式 */
        public static final String STREAMING = "streaming";
    }

    /**
     * 应用类型
     */
    public static class ApplicationType
    {
        /** 对话型应用 */
        public static final String CHAT = "chat";
        
        /** 文本生成应用 */
        public static final String COMPLETION = "completion";
        
        /** 工作流应用 */
        public static final String WORKFLOW = "workflow";
        
        /** 知识库 */
        public static final String DATASETS = "datasets";
    }

    /**
     * 事件类型
     */
    public static class EventType
    {
        /** 消息事件 */
        public static final String MESSAGE = "message";
        
        /** 消息结束事件 */
        public static final String MESSAGE_END = "message_end";
        
        /** 消息文件事件 */
        public static final String MESSAGE_FILE = "message_file";
        
        /** 文字转语音事件 */
        public static final String TTS_MESSAGE = "tts_message";
        
        /** 文字转语音结束事件 */
        public static final String TTS_MESSAGE_END = "tts_message_end";
        
        /** 消息替换事件 */
        public static final String MESSAGE_REPLACE = "message_replace";
        
        /** Agent消息事件 */
        public static final String AGENT_MESSAGE = "agent_message";
        
        /** Agent思考事件 */
        public static final String AGENT_THOUGHT = "agent_thought";
        
        /** 工作流开始事件 */
        public static final String WORKFLOW_STARTED = "workflow_started";
        
        /** 节点开始事件 */
        public static final String NODE_STARTED = "node_started";
        
        /** 节点完成事件 */
        public static final String NODE_FINISHED = "node_finished";
        
        /** 工作流完成事件 */
        public static final String WORKFLOW_FINISHED = "workflow_finished";
        
        /** 错误事件 */
        public static final String ERROR = "error";
        
        /** 心跳事件 */
        public static final String PING = "ping";
    }

    /**
     * 消息反馈类型
     */
    public static class FeedbackType
    {
        /** 点赞 */
        public static final String LIKE = "like";
        
        /** 点踩 */
        public static final String DISLIKE = "dislike";
    }

    /**
     * 文档处理模式
     */
    public static class ProcessMode
    {
        /** 自动处理 */
        public static final String AUTOMATIC = "automatic";
        
        /** 自定义处理 */
        public static final String CUSTOM = "custom";
    }

    /**
     * 索引技术
     */
    public static class IndexingTechnique
    {
        /** 高质量 */
        public static final String HIGH_QUALITY = "high_quality";
        
        /** 经济型 */
        public static final String ECONOMY = "economy";
    }

    /**
     * 文档形式
     */
    public static class DocForm
    {
        /** 文本模型 */
        public static final String TEXT_MODEL = "text_model";
        
        /** 问答模型 */
        public static final String QA_MODEL = "qa_model";
    }

    /**
     * 权限类型
     */
    public static class Permission
    {
        /** 仅自己 */
        public static final String ONLY_ME = "only_me";
        
        /** 所有团队成员 */
        public static final String ALL_TEAM_MEMBERS = "all_team_members";
        
        /** 部分团队成员 */
        public static final String PARTIAL_MEMBERS = "partial_members";
    }

    /**
     * 默认配置值
     */
    public static class DefaultValues
    {
        /** 默认用户ID */
        public static final String DEFAULT_USER = "system";
        
        /** 默认检索数量 */
        public static final int DEFAULT_TOP_K = 3;
        
        /** 默认相似度阈值 */
        public static final float DEFAULT_SCORE_THRESHOLD = 0.5f;
        
        /** 默认分页大小 */
        public static final int DEFAULT_PAGE_SIZE = 10;
        
        /** 默认连接超时时间（毫秒） */
        public static final int DEFAULT_CONNECT_TIMEOUT = 5000;
        
        /** 默认读取超时时间（毫秒） */
        public static final int DEFAULT_READ_TIMEOUT = 60000;
        
        /** 默认写入超时时间（毫秒） */
        public static final int DEFAULT_WRITE_TIMEOUT = 30000;
    }

    /**
     * 错误码
     */
    public static class ErrorCode
    {
        /** Dify服务不可用 */
        public static final String SERVICE_UNAVAILABLE = "DIFY_SERVICE_UNAVAILABLE";
        
        /** API密钥无效 */
        public static final String INVALID_API_KEY = "DIFY_INVALID_API_KEY";
        
        /** 请求参数无效 */
        public static final String INVALID_PARAMS = "DIFY_INVALID_PARAMS";

        /** Agent不存在 */
        public static final String AGENT_NOT_FOUND = "AGENT_NOT_FOUND";
        /** 配额不足 */
        public static final String QUOTA_EXCEEDED = "DIFY_QUOTA_EXCEEDED";
        
        /** 应用不存在 */
        public static final String APP_NOT_FOUND = "DIFY_APP_NOT_FOUND";
        
        /** 会话不存在 */
        public static final String CONVERSATION_NOT_FOUND = "DIFY_CONVERSATION_NOT_FOUND";
        
        /** 消息不存在 */
        public static final String MESSAGE_NOT_FOUND = "DIFY_MESSAGE_NOT_FOUND";
        
        /** 知识库不存在 */
        public static final String DATASET_NOT_FOUND = "DIFY_DATASET_NOT_FOUND";
        
        /** 文档不存在 */
        public static final String DOCUMENT_NOT_FOUND = "DIFY_DOCUMENT_NOT_FOUND";
    }
}
