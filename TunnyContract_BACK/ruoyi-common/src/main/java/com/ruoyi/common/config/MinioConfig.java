package com.ruoyi.common.config;

import io.minio.MinioClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;

/**
 * Minio 配置信息
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig
{
    private static final Logger log = LoggerFactory.getLogger(MinioConfig.class);
    /**
     * 服务地址
     */
    private static String url;

    /**
     * 用户名
     */
    private static String accessKey;

    /**
     * 密码
     */
    private static String secretKey;

    /**
     * 存储桶名称
     */
    private static String bucketName;

    public static String getUrl()
    {
        return url;
    }

    public void setUrl(String url)
    {
        MinioConfig.url = url;
    }

    public static String getAccessKey()
    {
        return accessKey;
    }

    public void setAccessKey(String accessKey)
    {
        MinioConfig.accessKey = accessKey;
    }

    public static String getSecretKey()
    {
        return secretKey;
    }

    public void setSecretKey(String secretKey)
    {
        MinioConfig.secretKey = secretKey;
    }

    public static String getBucketName()
    {
        return bucketName;
    }

    public void setBucketName(String bucketName)
    {
        MinioConfig.bucketName = bucketName;
    }

    /**
     * 配置加载后验证
     */
    @PostConstruct
    public void validateConfig()
    {
        log.info("=== MinIO配置验证 ===");
        log.info("URL: {}", url);
        log.info("AccessKey: {}", accessKey);
        log.info("SecretKey: {}", secretKey != null ? "***已配置***" : "未配置");
        log.info("BucketName: {}", bucketName);

        if (url == null || url.trim().isEmpty()) {
            log.error("MinIO URL未配置！");
        }
        if (accessKey == null || accessKey.trim().isEmpty()) {
            log.error("MinIO AccessKey未配置！");
        }
        if (secretKey == null || secretKey.trim().isEmpty()) {
            log.error("MinIO SecretKey未配置！");
        }
        if (bucketName == null || bucketName.trim().isEmpty()) {
            log.error("MinIO BucketName未配置！");
        }
        log.info("=== MinIO配置验证完成 ===");
    }

    @Bean
    public MinioClient getMinioClient()
    {
        log.info("创建MinioClient，URL: {}, AccessKey: {}", url, accessKey);

        if (url == null || accessKey == null || secretKey == null) {
            log.error("MinIO配置不完整，无法创建客户端！URL: {}, AccessKey: {}, SecretKey: {}",
                     url, accessKey, secretKey != null ? "已配置" : "未配置");
            throw new IllegalStateException("MinIO配置不完整");
        }

        try {
            MinioClient client = MinioClient.builder()
                .endpoint(url)
                .credentials(accessKey, secretKey)
                .build();
            log.info("MinioClient创建成功");
            return client;
        } catch (Exception e) {
            log.error("创建MinioClient失败：{}", e.getMessage(), e);
            throw e;
        }
    }
}
