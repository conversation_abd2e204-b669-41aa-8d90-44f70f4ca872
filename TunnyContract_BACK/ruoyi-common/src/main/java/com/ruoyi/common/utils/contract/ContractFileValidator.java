package com.ruoyi.common.utils.contract;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import org.apache.tika.Tika;
import org.apache.tika.mime.MimeType;
import org.apache.tika.mime.MimeTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 合同文件校验器
 * 专门用于合同文件上传的格式、大小、内容校验
 * 
 * <AUTHOR>
 */
public class ContractFileValidator
{
    private static final Logger log = LoggerFactory.getLogger(ContractFileValidator.class);
    
    /**
     * 支持的合同文件MIME类型
     */
    private static final Set<String> ALLOWED_MIME_TYPES = new HashSet<>(Arrays.asList(
        "application/pdf",                                                    // PDF
        "application/msword",                                                 // DOC
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // DOCX
        "application/vnd.ms-word.document.macroEnabled.12"                   // DOCM
    ));
    
    /**
     * 支持的文件扩展名
     */
    private static final Set<String> ALLOWED_EXTENSIONS = new HashSet<>(Arrays.asList(
        "pdf", "doc", "docx", "docm"
    ));
    
    /**
     * 文件大小限制：10MB
     */
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    /**
     * 文件名长度限制
     */
    private static final int MAX_FILENAME_LENGTH = 100;
    
    /**
     * Tika实例，用于文件类型检测
     */
    private static final Tika tika = new Tika();
    
    /**
     * 危险文件扩展名（防止伪装攻击）
     */
    private static final Set<String> DANGEROUS_EXTENSIONS = new HashSet<>(Arrays.asList(
        "exe", "bat", "cmd", "scr", "com", "pif", "js", "jar", "vbs", "ps1"
    ));

    /**
     * 校验合同文件
     * 
     * @param file 上传的文件
     * @throws ServiceException 校验失败时抛出异常
     */
    public static void validateContractFile(MultipartFile file)
    {
        if (file == null || file.isEmpty())
        {
            throw new ServiceException("文件不能为空");
        }

        // 校验文件名
        validateFileName(file.getOriginalFilename());
        
        // 校验文件大小
        validateFileSize(file.getSize());
        
        // 校验文件类型（基于内容）
        validateFileType(file);
        
        // 校验文件扩展名
        validateFileExtension(file.getOriginalFilename());
        
        log.info("合同文件校验通过：{}, 大小：{}字节", file.getOriginalFilename(), file.getSize());
    }

    /**
     * 校验文件名
     * 
     * @param fileName 文件名
     */
    private static void validateFileName(String fileName)
    {
        if (StringUtils.isEmpty(fileName))
        {
            throw new ServiceException("文件名不能为空");
        }
        
        if (fileName.length() > MAX_FILENAME_LENGTH)
        {
            throw new ServiceException("文件名长度不能超过" + MAX_FILENAME_LENGTH + "个字符");
        }
        
        // 检查危险字符
        if (fileName.contains("../") || fileName.contains("..\\"))
        {
            throw new ServiceException("文件名包含非法路径字符");
        }
        
        // 检查特殊字符
        String pattern = "[<>:\"/\\\\|?*\\x00-\\x1f]";
        if (fileName.matches(".*" + pattern + ".*"))
        {
            throw new ServiceException("文件名包含非法字符");
        }
    }

    /**
     * 校验文件大小
     * 
     * @param fileSize 文件大小（字节）
     */
    private static void validateFileSize(long fileSize)
    {
        if (fileSize <= 0)
        {
            throw new ServiceException("文件大小无效");
        }
        
        if (fileSize > MAX_FILE_SIZE)
        {
            throw new ServiceException("文件大小不能超过" + (MAX_FILE_SIZE / 1024 / 1024) + "MB");
        }
    }

    /**
     * 校验文件类型（基于文件内容）
     * 
     * @param file 上传的文件
     */
    private static void validateFileType(MultipartFile file)
    {
        try
        {
            // 使用Apache Tika检测实际文件类型
            String detectedMimeType = tika.detect(file.getInputStream(), file.getOriginalFilename());
            
            if (!ALLOWED_MIME_TYPES.contains(detectedMimeType))
            {
                log.warn("不支持的文件类型：{}, 文件名：{}", detectedMimeType, file.getOriginalFilename());
                throw new ServiceException("不支持的文件类型，仅支持PDF、DOC、DOCX格式");
            }
            
            log.debug("文件类型检测通过：{}", detectedMimeType);
        }
        catch (IOException e)
        {
            log.error("文件类型检测失败：{}", file.getOriginalFilename(), e);
            throw new ServiceException("文件内容检测失败，请确保文件完整");
        }
    }

    /**
     * 校验文件扩展名
     * 
     * @param fileName 文件名
     */
    private static void validateFileExtension(String fileName)
    {
        String extension = getFileExtension(fileName);
        
        if (StringUtils.isEmpty(extension))
        {
            throw new ServiceException("文件缺少扩展名");
        }
        
        // 检查是否为危险文件类型
        if (DANGEROUS_EXTENSIONS.contains(extension.toLowerCase()))
        {
            throw new ServiceException("不允许上传可执行文件");
        }
        
        // 检查是否为允许的扩展名
        if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase()))
        {
            throw new ServiceException("不支持的文件格式，仅支持：" + String.join("、", ALLOWED_EXTENSIONS));
        }
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名（小写）
     */
    public static String getFileExtension(String fileName)
    {
        if (StringUtils.isEmpty(fileName))
        {
            return "";
        }
        
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot == -1 || lastDot == fileName.length() - 1)
        {
            return "";
        }
        
        return fileName.substring(lastDot + 1).toLowerCase();
    }

    /**
     * 计算文件MD5值
     * 
     * @param file 文件
     * @return MD5值
     */
    public static String calculateFileMD5(MultipartFile file)
    {
        try
        {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(file.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest)
            {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }
        catch (Exception e)
        {
            log.error("计算文件MD5失败：{}", file.getOriginalFilename(), e);
            throw new ServiceException("文件校验失败");
        }
    }

    /**
     * 检查是否为合同文件（根据扩展名）
     * 
     * @param fileName 文件名
     * @return 是否为合同文件
     */
    public static boolean isContractFile(String fileName)
    {
        String extension = getFileExtension(fileName);
        return ALLOWED_EXTENSIONS.contains(extension);
    }

    /**
     * 获取友好的文件大小描述
     * 
     * @param size 文件大小（字节）
     * @return 友好描述
     */
    public static String getFileSizeDescription(long size)
    {
        if (size < 1024)
        {
            return size + " B";
        }
        else if (size < 1024 * 1024)
        {
            return String.format("%.1f KB", size / 1024.0);
        }
        else
        {
            return String.format("%.1f MB", size / 1024.0 / 1024.0);
        }
    }

    /**
     * 生成安全的文件名
     * 
     * @param originalFileName 原始文件名
     * @return 安全的文件名
     */
    public static String generateSafeFileName(String originalFileName)
    {
        if (StringUtils.isEmpty(originalFileName))
        {
            return "unknown_file";
        }
        
        String extension = getFileExtension(originalFileName);
        String baseName = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        
        // 移除特殊字符
        baseName = baseName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5_\\-]", "_");
        
        // 限制长度
        if (baseName.length() > 50)
        {
            baseName = baseName.substring(0, 50);
        }
        
        return baseName + "_" + System.currentTimeMillis() + "." + extension;
    }
}