package com.ruoyi.common.utils.contract;

import com.ruoyi.common.config.MinioConfig;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.MinioUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 合同文件上传服务
 * 专门处理合同文件的上传、校验、存储
 * 
 * <AUTHOR>
 */
@Service
public class ContractFileUploadService
{
    private static final Logger log = LoggerFactory.getLogger(ContractFileUploadService.class);
    
    /**
     * 合同文件存储路径前缀
     */
    private static final String CONTRACT_PATH_PREFIX = "contract/files/";

    /**
     * 上传合同文件
     * 
     * @param file 上传的文件
     * @return 文件上传结果信息
     */
    public Map<String, Object> uploadContractFile(MultipartFile file)
    {
        // 1. 校验文件
        ContractFileValidator.validateContractFile(file);
        
        // 2. 计算文件MD5
        String fileMd5 = ContractFileValidator.calculateFileMD5(file);
        
        // 3. 生成安全的文件名
        String safeFileName = ContractFileValidator.generateSafeFileName(file.getOriginalFilename());
        
        // 4. 构建存储路径
        String storagePath = buildStoragePath(safeFileName);
        
        try
        {
            // 5. 上传到MinIO
            String minioPath = MinioUtil.uploadFile(MinioConfig.getBucketName(), storagePath, file);
            
            // 6. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("originalFileName", file.getOriginalFilename());
            result.put("fileName", safeFileName);
            result.put("filePath", minioPath);
            result.put("fileSize", file.getSize());
            result.put("fileSizeDesc", ContractFileValidator.getFileSizeDescription(file.getSize()));
            result.put("fileType", ContractFileValidator.getFileExtension(file.getOriginalFilename()));
            result.put("fileMd5", fileMd5);
            result.put("uploadTime", DateUtils.getNowDate());
            result.put("storagePath", storagePath);
            
            log.info("合同文件上传成功：{} -> {}, MD5: {}", 
                    file.getOriginalFilename(), minioPath, fileMd5);
                    
            return result;
        }
        catch (Exception e)
        {
            log.error("合同文件上传失败：{}", file.getOriginalFilename(), e);
            throw new ServiceException("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 批量上传合同文件
     * 
     * @param files 文件数组
     * @return 批量上传结果
     */
    public Map<String, Object> uploadContractFiles(MultipartFile[] files)
    {
        if (files == null || files.length == 0)
        {
            throw new ServiceException("请选择要上传的文件");
        }
        
        if (files.length > 10)
        {
            throw new ServiceException("单次最多上传10个文件");
        }
        
        Map<String, Object> batchResult = new HashMap<>();
        Map<String, Object> successResults = new HashMap<>();
        Map<String, String> failureResults = new HashMap<>();
        
        int successCount = 0;
        int failureCount = 0;
        
        for (int i = 0; i < files.length; i++)
        {
            MultipartFile file = files[i];
            try
            {
                Map<String, Object> result = uploadContractFile(file);
                successResults.put("file_" + i, result);
                successCount++;
            }
            catch (Exception e)
            {
                failureResults.put(file.getOriginalFilename(), e.getMessage());
                failureCount++;
                log.warn("批量上传中文件失败：{}, 错误：{}", file.getOriginalFilename(), e.getMessage());
            }
        }
        
        batchResult.put("totalCount", files.length);
        batchResult.put("successCount", successCount);
        batchResult.put("failureCount", failureCount);
        batchResult.put("successResults", successResults);
        batchResult.put("failureResults", failureResults);
        batchResult.put("uploadTime", DateUtils.getNowDate());
        
        log.info("批量上传完成：总数{}, 成功{}, 失败{}", files.length, successCount, failureCount);
        
        return batchResult;
    }

    /**
     * 检查文件MD5是否已存在（去重）
     * 
     * @param fileMd5 文件MD5值
     * @return 是否已存在
     */
    public boolean checkFileExists(String fileMd5)
    {
        // TODO: 实现MD5去重检查，需要查询数据库
        // 这里暂时返回false，具体实现需要在ContractFileController中结合数据库查询
        return false;
    }

    /**
     * 获取文件基础信息（不上传）
     * 
     * @param file 文件
     * @return 文件信息
     */
    public Map<String, Object> getFileInfo(MultipartFile file)
    {
        try
        {
            // 校验文件（但不上传）
            ContractFileValidator.validateContractFile(file);
            
            // 计算MD5
            String fileMd5 = ContractFileValidator.calculateFileMD5(file);
            
            Map<String, Object> info = new HashMap<>();
            info.put("originalFileName", file.getOriginalFilename());
            info.put("fileSize", file.getSize());
            info.put("fileSizeDesc", ContractFileValidator.getFileSizeDescription(file.getSize()));
            info.put("fileType", ContractFileValidator.getFileExtension(file.getOriginalFilename()));
            info.put("fileMd5", fileMd5);
            info.put("isContractFile", ContractFileValidator.isContractFile(file.getOriginalFilename()));
            info.put("checkTime", DateUtils.getNowDate());
            
            return info;
        }
        catch (Exception e)
        {
            log.error("获取文件信息失败：{}", file.getOriginalFilename(), e);
            throw new ServiceException("文件信息获取失败：" + e.getMessage());
        }
    }

    /**
     * 构建文件存储路径
     * 
     * @param fileName 文件名
     * @return 存储路径
     */
    private String buildStoragePath(String fileName)
    {
        // 按日期分目录存储：contract/files/2025/09/02/filename
        String datePath = DateUtils.datePath();
        return CONTRACT_PATH_PREFIX + datePath + "/" + fileName;
    }

    /**
     * 校验上传参数
     * 
     * @param file 文件
     * @param taskId 任务ID（可选）
     */
    public void validateUploadParams(MultipartFile file, Long taskId)
    {
        if (file == null || file.isEmpty())
        {
            throw new ServiceException("请选择要上传的文件");
        }
        
        // 基础文件校验
        ContractFileValidator.validateContractFile(file);
        
        // 任务ID校验（如果提供）
        if (taskId != null && taskId <= 0)
        {
            throw new ServiceException("任务ID无效");
        }
        
        log.debug("上传参数校验通过：文件={}, 任务ID={}", file.getOriginalFilename(), taskId);
    }

    /**
     * 预估文件页数（基于文件大小的简单估算）
     * 
     * @param file 文件
     * @return 预估页数
     */
    public long estimatePageCount(MultipartFile file)
    {
        long fileSize = file.getSize();
        String fileType = ContractFileValidator.getFileExtension(file.getOriginalFilename());
        
        // 基于经验值的简单估算
        long estimatedPages;
        switch (fileType.toLowerCase())
        {
            case "pdf":
                // PDF平均每页约100KB
                estimatedPages = Math.max(1, fileSize / (100 * 1024));
                break;
            case "doc":
            case "docx":
                // Word文档平均每页约50KB
                estimatedPages = Math.max(1, fileSize / (50 * 1024));
                break;
            default:
                estimatedPages = 1;
        }
        
        // 限制在合理范围内
        return Math.min(estimatedPages, 1000);
    }

    /**
     * 获取支持的文件格式信息
     * 
     * @return 支持的格式信息
     */
    public Map<String, Object> getSupportedFormats()
    {
        Map<String, Object> formats = new HashMap<>();
        formats.put("extensions", new String[]{"pdf", "doc", "docx"});
        formats.put("maxSize", "100MB");
        formats.put("maxSizeBytes", 100 * 1024 * 1024);
        formats.put("description", "支持PDF、Word文档格式，单个文件不超过100MB");
        
        return formats;
    }
}