package com.ruoyi.common.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 合同文档转换配置类
 * 参考MinioConfig的写法，提供统一的配置管理
 * 
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "contract.conversion")
public class ContractConversionConfig {
    
    private static final Logger log = LoggerFactory.getLogger(ContractConversionConfig.class);
    
    /**
     * LibreOffice可执行文件路径
     */
    private static String libreOfficePath;
    
    /**
     * 转换超时时间(秒)
     */
    private static Integer timeout = 30;
    
    /**
     * 临时文件目录
     */
    private static String tempDir = "/tmp/contract-render";
    
    /**
     * PDF缓存目录
     */
    private static String pdfCacheDir = "/tmp/contract-pdf-cache";
    
    public static String getLibreOfficePath() {
        return libreOfficePath;
    }
    
    public void setLibreOfficePath(String libreOfficePath) {
        ContractConversionConfig.libreOfficePath = libreOfficePath;
    }
    
    public static Integer getTimeout() {
        return timeout;
    }
    
    public void setTimeout(Integer timeout) {
        ContractConversionConfig.timeout = timeout;
    }
    
    public static String getTempDir() {
        return tempDir;
    }
    
    public void setTempDir(String tempDir) {
        ContractConversionConfig.tempDir = tempDir;
    }
    
    public static String getPdfCacheDir() {
        return pdfCacheDir;
    }
    
    public void setPdfCacheDir(String pdfCacheDir) {
        ContractConversionConfig.pdfCacheDir = pdfCacheDir;
    }
    
    /**
     * 配置加载后验证
     */
    @PostConstruct
    public void validateConfig() {
        log.info("=== 合同文档转换配置验证 ===");
        log.info("LibreOffice路径: {}", libreOfficePath);
        log.info("转换超时时间: {}秒", timeout);
        log.info("临时文件目录: {}", tempDir);
        log.info("PDF缓存目录: {}", pdfCacheDir);
        
        // 验证LibreOffice路径
        if (libreOfficePath == null || libreOfficePath.trim().isEmpty()) {
            log.error("LibreOffice路径未配置！");
        } else {
            File libreOfficeFile = new File(libreOfficePath);
            if (!libreOfficeFile.exists()) {
                log.warn("LibreOffice可执行文件不存在: {}", libreOfficePath);
            } else if (!libreOfficeFile.canExecute()) {
                log.warn("LibreOffice文件没有执行权限: {}", libreOfficePath);
            } else {
                log.info("LibreOffice路径验证通过");
            }
        }
        
        // 验证并创建目录
        validateAndCreateDirectory(tempDir, "临时文件目录");
        validateAndCreateDirectory(pdfCacheDir, "PDF缓存目录");
        
        // 验证超时时间
        if (timeout == null || timeout <= 0) {
            log.warn("转换超时时间配置无效，使用默认值30秒");
            timeout = 30;
        }
        
        log.info("=== 合同文档转换配置验证完成 ===");
    }
    
    /**
     * 验证并创建目录
     * 
     * @param dirPath 目录路径
     * @param dirName 目录名称（用于日志）
     */
    private void validateAndCreateDirectory(String dirPath, String dirName) {
        if (dirPath == null || dirPath.trim().isEmpty()) {
            log.error("{}未配置！", dirName);
            return;
        }
        
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                log.info("{}创建成功: {}", dirName, dirPath);
            } else {
                log.info("{}已存在: {}", dirName, dirPath);
            }
            
            // 检查目录权限
            if (!Files.isWritable(path)) {
                log.warn("{}没有写入权限: {}", dirName, dirPath);
            }
        } catch (Exception e) {
            log.error("创建{}失败: {}, 错误: {}", dirName, dirPath, e.getMessage());
        }
    }
}