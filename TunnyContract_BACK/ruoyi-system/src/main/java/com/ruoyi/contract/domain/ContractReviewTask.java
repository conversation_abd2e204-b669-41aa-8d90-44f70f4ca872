package com.ruoyi.contract.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同审查任务对象 contract_review_task
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractReviewTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long id;

    /** 任务编号 */
    private String taskNo;

    /** 任务名称 */
    private String taskName;

    /** 合同分类ID */
    private Long categoryId;

    /** 合同分类名称 */
    private String categoryName;

    /** 审查策略ID */
    private Long strategyId;

    /** 审查策略名称 */
    private String strategyName;

    /** 审查立场(1-甲方，2-乙方) */
    private String reviewPosition;

    /** 任务状态(0-待处理，1-处理中，2-已完成，3-失败) */
    private String taskStatus;

    /** 开始处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /** 识别风险总数 */
    private Long totalRiskCount;

    /** 重大风险数量 */
    private Long highRiskCount;

    /** 一般风险数量 */
    private Long normalRiskCount;

    /** 错误信息 */
    private String errorMessage;

    /** 0-正常，2-删除 */
    private String delFlag;

    /** 保留字段1 */
    private String attr1;

    /** 保留字段2 */
    private String attr2;

    /** 保留字段3 */
    private String attr3;

    /** 文件总数 */
    private Integer totalFiles;

    /** 已解析文件数 */
    private Integer parsedFiles;

    /** 解析失败文件数 */
    private Integer failedFiles;

    /** 解析中文件数 */
    private Integer parsingFiles;

    /** 待解析文件数 */
    private Integer pendingFiles;

    /** 文件解析汇总状态 (0-未开始，1-解析中，2-全部成功，3-部分失败) */
    private String fileParseStatus;

    /** 审查结果JSON */
    private String reviewResult;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTaskNo(String taskNo) 
    {
        this.taskNo = taskNo;
    }

    public String getTaskNo() 
    {
        return taskNo;
    }

    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setStrategyId(Long strategyId)
    {
        this.strategyId = strategyId;
    }

    public Long getStrategyId()
    {
        return strategyId;
    }

    public void setStrategyName(String strategyName)
    {
        this.strategyName = strategyName;
    }

    public String getStrategyName()
    {
        return strategyName;
    }

    public void setReviewPosition(String reviewPosition) 
    {
        this.reviewPosition = reviewPosition;
    }

    public String getReviewPosition() 
    {
        return reviewPosition;
    }

    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() 
    {
        return taskStatus;
    }

    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }

    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }

    public void setTotalRiskCount(Long totalRiskCount) 
    {
        this.totalRiskCount = totalRiskCount;
    }

    public Long getTotalRiskCount() 
    {
        return totalRiskCount;
    }

    public void setHighRiskCount(Long highRiskCount) 
    {
        this.highRiskCount = highRiskCount;
    }

    public Long getHighRiskCount() 
    {
        return highRiskCount;
    }

    public void setNormalRiskCount(Long normalRiskCount) 
    {
        this.normalRiskCount = normalRiskCount;
    }

    public Long getNormalRiskCount() 
    {
        return normalRiskCount;
    }

    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3()
    {
        return attr3;
    }

    public void setTotalFiles(Integer totalFiles)
    {
        this.totalFiles = totalFiles;
    }

    public Integer getTotalFiles()
    {
        return totalFiles;
    }

    public void setParsedFiles(Integer parsedFiles)
    {
        this.parsedFiles = parsedFiles;
    }

    public Integer getParsedFiles()
    {
        return parsedFiles;
    }

    public void setFailedFiles(Integer failedFiles)
    {
        this.failedFiles = failedFiles;
    }

    public Integer getFailedFiles()
    {
        return failedFiles;
    }

    public void setParsingFiles(Integer parsingFiles)
    {
        this.parsingFiles = parsingFiles;
    }

    public Integer getParsingFiles()
    {
        return parsingFiles;
    }

    public void setPendingFiles(Integer pendingFiles)
    {
        this.pendingFiles = pendingFiles;
    }

    public Integer getPendingFiles()
    {
        return pendingFiles;
    }

    public void setFileParseStatus(String fileParseStatus)
    {
        this.fileParseStatus = fileParseStatus;
    }

    public String getFileParseStatus()
    {
        return fileParseStatus;
    }

    public void setReviewResult(String reviewResultJson)
    {
        this.reviewResult = reviewResultJson;
    }

    public String getReviewResult()
    {
        return reviewResult;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskNo", getTaskNo())
            .append("taskName", getTaskName())
            .append("categoryId", getCategoryId())
            .append("strategyId", getStrategyId())
            .append("reviewPosition", getReviewPosition())
            .append("taskStatus", getTaskStatus())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("totalRiskCount", getTotalRiskCount())
            .append("highRiskCount", getHighRiskCount())
            .append("normalRiskCount", getNormalRiskCount())
            .append("errorMessage", getErrorMessage())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .append("reviewResultJson", getReviewResult())
            .toString();
    }
}
