package com.ruoyi.contract.service;

import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.contract.domain.ContractFile;

/**
 * 合同文件处理Service接口
 * 整合文件上传、校验、解析等功能，提供统一的文件处理服务
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
public interface IContractFileProcessService 
{
    /**
     * 处理上传的合同文件
     * 包括文件上传、校验、MD5去重、数据库保存、异步解析的完整流程
     * 
     * @param taskId 关联的任务ID
     * @param files 上传的文件列表
     * @return 处理结果列表，包含成功和失败的文件信息
     */
    public List<ContractFile> processUploadedFiles(Long taskId, MultipartFile[] files);
    
    /**
     * 异步解析合同文件
     * 
     * @param contractFile 合同文件对象
     * @return 是否成功提交解析任务
     */
    public boolean parseFileAsync(ContractFile contractFile);
    
    /**
     * 批量异步解析合同文件
     * 
     * @param contractFiles 合同文件列表
     * @return 成功提交解析任务的数量
     */
    public int parseFilesAsync(List<ContractFile> contractFiles);
    
    /**
     * 校验上传的文件
     * 
     * @param file 上传的文件
     * @return 校验结果
     */
    public boolean validateUploadFile(MultipartFile file);
    
    /**
     * 根据任务ID删除相关文件
     * 包括删除MinIO存储文件和数据库记录
     * 
     * @param taskId 任务ID
     * @return 删除的文件数量
     */
    public int deleteFilesByTaskId(Long taskId);
    
    /**
     * 根据文件ID更新解析状态
     * 
     * @param fileId 文件ID
     * @param parseStatus 解析状态
     * @param contentText 解析后的文本内容（可选）
     * @return 更新结果
     */
    public boolean updateParseStatus(Long fileId, String parseStatus, String contentText);
}
