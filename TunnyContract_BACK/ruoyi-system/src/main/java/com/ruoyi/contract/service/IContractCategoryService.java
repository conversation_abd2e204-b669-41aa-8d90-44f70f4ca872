package com.ruoyi.contract.service;

import java.util.List;
import com.ruoyi.contract.domain.ContractCategory;

/**
 * 合同分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IContractCategoryService 
{
    /**
     * 查询合同分类
     * 
     * @param id 合同分类主键
     * @return 合同分类
     */
    public ContractCategory selectContractCategoryById(Long id);

    /**
     * 查询合同分类列表
     * 
     * @param contractCategory 合同分类
     * @return 合同分类集合
     */
    public List<ContractCategory> selectContractCategoryList(ContractCategory contractCategory);

    /**
     * 新增合同分类
     * 
     * @param contractCategory 合同分类
     * @return 结果
     */
    public int insertContractCategory(ContractCategory contractCategory);

    /**
     * 修改合同分类
     * 
     * @param contractCategory 合同分类
     * @return 结果
     */
    public int updateContractCategory(ContractCategory contractCategory);

    /**
     * 批量删除合同分类
     * 
     * @param ids 需要删除的合同分类主键集合
     * @return 结果
     */
    public int deleteContractCategoryByIds(Long[] ids);

    /**
     * 删除合同分类信息
     * 
     * @param id 合同分类主键
     * @return 结果
     */
    public int deleteContractCategoryById(Long id);
}
