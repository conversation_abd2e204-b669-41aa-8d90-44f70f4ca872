package com.ruoyi.contract.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同审查策略对象 contract_review_strategy
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractReviewStrategy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 策略ID */
    private Long id;

    /** 策略名称 */
    @Excel(name = "策略名称")
    private String strategyName;

    /** 策略描述 */
    @Excel(name = "策略描述")
    private String strategyDesc;

    /** 关联合同分类ID */
    @Excel(name = "关联合同分类ID")
    private Long categoryId;

    /** 关联合同分类名称 */
    @Excel(name = "关联合同分类名称")
    private String categoryName;

    /** 审查立场(1-甲方，2-乙方) */
    @Excel(name = "审查立场(1-甲方，2-乙方)")
    private String reviewPosition;

    /** 策略状态(0-草稿，1-已发布) */
    @Excel(name = "策略状态(0-草稿，1-已发布)")
    private String strategyStatus;

    /** 策略版本号 */
    @Excel(name = "策略版本号")
    private String version;

    /** 是否默认策略(1-是，0-否) */
    @Excel(name = "是否默认策略(1-是，0-否)")
    private String isDefault;

    /** 0-正常，2-删除 */
    private String delFlag;

    /** 保留字段1 */
    @Excel(name = "保留字段1")
    private String attr1;

    /** 保留字段2 */
    @Excel(name = "保留字段2")
    private String attr2;

    /** 保留字段3 */
    @Excel(name = "保留字段3")
    private String attr3;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStrategyName(String strategyName) 
    {
        this.strategyName = strategyName;
    }

    public String getStrategyName() 
    {
        return strategyName;
    }

    public void setStrategyDesc(String strategyDesc) 
    {
        this.strategyDesc = strategyDesc;
    }

    public String getStrategyDesc() 
    {
        return strategyDesc;
    }

    public void setCategoryId(Long categoryId) 
    {
        this.categoryId = categoryId;
    }

    public Long getCategoryId()
    {
        return categoryId;
    }

    public void setCategoryName(String categoryName)
    {
        this.categoryName = categoryName;
    }

    public String getCategoryName()
    {
        return categoryName;
    }

    public void setReviewPosition(String reviewPosition)
    {
        this.reviewPosition = reviewPosition;
    }

    public String getReviewPosition() 
    {
        return reviewPosition;
    }

    public void setStrategyStatus(String strategyStatus) 
    {
        this.strategyStatus = strategyStatus;
    }

    public String getStrategyStatus() 
    {
        return strategyStatus;
    }

    public void setVersion(String version) 
    {
        this.version = version;
    }

    public String getVersion() 
    {
        return version;
    }

    public void setIsDefault(String isDefault) 
    {
        this.isDefault = isDefault;
    }

    public String getIsDefault() 
    {
        return isDefault;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3() 
    {
        return attr3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("strategyName", getStrategyName())
            .append("strategyDesc", getStrategyDesc())
            .append("categoryId", getCategoryId())
            .append("reviewPosition", getReviewPosition())
            .append("strategyStatus", getStrategyStatus())
            .append("version", getVersion())
            .append("isDefault", getIsDefault())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .toString();
    }
}
