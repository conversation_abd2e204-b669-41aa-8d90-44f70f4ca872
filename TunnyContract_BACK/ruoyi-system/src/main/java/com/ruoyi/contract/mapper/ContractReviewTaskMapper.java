package com.ruoyi.contract.mapper;

import java.util.List;
import com.ruoyi.contract.domain.ContractReviewTask;

/**
 * 合同审查任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface ContractReviewTaskMapper 
{
    /**
     * 查询合同审查任务
     * 
     * @param id 合同审查任务主键
     * @return 合同审查任务
     */
    public ContractReviewTask selectContractReviewTaskById(Long id);

    /**
     * 查询合同审查任务列表
     * 
     * @param contractReviewTask 合同审查任务
     * @return 合同审查任务集合
     */
    public List<ContractReviewTask> selectContractReviewTaskList(ContractReviewTask contractReviewTask);

    /**
     * 新增合同审查任务
     * 
     * @param contractReviewTask 合同审查任务
     * @return 结果
     */
    public int insertContractReviewTask(ContractReviewTask contractReviewTask);

    /**
     * 修改合同审查任务
     * 
     * @param contractReviewTask 合同审查任务
     * @return 结果
     */
    public int updateContractReviewTask(ContractReviewTask contractReviewTask);

    /**
     * 删除合同审查任务
     * 
     * @param id 合同审查任务主键
     * @return 结果
     */
    public int deleteContractReviewTaskById(Long id);

    /**
     * 批量删除合同审查任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractReviewTaskByIds(Long[] ids);
}
