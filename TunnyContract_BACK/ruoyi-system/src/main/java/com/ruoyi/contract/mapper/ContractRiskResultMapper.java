package com.ruoyi.contract.mapper;

import java.util.List;
import com.ruoyi.contract.domain.ContractRiskResult;

/**
 * 合同风险识别结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface ContractRiskResultMapper 
{
    /**
     * 查询合同风险识别结果
     * 
     * @param id 合同风险识别结果主键
     * @return 合同风险识别结果
     */
    public ContractRiskResult selectContractRiskResultById(Long id);

    /**
     * 查询合同风险识别结果列表
     * 
     * @param contractRiskResult 合同风险识别结果
     * @return 合同风险识别结果集合
     */
    public List<ContractRiskResult> selectContractRiskResultList(ContractRiskResult contractRiskResult);

    /**
     * 新增合同风险识别结果
     * 
     * @param contractRiskResult 合同风险识别结果
     * @return 结果
     */
    public int insertContractRiskResult(ContractRiskResult contractRiskResult);

    /**
     * 修改合同风险识别结果
     * 
     * @param contractRiskResult 合同风险识别结果
     * @return 结果
     */
    public int updateContractRiskResult(ContractRiskResult contractRiskResult);

    /**
     * 删除合同风险识别结果
     * 
     * @param id 合同风险识别结果主键
     * @return 结果
     */
    public int deleteContractRiskResultById(Long id);

    /**
     * 批量删除合同风险识别结果
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractRiskResultByIds(Long[] ids);
}
