package com.ruoyi.contract.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同文件对象 contract_file
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文件ID */
    private Long id;

    /** 关联审查任务ID */
    @Excel(name = "关联审查任务ID")
    private Long taskId;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    private String fileName;

    /** 文件存储路径 */
    @Excel(name = "文件存储路径")
    private String filePath;

    /** 文件大小(字节) */
    @Excel(name = "文件大小(字节)")
    private Long fileSize;

    /** 文件类型(pdf/doc/docx) */
    @Excel(name = "文件类型(pdf/doc/docx)")
    private String fileType;

    /** 文件MD5值 */
    @Excel(name = "文件MD5值")
    private String fileMd5;

    /** 上传状态(1-成功，0-失败) */
    @Excel(name = "上传状态(1-成功，0-失败)")
    private String uploadStatus;

    /** 解析状态(0-未解析，1-解析中，2-解析成功，3-解析失败) */
    @Excel(name = "解析状态(0-未解析，1-解析中，2-解析成功，3-解析失败)")
    private String parseStatus;

    /** 解析后的文本内容 */
    @Excel(name = "解析后的文本内容")
    private String contentText;

    /** 文档页数 */
    @Excel(name = "文档页数")
    private Long pageCount;

    /** 0-正常，2-删除 */
    private String delFlag;

    /** 保留字段1 */
    @Excel(name = "保留字段1")
    private String attr1;

    /** 保留字段2 */
    @Excel(name = "保留字段2")
    private String attr2;

    /** 保留字段3 */
    @Excel(name = "保留字段3")
    private String attr3;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }

    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }

    public void setFileSize(Long fileSize) 
    {
        this.fileSize = fileSize;
    }

    public Long getFileSize() 
    {
        return fileSize;
    }

    public void setFileType(String fileType) 
    {
        this.fileType = fileType;
    }

    public String getFileType() 
    {
        return fileType;
    }

    public void setFileMd5(String fileMd5) 
    {
        this.fileMd5 = fileMd5;
    }

    public String getFileMd5() 
    {
        return fileMd5;
    }

    public void setUploadStatus(String uploadStatus) 
    {
        this.uploadStatus = uploadStatus;
    }

    public String getUploadStatus() 
    {
        return uploadStatus;
    }

    public void setParseStatus(String parseStatus) 
    {
        this.parseStatus = parseStatus;
    }

    public String getParseStatus() 
    {
        return parseStatus;
    }

    public void setContentText(String contentText) 
    {
        this.contentText = contentText;
    }

    public String getContentText() 
    {
        return contentText;
    }

    public void setPageCount(Long pageCount) 
    {
        this.pageCount = pageCount;
    }

    public Long getPageCount() 
    {
        return pageCount;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3() 
    {
        return attr3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskId", getTaskId())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .append("fileSize", getFileSize())
            .append("fileType", getFileType())
            .append("fileMd5", getFileMd5())
            .append("uploadStatus", getUploadStatus())
            .append("parseStatus", getParseStatus())
            .append("contentText", getContentText())
            .append("pageCount", getPageCount())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .toString();
    }
}
