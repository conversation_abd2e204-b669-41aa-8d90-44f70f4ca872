package com.ruoyi.contract.mapper;

import java.util.List;
import com.ruoyi.contract.domain.ContractCategory;

/**
 * 合同分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface ContractCategoryMapper 
{
    /**
     * 查询合同分类
     * 
     * @param id 合同分类主键
     * @return 合同分类
     */
    public ContractCategory selectContractCategoryById(Long id);

    /**
     * 查询合同分类列表
     * 
     * @param contractCategory 合同分类
     * @return 合同分类集合
     */
    public List<ContractCategory> selectContractCategoryList(ContractCategory contractCategory);

    /**
     * 新增合同分类
     * 
     * @param contractCategory 合同分类
     * @return 结果
     */
    public int insertContractCategory(ContractCategory contractCategory);

    /**
     * 修改合同分类
     * 
     * @param contractCategory 合同分类
     * @return 结果
     */
    public int updateContractCategory(ContractCategory contractCategory);

    /**
     * 删除合同分类
     * 
     * @param id 合同分类主键
     * @return 结果
     */
    public int deleteContractCategoryById(Long id);

    /**
     * 批量删除合同分类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractCategoryByIds(Long[] ids);
}
