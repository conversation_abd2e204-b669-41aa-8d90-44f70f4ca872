package com.ruoyi.contract.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.contract.domain.ContractReviewStrategy;
import com.ruoyi.contract.dto.StrategyClauseRiskDTO;

/**
 * 合同审查策略Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IContractReviewStrategyService 
{
    /**
     * 查询合同审查策略
     * 
     * @param id 合同审查策略主键
     * @return 合同审查策略
     */
    public ContractReviewStrategy selectContractReviewStrategyById(Long id);

    /**
     * 查询合同审查策略列表
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 合同审查策略集合
     */
    public List<ContractReviewStrategy> selectContractReviewStrategyList(ContractReviewStrategy contractReviewStrategy);

    /**
     * 新增合同审查策略
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 结果
     */
    public int insertContractReviewStrategy(ContractReviewStrategy contractReviewStrategy);

    /**
     * 修改合同审查策略
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 结果
     */
    public int updateContractReviewStrategy(ContractReviewStrategy contractReviewStrategy);

    /**
     * 批量删除合同审查策略
     * 
     * @param ids 需要删除的合同审查策略主键集合
     * @return 结果
     */
    public int deleteContractReviewStrategyByIds(Long[] ids);

    /**
     * 删除合同审查策略信息
     *
     * @param id 合同审查策略主键
     * @return 结果
     */
    public int deleteContractReviewStrategyById(Long id);

    /**
     * 发布策略
     *
     * @param id 策略主键
     * @return 结果
     */
    public int publishStrategy(Long id);

    /**
     * 取消发布策略（设为草稿）
     *
     * @param id 策略主键
     * @return 结果
     */
    public int unpublishStrategy(Long id);

    /**
     * 复制策略
     *
     * @param id 源策略主键
     * @param strategyName 新策略名称
     * @return 结果
     */
    public int copyStrategy(Long id, String strategyName);

    /**
     * 获取策略统计信息
     *
     * @return 统计数据
     */
    public Map<String, Object> getStrategyStats();

    /**
     * 根据策略ID获取关联的审查条款清单及每个条款对应的风险点列表
     *
     * @param strategyId 审查策略ID
     * @return 条款和风险点列表
     */
    public List<StrategyClauseRiskDTO> getStrategyClausesWithRiskPoints(Long strategyId);
}
