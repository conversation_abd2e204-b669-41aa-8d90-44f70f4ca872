package com.ruoyi.contract.service;

import java.util.List;
import com.ruoyi.contract.domain.ContractReviewTask;
import com.ruoyi.contract.domain.ContractReviewStrategy;

/**
 * 合同审查任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IContractReviewTaskService 
{
    /**
     * 查询合同审查任务
     * 
     * @param id 合同审查任务主键
     * @return 合同审查任务
     */
    public ContractReviewTask selectContractReviewTaskById(Long id);

    /**
     * 查询合同审查任务列表
     * 
     * @param contractReviewTask 合同审查任务
     * @return 合同审查任务集合
     */
    public List<ContractReviewTask> selectContractReviewTaskList(ContractReviewTask contractReviewTask);

    /**
     * 新增合同审查任务
     * 
     * @param contractReviewTask 合同审查任务
     * @return 结果
     */
    public int insertContractReviewTask(ContractReviewTask contractReviewTask);

    /**
     * 修改合同审查任务
     * 
     * @param contractReviewTask 合同审查任务
     * @return 结果
     */
    public int updateContractReviewTask(ContractReviewTask contractReviewTask);

    /**
     * 批量删除合同审查任务
     * 
     * @param ids 需要删除的合同审查任务主键集合
     * @return 结果
     */
    public int deleteContractReviewTaskByIds(Long[] ids);

    /**
     * 删除合同审查任务信息
     *
     * @param id 合同审查任务主键
     * @return 结果
     */
    public int deleteContractReviewTaskById(Long id);

    /**
     * 根据合同分类和审查立场获取可用策略列表
     *
     * @param categoryId 合同分类ID
     * @param reviewPosition 审查立场
     * @return 策略列表
     */
    public List<ContractReviewStrategy> getAvailableStrategies(Long categoryId, String reviewPosition);
}
