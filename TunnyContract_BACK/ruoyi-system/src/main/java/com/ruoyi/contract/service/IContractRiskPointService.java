package com.ruoyi.contract.service;

import java.util.List;
import com.ruoyi.contract.domain.ContractRiskPoint;

/**
 * 合同风险点Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IContractRiskPointService 
{
    /**
     * 查询合同风险点
     * 
     * @param id 合同风险点主键
     * @return 合同风险点
     */
    public ContractRiskPoint selectContractRiskPointById(Long id);

    /**
     * 查询合同风险点列表
     * 
     * @param contractRiskPoint 合同风险点
     * @return 合同风险点集合
     */
    public List<ContractRiskPoint> selectContractRiskPointList(ContractRiskPoint contractRiskPoint);

    /**
     * 新增合同风险点
     * 
     * @param contractRiskPoint 合同风险点
     * @return 结果
     */
    public int insertContractRiskPoint(ContractRiskPoint contractRiskPoint);

    /**
     * 修改合同风险点
     * 
     * @param contractRiskPoint 合同风险点
     * @return 结果
     */
    public int updateContractRiskPoint(ContractRiskPoint contractRiskPoint);

    /**
     * 批量删除合同风险点
     * 
     * @param ids 需要删除的合同风险点主键集合
     * @return 结果
     */
    public int deleteContractRiskPointByIds(Long[] ids);

    /**
     * 删除合同风险点信息
     * 
     * @param id 合同风险点主键
     * @return 结果
     */
    public int deleteContractRiskPointById(Long id);
}
