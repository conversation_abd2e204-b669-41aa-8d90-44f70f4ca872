package com.ruoyi.contract.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.contract.AsyncDocumentParseService;
import com.ruoyi.common.utils.contract.ContractFileUploadService;
import com.ruoyi.common.utils.contract.ContractFileValidator;
import com.ruoyi.common.utils.contract.PdfParseUtil;
import com.ruoyi.common.utils.contract.WordParseUtil;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractFileProcessService;
import com.ruoyi.contract.service.IContractFileService;

/**
 * 合同文件处理Service业务层处理
 * 整合文件上传、校验、解析等功能，提供统一的文件处理服务
 * 
 * <AUTHOR>
 * @date 2025-09-05
 */
@Service
public class ContractFileProcessServiceImpl implements IContractFileProcessService 
{
    private static final Logger log = LoggerFactory.getLogger(ContractFileProcessServiceImpl.class);

    @Autowired
    private ContractFileUploadService contractFileUploadService;

    @Autowired
    private AsyncDocumentParseService asyncDocumentParseService;

    @Autowired
    private IContractFileService contractFileService;

    /**
     * 处理上传的合同文件
     * 包括文件上传、校验、MD5去重、数据库保存、异步解析的完整流程
     * 
     * @param taskId 关联的任务ID
     * @param files 上传的文件列表
     * @return 处理结果列表，包含成功和失败的文件信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ContractFile> processUploadedFiles(Long taskId, MultipartFile[] files)
    {
        if (taskId == null)
        {
            throw new ServiceException("任务ID不能为空");
        }
        
        if (files == null || files.length == 0)
        {
            //log.warn("没有文件需要处理，任务ID：{}", taskId);
            return new ArrayList<>();
        }

        if (files.length > 10)
        {
            throw new ServiceException("单次最多上传10个文件");
        }

        List<ContractFile> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        //log.info("开始处理文件上传，任务ID：{}，文件数量：{}", taskId, files.length);

        for (MultipartFile file : files)
        {
            try
            {
                ContractFile contractFile = processSingleFile(taskId, file);
                if (contractFile != null)
                {
                    results.add(contractFile);
                    successCount++;
                    //log.info("文件处理成功：{}", file.getOriginalFilename());
                }
            }
            catch (Exception e)
            {
                failureCount++;
                //log.error("文件处理失败：{}，错误：{}", file.getOriginalFilename(), e.getMessage(), e);
                
                // 创建失败记录
                ContractFile failedFile = createFailedFileRecord(taskId, file, e.getMessage());
                results.add(failedFile);
            }
        }

        //log.info("文件处理完成，任务ID：{}，成功：{}，失败：{}", taskId, successCount, failureCount);
        return results;
    }

    /**
     * 处理单个文件
     * 
     * @param taskId 任务ID
     * @param file 文件
     * @return 处理结果
     */
    private ContractFile processSingleFile(Long taskId, MultipartFile file) throws Exception
    {
        // 1. 校验文件
        if (!validateUploadFile(file))
        {
            throw new ServiceException("文件校验失败：" + file.getOriginalFilename());
        }

        // 2. 计算MD5，检查是否重复
       /* String fileMd5 = ContractFileValidator.calculateFileMD5(file);
        ContractFile existingFile = contractFileService.selectContractFileByMd5(fileMd5);
        if (existingFile != null)
        {
            //log.warn("检测到重复文件，MD5：{}，原文件：{}", fileMd5, existingFile.getFileName());
            
            // 如果是同一个任务的文件，直接返回现有记录
            if (taskId.equals(existingFile.getTaskId()))
            {
                return existingFile;
            }
            
            // 如果是不同任务，创建新记录但复用文件路径
            return createFileRecordFromExisting(taskId, file, existingFile);
        }*/

        // 3. 上传文件到MinIO
        Map<String, Object> uploadResult;
        try {
            //log.info("开始上传文件到MinIO：{}，大小：{}字节", file.getOriginalFilename(), file.getSize());
            uploadResult = contractFileUploadService.uploadContractFile(file);
            //log.info("文件上传MinIO成功：{}", uploadResult);
        } catch (Exception e) {
            //log.error("MinIO上传失败，文件：{}，错误：{}", file.getOriginalFilename(), e.getMessage(), e);

            // 检查具体错误类型
            if (e.getMessage().contains("NoSuchBucket")) {
                throw new ServiceException("MinIO存储桶不存在，请联系管理员创建bucket：contract-data");
            } else if (e.getMessage().contains("AccessDenied")) {
                throw new ServiceException("MinIO访问权限不足，请检查accessKey和secretKey配置");
            } else if (e.getMessage().contains("unexpected end of stream")) {
                throw new ServiceException("文件上传中断，可能是网络问题或文件过大，请重试");
            } else {
                throw new ServiceException("文件上传失败：" + e.getMessage());
            }
        }

        // 4. 创建数据库记录
        ContractFile contractFile = createFileRecord(taskId, file, uploadResult);
        int result = contractFileService.insertContractFile(contractFile);
        
        if (result <= 0)
        {
            throw new ServiceException("保存文件记录失败");
        }

        // 5. 提交异步解析任务
        if (parseFileAsync(contractFile))
        {
            //log.info("异步解析任务提交成功，文件ID：{}", contractFile.getId());
        }
        else
        {
            //log.warn("异步解析任务提交失败，文件ID：{}", contractFile.getId());
        }

        return contractFile;
    }

    /**
     * 创建文件记录
     *
     * @param taskId 任务ID
     * @param file 文件
     * @param uploadResult 上传结果
     * @return 文件记录
     */
    private ContractFile createFileRecord(Long taskId, MultipartFile file, Map<String, Object> uploadResult)
    {
        ContractFile contractFile = new ContractFile();
        contractFile.setTaskId(taskId);
        contractFile.setFileName(file.getOriginalFilename());
        contractFile.setFileSize(file.getSize());
        contractFile.setFileType(ContractFileValidator.getFileExtension(file.getOriginalFilename()));
        contractFile.setFileMd5((String) uploadResult.get("fileMd5"));
        contractFile.setFilePath((String) uploadResult.get("filePath"));
        contractFile.setUploadStatus("1"); // 上传成功
        contractFile.setParseStatus("0"); // 未解析
        contractFile.setCreateBy(SecurityUtils.getUsername());
        contractFile.setCreateTime(DateUtils.getNowDate());
        contractFile.setUpdateBy(SecurityUtils.getUsername());
        contractFile.setUpdateTime(DateUtils.getNowDate());

        return contractFile;
    }

    /**
     * 从现有文件创建新记录（MD5重复情况）
     *
     * @param taskId 任务ID
     * @param file 文件
     * @param existingFile 现有文件记录
     * @return 新文件记录
     */
    private ContractFile createFileRecordFromExisting(Long taskId, MultipartFile file, ContractFile existingFile)
    {
        ContractFile contractFile = new ContractFile();
        contractFile.setTaskId(taskId);
        contractFile.setFileName(file.getOriginalFilename());
        contractFile.setFileSize(file.getSize());
        contractFile.setFileType(existingFile.getFileType());
        contractFile.setFileMd5(existingFile.getFileMd5());
        contractFile.setFilePath(existingFile.getFilePath()); // 复用文件路径
        contractFile.setUploadStatus("1"); // 上传成功
        contractFile.setParseStatus("0"); // 未解析
        contractFile.setCreateBy(SecurityUtils.getUsername());
        contractFile.setCreateTime(DateUtils.getNowDate());
        contractFile.setUpdateBy(SecurityUtils.getUsername());
        contractFile.setUpdateTime(DateUtils.getNowDate());

        // 保存到数据库
        contractFileService.insertContractFile(contractFile);

        return contractFile;
    }

    /**
     * 创建失败文件记录
     *
     * @param taskId 任务ID
     * @param file 文件
     * @param errorMessage 错误信息
     * @return 失败文件记录
     */
    private ContractFile createFailedFileRecord(Long taskId, MultipartFile file, String errorMessage)
    {
        ContractFile contractFile = new ContractFile();
        contractFile.setTaskId(taskId);
        contractFile.setFileName(file.getOriginalFilename());
        contractFile.setFileSize(file.getSize());
        contractFile.setFileType(ContractFileValidator.getFileExtension(file.getOriginalFilename()));
        contractFile.setUploadStatus("0"); // 上传失败
        contractFile.setParseStatus("0"); // 未解析
        contractFile.setRemark(errorMessage); // 将错误信息存储在备注字段
        contractFile.setCreateBy(SecurityUtils.getUsername());
        contractFile.setCreateTime(DateUtils.getNowDate());
        contractFile.setUpdateBy(SecurityUtils.getUsername());
        contractFile.setUpdateTime(DateUtils.getNowDate());

        return contractFile;
    }

    /**
     * 异步解析合同文件
     *
     * @param contractFile 合同文件对象
     * @return 是否成功提交解析任务
     */
    @Override
    public boolean parseFileAsync(ContractFile contractFile)
    {
        if (contractFile == null || contractFile.getId() == null)
        {
            //log.warn("文件对象或文件ID为空，无法提交解析任务");
            return false;
        }

        try
        {

            // 更新解析状态为解析中
            updateParseStatus(contractFile.getId(), "1", null);

            // 创建解析完成回调
            AsyncDocumentParseService.ParseCompletionCallback callback = new AsyncDocumentParseService.ParseCompletionCallback()
            {
                @Override
                public void onParseSuccess(String taskId, Object parseResult, AsyncDocumentParseService.ParseTask task)
                {
                    handleParseSuccess(contractFile.getId(), parseResult, task );
                }

                @Override
                public void onParseFailure(String taskId, String errorMessage, AsyncDocumentParseService.ParseTask task)
                {
                    handleParseFailure(contractFile.getId(), errorMessage, task);
                }
            };

            // 提交异步解析任务，使用文件路径和回调
            String taskId = asyncDocumentParseService.submitParseTask(contractFile.getFilePath(), callback);

            //log.info("异步解析任务提交成功，文件ID：{}，文件名：{}，解析任务ID：{}",
                    //contractFile.getId(), contractFile.getFileName(), taskId);
            return true;
        }
        catch (Exception e)
        {
            //log.error("提交异步解析任务失败，文件ID：{}，错误：{}", contractFile.getId(), e.getMessage(), e);

            // 更新解析状态为失败
            updateParseStatus(contractFile.getId(), "3", "解析任务提交失败：" + e.getMessage());
            return false;
        }
    }

    /**
     * 批量异步解析合同文件
     *
     * @param contractFiles 合同文件列表
     * @return 成功提交解析任务的数量
     */
    @Override
    public int parseFilesAsync(List<ContractFile> contractFiles)
    {
        if (contractFiles == null || contractFiles.isEmpty())
        {
            //log.warn("文件列表为空，无需提交解析任务");
            return 0;
        }

        int successCount = 0;
        for (ContractFile contractFile : contractFiles)
        {
            if (parseFileAsync(contractFile))
            {
                successCount++;
            }
        }

        //log.info("批量异步解析任务提交完成，总数：{}，成功：{}", contractFiles.size(), successCount);
        return successCount;
    }

    /**
     * 校验上传的文件
     *
     * @param file 上传的文件
     * @return 校验结果
     */
    @Override
    public boolean validateUploadFile(MultipartFile file)
    {
        try
        {
            ContractFileValidator.validateContractFile(file);
            return true;
        }
        catch (Exception e)
        {
            //log.warn("文件校验失败：{}，错误：{}", file.getOriginalFilename(), e.getMessage());
            return false;
        }
    }

    /**
     * 根据任务ID删除相关文件
     * 包括删除MinIO存储文件和数据库记录
     *
     * @param taskId 任务ID
     * @return 删除的文件数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFilesByTaskId(Long taskId)
    {
        if (taskId == null)
        {
            //log.warn("任务ID为空，无法删除文件");
            return 0;
        }

        try
        {
            // 查询任务相关的所有文件
            ContractFile queryParam = new ContractFile();
            queryParam.setTaskId(taskId);
            List<ContractFile> files = contractFileService.selectContractFileList(queryParam);

            if (files.isEmpty())
            {
                //log.info("任务ID：{}，没有关联的文件需要删除", taskId);
                return 0;
            }

            int deleteCount = 0;
            for (ContractFile file : files)
            {
                try
                {
                    // 删除数据库记录（软删除）
                    int result = contractFileService.deleteContractFileById(file.getId());
                    if (result > 0)
                    {
                        deleteCount++;
                        //log.info("删除文件记录成功，文件ID：{}，文件名：{}", file.getId(), file.getFileName());
                    }
                }
                catch (Exception e)
                {
                    //log.error("删除文件失败，文件ID：{}，错误：{}", file.getId(), e.getMessage(), e);
                }
            }

            //log.info("删除任务文件完成，任务ID：{}，删除数量：{}", taskId, deleteCount);
            return deleteCount;
        }
        catch (Exception e)
        {
            //log.error("删除任务文件失败，任务ID：{}，错误：{}", taskId, e.getMessage(), e);
            throw new ServiceException("删除文件失败：" + e.getMessage());
        }
    }

    /**
     * 根据文件ID更新解析状态
     *
     * @param fileId 文件ID
     * @param parseStatus 解析状态
     * @param contentText 解析后的文本内容（可选）
     * @return 更新结果
     */
    @Override
    public boolean updateParseStatus(Long fileId, String parseStatus, String contentText)
    {
        if (fileId == null)
        {
            //log.warn("文件ID为空，无法更新解析状态");
            return false;
        }

        try
        {
            ContractFile updateFile = new ContractFile();
            updateFile.setId(fileId);
            updateFile.setParseStatus(parseStatus);
            //updateFile.setUpdateBy(SecurityUtils.getUsername());
            updateFile.setUpdateTime(DateUtils.getNowDate());

            if (contentText != null)
            {
                updateFile.setContentText(contentText);
            }

            int result = contractFileService.updateContractFile(updateFile);
            if (result > 0)
            {
                //log.info("更新文件解析状态成功，文件ID：{}，状态：{}", fileId, parseStatus);
                return true;
            }
            else
            {
                //log.warn("更新文件解析状态失败，文件ID：{}，状态：{}", fileId, parseStatus);
                return false;
            }
        }
        catch (Exception e)
        {
            //log.error("更新文件解析状态异常，文件ID：{}，错误：{}", fileId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理解析成功回调
     *
     * @param fileId 文件ID
     * @param parseResult 解析结果
     * @param task 解析任务
     */
    private void handleParseSuccess(Long fileId, Object parseResult, AsyncDocumentParseService.ParseTask task)
    {
        try
        {
            String contentText = "";

            // 根据解析结果类型提取文本内容
            if (parseResult instanceof PdfParseUtil.PdfParseResult)
            {
                PdfParseUtil.PdfParseResult pdfResult = (PdfParseUtil.PdfParseResult) parseResult;
                contentText = pdfResult.getFullText();
                //log.info("PDF解析成功，文件ID：{}，页数：{}，文本长度：{}",
                        //fileId, pdfResult.getPageCount(), contentText.length());
            }
            else if (parseResult instanceof WordParseUtil.WordParseResult)
            {
                WordParseUtil.WordParseResult wordResult = (WordParseUtil.WordParseResult) parseResult;
                contentText = wordResult.getFullText();
                //log.info("Word解析成功，文件ID：{}，段落数：{}，文本长度：{}",
                        //fileId, wordResult.getParagraphCount(), contentText.length());
            }

            // 更新解析状态为成功，并保存解析内容
            updateParseStatus(fileId, "2", contentText);

            //log.info("文件解析结果已保存到数据库，文件ID：{}，任务：{}", fileId, task.getTaskId());
        }
        catch (Exception e)
        {
            //log.error("处理解析成功回调时发生异常，文件ID：{}，任务：{}，错误：{}",
                     //fileId, task.getTaskId(), e.getMessage(), e);
            // 如果保存失败，更新状态为失败
            updateParseStatus(fileId, "3", "保存解析结果失败：" + e.getMessage());
        }
    }

    /**
     * 处理解析失败回调
     *
     * @param fileId 文件ID
     * @param errorMessage 错误信息
     * @param task 解析任务
     */
    private void handleParseFailure(Long fileId, String errorMessage, AsyncDocumentParseService.ParseTask task)
    {
        try
        {
            //log.error("文件解析失败，文件ID：{}，任务：{}，错误：{}", fileId, task.getTaskId(), errorMessage);

            // 更新解析状态为失败
            updateParseStatus(fileId, "3", "解析失败：" + errorMessage);
        }
        catch (Exception e)
        {
            //log.error("处理解析失败回调时发生异常，文件ID：{}，任务：{}，错误：{}",
                     //fileId, task.getTaskId(), e.getMessage(), e);
        }
    }
}
