package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractReviewResultMapper;
import com.ruoyi.contract.domain.ContractReviewResult;
import com.ruoyi.contract.service.IContractReviewResultService;

/**
 * 合同审查结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractReviewResultServiceImpl implements IContractReviewResultService 
{
    @Autowired
    private ContractReviewResultMapper contractReviewResultMapper;

    /**
     * 查询合同审查结果
     * 
     * @param id 合同审查结果主键
     * @return 合同审查结果
     */
    @Override
    public ContractReviewResult selectContractReviewResultById(Long id)
    {
        return contractReviewResultMapper.selectContractReviewResultById(id);
    }

    /**
     * 查询合同审查结果列表
     * 
     * @param contractReviewResult 合同审查结果
     * @return 合同审查结果
     */
    @Override
    public List<ContractReviewResult> selectContractReviewResultList(ContractReviewResult contractReviewResult)
    {
        return contractReviewResultMapper.selectContractReviewResultList(contractReviewResult);
    }

    /**
     * 新增合同审查结果
     * 
     * @param contractReviewResult 合同审查结果
     * @return 结果
     */
    @Override
    public int insertContractReviewResult(ContractReviewResult contractReviewResult)
    {
        contractReviewResult.setCreateTime(DateUtils.getNowDate());
        return contractReviewResultMapper.insertContractReviewResult(contractReviewResult);
    }

    /**
     * 修改合同审查结果
     * 
     * @param contractReviewResult 合同审查结果
     * @return 结果
     */
    @Override
    public int updateContractReviewResult(ContractReviewResult contractReviewResult)
    {
        contractReviewResult.setUpdateTime(DateUtils.getNowDate());
        return contractReviewResultMapper.updateContractReviewResult(contractReviewResult);
    }

    /**
     * 批量删除合同审查结果
     * 
     * @param ids 需要删除的合同审查结果主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewResultByIds(Long[] ids)
    {
        return contractReviewResultMapper.deleteContractReviewResultByIds(ids);
    }

    /**
     * 删除合同审查结果信息
     * 
     * @param id 合同审查结果主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewResultById(Long id)
    {
        return contractReviewResultMapper.deleteContractReviewResultById(id);
    }
}
