package com.ruoyi.contract.service;

import com.ruoyi.contract.domain.ContractFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 合同文档坐标解析服务接口
 * 负责从PDF中提取文本坐标信息，并进行风险点文本匹配
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface IContractCoordinateService 
{
    /**
     * 文本坐标信息
     */
    public static class TextCoordinate {
        private String text;          // 文本内容
        private int pageNumber;       // 页码（从1开始）
        private float x;              // X坐标
        private float y;              // Y坐标
        private float width;          // 文本宽度
        private float height;         // 文本高度
        private String fontName;      // 字体名称
        private float fontSize;       // 字体大小
        
        // 构造方法
        public TextCoordinate() {}
        
        public TextCoordinate(String text, int pageNumber, float x, float y, float width, float height) {
            this.text = text;
            this.pageNumber = pageNumber;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
        
        // Getter and Setter methods
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        
        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
        
        public float getX() { return x; }
        public void setX(float x) { this.x = x; }
        
        public float getY() { return y; }
        public void setY(float y) { this.y = y; }
        
        public float getWidth() { return width; }
        public void setWidth(float width) { this.width = width; }
        
        public float getHeight() { return height; }
        public void setHeight(float height) { this.height = height; }
        
        public String getFontName() { return fontName; }
        public void setFontName(String fontName) { this.fontName = fontName; }
        
        public float getFontSize() { return fontSize; }
        public void setFontSize(float fontSize) { this.fontSize = fontSize; }
    }
    
    /**
     * 条款匹配结果
     */
    public static class ClauseTextMatch {
        private String clauseName;          // 条款名称
        private String originalText;        // 原始条款文本
        private String matchedText;         // 匹配到的PDF文本
        private List<TextCoordinate> coordinates;  // 匹配文本的坐标列表
        private double matchConfidence;     // 匹配置信度 (0.0 - 1.0)
        private String matchType;           // 匹配类型：exact, fuzzy, partial
        private List<String> riskNames;     // 该条款包含的风险点名称列表
        
        public ClauseTextMatch() {}
        
        public ClauseTextMatch(String clauseName, String originalText, String matchedText, 
                              List<TextCoordinate> coordinates, double matchConfidence, String matchType) {
            this.clauseName = clauseName;
            this.originalText = originalText;
            this.matchedText = matchedText;
            this.coordinates = coordinates;
            this.matchConfidence = matchConfidence;
            this.matchType = matchType;
            this.riskNames = new ArrayList<>();
        }
        
        // Getter and Setter methods
        public String getClauseName() { return clauseName; }
        public void setClauseName(String clauseName) { this.clauseName = clauseName; }
        
        public String getOriginalText() { return originalText; }
        public void setOriginalText(String originalText) { this.originalText = originalText; }
        
        public String getMatchedText() { return matchedText; }
        public void setMatchedText(String matchedText) { this.matchedText = matchedText; }
        
        public List<TextCoordinate> getCoordinates() { return coordinates; }
        public void setCoordinates(List<TextCoordinate> coordinates) { this.coordinates = coordinates; }
        
        public double getMatchConfidence() { return matchConfidence; }
        public void setMatchConfidence(double matchConfidence) { this.matchConfidence = matchConfidence; }
        
        public String getMatchType() { return matchType; }
        public void setMatchType(String matchType) { this.matchType = matchType; }
        
        public List<String> getRiskNames() { return riskNames; }
        public void setRiskNames(List<String> riskNames) { this.riskNames = riskNames; }
    }
    
    /**
     * 废弃：风险文本匹配结果（保留兼容性）
     * @deprecated 使用ClauseTextMatch替代
     */
    @Deprecated
    public static class RiskTextMatch extends ClauseTextMatch {
        public RiskTextMatch() {
            super();
        }
        
        public RiskTextMatch(String originalText, String matchedText, 
                           List<TextCoordinate> coordinates, double matchConfidence, String matchType) {
            super("", originalText, matchedText, coordinates, matchConfidence, matchType);
        }
    }
    
    /**
     * 从PDF文件中提取所有文本的坐标信息
     * 
     * @param contractFile 合同文件
     * @return 按页码分组的文本坐标列表
     * @throws Exception 解析异常
     */
    Map<Integer, List<TextCoordinate>> extractTextCoordinates(ContractFile contractFile) throws Exception;
    
    /**
     * 在PDF中查找指定文本的坐标位置
     * 
     * @param contractFile 合同文件
     * @param searchText 要查找的文本
     * @param matchType 匹配类型：exact(精确匹配)、fuzzy(模糊匹配)、partial(部分匹配)
     * @return 匹配结果列表
     * @throws Exception 查找异常
     */
    List<RiskTextMatch> findTextCoordinates(ContractFile contractFile, String searchText, String matchType) throws Exception;
    
    /**
     * 批量查找条款文本的坐标位置
     * 
     * @param contractFile 合同文件
     * @param clauseTexts 条款文本列表
     * @return 条款文本匹配结果映射 (条款名称 -> 匹配结果列表)
     * @throws Exception 查找异常
     */
    Map<String, List<ClauseTextMatch>> batchFindClauseTextCoordinates(ContractFile contractFile, Map<String, String> clauseTexts) throws Exception;
    
    /**
     * 根据审查结果JSON解析条款坐标
     * 
     * @param contractFile 合同文件
     * @param reviewResultJson 审查结果JSON
     * @return 完整的条款坐标解析结果
     * @throws Exception 解析异常
     */
    Map<String, Object> parseClauseCoordinatesFromReviewResult(ContractFile contractFile, String reviewResultJson) throws Exception;
    
    /**
     * @deprecated 使用batchFindClauseTextCoordinates替代
     */
    @Deprecated
    Map<String, List<RiskTextMatch>> batchFindRiskTextCoordinates(ContractFile contractFile, List<String> riskTexts) throws Exception;
    
    /**
     * @deprecated 使用parseClauseCoordinatesFromReviewResult替代
     */
    @Deprecated
    Map<String, Object> parseRiskCoordinatesFromReviewResult(ContractFile contractFile, String reviewResultJson) throws Exception;
    
    /**
     * 清理缓存的坐标数据
     * 
     * @param fileMd5 文件MD5值
     */
    void clearCoordinateCache(String fileMd5);
    
    /**
     * 检查PDF文件是否支持坐标提取
     * 
     * @param contractFile 合同文件
     * @return 是否支持坐标提取
     */
    boolean isSupportedForCoordinateExtraction(ContractFile contractFile);
}