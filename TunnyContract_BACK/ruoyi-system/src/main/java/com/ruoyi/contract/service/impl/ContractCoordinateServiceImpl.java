package com.ruoyi.contract.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractCoordinateService;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.pdfbox.text.TextPosition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.geom.Rectangle2D;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 合同文档坐标解析服务实现类
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Service
public class ContractCoordinateServiceImpl implements IContractCoordinateService
{
    private static final Logger logger = LoggerFactory.getLogger(ContractCoordinateServiceImpl.class);
    
    @Value("${file.temp-dir:/tmp/contract-render}")
    private String tempDir;
    
    // 坐标缓存，key为文件MD5，value为坐标数据
    private final Map<String, Map<Integer, List<TextCoordinate>>> coordinateCache = new ConcurrentHashMap<>();
    
    /**
     * 自定义PDF文本提取器，用于获取文本坐标信息
     */
    private static class CoordinateTextStripper extends PDFTextStripper {
        private Map<Integer, List<TextCoordinate>> pageTextCoordinates = new HashMap<>();
        private int currentPage = 1;
        
        public CoordinateTextStripper() throws IOException {
            super();
            setSortByPosition(true);
        }
        
        @Override
        protected void startPage(org.apache.pdfbox.pdmodel.PDPage page) throws IOException {
            super.startPage(page);
            currentPage = getCurrentPageNo();
            pageTextCoordinates.putIfAbsent(currentPage, new ArrayList<>());
        }
        
        @Override
        protected void writeString(String string, List<TextPosition> textPositions) throws IOException {
            super.writeString(string, textPositions);
            
            if (textPositions != null && !textPositions.isEmpty()) {
                // 处理每个文本位置
                for (TextPosition textPosition : textPositions) {
                    TextCoordinate coord = new TextCoordinate();
                    coord.setText(textPosition.getUnicode());
                    coord.setPageNumber(currentPage);
                    coord.setX(textPosition.getXDirAdj());
                    coord.setY(textPosition.getYDirAdj());
                    coord.setWidth(textPosition.getWidthDirAdj());
                    coord.setHeight(textPosition.getHeightDir());
                    coord.setFontName(textPosition.getFont().getName());
                    coord.setFontSize(textPosition.getFontSizeInPt());
                    
                    pageTextCoordinates.get(currentPage).add(coord);
                }
            }
        }
        
        public Map<Integer, List<TextCoordinate>> getPageTextCoordinates() {
            return pageTextCoordinates;
        }
    }
    
    /**
     * 从PDF文件中提取所有文本的坐标信息
     */
    @Override
    public Map<Integer, List<TextCoordinate>> extractTextCoordinates(ContractFile contractFile) throws Exception
    {
        String fileMd5 = contractFile.getFileMd5();
        
        // 检查缓存
        if (coordinateCache.containsKey(fileMd5)) {
            logger.debug("使用缓存的坐标数据，文件MD5: {}", fileMd5);
            return coordinateCache.get(fileMd5);
        }
        
        logger.info("开始提取PDF文本坐标，文件: {}", contractFile.getFileName());
        
        try (PDDocument document = loadPdfDocument(contractFile)) {
            CoordinateTextStripper stripper = new CoordinateTextStripper();
            
            // 提取所有页的文本坐标
            stripper.getText(document);
            Map<Integer, List<TextCoordinate>> coordinates = stripper.getPageTextCoordinates();
            
            // 缓存结果
            coordinateCache.put(fileMd5, coordinates);
            
            logger.info("PDF文本坐标提取完成，共{}页，文件MD5: {}", coordinates.size(), fileMd5);
            return coordinates;
            
        } catch (Exception e) {
            logger.error("提取PDF文本坐标失败，文件: {}", contractFile.getFileName(), e);
            throw new Exception("PDF文本坐标提取失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 在PDF中查找指定文本的坐标位置
     */
    @Override
    public List<RiskTextMatch> findTextCoordinates(ContractFile contractFile, String searchText, String matchType) throws Exception
    {
        if (searchText == null || searchText.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        Map<Integer, List<TextCoordinate>> allCoordinates = extractTextCoordinates(contractFile);
        List<RiskTextMatch> matches = new ArrayList<>();
        
        // 根据匹配类型采用不同策略
        switch (matchType.toLowerCase()) {
            case "exact":
                matches.addAll(findExactMatches(searchText, allCoordinates));
                break;
            case "fuzzy":
                matches.addAll(findFuzzyMatches(searchText, allCoordinates));
                break;
            case "partial":
                matches.addAll(findPartialMatches(searchText, allCoordinates));
                break;
            default:
                // 默认尝试精确匹配，如果没找到再尝试模糊匹配
                matches.addAll(findExactMatches(searchText, allCoordinates));
                if (matches.isEmpty()) {
                    matches.addAll(findFuzzyMatches(searchText, allCoordinates));
                }
                break;
        }
        
        logger.debug("文本匹配完成，搜索文本: [{}], 匹配类型: {}, 找到{}个匹配项", 
                    searchText, matchType, matches.size());
        
        return matches;
    }
    
    /**
     * 批量查找条款文本的坐标位置（新实现）
     */
    @Override
    public Map<String, List<ClauseTextMatch>> batchFindClauseTextCoordinates(ContractFile contractFile, Map<String, String> clauseTexts) throws Exception
    {
        Map<String, List<ClauseTextMatch>> result = new HashMap<>();
        
        for (Map.Entry<String, String> entry : clauseTexts.entrySet()) {
            String clauseName = entry.getKey();
            String clauseContent = entry.getValue();
            
            if (clauseContent != null && !clauseContent.trim().isEmpty()) {
                // 使用现有的findTextCoordinates方法查找坐标
                List<RiskTextMatch> matches = findTextCoordinates(contractFile, clauseContent.trim(), "auto");
                
                // 转换为ClauseTextMatch格式
                List<ClauseTextMatch> clauseMatches = new ArrayList<>();
                for (RiskTextMatch match : matches) {
                    ClauseTextMatch clauseMatch = new ClauseTextMatch();
                    clauseMatch.setClauseName(clauseName);
                    clauseMatch.setOriginalText(match.getOriginalText());
                    clauseMatch.setMatchedText(match.getMatchedText());
                    clauseMatch.setCoordinates(match.getCoordinates());
                    clauseMatch.setMatchConfidence(match.getMatchConfidence());
                    clauseMatch.setMatchType(match.getMatchType());
                    clauseMatch.setRiskNames(new ArrayList<>()); // 稍后填充
                    clauseMatches.add(clauseMatch);
                }
                
                result.put(clauseName, clauseMatches);
                
                logger.debug("条款[{}]匹配完成，找到{}个匹配项", clauseName, clauseMatches.size());
            }
        }
        
        logger.info("批量条款文本匹配完成，处理{}个条款", clauseTexts.size());
        return result;
    }
    
    /**
     * @deprecated 兼容旧方法
     */
    @Override
    @Deprecated
    public Map<String, List<RiskTextMatch>> batchFindRiskTextCoordinates(ContractFile contractFile, List<String> riskTexts) throws Exception
    {
        Map<String, List<RiskTextMatch>> result = new HashMap<>();
        
        for (String riskText : riskTexts) {
            if (riskText != null && !riskText.trim().isEmpty()) {
                List<RiskTextMatch> matches = findTextCoordinates(contractFile, riskText.trim(), "auto");
                result.put(riskText, matches);
            }
        }
        
        logger.info("批量文本匹配完成，处理{}个风险文本", riskTexts.size());
        return result;
    }
    
    /**
     * 根据审查结果JSON解析条款坐标（新实现）
     */
    @Override
    public Map<String, Object> parseClauseCoordinatesFromReviewResult(ContractFile contractFile, String reviewResultJson) throws Exception
    {
        if (reviewResultJson == null || reviewResultJson.trim().isEmpty()) {
            throw new IllegalArgumentException("审查结果JSON不能为空");
        }
        
        Map<String, Object> result = new HashMap<>();
        Map<String, String> clauseTexts = new HashMap<>();
        Map<String, List<String>> clauseRiskNames = new HashMap<>();
        
        try {
            // 解析审查结果JSON
            JSONObject reviewData = JSON.parseObject(reviewResultJson);
            JSONArray dataArray = reviewData.getJSONArray("data");
            
            if (dataArray == null) {
                logger.warn("审查结果JSON中未找到data数组");
                return result;
            }
            
            // 提取所有条款文本内容
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject clause = dataArray.getJSONObject(i);
                String clauseName = clause.getString("clauseName");
                String clauseContent = clause.getString("clauseContent");
                
                if (clauseName != null && clauseContent != null && !clauseContent.trim().isEmpty()) {
                    clauseTexts.put(clauseName, clauseContent.trim());
                    
                    // 收集该条款下的风险点名称
                    List<String> riskNames = new ArrayList<>();
                    JSONArray riskPoints = clause.getJSONArray("riskPoints");
                    if (riskPoints != null) {
                        for (int j = 0; j < riskPoints.size(); j++) {
                            JSONObject riskPoint = riskPoints.getJSONObject(j);
                            String riskName = riskPoint.getString("riskName");
                            if (riskName != null && !riskName.trim().isEmpty()) {
                                riskNames.add(riskName.trim());
                            }
                        }
                    }
                    clauseRiskNames.put(clauseName, riskNames);
                }
            }
            
            logger.info("从审查结果中提取到{}个条款文本", clauseTexts.size());
            
            // 批量查找条款文本坐标
            Map<String, List<ClauseTextMatch>> coordinateMatches = batchFindClauseTextCoordinates(contractFile, clauseTexts);
            
            // 为每个匹配结果添加风险点信息
            for (Map.Entry<String, List<ClauseTextMatch>> entry : coordinateMatches.entrySet()) {
                String clauseName = entry.getKey();
                List<String> riskNames = clauseRiskNames.get(clauseName);
                for (ClauseTextMatch match : entry.getValue()) {
                    match.setRiskNames(riskNames != null ? riskNames : new ArrayList<>());
                }
            }
            
            // 构建返回结果
            result.put("contractFileId", contractFile.getId());
            result.put("contractFileName", contractFile.getFileName());
            result.put("totalClauses", clauseTexts.size());
            result.put("clauseMatches", coordinateMatches);
            
            // 统计匹配情况
            int totalMatches = coordinateMatches.values().stream()
                    .mapToInt(List::size)
                    .sum();
            int matchedClauses = (int) coordinateMatches.values().stream()
                    .filter(list -> !list.isEmpty())
                    .count();
            int totalRiskPoints = clauseRiskNames.values().stream()
                    .mapToInt(List::size)
                    .sum();
                    
            result.put("totalMatches", totalMatches);
            result.put("matchedClauses", matchedClauses);
            result.put("unmatchedClauses", clauseTexts.size() - matchedClauses);
            result.put("totalRiskPoints", totalRiskPoints);
            
            logger.info("条款坐标解析完成，共{}个条款，找到{}个匹配项，{}个条款已匹配，包含{}个风险点", 
                       clauseTexts.size(), totalMatches, matchedClauses, totalRiskPoints);
            
        } catch (Exception e) {
            logger.error("解析审查结果JSON失败", e);
            throw new Exception("解析条款坐标失败: " + e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * @deprecated 兼容旧方法，重定向到新方法
     */
    @Override
    @Deprecated
    public Map<String, Object> parseRiskCoordinatesFromReviewResult(ContractFile contractFile, String reviewResultJson) throws Exception
    {
        logger.warn("使用已废弃的parseRiskCoordinatesFromReviewResult方法，建议使用parseClauseCoordinatesFromReviewResult");
        return parseClauseCoordinatesFromReviewResult(contractFile, reviewResultJson);
    }
    
    /**
     * 精确匹配
     */
    private List<RiskTextMatch> findExactMatches(String searchText, Map<Integer, List<TextCoordinate>> allCoordinates) {
        List<RiskTextMatch> matches = new ArrayList<>();
        
        for (Map.Entry<Integer, List<TextCoordinate>> pageEntry : allCoordinates.entrySet()) {
            List<TextCoordinate> pageCoords = pageEntry.getValue();
            
            // 构建页面文本
            StringBuilder pageText = new StringBuilder();
            List<TextCoordinate> coordsByIndex = new ArrayList<>();
            
            for (TextCoordinate coord : pageCoords) {
                int startIndex = pageText.length();
                pageText.append(coord.getText());
                
                // 记录字符索引与坐标的对应关系
                for (int i = 0; i < coord.getText().length(); i++) {
                    coordsByIndex.add(coord);
                }
            }
            
            // 精确匹配搜索文本
            String pageTextStr = pageText.toString();
            int index = pageTextStr.indexOf(searchText);
            
            while (index != -1) {
                List<TextCoordinate> matchCoords = extractCoordinatesForRange(coordsByIndex, index, index + searchText.length());
                
                if (!matchCoords.isEmpty()) {
                    RiskTextMatch match = new RiskTextMatch(searchText, searchText, matchCoords, 1.0, "exact");
                    matches.add(match);
                }
                
                index = pageTextStr.indexOf(searchText, index + 1);
            }
        }
        
        return matches;
    }
    
    /**
     * 模糊匹配（支持正则表达式和相似度匹配）
     */
    private List<RiskTextMatch> findFuzzyMatches(String searchText, Map<Integer, List<TextCoordinate>> allCoordinates) {
        List<RiskTextMatch> matches = new ArrayList<>();
        
        // 创建模糊匹配的正则表达式（允许空白字符变化）
        String fuzzyPattern = searchText.replaceAll("\\s+", "\\\\s*");
        Pattern pattern = Pattern.compile(fuzzyPattern, Pattern.CASE_INSENSITIVE);
        
        for (Map.Entry<Integer, List<TextCoordinate>> pageEntry : allCoordinates.entrySet()) {
            List<TextCoordinate> pageCoords = pageEntry.getValue();
            
            StringBuilder pageText = new StringBuilder();
            List<TextCoordinate> coordsByIndex = new ArrayList<>();
            
            for (TextCoordinate coord : pageCoords) {
                pageText.append(coord.getText());
                for (int i = 0; i < coord.getText().length(); i++) {
                    coordsByIndex.add(coord);
                }
            }
            
            Matcher matcher = pattern.matcher(pageText.toString());
            while (matcher.find()) {
                String matchedText = matcher.group();
                int startIndex = matcher.start();
                int endIndex = matcher.end();
                
                List<TextCoordinate> matchCoords = extractCoordinatesForRange(coordsByIndex, startIndex, endIndex);
                
                if (!matchCoords.isEmpty()) {
                    double confidence = calculateSimilarity(searchText, matchedText);
                    RiskTextMatch match = new RiskTextMatch(searchText, matchedText, matchCoords, confidence, "fuzzy");
                    matches.add(match);
                }
            }
        }
        
        return matches;
    }
    
    /**
     * 部分匹配
     */
    private List<RiskTextMatch> findPartialMatches(String searchText, Map<Integer, List<TextCoordinate>> allCoordinates) {
        List<RiskTextMatch> matches = new ArrayList<>();
        
        // 将搜索文本分词，查找部分匹配
        String[] searchWords = searchText.split("\\s+");
        
        for (String word : searchWords) {
            if (word.length() >= 2) { // 忽略太短的词
                matches.addAll(findExactMatches(word, allCoordinates));
            }
        }
        
        // 标记为部分匹配
        for (RiskTextMatch match : matches) {
            match.setMatchType("partial");
            match.setMatchConfidence(0.7); // 部分匹配的置信度较低
        }
        
        return matches;
    }
    
    /**
     * 提取指定范围的坐标信息
     */
    private List<TextCoordinate> extractCoordinatesForRange(List<TextCoordinate> coordsByIndex, int startIndex, int endIndex) {
        List<TextCoordinate> result = new ArrayList<>();
        
        for (int i = startIndex; i < endIndex && i < coordsByIndex.size(); i++) {
            TextCoordinate coord = coordsByIndex.get(i);
            if (coord != null) {
                result.add(coord);
            }
        }
        
        return result;
    }
    
    /**
     * 计算文本相似度
     */
    private double calculateSimilarity(String text1, String text2) {
        if (text1 == null || text2 == null) {
            return 0.0;
        }
        
        if (text1.equals(text2)) {
            return 1.0;
        }
        
        // 简单的Levenshtein距离相似度计算
        int distance = levenshteinDistance(text1.toLowerCase(), text2.toLowerCase());
        int maxLength = Math.max(text1.length(), text2.length());
        
        return maxLength == 0 ? 1.0 : 1.0 - (double) distance / maxLength;
    }
    
    /**
     * Levenshtein距离计算
     */
    private int levenshteinDistance(String str1, String str2) {
        int[][] dp = new int[str1.length() + 1][str2.length() + 1];
        
        for (int i = 0; i <= str1.length(); i++) {
            for (int j = 0; j <= str2.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else {
                    dp[i][j] = Math.min(
                        dp[i - 1][j] + 1,
                        Math.min(
                            dp[i][j - 1] + 1,
                            dp[i - 1][j - 1] + (str1.charAt(i - 1) == str2.charAt(j - 1) ? 0 : 1)
                        )
                    );
                }
            }
        }
        
        return dp[str1.length()][str2.length()];
    }
    
    /**
     * 加载PDF文档
     */
    private PDDocument loadPdfDocument(ContractFile contractFile) throws IOException {
        String filePath = contractFile.getFilePath();
        
        if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
            // 网络URL，下载到临时文件
            return loadPdfFromUrl(filePath, contractFile.getFileName());
        } else {
            // 本地文件
            return PDDocument.load(new File(filePath));
        }
    }
    
    /**
     * 从URL加载PDF文档
     */
    private PDDocument loadPdfFromUrl(String urlStr, String fileName) throws IOException {
        try {
            URL url = new URL(urlStr);
            Path tempFilePath = Paths.get(tempDir, System.currentTimeMillis() + "_" + fileName);
            
            // 确保临时目录存在
            Files.createDirectories(tempFilePath.getParent());
            
            // 下载文件
            try (InputStream inputStream = url.openStream()) {
                Files.copy(inputStream, tempFilePath);
            }
            
            // 加载PDF文档
            return PDDocument.load(tempFilePath.toFile());
            
        } catch (Exception e) {
            logger.error("从URL加载PDF失败: {}", urlStr, e);
            throw new IOException("无法从URL加载PDF: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void clearCoordinateCache(String fileMd5) {
        coordinateCache.remove(fileMd5);
        logger.debug("清理坐标缓存，文件MD5: {}", fileMd5);
    }
    
    @Override
    public boolean isSupportedForCoordinateExtraction(ContractFile contractFile) {
        return contractFile != null && 
               "pdf".equalsIgnoreCase(contractFile.getFileType()) &&
               contractFile.getFilePath() != null && 
               !contractFile.getFilePath().trim().isEmpty();
    }
}