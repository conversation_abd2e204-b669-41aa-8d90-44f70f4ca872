package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractReviewTaskMapper;
import com.ruoyi.contract.domain.ContractReviewTask;
import com.ruoyi.contract.domain.ContractReviewStrategy;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.mapper.ContractReviewStrategyMapper;
import com.ruoyi.contract.service.IContractReviewTaskService;
import com.ruoyi.contract.service.IContractFileService;

/**
 * 合同审查任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractReviewTaskServiceImpl implements IContractReviewTaskService 
{
    @Autowired
    private ContractReviewTaskMapper contractReviewTaskMapper;

    @Autowired
    private ContractReviewStrategyMapper contractReviewStrategyMapper;

    @Autowired
    private IContractFileService contractFileService;

    /**
     * 查询合同审查任务
     * 
     * @param id 合同审查任务主键
     * @return 合同审查任务
     */
    @Override
    public ContractReviewTask selectContractReviewTaskById(Long id)
    {
        return contractReviewTaskMapper.selectContractReviewTaskById(id);
    }

    /**
     * 查询合同审查任务列表
     * 同时查询每个任务的文件解析状态
     *
     * @param contractReviewTask 合同审查任务
     * @return 合同审查任务
     */
    @Override
    public List<ContractReviewTask> selectContractReviewTaskList(ContractReviewTask contractReviewTask)
    {
        List<ContractReviewTask> taskList = contractReviewTaskMapper.selectContractReviewTaskList(contractReviewTask);

        // 为每个任务查询文件状态
        for (ContractReviewTask task : taskList)
        {
            if (task.getId() != null)
            {
                enrichTaskWithFileStatus(task);
            }
        }

        return taskList;
    }

    /**
     * 为任务补充文件状态信息
     *
     * @param task 合同审查任务
     */
    private void enrichTaskWithFileStatus(ContractReviewTask task)
    {
        try
        {
            ContractFile queryParam = new ContractFile();
            queryParam.setTaskId(task.getId());
            List<ContractFile> files = contractFileService.selectContractFileList(queryParam);

            // 计算文件状态统计
            int totalFiles = files.size();
            long parsedFiles = files.stream().filter(f -> "2".equals(f.getParseStatus())).count();
            long failedFiles = files.stream().filter(f -> "3".equals(f.getParseStatus())).count();
            long parsingFiles = files.stream().filter(f -> "1".equals(f.getParseStatus())).count();
            long pendingFiles = totalFiles - parsedFiles - failedFiles - parsingFiles;

            // 设置文件状态字段
            task.setTotalFiles(totalFiles);
            task.setParsedFiles((int) parsedFiles);
            task.setFailedFiles((int) failedFiles);
            task.setParsingFiles((int) parsingFiles);
            task.setPendingFiles((int) pendingFiles);

            // 计算汇总状态
            String fileParseStatus = calculateFileParseStatus(totalFiles, parsedFiles, failedFiles, parsingFiles);
            task.setFileParseStatus(fileParseStatus);
        }
        catch (Exception e)
        {
            // 如果查询文件状态失败，设置默认值
            task.setTotalFiles(0);
            task.setParsedFiles(0);
            task.setFailedFiles(0);
            task.setParsingFiles(0);
            task.setPendingFiles(0);
            task.setFileParseStatus("0");
        }
    }

    /**
     * 计算文件解析的汇总状态
     *
     * @param totalFiles 总文件数
     * @param parsedFiles 已解析文件数
     * @param failedFiles 解析失败文件数
     * @param parsingFiles 解析中文件数
     * @return 汇总状态 (0-未开始，1-解析中，2-全部成功，3-部分失败)
     */
    private String calculateFileParseStatus(long totalFiles, long parsedFiles, long failedFiles, long parsingFiles)
    {
        if (totalFiles == 0)
        {
            return "0"; // 无文件
        }

        if (parsedFiles == totalFiles)
        {
            return "2"; // 全部解析成功
        }

        if (failedFiles > 0)
        {
            return "3"; // 有解析失败
        }

        if (parsingFiles > 0)
        {
            return "1"; // 解析中
        }

        return "0"; // 未开始解析
    }

    /**
     * 新增合同审查任务
     *
     * @param contractReviewTask 合同审查任务
     * @return 结果
     */
    @Override
    public int insertContractReviewTask(ContractReviewTask contractReviewTask)
    {
        // 自动生成任务编号
        if (contractReviewTask.getTaskNo() == null || contractReviewTask.getTaskNo().isEmpty()) {
            contractReviewTask.setTaskNo(generateTaskNo());
        }
        contractReviewTask.setCreateTime(DateUtils.getNowDate());
        contractReviewTask.setCreateBy(SecurityUtils.getUsername());
        return contractReviewTaskMapper.insertContractReviewTask(contractReviewTask);
    }

    /**
     * 生成任务编号
     * 格式：TASK + YYYYMMDD + 4位序号
     * 例如：TASK202509050001
     *
     * @return 任务编号
     */
    private String generateTaskNo() {
        String dateStr = DateUtils.dateTimeNow("yyyyMMdd");
        String prefix = "TASK" + dateStr;

        // 简化实现：使用时间戳确保唯一性
        long timestamp = System.currentTimeMillis();
        String sequence = String.valueOf(timestamp % 10000);
        String taskNo = prefix + String.format("%04d", Integer.parseInt(sequence));

        // 如果生成的编号已存在，则递增序号
        int counter = 1;
        String originalTaskNo = taskNo;
        while (isTaskNoExists(taskNo)) {
            taskNo = prefix + String.format("%04d", (Integer.parseInt(sequence) + counter) % 10000);
            counter++;
            if (counter > 100) { // 防止无限循环
                taskNo = originalTaskNo + System.nanoTime() % 1000;
                break;
            }
        }

        return taskNo;
    }

    /**
     * 检查任务编号是否已存在
     *
     * @param taskNo 任务编号
     * @return 是否存在
     */
    private boolean isTaskNoExists(String taskNo) {
        ContractReviewTask queryTask = new ContractReviewTask();
        queryTask.setTaskNo(taskNo);
        List<ContractReviewTask> existTasks = contractReviewTaskMapper.selectContractReviewTaskList(queryTask);
        return !existTasks.isEmpty();
    }

    /**
     * 修改合同审查任务
     * 
     * @param contractReviewTask 合同审查任务
     * @return 结果
     */
    @Override
    public int updateContractReviewTask(ContractReviewTask contractReviewTask)
    {
        contractReviewTask.setUpdateTime(DateUtils.getNowDate());
        contractReviewTask.setUpdateBy(SecurityUtils.getUsername());
        return contractReviewTaskMapper.updateContractReviewTask(contractReviewTask);
    }

    /**
     * 批量删除合同审查任务
     * 
     * @param ids 需要删除的合同审查任务主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewTaskByIds(Long[] ids)
    {
        return contractReviewTaskMapper.deleteContractReviewTaskByIds(ids);
    }

    /**
     * 删除合同审查任务信息
     *
     * @param id 合同审查任务主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewTaskById(Long id)
    {
        return contractReviewTaskMapper.deleteContractReviewTaskById(id);
    }

    /**
     * 根据合同分类和审查立场获取可用策略列表
     *
     * @param categoryId 合同分类ID
     * @param reviewPosition 审查立场
     * @return 策略列表
     */
    @Override
    public List<ContractReviewStrategy> getAvailableStrategies(Long categoryId, String reviewPosition)
    {
        ContractReviewStrategy strategy = new ContractReviewStrategy();
        strategy.setCategoryId(categoryId);
        strategy.setReviewPosition(reviewPosition);
        strategy.setStrategyStatus("1"); // 只查询已发布的策略
        return contractReviewStrategyMapper.selectContractReviewStrategyList(strategy);
    }
}
