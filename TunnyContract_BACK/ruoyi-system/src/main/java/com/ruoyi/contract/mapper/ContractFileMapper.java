package com.ruoyi.contract.mapper;

import java.util.List;
import com.ruoyi.contract.domain.ContractFile;

/**
 * 合同文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface ContractFileMapper 
{
    /**
     * 查询合同文件
     * 
     * @param id 合同文件主键
     * @return 合同文件
     */
    public ContractFile selectContractFileById(Long id);

    /**
     * 查询合同文件列表
     * 
     * @param contractFile 合同文件
     * @return 合同文件集合
     */
    public List<ContractFile> selectContractFileList(ContractFile contractFile);

    /**
     * 新增合同文件
     * 
     * @param contractFile 合同文件
     * @return 结果
     */
    public int insertContractFile(ContractFile contractFile);

    /**
     * 修改合同文件
     * 
     * @param contractFile 合同文件
     * @return 结果
     */
    public int updateContractFile(ContractFile contractFile);

    /**
     * 删除合同文件
     * 
     * @param id 合同文件主键
     * @return 结果
     */
    public int deleteContractFileById(Long id);

    /**
     * 批量删除合同文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractFileByIds(Long[] ids);

    /**
     * 根据MD5值查询合同文件
     * 
     * @param fileMd5 文件MD5值
     * @return 合同文件
     */
    public ContractFile selectContractFileByMd5(String fileMd5);
}
