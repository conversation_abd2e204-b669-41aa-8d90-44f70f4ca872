package com.ruoyi.contract.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同风险识别结果对象 contract_risk_result
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractRiskResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 风险结果ID */
    private Long id;

    /** 关联审查结果ID */
    @Excel(name = "关联审查结果ID")
    private Long resultId;

    /** 关联风险点ID */
    @Excel(name = "关联风险点ID")
    private Long riskPointId;

    /** 匹配到的文本内容 */
    @Excel(name = "匹配到的文本内容")
    private String matchedText;

    /** 所在页码 */
    @Excel(name = "所在页码")
    private Long pageNumber;

    /** 文本开始位置 */
    @Excel(name = "文本开始位置")
    private Long positionStart;

    /** 文本结束位置 */
    @Excel(name = "文本结束位置")
    private Long positionEnd;

    /** 匹配置信度得分 */
    @Excel(name = "匹配置信度得分")
    private BigDecimal confidenceScore;

    /** 风险等级(1-重大风险，2-一般风险) */
    @Excel(name = "风险等级(1-重大风险，2-一般风险)")
    private String riskLevel;

    /** 修改建议 */
    @Excel(name = "修改建议")
    private String suggestion;

    /** 是否已确认(1-已确认，0-待确认) */
    @Excel(name = "是否已确认(1-已确认，0-待确认)")
    private String isConfirmed;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date confirmTime;

    /** 确认用户 */
    @Excel(name = "确认用户")
    private String confirmUser;

    /** 0-正常，2-删除 */
    private String delFlag;

    /** 保留字段1 */
    @Excel(name = "保留字段1")
    private String attr1;

    /** 保留字段2 */
    @Excel(name = "保留字段2")
    private String attr2;

    /** 保留字段3 */
    @Excel(name = "保留字段3")
    private String attr3;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setResultId(Long resultId) 
    {
        this.resultId = resultId;
    }

    public Long getResultId() 
    {
        return resultId;
    }

    public void setRiskPointId(Long riskPointId) 
    {
        this.riskPointId = riskPointId;
    }

    public Long getRiskPointId() 
    {
        return riskPointId;
    }

    public void setMatchedText(String matchedText) 
    {
        this.matchedText = matchedText;
    }

    public String getMatchedText() 
    {
        return matchedText;
    }

    public void setPageNumber(Long pageNumber) 
    {
        this.pageNumber = pageNumber;
    }

    public Long getPageNumber() 
    {
        return pageNumber;
    }

    public void setPositionStart(Long positionStart) 
    {
        this.positionStart = positionStart;
    }

    public Long getPositionStart() 
    {
        return positionStart;
    }

    public void setPositionEnd(Long positionEnd) 
    {
        this.positionEnd = positionEnd;
    }

    public Long getPositionEnd() 
    {
        return positionEnd;
    }

    public void setConfidenceScore(BigDecimal confidenceScore) 
    {
        this.confidenceScore = confidenceScore;
    }

    public BigDecimal getConfidenceScore() 
    {
        return confidenceScore;
    }

    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    public void setSuggestion(String suggestion) 
    {
        this.suggestion = suggestion;
    }

    public String getSuggestion() 
    {
        return suggestion;
    }

    public void setIsConfirmed(String isConfirmed) 
    {
        this.isConfirmed = isConfirmed;
    }

    public String getIsConfirmed() 
    {
        return isConfirmed;
    }

    public void setConfirmTime(Date confirmTime) 
    {
        this.confirmTime = confirmTime;
    }

    public Date getConfirmTime() 
    {
        return confirmTime;
    }

    public void setConfirmUser(String confirmUser) 
    {
        this.confirmUser = confirmUser;
    }

    public String getConfirmUser() 
    {
        return confirmUser;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3() 
    {
        return attr3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("resultId", getResultId())
            .append("riskPointId", getRiskPointId())
            .append("matchedText", getMatchedText())
            .append("pageNumber", getPageNumber())
            .append("positionStart", getPositionStart())
            .append("positionEnd", getPositionEnd())
            .append("confidenceScore", getConfidenceScore())
            .append("riskLevel", getRiskLevel())
            .append("suggestion", getSuggestion())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmTime", getConfirmTime())
            .append("confirmUser", getConfirmUser())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .toString();
    }
}
