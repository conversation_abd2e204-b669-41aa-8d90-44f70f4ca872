package com.ruoyi.contract.service.impl;

import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractDocumentRenderService;
import com.ruoyi.contract.service.IContractFileService;
import com.ruoyi.common.config.MinioConfig;
import com.ruoyi.common.config.ContractConversionConfig;
import com.ruoyi.common.utils.file.MinioUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.awt.GraphicsEnvironment;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

// Apache POI imports
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;

// PDFBox imports
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;

/**
 * 合同文档渲染服务实现类
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Service
public class ContractDocumentRenderServiceImpl implements IContractDocumentRenderService {
    
    private static final Logger logger = LoggerFactory.getLogger(ContractDocumentRenderServiceImpl.class);
    
    @Autowired
    private IContractFileService contractFileService;
    
    // 支持的预览文件类型
    private static final List<String> SUPPORTED_PREVIEW_TYPES = Arrays.asList("pdf", "doc", "docx");
    
    /**
     * 将Word文档转换为PDF
     * 
     * @param contractFile 合同文件信息
     * @return PDF文件的访问URL
     * @throws Exception 转换异常
     */
    @Override
    public String convertWordToPdf(ContractFile contractFile) throws Exception
    {
        String fileName = contractFile.getFileName();
        String fileMd5 = contractFile.getFileMd5();
        
        if (fileName == null || fileMd5 == null) {
            throw new IllegalArgumentException("文件名或MD5值不能为空");
        }
        
        // 1. 检查缓存
        String cachedPdfPath = checkPdfCache(fileMd5);
        if (cachedPdfPath != null) {
            logger.info("找到PDF缓存文件，文件MD5: {}", fileMd5);
            return cachedPdfPath;
        }
        
        // 1. 确保目录存在
        ensureDirectoryExists(ContractConversionConfig.getTempDir());
        ensureDirectoryExists(ContractConversionConfig.getPdfCacheDir());
        
        // 3. 下载Word文档到临时目录
        String wordFilePath = downloadToTemp(contractFile.getFilePath(), fileName);
        
        try {
            // 4. 转换Word为PDF
            String pdfFileName = fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";
            String pdfFilePath = Paths.get(ContractConversionConfig.getPdfCacheDir(), fileMd5 + ".pdf").toString();
            
            boolean conversionSuccess = performWordToPdfConversion(wordFilePath, pdfFilePath);
            
            if (conversionSuccess) {
                // 5. 上传PDF到MinIO并返回URL
                String pdfUrl = convertWordToPdfAndUploadToMinIO(contractFile);
                logger.info("Word转PDF成功，文件MD5: {}, PDF路径: {}", fileMd5, pdfUrl);
                return pdfUrl;
            } else {
                throw new RuntimeException("Word转PDF转换失败");
            }
            
        } finally {
            // 6. 清理临时Word文件
            cleanupTempFile(wordFilePath);
        }
    }
    
    /**
     * 检查PDF转换缓存是否存在
     * 
     * @param fileMd5 文件MD5值
     * @return 缓存的PDF访问路径，如果不存在返回null
     */
    @Override
    public String checkPdfCache(String fileMd5)
    {
        if (fileMd5 == null) {
            return null;
        }
        
        // 通过MD5查找原始文件
        ContractFile originalFile = contractFileService.selectContractFileByMd5(fileMd5);
        if (originalFile == null) {
            return null;
        }
        
        // 检查是否已存在PDF版本的文件记录
        ContractFile pdfFile = findExistingPdfVersion(originalFile);
        if (pdfFile != null && pdfFile.getFilePath() != null) {
            return pdfFile.getFilePath();
        }
        
        return null;
    }
    
    /**
     * 获取文档预览URL（方案C：直接返回MinIO URL）
     * 
     * @param contractFile 合同文件
     * @return 预览URL
     * @throws Exception 处理异常
     */
    @Override
    public String getDocumentPreviewUrl(ContractFile contractFile) throws Exception
    {
        String fileType = contractFile.getFileType();
        
        if (!isSupportedForPreview(fileType)) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }
        
        if ("pdf".equalsIgnoreCase(fileType)) {
            // PDF文件直接返回MinIO URL
            String filePath = contractFile.getFilePath();
            if (filePath != null && (filePath.startsWith("http://") || filePath.startsWith("https://"))) {
                return filePath; // 直接返回MinIO URL
            } else {
                throw new Exception("PDF文件路径无效，不是有效的URL: " + filePath);
            }
        } else if ("doc".equalsIgnoreCase(fileType) || "docx".equalsIgnoreCase(fileType)) {
            // Word文档需要转换为PDF并上传到MinIO
            return convertWordToPdfAndUploadToMinIO(contractFile);
        }
        
        throw new IllegalArgumentException("无法处理的文件类型: " + fileType);
    }
    
    /**
     * 清理临时文件和缓存
     * 
     * @param fileMd5 文件MD5值
     */
    @Override
    public void cleanupTempFiles(String fileMd5)
    {
        if (fileMd5 == null) {
            return;
        }
        
        try {
            // 清理PDF缓存文件
            String pdfFilePath = Paths.get(ContractConversionConfig.getPdfCacheDir(), fileMd5 + ".pdf").toString();
            Files.deleteIfExists(Paths.get(pdfFilePath));
            
            logger.info("已清理文件缓存，MD5: {}", fileMd5);
        } catch (Exception e) {
            logger.warn("清理缓存文件失败，MD5: {}, 错误: {}", fileMd5, e.getMessage());
        }
    }
    
    /**
     * 检查文档是否支持在线预览
     * 
     * @param fileType 文件类型
     * @return 是否支持预览
     */
    @Override
    public boolean isSupportedForPreview(String fileType)
    {
        return fileType != null && SUPPORTED_PREVIEW_TYPES.contains(fileType.toLowerCase());
    }
    
    /**
     * 下载文件到临时目录
     * 
     * @param fileUrl 文件URL
     * @param fileName 文件名
     * @return 临时文件路径
     * @throws Exception 下载异常
     */
    private String downloadToTemp(String fileUrl, String fileName) throws Exception
    {
        String tempFilePath = Paths.get(ContractConversionConfig.getTempDir(), System.currentTimeMillis() + "_" + fileName).toString();
        
        try (InputStream inputStream = openInputStream(fileUrl);
             FileOutputStream outputStream = new FileOutputStream(tempFilePath)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        
        logger.debug("文件下载到临时目录完成: {}", tempFilePath);
        return tempFilePath;
    }
    
    /**
     * 打开文件输入流（支持URL和本地路径）
     * 
     * @param filePath 文件路径
     * @return 输入流
     * @throws Exception 打开异常
     */
    private InputStream openInputStream(String filePath) throws Exception
    {
        if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
            // 网络URL
            URL url = new URL(filePath);
            URLConnection connection = url.openConnection();
            return connection.getInputStream();
        } else {
            // 本地文件
            return Files.newInputStream(Paths.get(filePath));
        }
    }
    
    /**
     * 执行Word到PDF的实际转换
     * 
     * @param wordFilePath Word文件路径
     * @param pdfFilePath PDF输出路径
     * @return 转换是否成功
     */
    private boolean performWordToPdfConversion(String wordFilePath, String pdfFilePath)
    {
        try {
            logger.info("开始转换Word文档: {} -> {}", wordFilePath, pdfFilePath);
            
            // 使用LibreOffice命令行工具进行转换（推荐方案）
            if (convertWithLibreOffice(wordFilePath, pdfFilePath)) {
                logger.info("LibreOffice转换成功: {}", pdfFilePath);
                return true;
            }
            
            // 如果LibreOffice不可用，使用Apache POI + PDFBox方案
            logger.warn("LibreOffice转换失败，尝试使用Apache POI方案");

            // 检查文件是否包含中文，如果包含则不使用POI
            if (containsChineseInFilePath(wordFilePath)) {
                logger.error("文档路径包含中文字符且LibreOffice转换失败，POI方案无法处理中文");
                return false;
            }

            if (convertWithApachePOI(wordFilePath, pdfFilePath)) {
                logger.info("Apache POI转换成功: {}", pdfFilePath);
                return true;
            }
            
            logger.error("所有转换方案均失败");
            return false;
            
        } catch (Exception e) {
            logger.error("Word转PDF转换异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 使用LibreOffice命令行工具转换
     */
    private boolean convertWithLibreOffice(String wordFilePath, String pdfFilePath) {
        // 检测操作系统，针对macOS使用特殊处理避免Java进程阻塞
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("mac")) {
            return convertWithLibreOfficeOnMacOS(wordFilePath, pdfFilePath);
        } else {
            return convertWithLibreOfficeGeneral(wordFilePath, pdfFilePath);
        }
    }

    /**
     * macOS专用LibreOffice转换方法 - 通过Shell脚本避免Java进程阻塞
     */
    private boolean convertWithLibreOfficeOnMacOS(String wordFilePath, String pdfFilePath) {
        try {
            // 使用配置的LibreOffice路径
            if (ContractConversionConfig.getLibreOfficePath() == null || ContractConversionConfig.getLibreOfficePath().trim().isEmpty()) {
                logger.error("LibreOffice路径未配置，请检查配置文件中的contract.conversion.libreoffice-path参数");
                return false;
            }

            // 确保输出目录存在
            Path outputDir = Paths.get(pdfFilePath).getParent();
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            // 创建临时shell脚本来执行LibreOffice，避免Java进程直接调用的阻塞问题
            Path tempScript = Files.createTempFile("libreoffice_convert_", ".sh");

            String scriptContent = String.format(
                "#!/bin/bash\n" +
                "export SAL_USE_VCLPLUGIN=svp\n" +
                "export DISPLAY=''\n" +
                "export HOME='%s'\n" +
                "cd '%s'\n" +
                "'%s' --headless --convert-to pdf --outdir '%s' '%s' 2>&1\n" +
                "echo \"EXIT_CODE:$?\"\n",
                System.getProperty("user.home"),
                outputDir.toString(),
                ContractConversionConfig.getLibreOfficePath(),
                outputDir.toString(),
                wordFilePath
            );

            Files.write(tempScript, scriptContent.getBytes("UTF-8"));
            tempScript.toFile().setExecutable(true);

            logger.debug("创建macOS LibreOffice转换脚本: {}", tempScript);
            logger.debug("转换脚本内容: {}", scriptContent);

            // 通过bash执行脚本，这样避免了Java直接调用LibreOffice的阻塞问题
            ProcessBuilder processBuilder = new ProcessBuilder("bash", tempScript.toString());
            Process process = processBuilder.start();

            // 读取输出，60秒超时
            StringBuilder output = new StringBuilder();
            StringBuilder errorOutput = new StringBuilder();
            
            // 异步读取输出流，避免缓冲区满导致阻塞
            java.util.concurrent.CompletableFuture<String> outputFuture = java.util.concurrent.CompletableFuture.supplyAsync(() -> {
                StringBuilder output = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                        logger.debug("LibreOffice输出: {}", line);
                    }
                } catch (java.io.IOException e) {
                    logger.debug("读取输出流异常: {}", e.getMessage());
                }
                return output.toString();
            });
            
            java.util.concurrent.CompletableFuture<String> errorFuture = java.util.concurrent.CompletableFuture.supplyAsync(() -> {
                StringBuilder error = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream(), "UTF-8"))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        error.append(line).append("\n");
                        logger.debug("LibreOffice错误: {}", line);
                    }
                } catch (java.io.IOException e) {
                    logger.debug("读取错误流异常: {}", e.getMessage());
                }
                return error.toString();
            });
            
            // 等待进程完成，使用配置的超时时间
            boolean finished = process.waitFor(ContractConversionConfig.getTimeout(), TimeUnit.SECONDS);
            
            if (!finished) {
                logger.warn("LibreOffice转换超时({} 秒)，强制终止进程", ContractConversionConfig.getTimeout());
                process.destroyForcibly();
                // 等待进程真正结束
                try {
                    process.waitFor(5, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return false;
            }
            
            int exitCode = process.exitValue();
            
            // 获取输出信息
            String output = "";
            String error = "";
            try {
                output = outputFuture.get(2, TimeUnit.SECONDS);
                error = errorFuture.get(2, TimeUnit.SECONDS);
            } catch (Exception e) {
                logger.debug("获取进程输出异常: {}", e.getMessage());
            }
            
            logger.debug("LibreOffice转换完成，退出码: {}", exitCode);
            if (!output.isEmpty()) {
                logger.debug("LibreOffice完整输出: {}", output);
            }
            if (!error.isEmpty()) {
                logger.debug("LibreOffice完整错误: {}", error);
            }
            
            if (exitCode == 0) {
                // 检查输出文件是否生成
                if (Files.exists(Paths.get(pdfFilePath))) {
                    logger.info("LibreOffice转换成功: {}", pdfFilePath);
                    return true;
                } else {
                    // 尝试查找可能的输出文件名
                    String fileName = Paths.get(wordFilePath).getFileName().toString();
                    String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                    Path possibleOutput = outputDir.resolve(baseName + ".pdf");
                    
                    if (Files.exists(possibleOutput)) {
                        // 重命名到期望的文件名
                        Files.move(possibleOutput, Paths.get(pdfFilePath));
                        logger.info("LibreOffice转换成功，文件已重命名: {}", pdfFilePath);
                        return true;
                    } else {
                        logger.error("LibreOffice转换完成但未找到输出文件: {}，期望位置: {}", pdfFilePath, possibleOutput);
                        // 列出输出目录的所有文件
                        try {
                            Files.list(outputDir).forEach(path -> logger.debug("输出目录文件: {}", path));
                        } catch (Exception e) {
                            logger.debug("列出输出目录失败: {}", e.getMessage());
                        }
                        return false;
                    }
                }
            } else {
                logger.error("LibreOffice转换失败，退出码: {}，错误信息: {}", exitCode, error);
                return false;
            }
            
        } catch (Exception e) {
            logger.error("LibreOffice转换失败: {}", e.getMessage(), e);
            return false;
        }
    }
    

    
    /**
     * 使用Apache POI + PDFBox转换（备用方案）
     * 修复中文字符编码问题
     */
    private boolean convertWithApachePOI(String wordFilePath, String pdfFilePath) {
        try {
            // 读取Word文档内容（使用Apache POI）
            String textContent = extractTextFromWord(wordFilePath);
            
            // 使用PDFBox创建PDF
            try (org.apache.pdfbox.pdmodel.PDDocument document = new org.apache.pdfbox.pdmodel.PDDocument()) {
                org.apache.pdfbox.pdmodel.PDPage page = new org.apache.pdfbox.pdmodel.PDPage();
                document.addPage(page);
                
                // 加载支持中文的字体
                org.apache.pdfbox.pdmodel.font.PDFont font = loadChineseFont(document);
                
                try (org.apache.pdfbox.pdmodel.PDPageContentStream contentStream = 
                     new org.apache.pdfbox.pdmodel.PDPageContentStream(document, page)) {
                    
                    contentStream.beginText();
                    contentStream.setFont(font, 12);
                    contentStream.newLineAtOffset(50, 750);
                    
                    float yPosition = 750;
                    float pageHeight = page.getMediaBox().getHeight();
                    
                    // 处理文本内容，支持中文字符
                    String[] lines = textContent.split("\n");
                    for (String line : lines) {
                        if (line.trim().length() > 0) {
                            // 过滤和转换中文字符
                            String processedLine = processChineseText(line);
                            
                            // 处理长行文本，按字符数而非单词分割
                            if (processedLine.length() > 60) {
                                List<String> wrappedLines = wrapTextForChinese(processedLine, 60);
                                for (String wrappedLine : wrappedLines) {
                                    if (yPosition < 50) {
                                        // 换页
                                        contentStream.endText();
                                        page = new org.apache.pdfbox.pdmodel.PDPage();
                                        document.addPage(page);
                                        contentStream.close();
                                        
                                        org.apache.pdfbox.pdmodel.PDPageContentStream newContentStream = 
                                            new org.apache.pdfbox.pdmodel.PDPageContentStream(document, page);
                                        newContentStream.beginText();
                                        newContentStream.setFont(font, 12);
                                        newContentStream.newLineAtOffset(50, 750);
                                        yPosition = 750;
                                        
                                        newContentStream.showText(wrappedLine);
                                        newContentStream.newLineAtOffset(0, -15);
                                        yPosition -= 15;
                                        
                                        newContentStream.endText();
                                        newContentStream.close();
                                    } else {
                                        contentStream.showText(wrappedLine);
                                        contentStream.newLineAtOffset(0, -15);
                                        yPosition -= 15;
                                    }
                                }
                            } else {
                                if (yPosition < 50) {
                                    // 换页逻辑同上
                                    contentStream.endText();
                                    page = new org.apache.pdfbox.pdmodel.PDPage();
                                    document.addPage(page);
                                    contentStream.close();
                                    
                                    org.apache.pdfbox.pdmodel.PDPageContentStream newContentStream = 
                                        new org.apache.pdfbox.pdmodel.PDPageContentStream(document, page);
                                    newContentStream.beginText();
                                    newContentStream.setFont(font, 12);
                                    newContentStream.newLineAtOffset(50, 750);
                                    yPosition = 750;
                                    
                                    newContentStream.showText(processedLine);
                                    newContentStream.newLineAtOffset(0, -15);
                                    yPosition -= 15;
                                    
                                    newContentStream.endText();
                                    newContentStream.close();
                                } else {
                                    contentStream.showText(processedLine);
                                    contentStream.newLineAtOffset(0, -15);
                                    yPosition -= 15;
                                }
                            }
                        } else {
                            contentStream.newLineAtOffset(0, -15);
                            yPosition -= 15;
                        }
                    }
                    
                    contentStream.endText();
                }
                
                document.save(pdfFilePath);
                logger.info("Apache POI转换成功，生成PDF: {}", pdfFilePath);
                return true;
            }
            
        } catch (Exception e) {
            logger.error("Apache POI转换失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 加载支持中文的字体 - 优化版本
     */
    private org.apache.pdfbox.pdmodel.font.PDFont loadChineseFont(org.apache.pdfbox.pdmodel.PDDocument document) {
        try {
            // 首先尝试从classpath加载内嵌字体
            org.apache.pdfbox.pdmodel.font.PDFont embeddedFont = loadEmbeddedChineseFont(document);
            if (embeddedFont != null) {
                logger.info("成功加载内嵌中文字体");
                return embeddedFont;
            }

            // 检测操作系统
            String osName = System.getProperty("os.name").toLowerCase();
            logger.debug("当前操作系统: {}", osName);

            // 根据操作系统设置字体路径（更新macOS路径）
            String[] chineseFontPaths;
            if (osName.contains("mac")) {
                // macOS 字体路径 - 使用TTF而非TTC避免兼容性问题
                chineseFontPaths = new String[]{
                    "/System/Library/Fonts/Apple SD Gothic Neo.ttc",  // 韩文字体但支持中文
                    "/Library/Fonts/Arial Unicode MS.ttf",
                    "/System/Library/Fonts/Monaco.ttf",              // 等宽字体
                    "/System/Library/Fonts/Times.ttc",               // Times字体集合
                    "/System/Library/Fonts/Courier New.ttf"          // Courier New
                };
            } else if (osName.contains("linux")) {
                // Linux 字体路径
                chineseFontPaths = new String[]{
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttf",
                    "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.otf"
                };
            } else {
                // Windows 字体路径
                chineseFontPaths = new String[]{
                    "C:/Windows/Fonts/msyh.ttf",      // 微软雅黑
                    "C:/Windows/Fonts/simsun.ttc",    // 宋体
                    "C:/Windows/Fonts/arial.ttf"      // Arial
                };
            }

            // 尝试加载系统字体
            for (String fontPath : chineseFontPaths) {
                try {
                    File fontFile = new File(fontPath);
                    if (fontFile.exists() && fontFile.canRead()) {
                        logger.debug("尝试加载字体: {}", fontPath);

                        // 根据文件扩展名选择不同的加载方式
                        org.apache.pdfbox.pdmodel.font.PDFont font;
                        if (fontPath.endsWith(".ttc")) {
                            // TTC文件需要特殊处理，尝试加载第一个字体
                            font = loadTTCFont(document, fontFile);
                        } else {
                            font = org.apache.pdfbox.pdmodel.font.PDType0Font.load(document, fontFile);
                        }

                        if (font != null) {
                            logger.info("成功加载中文字体: {}", fontPath);
                            return font;
                        }
                    } else {
                        logger.debug("字体文件不存在或不可读: {}", fontPath);
                    }
                } catch (Exception e) {
                    logger.debug("加载字体失败: {} - {}", fontPath, e.getMessage());
                }
            }

            // 最后的降级方案：抛出异常，让调用方决定如何处理
            String errorMsg = "无法加载中文字体，文档包含中文字符无法转换为PDF";
            logger.error(errorMsg);
            throw new RuntimeException(errorMsg);

        } catch (RuntimeException e) {
            throw e;  // 重新抛出运行时异常
        } catch (Exception e) {
            logger.error("加载中文字体过程中发生异常: {}", e.getMessage());
            throw new RuntimeException("中文字体加载失败：" + e.getMessage(), e);
        }
    }

    /**
     * 尝试从classpath加载内嵌的中文字体
     */
    private org.apache.pdfbox.pdmodel.font.PDFont loadEmbeddedChineseFont(org.apache.pdfbox.pdmodel.PDDocument document) {
        String[] embeddedFontPaths = {
            "/fonts/NotoSansCJK-Regular.otf",
            "/fonts/SourceHanSans-Regular.otf",
            "/fonts/SimSun.ttf"
        };

        for (String fontPath : embeddedFontPaths) {
            try (java.io.InputStream fontStream = getClass().getResourceAsStream(fontPath)) {
                if (fontStream != null) {
                    logger.debug("尝试加载内嵌字体: {}", fontPath);
                    return org.apache.pdfbox.pdmodel.font.PDType0Font.load(document, fontStream);
                }
            } catch (Exception e) {
                logger.debug("加载内嵌字体失败: {} - {}", fontPath, e.getMessage());
            }
        }
        return null;
    }

    /**
     * 安全加载TTC字体文件
     */
    private org.apache.pdfbox.pdmodel.font.PDFont loadTTCFont(org.apache.pdfbox.pdmodel.PDDocument document, File fontFile) {
        try {
            // TTC文件包含多个字体，尝试加载第一个可用的
            return org.apache.pdfbox.pdmodel.font.PDType0Font.load(document, fontFile);
        } catch (Exception e) {
            logger.debug("TTC字体加载失败: {} - {}", fontFile.getPath(), e.getMessage());
            return null;
        }
    }

    /**
     * 检查合同文件是否包含中文字符
     */
    private boolean containsChineseCharacters(ContractFile contractFile) {
        try {
            // 简单检查文件名是否包含中文
            String fileName = contractFile.getFileName();
            if (fileName != null && containsChinese(fileName)) {
                return true;
            }

            // 这里可以扩展为检查文件内容，但为了性能考虑，先只检查文件名
            // 实际应用中，可以在文件上传时就标记是否包含中文
            return false;

        } catch (Exception e) {
            logger.debug("检查中文字符时出现异常: {}", e.getMessage());
            // 出现异常时保守处理，假设包含中文
            return true;
        }
    }

    /**
     * 检查文件路径是否包含中文字符
     */
    private boolean containsChineseInFilePath(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }

        // 提取文件名部分进行检查
        String fileName = java.nio.file.Paths.get(filePath).getFileName().toString();
        return containsChinese(fileName);
    }

    /**
     * 检查字符串是否包含中文字符
     */
    private boolean containsChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }

        for (char c : text.toCharArray()) {
            // 检查是否为中文字符
            Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
            if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS_SUPPLEMENT) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 加载中文字体（AWT版本）
     */
    private java.awt.Font loadChineseFont() {
        try {
            // 检测操作系统
            String osName = System.getProperty("os.name").toLowerCase();
            logger.debug("当前操作系统: {}", osName);
            
            // 根据操作系统设置字体路径
            String[] fontPaths;
            if (osName.contains("mac")) {
                // macOS 字体路径
                fontPaths = new String[]{
                    "/System/Library/Fonts/PingFang.ttc",
                    "/System/Library/Fonts/Supplemental/PingFang.ttc",
                    "/System/Library/Fonts/STHeiti Light.ttc",
                    "/System/Library/Fonts/STHeiti Medium.ttc",
                    "/System/Library/Fonts/Helvetica.ttc",
                    "/System/Library/Fonts/Arial.ttf",
                    "/Library/Fonts/Arial Unicode MS.ttf"
                };
            } else if (osName.contains("linux")) {
                // Linux 字体路径
                fontPaths = new String[]{
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                    "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
                    "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",
                    "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
                    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
                };
            } else {
                // Windows 字体路径
                fontPaths = new String[]{
                    "C:/Windows/Fonts/msyh.ttc",
                    "C:/Windows/Fonts/simsun.ttc",
                    "C:/Windows/Fonts/arial.ttf"
                };
            }
            
            // 尝试加载字体
            for (String fontPath : fontPaths) {
                try {
                    java.io.File fontFile = new java.io.File(fontPath);
                    if (fontFile.exists() && fontFile.canRead()) {
                        logger.debug("尝试加载字体: {}", fontPath);
                        java.awt.Font font = java.awt.Font.createFont(java.awt.Font.TRUETYPE_FONT, fontFile);
                        font = font.deriveFont(12f);
                        
                        // 测试字体是否支持中文字符
                        if (font.canDisplay('中') && font.canDisplay('文')) {
                            logger.info("成功加载中文字体: {} ({})", font.getFontName(), fontPath);
                            return font;
                        } else {
                            logger.debug("字体不支持中文字符: {}", fontPath);
                        }
                    } else {
                        logger.debug("字体文件不存在或不可读: {}", fontPath);
                    }
                } catch (Exception e) {
                    logger.debug("加载字体失败: {} - {}", fontPath, e.getMessage());
                }
            }
            
            // 尝试使用系统默认字体
            logger.debug("尝试使用系统默认字体");
            java.awt.Font[] systemFonts = GraphicsEnvironment.getLocalGraphicsEnvironment().getAllFonts();
            for (java.awt.Font systemFont : systemFonts) {
                if (systemFont.canDisplay('中') && systemFont.canDisplay('文')) {
                    java.awt.Font derivedFont = systemFont.deriveFont(12f);
                    logger.info("使用系统字体: {}", systemFont.getFontName());
                    return derivedFont;
                }
            }
            
            logger.warn("未找到支持中文的字体，使用内置Serif字体");
            return new java.awt.Font(java.awt.Font.SERIF, java.awt.Font.PLAIN, 12);
            
        } catch (Exception e) {
            logger.error("加载中文字体失败: {}", e.getMessage(), e);
            return new java.awt.Font(java.awt.Font.SERIF, java.awt.Font.PLAIN, 12);
        }
    }
    
    /**
     * 处理中文文本，确保字符兼容性
     */
    private String processChineseText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        
        // 移除或替换不支持的字符
        StringBuilder processed = new StringBuilder();
        for (char c : text.toCharArray()) {
            // 保留ASCII字符、中文字符和常用标点
            if ((c >= 32 && c <= 126) ||           // ASCII可打印字符
                (c >= 0x4E00 && c <= 0x9FFF) ||    // 中文汉字
                (c >= 0x3000 && c <= 0x303F) ||    // 中文标点
                (c >= 0xFF00 && c <= 0xFFEF)) {     // 全角字符
                processed.append(c);
            } else {
                // 替换不支持的字符为空格
                processed.append(' ');
            }
        }
        
        return processed.toString();
    }
    
    /**
     * 中文文本换行处理
     */
    private List<String> wrapTextForChinese(String text, int maxLength) {
        List<String> lines = new java.util.ArrayList<>();
        
        if (text.length() <= maxLength) {
            lines.add(text);
            return lines;
        }
        
        int start = 0;
        while (start < text.length()) {
            int end = Math.min(start + maxLength, text.length());
            
            // 尝试在合适的位置断行（避免在汉字中间断开）
            if (end < text.length()) {
                // 向前查找合适的断点
                for (int i = end; i > start + maxLength / 2; i--) {
                    char c = text.charAt(i);
                    if (c == ' ' || c == '，' || c == '。' || c == '；' || c == '：') {
                        end = i + 1;
                        break;
                    }
                }
            }
            
            lines.add(text.substring(start, end));
            start = end;
        }
        
        return lines;
    }
    
    /**
     * 从Word文档中提取文本内容
     */
    private String extractTextFromWord(String wordFilePath) throws Exception {
        StringBuilder textContent = new StringBuilder();
        
        try (FileInputStream fis = new FileInputStream(wordFilePath)) {
            if (wordFilePath.toLowerCase().endsWith(".docx")) {
                // 处理.docx文件
                org.apache.poi.xwpf.usermodel.XWPFDocument document = 
                    new org.apache.poi.xwpf.usermodel.XWPFDocument(fis);
                
                for (org.apache.poi.xwpf.usermodel.XWPFParagraph paragraph : document.getParagraphs()) {
                    textContent.append(paragraph.getText()).append("\n");
                }
                
                // 处理表格
                for (org.apache.poi.xwpf.usermodel.XWPFTable table : document.getTables()) {
                    for (org.apache.poi.xwpf.usermodel.XWPFTableRow row : table.getRows()) {
                        for (org.apache.poi.xwpf.usermodel.XWPFTableCell cell : row.getTableCells()) {
                            textContent.append(cell.getText()).append("\t");
                        }
                        textContent.append("\n");
                    }
                }
                
                document.close();
            } else {
                // 处理.doc文件
                org.apache.poi.hwpf.HWPFDocument document = new org.apache.poi.hwpf.HWPFDocument(fis);
                org.apache.poi.hwpf.extractor.WordExtractor extractor = new org.apache.poi.hwpf.extractor.WordExtractor(document);
                textContent.append(extractor.getText());
                document.close();
            }
        }
        
        return textContent.toString();
    }
    

    
    /**
     * 确保目录存在
     * 
     * @param dirPath 目录路径
     * @throws Exception 创建目录异常
     */
    private void ensureDirectoryExists(String dirPath) throws Exception
    {
        Path path = Paths.get(dirPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
            logger.debug("创建目录: {}", dirPath);
        }
    }
    
    /**
     * 清理临时文件
     * 
     * @param filePath 文件路径
     */
    private void cleanupTempFile(String filePath)
    {
        try {
            if (filePath != null) {
                Files.deleteIfExists(Paths.get(filePath));
                logger.debug("清理临时文件: {}", filePath);
            }
        } catch (Exception e) {
            logger.warn("清理临时文件失败: {}, 错误: {}", filePath, e.getMessage());
        }
    }
    
    /**
     * Word转PDF并上传到MinIO（方案C的核心实现）
     * 
     * @param contractFile 原Word文件信息
     * @return PDF的MinIO访问URL
     * @throws Exception 转换或上传异常
     */
    private String convertWordToPdfAndUploadToMinIO(ContractFile contractFile) throws Exception
    {
        String fileMd5 = contractFile.getFileMd5();
        
        // 1. 检查数据库中是否已有转换后的PDF记录
//        ContractFile existingPdfFile = findExistingPdfVersion(contractFile);
//        if (existingPdfFile != null) {
//            logger.info("找到已转换的PDF版本，文件ID: {}, URL: {}", existingPdfFile.getId(), existingPdfFile.getFilePath());
//            return existingPdfFile.getFilePath();
//        }
        
        // 2. 下载Word文档到临时目录
        String fileName = contractFile.getFileName();
        String wordFilePath = downloadToTemp(contractFile.getFilePath(), fileName);
        
        try {
            // 3. 转换Word为PDF
            String pdfFileName = fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";
            String tempPdfPath = Paths.get(ContractConversionConfig.getTempDir(), System.currentTimeMillis() + "_" + pdfFileName).toString();
            
            boolean conversionSuccess = performWordToPdfConversion(wordFilePath, tempPdfPath);
            
            if (!conversionSuccess) {
                throw new Exception("Word转PDF转换失败");
            }
            
            // 4. 上传PDF到MinIO（这里需要集成实际的MinIO上传逻辑）
            String pdfMinIOUrl = uploadPdfToMinIO(tempPdfPath, pdfFileName, contractFile);
            
            // 5. 创建新的ContractFile记录（PDF版本）
            ContractFile pdfContractFile = createPdfContractFileRecord(contractFile, pdfFileName, pdfMinIOUrl);
            contractFileService.insertContractFile(pdfContractFile);
            
            logger.info("Word转PDF完成并上传到MinIO，原文件ID: {}, 新PDF文件ID: {}, URL: {}", 
                       contractFile.getId(), pdfContractFile.getId(), pdfMinIOUrl);
            
            // 6. 清理临时文件
            cleanupTempFile(tempPdfPath);
            
            return pdfMinIOUrl;
            
        } finally {
            // 清理临时Word文件
            cleanupTempFile(wordFilePath);
        }
    }
    
    /**
     * 查找已存在的PDF转换版本
     */
    private ContractFile findExistingPdfVersion(ContractFile originalFile)
    {
        // 通过任务ID和PDF文件类型查找
        ContractFile queryFile = new ContractFile();
        queryFile.setTaskId(originalFile.getTaskId());
        queryFile.setFileType("pdf");
        
        List<ContractFile> pdfFiles = contractFileService.selectContractFileList(queryFile);
        
        // 查找原始文件名匹配的PDF版本
        String originalBaseName = originalFile.getFileName().substring(0, originalFile.getFileName().lastIndexOf("."));
        for (ContractFile pdfFile : pdfFiles) {
            if (pdfFile.getFileName() != null && 
                pdfFile.getFileName().startsWith(originalBaseName) && 
                pdfFile.getFileName().endsWith(".pdf")) {
                return pdfFile;
            }
        }
        
        return null;
    }
    
    /**
     * 上传PDF到MinIO
     */
    private String uploadPdfToMinIO(String tempPdfPath, String fileName, ContractFile originalFile) throws Exception
    {
        try {
            // 1. 构建MinIO对象名称
            String objectName = "contract-files/pdf/" + originalFile.getTaskId() + "/" + fileName;
            
            // 2. 创建File对象
            File pdfFile = new File(tempPdfPath);
            if (!pdfFile.exists()) {
                throw new Exception("PDF文件不存在: " + tempPdfPath);
            }
            
            logger.info("开始上传PDF到MinIO: {} -> {}", tempPdfPath, objectName);
            
            // 3. 使用MinioUtil上传文件
            String uploadResult = MinioUtil.uploadFileFromFile(MinioConfig.getBucketName(), objectName, pdfFile);
            
            if (uploadResult == null) {
                throw new Exception("MinIO上传失败，返回结果为空");
            }
            
            // 4. 构建完整的MinIO访问URL
            String minioUrl = MinioConfig.getUrl() + "/" + MinioConfig.getBucketName() + "/" + objectName;
            
            logger.info("PDF上传到MinIO成功: {}", minioUrl);
            
            return minioUrl;
            
        } catch (Exception e) {
            logger.error("上传PDF到MinIO失败: {}", e.getMessage(), e);
            throw new Exception("MinIO上传失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建PDF版本的ContractFile记录
     */
    private ContractFile createPdfContractFileRecord(ContractFile originalFile, String pdfFileName, String pdfMinIOUrl)
    {
        ContractFile pdfFile = new ContractFile();
        
        // 复制原文件的基本信息
        pdfFile.setTaskId(originalFile.getTaskId());
        pdfFile.setFileName(pdfFileName);
        pdfFile.setFileType("pdf");
        pdfFile.setFilePath(pdfMinIOUrl);
        pdfFile.setUploadStatus("1"); // 上传成功
        pdfFile.setParseStatus("0");  // PDF不需要解析，或者标记为已解析
        
        // 文件大小和MD5需要根据实际PDF文件计算
        // 这里简化处理
        pdfFile.setFileSize(0L); // TODO: 计算实际PDF文件大小
        pdfFile.setFileMd5(generatePdfMd5(originalFile.getFileMd5())); // 基于原文件MD5生成
        
        // 添加备注说明这是转换文件
        pdfFile.setRemark("由Word文档转换生成，原文件ID: " + originalFile.getId());
        
        return pdfFile;
    }
    
    /**
     * 生成PDF版本的MD5（基于原文件MD5）
     */
    private String generatePdfMd5(String originalMd5)
    {
        // 简单的MD5变换，实际应该计算真实PDF文件的MD5
        return originalMd5 + "_pdf";
    }

    /**
     * macOS专用LibreOffice转换方法 - 通过Shell脚本避免Java进程阻塞
     */
    private boolean convertWithLibreOfficeOnMacOS(String wordFilePath, String pdfFilePath) {
        try {
            // 使用配置的LibreOffice路径
            if (ContractConversionConfig.getLibreOfficePath() == null || ContractConversionConfig.getLibreOfficePath().trim().isEmpty()) {
                logger.error("LibreOffice路径未配置，请检查配置文件中的contract.conversion.libreoffice-path参数");
                return false;
            }

            // 确保输出目录存在
            Path outputDir = Paths.get(pdfFilePath).getParent();
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            // 创建临时shell脚本来执行LibreOffice，避免Java进程直接调用的阻塞问题
            Path tempScript = Files.createTempFile("libreoffice_convert_", ".sh");

            String scriptContent = String.format(
                "#!/bin/bash\n" +
                "export SAL_USE_VCLPLUGIN=svp\n" +
                "export DISPLAY=''\n" +
                "export HOME='%s'\n" +
                "cd '%s'\n" +
                "'%s' --headless --convert-to pdf --outdir '%s' '%s' 2>&1\n" +
                "echo \"EXIT_CODE:$?\"\n",
                System.getProperty("user.home"),
                outputDir.toString(),
                ContractConversionConfig.getLibreOfficePath(),
                outputDir.toString(),
                wordFilePath
            );

            Files.write(tempScript, scriptContent.getBytes("UTF-8"));
            tempScript.toFile().setExecutable(true);

            logger.info("🔧 macOS LibreOffice转换 - 使用Shell脚本方案避免Java进程阻塞");
            logger.debug("创建macOS LibreOffice转换脚本: {}", tempScript);
            logger.debug("转换脚本内容: {}", scriptContent);

            // 通过bash执行脚本，这样避免了Java直接调用LibreOffice的阻塞问题
            ProcessBuilder processBuilder = new ProcessBuilder("bash", tempScript.toString());
            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            StringBuilder errorOutput = new StringBuilder();

            Thread outputThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        output.append(line).append("\n");
                        logger.debug("macOS LibreOffice输出: {}", line);
                    }
                } catch (IOException e) {
                    logger.debug("读取输出异常: {}", e.getMessage());
                }
            });

            Thread errorThread = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append("\n");
                        logger.debug("macOS LibreOffice错误: {}", line);
                    }
                } catch (IOException e) {
                    logger.debug("读取错误异常: {}", e.getMessage());
                }
            });

            outputThread.start();
            errorThread.start();

            // 等待进程完成，60秒超时（比原来300秒短很多，因为Shell方案更快）
            boolean finished = process.waitFor(60, TimeUnit.SECONDS);

            // 等待读取线程完成
            outputThread.join(3000);
            errorThread.join(3000);

            // 清理临时脚本
            try {
                Files.deleteIfExists(tempScript);
            } catch (Exception e) {
                logger.debug("清理临时脚本失败: {}", e.getMessage());
            }

            if (!finished) {
                logger.error("❌ macOS LibreOffice转换超时(60秒)，强制终止进程");
                process.destroyForcibly();
                return false;
            }

            int exitCode = process.exitValue();
            if (exitCode == 0) {
                // 检查输出文件是否存在
                if (Files.exists(Paths.get(pdfFilePath))) {
                    logger.info("✅ macOS LibreOffice转换成功: {}", pdfFilePath);
                    return true;
                } else {
                    logger.warn("⚠️ macOS LibreOffice进程成功退出，但未找到输出文件: {}", pdfFilePath);
                    return false;
                }
            } else {
                logger.error("❌ macOS LibreOffice转换失败，退出码: {}, 错误输出: {}", exitCode, errorOutput.toString());
                return false;
            }

        } catch (Exception e) {
            logger.error("💥 macOS LibreOffice转换过程中发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 通用LibreOffice转换方法（非macOS系统）
     */
    private boolean convertWithLibreOfficeGeneral(String wordFilePath, String pdfFilePath) {
        try {
            // 使用配置的LibreOffice路径
            if (ContractConversionConfig.getLibreOfficePath() == null || ContractConversionConfig.getLibreOfficePath().trim().isEmpty()) {
                logger.error("LibreOffice路径未配置，请检查配置文件中的contract.conversion.libreoffice-path参数");
                return false;
            }

            // 确保输出目录存在
            Path outputDir = Paths.get(pdfFilePath).getParent();
            if (!Files.exists(outputDir)) {
                Files.createDirectories(outputDir);
            }

            // 构建LibreOffice转换命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                ContractConversionConfig.getLibreOfficePath(),
                "--headless",
                "--invisible",
                "--nodefault",
                "--nolockcheck",
                "--nologo",
                "--norestore",
                "--convert-to", "pdf",
                "--outdir", outputDir.toString(),
                wordFilePath
            );

            // 设置环境变量
            java.util.Map<String, String> env = processBuilder.environment();
            env.put("HOME", System.getProperty("user.home"));
            env.put("USER", System.getProperty("user.name"));
            env.put("SAL_USE_VCLPLUGIN", "svp");
            env.put("SAL_NO_X11", "1");
            env.put("DISPLAY", "");
            env.put("SAL_DISABLE_OPENCL", "1");

            logger.debug("执行通用LibreOffice转换命令: {}", String.join(" ", processBuilder.command()));

            Process process = processBuilder.start();

            // 等待进程完成
            boolean finished = process.waitFor(ContractConversionConfig.getTimeout(), TimeUnit.SECONDS);

            if (!finished) {
                logger.warn("通用LibreOffice转换超时({} 秒)，强制终止进程", ContractConversionConfig.getTimeout());
                process.destroyForcibly();
                return false;
            }

            int exitCode = process.exitValue();
            if (exitCode == 0 && Files.exists(Paths.get(pdfFilePath))) {
                logger.info("通用LibreOffice转换成功: {}", pdfFilePath);
                return true;
            } else {
                logger.warn("通用LibreOffice转换失败，退出码: {}", exitCode);
                return false;
            }

        } catch (Exception e) {
            logger.error("通用LibreOffice转换过程中发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
}