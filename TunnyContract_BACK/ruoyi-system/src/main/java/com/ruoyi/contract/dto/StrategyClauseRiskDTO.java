package com.ruoyi.contract.dto;

import java.util.List;
import com.ruoyi.contract.domain.ContractRiskPoint;

/**
 * 策略关联条款和风险点DTO
 * 
 * <AUTHOR>
 * @date 2025-01-13
 */
public class StrategyClauseRiskDTO 
{
    /** 条款ID */
    private Long clauseId;
    
    /** 条款名称 */
    private String clauseName;
    
    /** 条款说明 */
    private String clauseDesc;
    
    /** 条款内容 */
    private String clauseContent;
    
    /** 排序序号 */
    private Long sortOrder;
    
    /** 条款状态 */
    private String clauseStatus;
    
    /** 关联的风险点列表 */
    private List<ContractRiskPoint> riskPoints;

    public Long getClauseId() {
        return clauseId;
    }

    public void setClauseId(Long clauseId) {
        this.clauseId = clauseId;
    }

    public String getClauseName() {
        return clauseName;
    }

    public void setClauseName(String clauseName) {
        this.clauseName = clauseName;
    }

    public String getClauseDesc() {
        return clauseDesc;
    }

    public void setClauseDesc(String clauseDesc) {
        this.clauseDesc = clauseDesc;
    }

    public String getClauseContent() {
        return clauseContent;
    }

    public void setClauseContent(String clauseContent) {
        this.clauseContent = clauseContent;
    }

    public Long getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Long sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getClauseStatus() {
        return clauseStatus;
    }

    public void setClauseStatus(String clauseStatus) {
        this.clauseStatus = clauseStatus;
    }

    public List<ContractRiskPoint> getRiskPoints() {
        return riskPoints;
    }

    public void setRiskPoints(List<ContractRiskPoint> riskPoints) {
        this.riskPoints = riskPoints;
    }
}