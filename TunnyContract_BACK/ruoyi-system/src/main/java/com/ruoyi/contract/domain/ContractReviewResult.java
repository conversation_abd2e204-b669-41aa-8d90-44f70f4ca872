package com.ruoyi.contract.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同审查结果对象 contract_review_result
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractReviewResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 结果ID */
    private Long id;

    /** 关联审查任务ID */
    @Excel(name = "关联审查任务ID")
    private Long taskId;

    /** 关联文件ID */
    @Excel(name = "关联文件ID")
    private Long fileId;

    /** 审查总结 */
    @Excel(name = "审查总结")
    private String reviewSummary;

    /** 总体得分 */
    @Excel(name = "总体得分")
    private BigDecimal totalScore;

    /** 整体风险等级(1-高风险，2-中风险，3-低风险) */
    @Excel(name = "整体风险等级(1-高风险，2-中风险，3-低风险)")
    private String riskLevel;

    /** 完整报告JSON数据 */
    @Excel(name = "完整报告JSON数据")
    private String reportJson;

    /** 报告HTML格式 */
    @Excel(name = "报告HTML格式")
    private String reportHtml;

    /** 导出次数 */
    @Excel(name = "导出次数")
    private Long exportCount;

    /** 最后导出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后导出时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastExportTime;

    /** 0-正常，2-删除 */
    private String delFlag;

    /** 保留字段1 */
    @Excel(name = "保留字段1")
    private String attr1;

    /** 保留字段2 */
    @Excel(name = "保留字段2")
    private String attr2;

    /** 保留字段3 */
    @Excel(name = "保留字段3")
    private String attr3;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }

    public void setFileId(Long fileId) 
    {
        this.fileId = fileId;
    }

    public Long getFileId() 
    {
        return fileId;
    }

    public void setReviewSummary(String reviewSummary) 
    {
        this.reviewSummary = reviewSummary;
    }

    public String getReviewSummary() 
    {
        return reviewSummary;
    }

    public void setTotalScore(BigDecimal totalScore) 
    {
        this.totalScore = totalScore;
    }

    public BigDecimal getTotalScore() 
    {
        return totalScore;
    }

    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    public void setReportJson(String reportJson) 
    {
        this.reportJson = reportJson;
    }

    public String getReportJson() 
    {
        return reportJson;
    }

    public void setReportHtml(String reportHtml) 
    {
        this.reportHtml = reportHtml;
    }

    public String getReportHtml() 
    {
        return reportHtml;
    }

    public void setExportCount(Long exportCount) 
    {
        this.exportCount = exportCount;
    }

    public Long getExportCount() 
    {
        return exportCount;
    }

    public void setLastExportTime(Date lastExportTime) 
    {
        this.lastExportTime = lastExportTime;
    }

    public Date getLastExportTime() 
    {
        return lastExportTime;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3() 
    {
        return attr3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskId", getTaskId())
            .append("fileId", getFileId())
            .append("reviewSummary", getReviewSummary())
            .append("totalScore", getTotalScore())
            .append("riskLevel", getRiskLevel())
            .append("reportJson", getReportJson())
            .append("reportHtml", getReportHtml())
            .append("exportCount", getExportCount())
            .append("lastExportTime", getLastExportTime())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .toString();
    }
}
