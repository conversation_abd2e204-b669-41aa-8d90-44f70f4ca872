package com.ruoyi.contract.mapper;

import java.util.List;
import com.ruoyi.contract.domain.ContractReviewStrategy;

/**
 * 合同审查策略Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface ContractReviewStrategyMapper 
{
    /**
     * 查询合同审查策略
     * 
     * @param id 合同审查策略主键
     * @return 合同审查策略
     */
    public ContractReviewStrategy selectContractReviewStrategyById(Long id);

    /**
     * 查询合同审查策略列表
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 合同审查策略集合
     */
    public List<ContractReviewStrategy> selectContractReviewStrategyList(ContractReviewStrategy contractReviewStrategy);

    /**
     * 新增合同审查策略
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 结果
     */
    public int insertContractReviewStrategy(ContractReviewStrategy contractReviewStrategy);

    /**
     * 修改合同审查策略
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 结果
     */
    public int updateContractReviewStrategy(ContractReviewStrategy contractReviewStrategy);

    /**
     * 删除合同审查策略
     * 
     * @param id 合同审查策略主键
     * @return 结果
     */
    public int deleteContractReviewStrategyById(Long id);

    /**
     * 批量删除合同审查策略
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractReviewStrategyByIds(Long[] ids);
}
