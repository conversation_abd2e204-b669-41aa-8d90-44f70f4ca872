package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractReviewClauseMapper;
import com.ruoyi.contract.domain.ContractReviewClause;
import com.ruoyi.contract.service.IContractReviewClauseService;

/**
 * 合同审查条款Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractReviewClauseServiceImpl implements IContractReviewClauseService 
{
    @Autowired
    private ContractReviewClauseMapper contractReviewClauseMapper;

    /**
     * 查询合同审查条款
     * 
     * @param id 合同审查条款主键
     * @return 合同审查条款
     */
    @Override
    public ContractReviewClause selectContractReviewClauseById(Long id)
    {
        return contractReviewClauseMapper.selectContractReviewClauseById(id);
    }

    /**
     * 查询合同审查条款列表
     * 
     * @param contractReviewClause 合同审查条款
     * @return 合同审查条款
     */
    @Override
    public List<ContractReviewClause> selectContractReviewClauseList(ContractReviewClause contractReviewClause)
    {
        return contractReviewClauseMapper.selectContractReviewClauseList(contractReviewClause);
    }

    /**
     * 新增合同审查条款
     * 
     * @param contractReviewClause 合同审查条款
     * @return 结果
     */
    @Override
    public int insertContractReviewClause(ContractReviewClause contractReviewClause)
    {
        contractReviewClause.setCreateTime(DateUtils.getNowDate());
        return contractReviewClauseMapper.insertContractReviewClause(contractReviewClause);
    }

    /**
     * 修改合同审查条款
     * 
     * @param contractReviewClause 合同审查条款
     * @return 结果
     */
    @Override
    public int updateContractReviewClause(ContractReviewClause contractReviewClause)
    {
        contractReviewClause.setUpdateTime(DateUtils.getNowDate());
        return contractReviewClauseMapper.updateContractReviewClause(contractReviewClause);
    }

    /**
     * 批量删除合同审查条款
     * 
     * @param ids 需要删除的合同审查条款主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewClauseByIds(Long[] ids)
    {
        return contractReviewClauseMapper.deleteContractReviewClauseByIds(ids);
    }

    /**
     * 删除合同审查条款信息
     * 
     * @param id 合同审查条款主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewClauseById(Long id)
    {
        return contractReviewClauseMapper.deleteContractReviewClauseById(id);
    }
}
