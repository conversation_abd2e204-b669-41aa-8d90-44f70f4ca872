package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractFileMapper;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractFileService;

/**
 * 合同文件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractFileServiceImpl implements IContractFileService 
{
    @Autowired
    private ContractFileMapper contractFileMapper;

    /**
     * 查询合同文件
     * 
     * @param id 合同文件主键
     * @return 合同文件
     */
    @Override
    public ContractFile selectContractFileById(Long id)
    {
        return contractFileMapper.selectContractFileById(id);
    }

    /**
     * 查询合同文件列表
     * 
     * @param contractFile 合同文件
     * @return 合同文件
     */
    @Override
    public List<ContractFile> selectContractFileList(ContractFile contractFile)
    {
        return contractFileMapper.selectContractFileList(contractFile);
    }

    /**
     * 新增合同文件
     * 
     * @param contractFile 合同文件
     * @return 结果
     */
    @Override
    public int insertContractFile(ContractFile contractFile)
    {
        contractFile.setCreateTime(DateUtils.getNowDate());
        return contractFileMapper.insertContractFile(contractFile);
    }

    /**
     * 修改合同文件
     * 
     * @param contractFile 合同文件
     * @return 结果
     */
    @Override
    public int updateContractFile(ContractFile contractFile)
    {
        contractFile.setUpdateTime(DateUtils.getNowDate());
        return contractFileMapper.updateContractFile(contractFile);
    }

    /**
     * 批量删除合同文件
     * 
     * @param ids 需要删除的合同文件主键
     * @return 结果
     */
    @Override
    public int deleteContractFileByIds(Long[] ids)
    {
        return contractFileMapper.deleteContractFileByIds(ids);
    }

    /**
     * 删除合同文件信息
     * 
     * @param id 合同文件主键
     * @return 结果
     */
    @Override
    public int deleteContractFileById(Long id)
    {
        return contractFileMapper.deleteContractFileById(id);
    }

    /**
     * 根据MD5值查询合同文件
     * 
     * @param fileMd5 文件MD5值
     * @return 合同文件
     */
    @Override
    public ContractFile selectContractFileByMd5(String fileMd5)
    {
        return contractFileMapper.selectContractFileByMd5(fileMd5);
    }
}
