package com.ruoyi.contract.service;

import java.util.List;
import com.ruoyi.contract.domain.ContractReviewResult;

/**
 * 合同审查结果Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IContractReviewResultService 
{
    /**
     * 查询合同审查结果
     * 
     * @param id 合同审查结果主键
     * @return 合同审查结果
     */
    public ContractReviewResult selectContractReviewResultById(Long id);

    /**
     * 查询合同审查结果列表
     * 
     * @param contractReviewResult 合同审查结果
     * @return 合同审查结果集合
     */
    public List<ContractReviewResult> selectContractReviewResultList(ContractReviewResult contractReviewResult);

    /**
     * 新增合同审查结果
     * 
     * @param contractReviewResult 合同审查结果
     * @return 结果
     */
    public int insertContractReviewResult(ContractReviewResult contractReviewResult);

    /**
     * 修改合同审查结果
     * 
     * @param contractReviewResult 合同审查结果
     * @return 结果
     */
    public int updateContractReviewResult(ContractReviewResult contractReviewResult);

    /**
     * 批量删除合同审查结果
     * 
     * @param ids 需要删除的合同审查结果主键集合
     * @return 结果
     */
    public int deleteContractReviewResultByIds(Long[] ids);

    /**
     * 删除合同审查结果信息
     * 
     * @param id 合同审查结果主键
     * @return 结果
     */
    public int deleteContractReviewResultById(Long id);
}
