package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractRiskPointMapper;
import com.ruoyi.contract.domain.ContractRiskPoint;
import com.ruoyi.contract.service.IContractRiskPointService;

/**
 * 合同风险点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractRiskPointServiceImpl implements IContractRiskPointService 
{
    @Autowired
    private ContractRiskPointMapper contractRiskPointMapper;

    /**
     * 查询合同风险点
     * 
     * @param id 合同风险点主键
     * @return 合同风险点
     */
    @Override
    public ContractRiskPoint selectContractRiskPointById(Long id)
    {
        return contractRiskPointMapper.selectContractRiskPointById(id);
    }

    /**
     * 查询合同风险点列表
     * 
     * @param contractRiskPoint 合同风险点
     * @return 合同风险点
     */
    @Override
    public List<ContractRiskPoint> selectContractRiskPointList(ContractRiskPoint contractRiskPoint)
    {
        return contractRiskPointMapper.selectContractRiskPointList(contractRiskPoint);
    }

    /**
     * 新增合同风险点
     * 
     * @param contractRiskPoint 合同风险点
     * @return 结果
     */
    @Override
    public int insertContractRiskPoint(ContractRiskPoint contractRiskPoint)
    {
        contractRiskPoint.setCreateTime(DateUtils.getNowDate());
        return contractRiskPointMapper.insertContractRiskPoint(contractRiskPoint);
    }

    /**
     * 修改合同风险点
     * 
     * @param contractRiskPoint 合同风险点
     * @return 结果
     */
    @Override
    public int updateContractRiskPoint(ContractRiskPoint contractRiskPoint)
    {
        contractRiskPoint.setUpdateTime(DateUtils.getNowDate());
        return contractRiskPointMapper.updateContractRiskPoint(contractRiskPoint);
    }

    /**
     * 批量删除合同风险点
     * 
     * @param ids 需要删除的合同风险点主键
     * @return 结果
     */
    @Override
    public int deleteContractRiskPointByIds(Long[] ids)
    {
        return contractRiskPointMapper.deleteContractRiskPointByIds(ids);
    }

    /**
     * 删除合同风险点信息
     * 
     * @param id 合同风险点主键
     * @return 结果
     */
    @Override
    public int deleteContractRiskPointById(Long id)
    {
        return contractRiskPointMapper.deleteContractRiskPointById(id);
    }
}
