package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractCategoryMapper;
import com.ruoyi.contract.domain.ContractCategory;
import com.ruoyi.contract.service.IContractCategoryService;

/**
 * 合同分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractCategoryServiceImpl implements IContractCategoryService 
{
    @Autowired
    private ContractCategoryMapper contractCategoryMapper;

    /**
     * 查询合同分类
     * 
     * @param id 合同分类主键
     * @return 合同分类
     */
    @Override
    public ContractCategory selectContractCategoryById(Long id)
    {
        return contractCategoryMapper.selectContractCategoryById(id);
    }

    /**
     * 查询合同分类列表
     * 
     * @param contractCategory 合同分类
     * @return 合同分类
     */
    @Override
    public List<ContractCategory> selectContractCategoryList(ContractCategory contractCategory)
    {
        return contractCategoryMapper.selectContractCategoryList(contractCategory);
    }

    /**
     * 新增合同分类
     * 
     * @param contractCategory 合同分类
     * @return 结果
     */
    @Override
    public int insertContractCategory(ContractCategory contractCategory)
    {
        contractCategory.setCreateTime(DateUtils.getNowDate());
        contractCategory.setDelFlag("0");
        return contractCategoryMapper.insertContractCategory(contractCategory);
    }

    /**
     * 修改合同分类
     * 
     * @param contractCategory 合同分类
     * @return 结果
     */
    @Override
    public int updateContractCategory(ContractCategory contractCategory)
    {
        contractCategory.setUpdateTime(DateUtils.getNowDate());
        return contractCategoryMapper.updateContractCategory(contractCategory);
    }

    /**
     * 批量删除合同分类
     * 
     * @param ids 需要删除的合同分类主键
     * @return 结果
     */
    @Override
    public int deleteContractCategoryByIds(Long[] ids)
    {
        return contractCategoryMapper.deleteContractCategoryByIds(ids);
    }

    /**
     * 删除合同分类信息
     * 
     * @param id 合同分类主键
     * @return 结果
     */
    @Override
    public int deleteContractCategoryById(Long id)
    {
        return contractCategoryMapper.deleteContractCategoryById(id);
    }
}
