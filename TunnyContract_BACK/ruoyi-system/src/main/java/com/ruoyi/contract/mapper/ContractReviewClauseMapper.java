package com.ruoyi.contract.mapper;

import java.util.List;
import com.ruoyi.contract.domain.ContractReviewClause;

/**
 * 合同审查条款Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface ContractReviewClauseMapper 
{
    /**
     * 查询合同审查条款
     * 
     * @param id 合同审查条款主键
     * @return 合同审查条款
     */
    public ContractReviewClause selectContractReviewClauseById(Long id);

    /**
     * 查询合同审查条款列表
     * 
     * @param contractReviewClause 合同审查条款
     * @return 合同审查条款集合
     */
    public List<ContractReviewClause> selectContractReviewClauseList(ContractReviewClause contractReviewClause);

    /**
     * 新增合同审查条款
     * 
     * @param contractReviewClause 合同审查条款
     * @return 结果
     */
    public int insertContractReviewClause(ContractReviewClause contractReviewClause);

    /**
     * 修改合同审查条款
     * 
     * @param contractReviewClause 合同审查条款
     * @return 结果
     */
    public int updateContractReviewClause(ContractReviewClause contractReviewClause);

    /**
     * 删除合同审查条款
     * 
     * @param id 合同审查条款主键
     * @return 结果
     */
    public int deleteContractReviewClauseById(Long id);

    /**
     * 批量删除合同审查条款
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContractReviewClauseByIds(Long[] ids);
}
