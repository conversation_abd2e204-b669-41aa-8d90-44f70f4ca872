package com.ruoyi.contract.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractReviewStrategyMapper;
import com.ruoyi.contract.mapper.ContractReviewClauseMapper;
import com.ruoyi.contract.mapper.ContractRiskPointMapper;
import com.ruoyi.contract.domain.ContractReviewStrategy;
import com.ruoyi.contract.domain.ContractReviewClause;
import com.ruoyi.contract.domain.ContractRiskPoint;
import com.ruoyi.contract.dto.StrategyClauseRiskDTO;
import com.ruoyi.contract.service.IContractReviewStrategyService;

/**
 * 合同审查策略Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractReviewStrategyServiceImpl implements IContractReviewStrategyService 
{
    @Autowired
    private ContractReviewStrategyMapper contractReviewStrategyMapper;

    @Autowired
    private ContractReviewClauseMapper contractReviewClauseMapper;

    @Autowired
    private ContractRiskPointMapper contractRiskPointMapper;

    /**
     * 查询合同审查策略
     * 
     * @param id 合同审查策略主键
     * @return 合同审查策略
     */
    @Override
    public ContractReviewStrategy selectContractReviewStrategyById(Long id)
    {
        return contractReviewStrategyMapper.selectContractReviewStrategyById(id);
    }

    /**
     * 查询合同审查策略列表
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 合同审查策略
     */
    @Override
    public List<ContractReviewStrategy> selectContractReviewStrategyList(ContractReviewStrategy contractReviewStrategy)
    {
        return contractReviewStrategyMapper.selectContractReviewStrategyList(contractReviewStrategy);
    }

    /**
     * 新增合同审查策略
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 结果
     */
    @Override
    public int insertContractReviewStrategy(ContractReviewStrategy contractReviewStrategy)
    {
        contractReviewStrategy.setCreateTime(DateUtils.getNowDate());
        return contractReviewStrategyMapper.insertContractReviewStrategy(contractReviewStrategy);
    }

    /**
     * 修改合同审查策略
     * 
     * @param contractReviewStrategy 合同审查策略
     * @return 结果
     */
    @Override
    public int updateContractReviewStrategy(ContractReviewStrategy contractReviewStrategy)
    {
        contractReviewStrategy.setUpdateTime(DateUtils.getNowDate());
        return contractReviewStrategyMapper.updateContractReviewStrategy(contractReviewStrategy);
    }

    /**
     * 批量删除合同审查策略
     * 
     * @param ids 需要删除的合同审查策略主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewStrategyByIds(Long[] ids)
    {
        return contractReviewStrategyMapper.deleteContractReviewStrategyByIds(ids);
    }

    /**
     * 删除合同审查策略信息
     *
     * @param id 合同审查策略主键
     * @return 结果
     */
    @Override
    public int deleteContractReviewStrategyById(Long id)
    {
        return contractReviewStrategyMapper.deleteContractReviewStrategyById(id);
    }

    /**
     * 发布策略
     *
     * @param id 策略主键
     * @return 结果
     */
    @Override
    public int publishStrategy(Long id)
    {
        ContractReviewStrategy strategy = new ContractReviewStrategy();
        strategy.setId(id);
        strategy.setStrategyStatus("1"); // 已发布
        strategy.setUpdateTime(DateUtils.getNowDate());
        strategy.setUpdateBy(SecurityUtils.getUsername());
        return contractReviewStrategyMapper.updateContractReviewStrategy(strategy);
    }

    /**
     * 取消发布策略（设为草稿）
     *
     * @param id 策略主键
     * @return 结果
     */
    @Override
    public int unpublishStrategy(Long id)
    {
        ContractReviewStrategy strategy = new ContractReviewStrategy();
        strategy.setId(id);
        strategy.setStrategyStatus("0"); // 草稿
        strategy.setUpdateTime(DateUtils.getNowDate());
        strategy.setUpdateBy(SecurityUtils.getUsername());
        return contractReviewStrategyMapper.updateContractReviewStrategy(strategy);
    }

    /**
     * 复制策略
     *
     * @param id 源策略主键
     * @param strategyName 新策略名称
     * @return 结果
     */
    @Override
    public int copyStrategy(Long id, String strategyName)
    {
        // 获取源策略
        ContractReviewStrategy sourceStrategy = contractReviewStrategyMapper.selectContractReviewStrategyById(id);
        if (sourceStrategy == null) {
            return 0;
        }

        // 创建新策略
        ContractReviewStrategy newStrategy = new ContractReviewStrategy();
        BeanUtils.copyProperties(sourceStrategy, newStrategy);

        // 重置关键字段
        newStrategy.setId(null);
        newStrategy.setStrategyName(strategyName);
        newStrategy.setStrategyStatus("0"); // 复制的策略默认为草稿
        newStrategy.setIsDefault("0"); // 复制的策略不能是默认策略
        newStrategy.setVersion("1.0"); // 重置版本号
        newStrategy.setCreateTime(DateUtils.getNowDate());
        newStrategy.setCreateBy(SecurityUtils.getUsername());
        newStrategy.setUpdateTime(DateUtils.getNowDate());
        newStrategy.setUpdateBy(SecurityUtils.getUsername());

        return contractReviewStrategyMapper.insertContractReviewStrategy(newStrategy);
    }

    /**
     * 获取策略统计信息
     *
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getStrategyStats()
    {
        Map<String, Object> stats = new HashMap<>();

        // 查询总数
        ContractReviewStrategy queryParam = new ContractReviewStrategy();
        List<ContractReviewStrategy> allStrategies = contractReviewStrategyMapper.selectContractReviewStrategyList(queryParam);

        int totalCount = allStrategies.size();
        int draftCount = 0;
        int publishedCount = 0;
        int partyACount = 0;
        int partyBCount = 0;

        for (ContractReviewStrategy strategy : allStrategies) {
            // 统计状态
            if ("0".equals(strategy.getStrategyStatus())) {
                draftCount++;
            } else if ("1".equals(strategy.getStrategyStatus())) {
                publishedCount++;
            }

            // 统计立场
            if ("1".equals(strategy.getReviewPosition())) {
                partyACount++;
            } else if ("2".equals(strategy.getReviewPosition())) {
                partyBCount++;
            }
        }

        stats.put("totalCount", totalCount);
        stats.put("draftCount", draftCount);
        stats.put("publishedCount", publishedCount);
        stats.put("partyACount", partyACount);
        stats.put("partyBCount", partyBCount);

        return stats;
    }

    /**
     * 根据策略ID获取关联的审查条款清单及每个条款对应的风险点列表
     *
     * @param strategyId 审查策略ID
     * @return 条款和风险点列表
     */
    @Override
    public List<StrategyClauseRiskDTO> getStrategyClausesWithRiskPoints(Long strategyId)
    {
        List<StrategyClauseRiskDTO> result = new ArrayList<>();
        
        // 根据策略ID查询关联的审查条款，只返回启用状态的条款
        ContractReviewClause clauseQuery = new ContractReviewClause();
        clauseQuery.setStrategyId(strategyId);
        clauseQuery.setClauseStatus("1"); // 只查询启用状态的条款
        List<ContractReviewClause> clauses = contractReviewClauseMapper.selectContractReviewClauseList(clauseQuery);
        
        // 为每个条款查询对应的风险点
        for (ContractReviewClause clause : clauses) {
            StrategyClauseRiskDTO dto = new StrategyClauseRiskDTO();
            
            // 设置条款信息
            dto.setClauseId(clause.getId());
            dto.setClauseName(clause.getClauseName());
            dto.setClauseDesc(clause.getClauseDesc());
            dto.setClauseContent(clause.getClauseContent());
            dto.setSortOrder(clause.getSortOrder());
            dto.setClauseStatus(clause.getClauseStatus());
            
            // 根据条款ID查询关联的风险点
            ContractRiskPoint riskQuery = new ContractRiskPoint();
            riskQuery.setClauseId(clause.getId());
            List<ContractRiskPoint> riskPoints = contractRiskPointMapper.selectContractRiskPointList(riskQuery);
            dto.setRiskPoints(riskPoints);
            
            result.add(dto);
        }
        
        return result;
    }
}
