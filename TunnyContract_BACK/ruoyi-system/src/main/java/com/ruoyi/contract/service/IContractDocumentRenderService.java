package com.ruoyi.contract.service;

import com.ruoyi.contract.domain.ContractFile;

/**
 * 合同文档渲染服务接口
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface IContractDocumentRenderService 
{
    /**
     * 将Word文档转换为PDF
     * 
     * @param contractFile 合同文件信息
     * @return PDF文件的访问URL
     * @throws Exception 转换异常
     */
    String convertWordToPdf(ContractFile contractFile) throws Exception;
    
    /**
     * 检查PDF转换缓存是否存在
     * 
     * @param fileMd5 文件MD5值
     * @return 缓存的PDF访问路径，如果不存在返回null
     */
    String checkPdfCache(String fileMd5);
    
    /**
     * 获取文档预览URL
     * 
     * @param contractFile 合同文件
     * @return 预览URL
     * @throws Exception 处理异常
     */
    String getDocumentPreviewUrl(ContractFile contractFile) throws Exception;
    
    /**
     * 清理临时文件和缓存
     * 
     * @param fileMd5 文件MD5值
     */
    void cleanupTempFiles(String fileMd5);
    
    /**
     * 检查文档是否支持在线预览
     * 
     * @param fileType 文件类型
     * @return 是否支持预览
     */
    boolean isSupportedForPreview(String fileType);
}