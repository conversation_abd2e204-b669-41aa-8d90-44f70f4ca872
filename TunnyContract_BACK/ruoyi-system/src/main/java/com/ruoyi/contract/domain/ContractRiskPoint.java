package com.ruoyi.contract.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同风险点对象 contract_risk_point
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractRiskPoint extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 风险点ID */
    private Long id;

    /** 关联条款ID */
    @Excel(name = "关联条款ID")
    private Long clauseId;

    /** 风险点名称 */
    @Excel(name = "风险点名称")
    private String riskName;

    /** 风险点描述 */
    @Excel(name = "风险点描述")
    private String riskDesc;

    /** 风险等级(1-重大风险，2-一般风险) */
    @Excel(name = "风险等级(1-重大风险，2-一般风险)")
    private String riskLevel;

    /** 风险分析 */
    @Excel(name = "风险分析")
    private String riskAnalysis;

    /** 修改建议 */
    @Excel(name = "修改建议")
    private String suggestModify;

    /** 关键词匹配模式 */
    @Excel(name = "关键词匹配模式")
    private String keywordPattern;

    /** 排序序号 */
    @Excel(name = "排序序号")
    private Long sortOrder;

    /** 风险点状态(1-启用，0-禁用) */
    @Excel(name = "风险点状态(1-启用，0-禁用)")
    private String riskStatus;

    @JsonIgnore
    /** 0-正常，2-删除 */
    private String delFlag;
    @JsonIgnore
    /** 保留字段1 */
    @Excel(name = "保留字段1")
    private String attr1;
    @JsonIgnore
    /** 保留字段2 */
    @Excel(name = "保留字段2")
    private String attr2;
    @JsonIgnore
    /** 保留字段3 */
    @Excel(name = "保留字段3")
    private String attr3;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setClauseId(Long clauseId) 
    {
        this.clauseId = clauseId;
    }

    public Long getClauseId() 
    {
        return clauseId;
    }

    public void setRiskName(String riskName) 
    {
        this.riskName = riskName;
    }

    public String getRiskName() 
    {
        return riskName;
    }

    public void setRiskDesc(String riskDesc) 
    {
        this.riskDesc = riskDesc;
    }

    public String getRiskDesc() 
    {
        return riskDesc;
    }

    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    public void setRiskAnalysis(String riskAnalysis) 
    {
        this.riskAnalysis = riskAnalysis;
    }

    public String getRiskAnalysis() 
    {
        return riskAnalysis;
    }

    public void setSuggestModify(String suggestModify) 
    {
        this.suggestModify = suggestModify;
    }

    public String getSuggestModify() 
    {
        return suggestModify;
    }

    public void setKeywordPattern(String keywordPattern) 
    {
        this.keywordPattern = keywordPattern;
    }

    public String getKeywordPattern() 
    {
        return keywordPattern;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    public void setRiskStatus(String riskStatus) 
    {
        this.riskStatus = riskStatus;
    }

    public String getRiskStatus() 
    {
        return riskStatus;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3() 
    {
        return attr3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("clauseId", getClauseId())
            .append("riskName", getRiskName())
            .append("riskDesc", getRiskDesc())
            .append("riskLevel", getRiskLevel())
            .append("riskAnalysis", getRiskAnalysis())
            .append("suggestModify", getSuggestModify())
            .append("keywordPattern", getKeywordPattern())
            .append("sortOrder", getSortOrder())
            .append("riskStatus", getRiskStatus())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .toString();
    }
}
