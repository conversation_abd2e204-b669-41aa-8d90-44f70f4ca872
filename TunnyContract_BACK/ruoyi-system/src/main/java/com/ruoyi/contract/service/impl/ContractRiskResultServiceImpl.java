package com.ruoyi.contract.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.contract.mapper.ContractRiskResultMapper;
import com.ruoyi.contract.domain.ContractRiskResult;
import com.ruoyi.contract.service.IContractRiskResultService;

/**
 * 合同风险识别结果Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
public class ContractRiskResultServiceImpl implements IContractRiskResultService 
{
    @Autowired
    private ContractRiskResultMapper contractRiskResultMapper;

    /**
     * 查询合同风险识别结果
     * 
     * @param id 合同风险识别结果主键
     * @return 合同风险识别结果
     */
    @Override
    public ContractRiskResult selectContractRiskResultById(Long id)
    {
        return contractRiskResultMapper.selectContractRiskResultById(id);
    }

    /**
     * 查询合同风险识别结果列表
     * 
     * @param contractRiskResult 合同风险识别结果
     * @return 合同风险识别结果
     */
    @Override
    public List<ContractRiskResult> selectContractRiskResultList(ContractRiskResult contractRiskResult)
    {
        return contractRiskResultMapper.selectContractRiskResultList(contractRiskResult);
    }

    /**
     * 新增合同风险识别结果
     * 
     * @param contractRiskResult 合同风险识别结果
     * @return 结果
     */
    @Override
    public int insertContractRiskResult(ContractRiskResult contractRiskResult)
    {
        contractRiskResult.setCreateTime(DateUtils.getNowDate());
        return contractRiskResultMapper.insertContractRiskResult(contractRiskResult);
    }

    /**
     * 修改合同风险识别结果
     * 
     * @param contractRiskResult 合同风险识别结果
     * @return 结果
     */
    @Override
    public int updateContractRiskResult(ContractRiskResult contractRiskResult)
    {
        contractRiskResult.setUpdateTime(DateUtils.getNowDate());
        return contractRiskResultMapper.updateContractRiskResult(contractRiskResult);
    }

    /**
     * 批量删除合同风险识别结果
     * 
     * @param ids 需要删除的合同风险识别结果主键
     * @return 结果
     */
    @Override
    public int deleteContractRiskResultByIds(Long[] ids)
    {
        return contractRiskResultMapper.deleteContractRiskResultByIds(ids);
    }

    /**
     * 删除合同风险识别结果信息
     * 
     * @param id 合同风险识别结果主键
     * @return 结果
     */
    @Override
    public int deleteContractRiskResultById(Long id)
    {
        return contractRiskResultMapper.deleteContractRiskResultById(id);
    }
}
