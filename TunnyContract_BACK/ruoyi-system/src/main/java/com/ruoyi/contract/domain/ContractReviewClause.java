package com.ruoyi.contract.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 合同审查条款对象 contract_review_clause
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public class ContractReviewClause extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 条款ID */
    private Long id;

    /** 关联策略ID */
    @Excel(name = "关联策略ID")
    private Long strategyId;

    /** 条款名称 */
    @Excel(name = "条款名称")
    private String clauseName;

    /** 条款说明 */
    @Excel(name = "条款说明")
    private String clauseDesc;

    /** 条款详细内容 */
    @Excel(name = "条款详细内容")
    private String clauseContent;

    /** 排序序号 */
    @Excel(name = "排序序号")
    private Long sortOrder;

    /** 条款状态(1-启用，0-禁用) */
    @Excel(name = "条款状态(1-启用，0-禁用)")
    private String clauseStatus;

    /** 0-正常，2-删除 */
    private String delFlag;

    /** 保留字段1 */
    @Excel(name = "保留字段1")
    private String attr1;

    /** 保留字段2 */
    @Excel(name = "保留字段2")
    private String attr2;

    /** 保留字段3 */
    @Excel(name = "保留字段3")
    private String attr3;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStrategyId(Long strategyId) 
    {
        this.strategyId = strategyId;
    }

    public Long getStrategyId() 
    {
        return strategyId;
    }

    public void setClauseName(String clauseName) 
    {
        this.clauseName = clauseName;
    }

    public String getClauseName() 
    {
        return clauseName;
    }

    public void setClauseDesc(String clauseDesc) 
    {
        this.clauseDesc = clauseDesc;
    }

    public String getClauseDesc() 
    {
        return clauseDesc;
    }

    public void setClauseContent(String clauseContent) 
    {
        this.clauseContent = clauseContent;
    }

    public String getClauseContent() 
    {
        return clauseContent;
    }

    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }

    public void setClauseStatus(String clauseStatus) 
    {
        this.clauseStatus = clauseStatus;
    }

    public String getClauseStatus() 
    {
        return clauseStatus;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setAttr1(String attr1) 
    {
        this.attr1 = attr1;
    }

    public String getAttr1() 
    {
        return attr1;
    }

    public void setAttr2(String attr2) 
    {
        this.attr2 = attr2;
    }

    public String getAttr2() 
    {
        return attr2;
    }

    public void setAttr3(String attr3) 
    {
        this.attr3 = attr3;
    }

    public String getAttr3() 
    {
        return attr3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("strategyId", getStrategyId())
            .append("clauseName", getClauseName())
            .append("clauseDesc", getClauseDesc())
            .append("clauseContent", getClauseContent())
            .append("sortOrder", getSortOrder())
            .append("clauseStatus", getClauseStatus())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("delFlag", getDelFlag())
            .append("attr1", getAttr1())
            .append("attr2", getAttr2())
            .append("attr3", getAttr3())
            .toString();
    }
}
