package com.ruoyi.system.service.callback;

import io.github.imfangs.dify.client.callback.WorkflowStreamCallback;
import io.github.imfangs.dify.client.event.*;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Consumer;

/**
 * 默认工作流流式回调处理器
 * 提供常用的回调逻辑封装，简化用户使用
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class DefaultWorkflowStreamCallback implements WorkflowStreamCallback {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultWorkflowStreamCallback.class);
    
    private final Consumer<String> onMessage;
    private final Consumer<WorkflowRunResponse> onComplete;
    private final Consumer<String> onError;
    private final boolean enableDebugLog;
    
    /**
     * 构造函数
     * 
     * @param onMessage 消息回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     */
    public DefaultWorkflowStreamCallback(Consumer<String> onMessage, 
                                       Consumer<WorkflowRunResponse> onComplete, 
                                       Consumer<String> onError) {
        this(onMessage, onComplete, onError, false);
    }
    
    /**
     * 构造函数
     * 
     * @param onMessage 消息回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     * @param enableDebugLog 是否启用调试日志
     */
    public DefaultWorkflowStreamCallback(Consumer<String> onMessage, 
                                       Consumer<WorkflowRunResponse> onComplete, 
                                       Consumer<String> onError,
                                       boolean enableDebugLog) {
        this.onMessage = onMessage;
        this.onComplete = onComplete;
        this.onError = onError;
        this.enableDebugLog = enableDebugLog;
    }
    
    @Override
    public void onWorkflowStarted(WorkflowStartedEvent event) {
        if (enableDebugLog) {
            log.debug("工作流开始执行: {}", event);
        }
        log.info("工作流已启动，任务ID: {}", event != null ? event.getTaskId() : "unknown");
    }
    
    @Override
    public void onNodeStarted(NodeStartedEvent event) {
        if (enableDebugLog) {
            log.debug("节点开始执行: {}", event);
        }
        if (event != null) {
            log.info("节点开始执行: {}", event.toString());
        }
    }
    
    @Override
    public void onNodeFinished(NodeFinishedEvent event) {
        if (enableDebugLog) {
            log.debug("节点执行完成: {}", event);
        }
        
        if (event != null) {
            log.info("节点执行完成: {}", event.toString());
            
            // 提取消息内容并调用onMessage回调
            if (onMessage != null) {
                try {
                    // 根据实际的事件对象结构来提取消息
                    String message = extractMessageFromNodeEvent(event);
                    if (message != null && !message.trim().isEmpty()) {
                        onMessage.accept(message);
                    }
                } catch (Exception e) {
                    log.warn("处理节点完成事件失败: {}", e.getMessage());
                }
            }
        }
    }
    
    @Override
    public void onWorkflowFinished(WorkflowFinishedEvent event) {
        if (enableDebugLog) {
            log.debug("工作流执行完成: {}", event);
        }
        
        log.info("工作流执行完成");
        
        if (onComplete != null && event != null) {
            try {
                // 根据实际情况构造WorkflowRunResponse
                WorkflowRunResponse response = buildWorkflowResponse(event);
                onComplete.accept(response);
            } catch (Exception e) {
                log.warn("处理工作流完成事件失败: {}", e.getMessage());
            }
        }
    }
    
    @Override
    public void onError(ErrorEvent event) {
        log.error("工作流执行出错: {}", event);
        
        if (onError != null && event != null) {
            try {
                String errorMessage = extractErrorMessage(event);
                onError.accept(errorMessage);
            } catch (Exception e) {
                log.warn("处理错误事件失败: {}", e.getMessage());
                onError.accept("工作流执行出错，详情请查看日志");
            }
        }
    }
    
    @Override
    public void onException(Throwable throwable) {
        log.error("工作流执行异常: {}", throwable.getMessage(), throwable);
        
        if (onError != null) {
            String errorMessage = throwable.getMessage() != null ? 
                throwable.getMessage() : "工作流执行发生未知异常";
            onError.accept(errorMessage);
        }
    }
    
    /**
     * 从节点事件中提取消息内容
     * 
     * @param event 节点完成事件
     * @return 消息内容
     */
    private String extractMessageFromNodeEvent(NodeFinishedEvent event) {
        try {
            // 这里需要根据实际的事件对象结构来提取消息
            // 由于具体的事件结构可能因版本而异，这里提供一个通用的实现
            if (event.getData() != null) {
                return event.getData().toString();
            }
            return event.toString();
        } catch (Exception e) {
            log.warn("提取节点消息失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 构建工作流响应对象
     * 
     * @param event 工作流完成事件
     * @return 工作流响应
     */
    private WorkflowRunResponse buildWorkflowResponse(WorkflowFinishedEvent event) {
        try {
            // 这里需要根据实际的事件对象结构来构造响应
            // 由于具体的事件结构可能因版本而异，这里提供一个基础实现
            // 暂时返回null，用户可以根据实际需要自定义实现
            log.info("工作流完成事件: {}", event.toString());
            return null;
        } catch (Exception e) {
            log.warn("构建工作流响应失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从错误事件中提取错误消息
     * 
     * @param event 错误事件
     * @return 错误消息
     */
    private String extractErrorMessage(ErrorEvent event) {
        try {
            if (event.getMessage() != null) {
                return event.getMessage();
            }
            if (event.getCode() != null) {
                return "错误代码: " + event.getCode();
            }
            return event.toString();
        } catch (Exception e) {
            log.warn("提取错误消息失败: {}", e.getMessage());
            return "工作流执行出错，详情请查看日志";
        }
    }
    
    /**
     * 创建简单的回调处理器
     * 只处理消息和错误，忽略完成事件
     * 
     * @param onMessage 消息回调
     * @param onError 错误回调
     * @return 回调处理器
     */
    public static DefaultWorkflowStreamCallback simple(Consumer<String> onMessage, Consumer<String> onError) {
        return new DefaultWorkflowStreamCallback(onMessage, null, onError);
    }
    
    /**
     * 创建完整的回调处理器
     * 
     * @param onMessage 消息回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     * @return 回调处理器
     */
    public static DefaultWorkflowStreamCallback full(Consumer<String> onMessage, 
                                                   Consumer<WorkflowRunResponse> onComplete, 
                                                   Consumer<String> onError) {
        return new DefaultWorkflowStreamCallback(onMessage, onComplete, onError);
    }
    
    /**
     * 创建调试模式的回调处理器
     * 
     * @param onMessage 消息回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     * @return 回调处理器
     */
    public static DefaultWorkflowStreamCallback debug(Consumer<String> onMessage, 
                                                     Consumer<WorkflowRunResponse> onComplete, 
                                                     Consumer<String> onError) {
        return new DefaultWorkflowStreamCallback(onMessage, onComplete, onError, true);
    }
}