package com.ruoyi.system.service;



import io.github.imfangs.dify.client.model.chat.ChatMessageResponse;
import io.github.imfangs.dify.client.model.completion.CompletionResponse;
import io.github.imfangs.dify.client.model.datasets.RetrieveResponse;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import io.github.imfangs.dify.client.callback.WorkflowStreamCallback;

import java.util.Map;
import java.util.function.Consumer;

/**
 * Dify服务接口
 * 
 * <AUTHOR>
 */
public interface IDifyService
{
    /**
     * 发送对话消息
     *
     * @param agentName 智能体名称
     * @param query 查询内容
     * @param user 用户标识
     * @param conversationId 会话ID（可选）
     * @return 对话响应
     */
    ChatMessageResponse sendChatMessage(String agentName, String query, String user, String conversationId);

    /**
     * 发送文本生成请求
     *
     * @param agentName 智能体名称
     * @param inputs 输入参数
     * @param user 用户标识
     * @return 文本生成响应
     */
    CompletionResponse sendCompletionMessage(String agentName, Map<String, Object> inputs, String user);

    /**
     * 运行工作流（阻塞模式）
     *
     * @param agentName 智能体名称
     * @param inputs 输入参数
     * @param user 用户标识
     * @return 工作流执行响应
     */
    WorkflowRunResponse runWorkflow(String agentName, Map<String, Object> inputs, String user);

    /**
     * 运行工作流（流式模式）
     * @param agentName 智能体名称
     * @param inputs 输入参数
     * @param user 用户标识
     * @param callback 流式回调处理器
     */
    void runWorkflowStream(String agentName, Map<String, Object> inputs, String user, WorkflowStreamCallback callback);

    /**
     * 运行工作流（流式模式，使用默认回调处理器）
     * @param agentName 智能体名称
     * @param inputs 输入参数
     * @param user 用户标识
     * @param onMessage 消息处理函数
     * @param onComplete 完成处理函数
     * @param onError 错误处理函数
     */
    void runWorkflowStream(String agentName, Map<String, Object> inputs, String user, 
                          Consumer<String> onMessage, 
                          Consumer<WorkflowRunResponse> onComplete, 
                          Consumer<String> onError);

    /**
     * 检索知识库
     *
     * @param agentName 智能体名称
     * @param datasetId 知识库ID
     * @param query 查询内容
     * @param topK 检索数量
     * @param scoreThreshold 相似度阈值
     * @return 检索响应
     */
    RetrieveResponse retrieveDataset(String agentName, String datasetId, String query, Integer topK, Float scoreThreshold);

    /**
     * 获取会话历史消息
     *
     * @param agentName 智能体名称
     * @param conversationId 会话ID
     * @param user 用户标识
     * @param firstId 起始消息ID
     * @param limit 限制数量
     * @return 消息列表响应
     */
    Object getMessages(String agentName, String conversationId, String user, String firstId, Integer limit);

    /**
     * 获取会话列表
     *
     * @param agentName 智能体名称
     * @param user 用户标识
     * @param lastId 最后一个会话ID
     * @param limit 限制数量
     * @param pinned 是否置顶
     * @return 会话列表响应
     */
    Object getConversations(String agentName, String user, String lastId, Integer limit, String pinned);

    /**
     * 重命名会话
     *
     * @param agentName 智能体名称
     * @param conversationId 会话ID
     * @param name 新名称
     * @param autoGenerate 是否自动生成
     * @param user 用户标识
     * @return 会话信息
     */
    Object renameConversation(String agentName, String conversationId, String name, Boolean autoGenerate, String user);

    /**
     * 删除会话
     *
     * @param agentName 智能体名称
     * @param conversationId 会话ID
     * @param user 用户标识
     * @return 删除结果
     */
    Object deleteConversation(String agentName, String conversationId, String user);

    /**
     * 消息反馈
     *
     * @param agentName 智能体名称
     * @param messageId 消息ID
     * @param rating 评分（like/dislike）
     * @param user 用户标识
     * @param content 反馈内容
     * @return 反馈结果
     */
    Object feedbackMessage(String agentName, String messageId, String rating, String user, String content);

    /**
     * 获取建议问题
     *
     * @param agentName 智能体名称
     * @param messageId 消息ID
     * @param user 用户标识
     * @return 建议问题列表
     */
    Object getSuggestedQuestions(String agentName, String messageId, String user);

    /**
     * 获取所有启用的智能体信息
     *
     * @return 智能体信息列表
     */
    Map<String, Object> getEnabledAgents();
}
