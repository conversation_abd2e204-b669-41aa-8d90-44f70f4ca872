package com.ruoyi.system.service.impl;

import com.ruoyi.common.constant.DifyConstants;
import com.ruoyi.common.exception.dify.DifyException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.dify.DifyClientUtil;
import com.ruoyi.common.config.DifyProperties;
import com.ruoyi.system.service.IDifyService;
import io.github.imfangs.dify.client.DifyChatClient;
import io.github.imfangs.dify.client.DifyCompletionClient;
import io.github.imfangs.dify.client.DifyWorkflowClient;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.model.chat.ChatMessage;
import io.github.imfangs.dify.client.model.chat.ChatMessageResponse;
import io.github.imfangs.dify.client.model.completion.CompletionRequest;
import io.github.imfangs.dify.client.model.completion.CompletionResponse;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunRequest;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import io.github.imfangs.dify.client.callback.WorkflowStreamCallback;
import com.ruoyi.system.service.callback.DefaultWorkflowStreamCallback;
import io.github.imfangs.dify.client.model.datasets.RetrieveRequest;
import io.github.imfangs.dify.client.model.datasets.RetrieveResponse;
import io.github.imfangs.dify.client.model.datasets.RetrievalModel;
import io.github.imfangs.dify.client.enums.ResponseMode;
import io.github.imfangs.dify.client.event.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;

/**
 * Dify服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DifyServiceImpl implements IDifyService
{
    private static final Logger log = LoggerFactory.getLogger(DifyServiceImpl.class);

    @Autowired
    private DifyProperties difyProperties;

    @Override
    public ChatMessageResponse sendChatMessage(String agentName, String query, String user, String conversationId)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(query))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "查询内容不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);

            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();

            ChatMessage.ChatMessageBuilder builder = ChatMessage.builder()
                    .query(query)
                    .user(actualUser)
                    .responseMode(ResponseMode.BLOCKING);

            if (StringUtils.isNotEmpty(conversationId))
            {
                builder.conversationId(conversationId);
            }

            ChatMessage message = builder.build();
            ChatMessageResponse response = chatClient.sendChatMessage(message);

            log.info("发送对话消息成功，智能体: {}, 用户: {}, 会话ID: {}, 消息ID: {}", agentName, actualUser, conversationId, response.getMessageId());
            return response;
        }
        catch (Exception e)
        {
            log.error("发送对话消息失败，智能体: {}, 用户: {}, 查询: {}", agentName, user, query, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "发送对话消息失败", e);
        }
    }

    @Override
    public CompletionResponse sendCompletionMessage(String agentName, Map<String, Object> inputs, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (inputs == null || inputs.isEmpty())
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "输入参数不能为空");
        }

        try
        {
            DifyCompletionClient completionClient = DifyClientUtil.getCompletionClient(agentName);

            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();

            CompletionRequest request = CompletionRequest.builder()
                    .inputs(inputs)
                    .responseMode(ResponseMode.BLOCKING)
                    .user(actualUser)
                    .build();

            CompletionResponse response = completionClient.sendCompletionMessage(request);

            log.info("发送文本生成请求成功，智能体: {}, 用户: {}, 消息ID: {}", agentName, actualUser, response.getMessageId());
            return response;
        }
        catch (Exception e)
        {
            log.error("发送文本生成请求失败，智能体: {}, 用户: {}, 输入: {}", agentName, user, inputs, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "发送文本生成请求失败", e);
        }
    }

    @Override
    public WorkflowRunResponse runWorkflow(String agentName, Map<String, Object> inputs, String user)
    {
        if (inputs == null || inputs.isEmpty())
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "输入参数不能为空");
        }

        try
        {
            DifyWorkflowClient workflowClient = DifyClientUtil.getWorkflowClient(agentName);
            
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            
            WorkflowRunRequest request = WorkflowRunRequest.builder()
                    .inputs(inputs)
                    .responseMode(ResponseMode.BLOCKING)
                    .user(actualUser)
                    .build();

            WorkflowRunResponse response = workflowClient.runWorkflow(request);
            
            log.info("执行工作流成功，用户: {}, 任务ID: {}", actualUser, response.getTaskId());
            return response;
        }
        catch (Exception e)
        {
            log.error("执行工作流失败，用户: {}, 输入: {}", user, inputs, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "执行工作流失败", e);
        }
    }

    @Override
    public void runWorkflowStream(String agentName, Map<String, Object> inputs, String user, WorkflowStreamCallback callback)
    {
        if (inputs == null || inputs.isEmpty())
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "输入参数不能为空");
        }
        if (callback == null)
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "回调处理器不能为空");
        }

        try
        {
            DifyWorkflowClient workflowClient = DifyClientUtil.getWorkflowClient(agentName);
            
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            
            log.info("开始执行流式工作流，智能体: {}, 用户: {}, 输入参数数量: {}", agentName, actualUser, inputs.size());
            
            WorkflowRunRequest request = WorkflowRunRequest.builder()
                    .inputs(inputs)
                    .responseMode(ResponseMode.STREAMING)
                    .user(actualUser)
                    .build();

            log.debug("工作流请求构建完成，开始调用Dify API，智能体: {}", agentName);
            workflowClient.runWorkflowStream(request, callback);
            
        }
        catch (IllegalArgumentException e)
        {
            log.error("工作流参数验证失败，智能体: {}, 用户: {}, 错误: {}", agentName, user, e.getMessage());
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "工作流参数验证失败", e);
        }
        catch (Exception e)
        {
            log.error("执行流式工作流失败，智能体: {}, 用户: {}, 输入: {}, 错误: {}", agentName, user, inputs, e.getMessage(), e);
            
            // 尝试通过回调通知错误
            try
            {
                if (callback != null)
                {
                    callback.onException(e);
                }
            }
            catch (Exception callbackException)
            {
                log.warn("回调错误处理失败，智能体: {}, 用户: {}, 错误: {}", agentName, user, callbackException.getMessage());
            }
            
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "执行流式工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void runWorkflowStream(String agentName, Map<String, Object> inputs, String user,
                                 Consumer<String> onMessage,
                                 Consumer<WorkflowRunResponse> onComplete,
                                 Consumer<String> onError)
    {
        log.info("开始执行流式工作流调用（多回调），智能体: {}, 用户: {}", agentName, user);
        
        // 使用DefaultWorkflowStreamCallback处理器
        DefaultWorkflowStreamCallback callback = DefaultWorkflowStreamCallback.full(
            onMessage,
            (response) -> {
                if (onComplete != null) {
                    onComplete.accept(response);
                }
            },
            onError
        );

        // 调用流式工作流方法
        runWorkflowStream(agentName, inputs, user, callback);
    }

    @Override
    public Object getMessages(String agentName, String conversationId, String user, String firstId, Integer limit)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(conversationId))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "会话ID不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            Integer actualLimit = limit != null ? limit : DifyConstants.DefaultValues.DEFAULT_PAGE_SIZE;
            
            Object response = chatClient.getMessages(conversationId, actualUser, firstId, actualLimit);
            log.info("获取会话历史消息成功，会话ID: {}, 用户: {}", conversationId, actualUser);
            return response;
        }
        catch (Exception e)
        {
            log.error("获取会话历史消息失败，会话ID: {}, 用户: {}", conversationId, user, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "获取会话历史消息失败", e);
        }
    }

    @Override
    public Object getConversations(String agentName, String user, String lastId, Integer limit, String pinned)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            Integer actualLimit = limit != null ? limit : DifyConstants.DefaultValues.DEFAULT_PAGE_SIZE;
            
            Object response = chatClient.getConversations(actualUser, lastId, actualLimit, pinned);
            log.info("获取会话列表成功，用户: {}", actualUser);
            return response;
        }
        catch (Exception e)
        {
            log.error("获取会话列表失败，用户: {}", user, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "获取会话列表失败", e);
        }
    }

    @Override
    public Object renameConversation(String agentName, String conversationId, String name, Boolean autoGenerate, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(conversationId))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "会话ID不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            
            Object response = chatClient.renameConversation(conversationId, name, autoGenerate != null ? autoGenerate : false, actualUser);
            log.info("重命名会话成功，会话ID: {}, 新名称: {}, 用户: {}", conversationId, name, actualUser);
            return response;
        }
        catch (Exception e)
        {
            log.error("重命名会话失败，会话ID: {}, 用户: {}", conversationId, user, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "重命名会话失败", e);
        }
    }

    @Override
    public Object deleteConversation(String agentName, String conversationId, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(conversationId))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "会话ID不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            
            Object response = chatClient.deleteConversation(conversationId, actualUser);
            log.info("删除会话成功，会话ID: {}, 用户: {}", conversationId, actualUser);
            return response;
        }
        catch (Exception e)
        {
            log.error("删除会话失败，会话ID: {}, 用户: {}", conversationId, user, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "删除会话失败", e);
        }
    }

    @Override
    public Object feedbackMessage(String agentName, String messageId, String rating, String user, String content)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(messageId))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "消息ID不能为空");
        }
        if (StringUtils.isEmpty(rating))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "评分不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            
            Object response = chatClient.feedbackMessage(messageId, rating, actualUser, content);
            log.info("消息反馈成功，消息ID: {}, 评分: {}, 用户: {}", messageId, rating, actualUser);
            return response;
        }
        catch (Exception e)
        {
            log.error("消息反馈失败，消息ID: {}, 用户: {}", messageId, user, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "消息反馈失败", e);
        }
    }

    @Override
    public Object getSuggestedQuestions(String agentName, String messageId, String user)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(messageId))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "消息ID不能为空");
        }

        try
        {
            DifyChatClient chatClient = DifyClientUtil.getChatClient(agentName);
            String actualUser = StringUtils.isNotEmpty(user) ? user : difyProperties.getDefaultUser();
            
            Object response = chatClient.getSuggestedQuestions(messageId, actualUser);
            log.info("获取建议问题成功，消息ID: {}, 用户: {}", messageId, actualUser);
            return response;
        }
        catch (Exception e)
        {
            log.error("获取建议问题失败，消息ID: {}, 用户: {}", messageId, user, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "获取建议问题失败", e);
        }
    }

    @Override
    public RetrieveResponse retrieveDataset(String agentName, String datasetId, String query, Integer topK, Float scoreThreshold)
    {
        if (StringUtils.isEmpty(agentName))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "智能体名称不能为空");
        }
        if (StringUtils.isEmpty(datasetId))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "数据集ID不能为空");
        }
        if (StringUtils.isEmpty(query))
        {
            throw new DifyException(DifyConstants.ErrorCode.INVALID_PARAMS, "查询内容不能为空");
        }

        try
        {
            DifyDatasetsClient datasetsClient = DifyClientUtil.getDatasetsClient(agentName);

            RetrievalModel retrievalModel = new RetrievalModel();
            retrievalModel.setTopK(topK != null ? topK : DifyConstants.DefaultValues.DEFAULT_TOP_K);
            retrievalModel.setScoreThreshold(scoreThreshold != null ? scoreThreshold : DifyConstants.DefaultValues.DEFAULT_SCORE_THRESHOLD);

            RetrieveRequest request = RetrieveRequest.builder()
                    .query(query)
                    .retrievalModel(retrievalModel)
                    .build();

            RetrieveResponse response = datasetsClient.retrieveDataset(datasetId, request);
            log.info("检索知识库成功，智能体: {}, 数据集: {}, 查询: {}, 结果数量: {}",
                    agentName, datasetId, query, response.getRecords().size());
            return response;
        }
        catch (Exception e)
        {
            log.error("检索知识库失败，智能体: {}, 数据集: {}, 查询: {}", agentName, datasetId, query, e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "检索知识库失败", e);
        }
    }

    @Override
    public Map<String, Object> getEnabledAgents()
    {
        try
        {
            return DifyClientUtil.getEnabledAgents();
        }
        catch (Exception e)
        {
            log.error("获取启用的智能体信息失败", e);
            throw new DifyException(DifyConstants.ErrorCode.SERVICE_UNAVAILABLE, "获取智能体信息失败", e);
        }
    }
}
