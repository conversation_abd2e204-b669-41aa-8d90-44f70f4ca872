<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractCategoryMapper">
    
    <resultMap type="ContractCategory" id="ContractCategoryResult">
        <result property="id"    column="id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryDesc"    column="category_desc"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractCategoryVo">
        select id, category_name, category_desc, sort_order, status, create_time, create_by, update_time, update_by, del_flag, attr1, attr2, attr3 from contract_category
    </sql>

    <select id="selectContractCategoryList" parameterType="ContractCategory" resultMap="ContractCategoryResult">
        <include refid="selectContractCategoryVo"/>
        <where>
            del_flag = '0'
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryDesc != null  and categoryDesc != ''"> and category_desc = #{categoryDesc}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>
        </where>
        order by sort_order, id
    </select>
    
    <select id="selectContractCategoryById" parameterType="Long" resultMap="ContractCategoryResult">
        <include refid="selectContractCategoryVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <insert id="insertContractCategory" parameterType="ContractCategory" useGeneratedKeys="true" keyProperty="id">
        insert into contract_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryDesc != null">category_desc,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryDesc != null">#{categoryDesc},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractCategory" parameterType="ContractCategory">
        update contract_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryDesc != null">category_desc = #{categoryDesc},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteContractCategoryById" parameterType="Long">
        update contract_category set del_flag = '2' where id = #{id}
    </update>

    <update id="deleteContractCategoryByIds" parameterType="String">
        update contract_category set del_flag = '2' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>