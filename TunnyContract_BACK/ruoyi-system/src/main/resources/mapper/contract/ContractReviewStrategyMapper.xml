<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractReviewStrategyMapper">
    
    <resultMap type="ContractReviewStrategy" id="ContractReviewStrategyResult">
        <result property="id"    column="id"    />
        <result property="strategyName"    column="strategy_name"    />
        <result property="strategyDesc"    column="strategy_desc"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="reviewPosition"    column="review_position"    />
        <result property="strategyStatus"    column="strategy_status"    />
        <result property="version"    column="version"    />
        <result property="isDefault"    column="is_default"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractReviewStrategyVo">
        select s.id, s.strategy_name, s.strategy_desc, s.category_id, c.category_name,
               s.review_position, s.strategy_status, s.version, s.is_default,
               s.create_time, s.create_by, s.update_time, s.update_by, s.del_flag,
               s.attr1, s.attr2, s.attr3
        from contract_review_strategy s
        left join contract_category c on s.category_id = c.id
    </sql>

    <select id="selectContractReviewStrategyList" parameterType="ContractReviewStrategy" resultMap="ContractReviewStrategyResult">
        <include refid="selectContractReviewStrategyVo"/>
        <where>
            <if test="strategyName != null  and strategyName != ''"> and s.strategy_name like concat('%', #{strategyName}, '%')</if>
            <if test="strategyDesc != null  and strategyDesc != ''"> and s.strategy_desc = #{strategyDesc}</if>
            <if test="categoryId != null "> and s.category_id = #{categoryId}</if>
            <if test="reviewPosition != null  and reviewPosition != ''"> and s.review_position = #{reviewPosition}</if>
            <if test="strategyStatus != null  and strategyStatus != ''"> and s.strategy_status = #{strategyStatus}</if>
            <if test="version != null  and version != ''"> and s.version = #{version}</if>
            <if test="isDefault != null  and isDefault != ''"> and s.is_default = #{isDefault}</if>
            <if test="attr1 != null  and attr1 != ''"> and s.attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and s.attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and s.attr3 = #{attr3}</if>
            and s.del_flag = '0'
            and (c.del_flag = '0' or c.del_flag is null)
        </where>
        order by s.create_time desc
    </select>
    
    <select id="selectContractReviewStrategyById" parameterType="Long" resultMap="ContractReviewStrategyResult">
        <include refid="selectContractReviewStrategyVo"/>
        where s.id = #{id} and s.del_flag = '0'
    </select>

    <insert id="insertContractReviewStrategy" parameterType="ContractReviewStrategy" useGeneratedKeys="true" keyProperty="id">
        insert into contract_review_strategy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyName != null and strategyName != ''">strategy_name,</if>
            <if test="strategyDesc != null">strategy_desc,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="reviewPosition != null and reviewPosition != ''">review_position,</if>
            <if test="strategyStatus != null">strategy_status,</if>
            <if test="version != null">version,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyName != null and strategyName != ''">#{strategyName},</if>
            <if test="strategyDesc != null">#{strategyDesc},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="reviewPosition != null and reviewPosition != ''">#{reviewPosition},</if>
            <if test="strategyStatus != null">#{strategyStatus},</if>
            <if test="version != null">#{version},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractReviewStrategy" parameterType="ContractReviewStrategy">
        update contract_review_strategy
        <trim prefix="SET" suffixOverrides=",">
            <if test="strategyName != null and strategyName != ''">strategy_name = #{strategyName},</if>
            <if test="strategyDesc != null">strategy_desc = #{strategyDesc},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="reviewPosition != null and reviewPosition != ''">review_position = #{reviewPosition},</if>
            <if test="strategyStatus != null">strategy_status = #{strategyStatus},</if>
            <if test="version != null">version = #{version},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractReviewStrategyById" parameterType="Long">
        delete from contract_review_strategy where id = #{id}
    </delete>

    <delete id="deleteContractReviewStrategyByIds" parameterType="String">
        delete from contract_review_strategy where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>