<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractRiskPointMapper">
    
    <resultMap type="ContractRiskPoint" id="ContractRiskPointResult">
        <result property="id"    column="id"    />
        <result property="clauseId"    column="clause_id"    />
        <result property="riskName"    column="risk_name"    />
        <result property="riskDesc"    column="risk_desc"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="riskAnalysis"    column="risk_analysis"    />
        <result property="suggestModify"    column="suggest_modify"    />
        <result property="keywordPattern"    column="keyword_pattern"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="riskStatus"    column="risk_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractRiskPointVo">
        select id, clause_id, risk_name, risk_desc, risk_level, risk_analysis, suggest_modify, keyword_pattern, sort_order, risk_status, create_time, create_by, update_time, update_by, del_flag, attr1, attr2, attr3 from contract_risk_point
    </sql>

    <select id="selectContractRiskPointList" parameterType="ContractRiskPoint" resultMap="ContractRiskPointResult">
        <include refid="selectContractRiskPointVo"/>
        <where>  
            <if test="clauseId != null "> and clause_id = #{clauseId}</if>
            <if test="riskName != null  and riskName != ''"> and risk_name like concat('%', #{riskName}, '%')</if>
            <if test="riskDesc != null  and riskDesc != ''"> and risk_desc = #{riskDesc}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="riskAnalysis != null  and riskAnalysis != ''"> and risk_analysis = #{riskAnalysis}</if>
            <if test="suggestModify != null  and suggestModify != ''"> and suggest_modify = #{suggestModify}</if>
            <if test="keywordPattern != null  and keywordPattern != ''"> and keyword_pattern = #{keywordPattern}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="riskStatus != null  and riskStatus != ''"> and risk_status = #{riskStatus}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>
        </where>
    </select>
    
    <select id="selectContractRiskPointById" parameterType="Long" resultMap="ContractRiskPointResult">
        <include refid="selectContractRiskPointVo"/>
        where id = #{id}
    </select>

    <insert id="insertContractRiskPoint" parameterType="ContractRiskPoint" useGeneratedKeys="true" keyProperty="id">
        insert into contract_risk_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="clauseId != null">clause_id,</if>
            <if test="riskName != null and riskName != ''">risk_name,</if>
            <if test="riskDesc != null">risk_desc,</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level,</if>
            <if test="riskAnalysis != null">risk_analysis,</if>
            <if test="suggestModify != null">suggest_modify,</if>
            <if test="keywordPattern != null">keyword_pattern,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="riskStatus != null">risk_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="clauseId != null">#{clauseId},</if>
            <if test="riskName != null and riskName != ''">#{riskName},</if>
            <if test="riskDesc != null">#{riskDesc},</if>
            <if test="riskLevel != null and riskLevel != ''">#{riskLevel},</if>
            <if test="riskAnalysis != null">#{riskAnalysis},</if>
            <if test="suggestModify != null">#{suggestModify},</if>
            <if test="keywordPattern != null">#{keywordPattern},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="riskStatus != null">#{riskStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractRiskPoint" parameterType="ContractRiskPoint">
        update contract_risk_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="clauseId != null">clause_id = #{clauseId},</if>
            <if test="riskName != null and riskName != ''">risk_name = #{riskName},</if>
            <if test="riskDesc != null">risk_desc = #{riskDesc},</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level = #{riskLevel},</if>
            <if test="riskAnalysis != null">risk_analysis = #{riskAnalysis},</if>
            <if test="suggestModify != null">suggest_modify = #{suggestModify},</if>
            <if test="keywordPattern != null">keyword_pattern = #{keywordPattern},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="riskStatus != null">risk_status = #{riskStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractRiskPointById" parameterType="Long">
        delete from contract_risk_point where id = #{id}
    </delete>

    <delete id="deleteContractRiskPointByIds" parameterType="String">
        delete from contract_risk_point where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>