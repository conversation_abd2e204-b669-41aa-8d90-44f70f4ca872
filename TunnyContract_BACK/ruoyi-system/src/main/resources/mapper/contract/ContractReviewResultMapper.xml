<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractReviewResultMapper">
    
    <resultMap type="ContractReviewResult" id="ContractReviewResultResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="fileId"    column="file_id"    />
        <result property="reviewSummary"    column="review_summary"    />
        <result property="totalScore"    column="total_score"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="reportJson"    column="report_json"    />
        <result property="reportHtml"    column="report_html"    />
        <result property="exportCount"    column="export_count"    />
        <result property="lastExportTime"    column="last_export_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractReviewResultVo">
        select id, task_id, file_id, review_summary, total_score, risk_level, report_json, report_html, export_count, last_export_time, create_time, create_by, update_time, update_by, del_flag, attr1, attr2, attr3 from contract_review_result
    </sql>

    <select id="selectContractReviewResultList" parameterType="ContractReviewResult" resultMap="ContractReviewResultResult">
        <include refid="selectContractReviewResultVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="fileId != null "> and file_id = #{fileId}</if>
            <if test="reviewSummary != null  and reviewSummary != ''"> and review_summary = #{reviewSummary}</if>
            <if test="totalScore != null "> and total_score = #{totalScore}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="reportJson != null  and reportJson != ''"> and report_json = #{reportJson}</if>
            <if test="reportHtml != null  and reportHtml != ''"> and report_html = #{reportHtml}</if>
            <if test="exportCount != null "> and export_count = #{exportCount}</if>
            <if test="lastExportTime != null "> and last_export_time = #{lastExportTime}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>
        </where>
    </select>
    
    <select id="selectContractReviewResultById" parameterType="Long" resultMap="ContractReviewResultResult">
        <include refid="selectContractReviewResultVo"/>
        where id = #{id}
    </select>

    <insert id="insertContractReviewResult" parameterType="ContractReviewResult" useGeneratedKeys="true" keyProperty="id">
        insert into contract_review_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="fileId != null">file_id,</if>
            <if test="reviewSummary != null">review_summary,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="reportJson != null">report_json,</if>
            <if test="reportHtml != null">report_html,</if>
            <if test="exportCount != null">export_count,</if>
            <if test="lastExportTime != null">last_export_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="reviewSummary != null">#{reviewSummary},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="reportJson != null">#{reportJson},</if>
            <if test="reportHtml != null">#{reportHtml},</if>
            <if test="exportCount != null">#{exportCount},</if>
            <if test="lastExportTime != null">#{lastExportTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractReviewResult" parameterType="ContractReviewResult">
        update contract_review_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="fileId != null">file_id = #{fileId},</if>
            <if test="reviewSummary != null">review_summary = #{reviewSummary},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="reportJson != null">report_json = #{reportJson},</if>
            <if test="reportHtml != null">report_html = #{reportHtml},</if>
            <if test="exportCount != null">export_count = #{exportCount},</if>
            <if test="lastExportTime != null">last_export_time = #{lastExportTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractReviewResultById" parameterType="Long">
        delete from contract_review_result where id = #{id}
    </delete>

    <delete id="deleteContractReviewResultByIds" parameterType="String">
        delete from contract_review_result where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>