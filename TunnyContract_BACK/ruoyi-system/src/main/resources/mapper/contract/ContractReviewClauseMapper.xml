<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractReviewClauseMapper">
    
    <resultMap type="ContractReviewClause" id="ContractReviewClauseResult">
        <result property="id"    column="id"    />
        <result property="strategyId"    column="strategy_id"    />
        <result property="clauseName"    column="clause_name"    />
        <result property="clauseDesc"    column="clause_desc"    />
        <result property="clauseContent"    column="clause_content"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="clauseStatus"    column="clause_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractReviewClauseVo">
        select id, strategy_id, clause_name, clause_desc, clause_content, sort_order, clause_status, create_time, create_by, update_time, update_by, del_flag, attr1, attr2, attr3 from contract_review_clause
    </sql>

    <select id="selectContractReviewClauseList" parameterType="ContractReviewClause" resultMap="ContractReviewClauseResult">
        <include refid="selectContractReviewClauseVo"/>
        <where>  
            <if test="strategyId != null "> and strategy_id = #{strategyId}</if>
            <if test="clauseName != null  and clauseName != ''"> and clause_name like concat('%', #{clauseName}, '%')</if>
            <if test="clauseDesc != null  and clauseDesc != ''"> and clause_desc = #{clauseDesc}</if>
            <if test="clauseContent != null  and clauseContent != ''"> and clause_content = #{clauseContent}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="clauseStatus != null  and clauseStatus != ''"> and clause_status = #{clauseStatus}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>
        </where>
    </select>
    
    <select id="selectContractReviewClauseById" parameterType="Long" resultMap="ContractReviewClauseResult">
        <include refid="selectContractReviewClauseVo"/>
        where id = #{id}
    </select>

    <insert id="insertContractReviewClause" parameterType="ContractReviewClause" useGeneratedKeys="true" keyProperty="id">
        insert into contract_review_clause
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">strategy_id,</if>
            <if test="clauseName != null and clauseName != ''">clause_name,</if>
            <if test="clauseDesc != null">clause_desc,</if>
            <if test="clauseContent != null">clause_content,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="clauseStatus != null">clause_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">#{strategyId},</if>
            <if test="clauseName != null and clauseName != ''">#{clauseName},</if>
            <if test="clauseDesc != null">#{clauseDesc},</if>
            <if test="clauseContent != null">#{clauseContent},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="clauseStatus != null">#{clauseStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractReviewClause" parameterType="ContractReviewClause">
        update contract_review_clause
        <trim prefix="SET" suffixOverrides=",">
            <if test="strategyId != null">strategy_id = #{strategyId},</if>
            <if test="clauseName != null and clauseName != ''">clause_name = #{clauseName},</if>
            <if test="clauseDesc != null">clause_desc = #{clauseDesc},</if>
            <if test="clauseContent != null">clause_content = #{clauseContent},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="clauseStatus != null">clause_status = #{clauseStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractReviewClauseById" parameterType="Long">
        delete from contract_review_clause where id = #{id}
    </delete>

    <delete id="deleteContractReviewClauseByIds" parameterType="String">
        delete from contract_review_clause where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>