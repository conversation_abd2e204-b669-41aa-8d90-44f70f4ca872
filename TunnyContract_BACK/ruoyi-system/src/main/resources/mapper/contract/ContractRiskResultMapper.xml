<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractRiskResultMapper">
    
    <resultMap type="ContractRiskResult" id="ContractRiskResultResult">
        <result property="id"    column="id"    />
        <result property="resultId"    column="result_id"    />
        <result property="riskPointId"    column="risk_point_id"    />
        <result property="matchedText"    column="matched_text"    />
        <result property="pageNumber"    column="page_number"    />
        <result property="positionStart"    column="position_start"    />
        <result property="positionEnd"    column="position_end"    />
        <result property="confidenceScore"    column="confidence_score"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="suggestion"    column="suggestion"    />
        <result property="isConfirmed"    column="is_confirmed"    />
        <result property="confirmTime"    column="confirm_time"    />
        <result property="confirmUser"    column="confirm_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractRiskResultVo">
        select id, result_id, risk_point_id, matched_text, page_number, position_start, position_end, confidence_score, risk_level, suggestion, is_confirmed, confirm_time, confirm_user, create_time, create_by, update_time, update_by, del_flag, attr1, attr2, attr3 from contract_risk_result
    </sql>

    <select id="selectContractRiskResultList" parameterType="ContractRiskResult" resultMap="ContractRiskResultResult">
        <include refid="selectContractRiskResultVo"/>
        <where>  
            <if test="resultId != null "> and result_id = #{resultId}</if>
            <if test="riskPointId != null "> and risk_point_id = #{riskPointId}</if>
            <if test="matchedText != null  and matchedText != ''"> and matched_text = #{matchedText}</if>
            <if test="pageNumber != null "> and page_number = #{pageNumber}</if>
            <if test="positionStart != null "> and position_start = #{positionStart}</if>
            <if test="positionEnd != null "> and position_end = #{positionEnd}</if>
            <if test="confidenceScore != null "> and confidence_score = #{confidenceScore}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="suggestion != null  and suggestion != ''"> and suggestion = #{suggestion}</if>
            <if test="isConfirmed != null  and isConfirmed != ''"> and is_confirmed = #{isConfirmed}</if>
            <if test="confirmTime != null "> and confirm_time = #{confirmTime}</if>
            <if test="confirmUser != null  and confirmUser != ''"> and confirm_user = #{confirmUser}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>
        </where>
    </select>
    
    <select id="selectContractRiskResultById" parameterType="Long" resultMap="ContractRiskResultResult">
        <include refid="selectContractRiskResultVo"/>
        where id = #{id}
    </select>

    <insert id="insertContractRiskResult" parameterType="ContractRiskResult" useGeneratedKeys="true" keyProperty="id">
        insert into contract_risk_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resultId != null">result_id,</if>
            <if test="riskPointId != null">risk_point_id,</if>
            <if test="matchedText != null">matched_text,</if>
            <if test="pageNumber != null">page_number,</if>
            <if test="positionStart != null">position_start,</if>
            <if test="positionEnd != null">position_end,</if>
            <if test="confidenceScore != null">confidence_score,</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level,</if>
            <if test="suggestion != null">suggestion,</if>
            <if test="isConfirmed != null">is_confirmed,</if>
            <if test="confirmTime != null">confirm_time,</if>
            <if test="confirmUser != null">confirm_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resultId != null">#{resultId},</if>
            <if test="riskPointId != null">#{riskPointId},</if>
            <if test="matchedText != null">#{matchedText},</if>
            <if test="pageNumber != null">#{pageNumber},</if>
            <if test="positionStart != null">#{positionStart},</if>
            <if test="positionEnd != null">#{positionEnd},</if>
            <if test="confidenceScore != null">#{confidenceScore},</if>
            <if test="riskLevel != null and riskLevel != ''">#{riskLevel},</if>
            <if test="suggestion != null">#{suggestion},</if>
            <if test="isConfirmed != null">#{isConfirmed},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="confirmUser != null">#{confirmUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractRiskResult" parameterType="ContractRiskResult">
        update contract_risk_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="resultId != null">result_id = #{resultId},</if>
            <if test="riskPointId != null">risk_point_id = #{riskPointId},</if>
            <if test="matchedText != null">matched_text = #{matchedText},</if>
            <if test="pageNumber != null">page_number = #{pageNumber},</if>
            <if test="positionStart != null">position_start = #{positionStart},</if>
            <if test="positionEnd != null">position_end = #{positionEnd},</if>
            <if test="confidenceScore != null">confidence_score = #{confidenceScore},</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level = #{riskLevel},</if>
            <if test="suggestion != null">suggestion = #{suggestion},</if>
            <if test="isConfirmed != null">is_confirmed = #{isConfirmed},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
            <if test="confirmUser != null">confirm_user = #{confirmUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractRiskResultById" parameterType="Long">
        delete from contract_risk_result where id = #{id}
    </delete>

    <delete id="deleteContractRiskResultByIds" parameterType="String">
        delete from contract_risk_result where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>