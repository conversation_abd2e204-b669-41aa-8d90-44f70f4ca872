<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractReviewTaskMapper">
    
    <resultMap type="ContractReviewTask" id="ContractReviewTaskResult">
        <result property="id"    column="id"    />
        <result property="taskNo"    column="task_no"    />
        <result property="taskName"    column="task_name"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="strategyId"    column="strategy_id"    />
        <result property="strategyName"    column="strategy_name"    />
        <result property="reviewPosition"    column="review_position"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="totalRiskCount"    column="total_risk_count"    />
        <result property="highRiskCount"    column="high_risk_count"    />
        <result property="normalRiskCount"    column="normal_risk_count"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="reviewResult"    column="review_result"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractReviewTaskVo">
        select t.id, t.task_no, t.task_name, t.category_id, c.category_name, t.strategy_id, s.strategy_name,
               t.review_position, t.task_status, t.start_time, t.end_time, t.total_risk_count, t.high_risk_count,
               t.normal_risk_count, t.error_message, t.review_result,  t.create_time, t.create_by, t.update_time, t.update_by,
               t.del_flag, t.attr1, t.attr2, t.attr3
        from contract_review_task t
        left join contract_category c on t.category_id = c.id
        left join contract_review_strategy s on t.strategy_id = s.id
    </sql>

    <select id="selectContractReviewTaskList" parameterType="ContractReviewTask" resultMap="ContractReviewTaskResult">
        <include refid="selectContractReviewTaskVo"/>
        <where>  
            <if test="taskNo != null  and taskNo != ''"> and task_no = #{taskNo}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="strategyId != null "> and strategy_id = #{strategyId}</if>
            <if test="reviewPosition != null  and reviewPosition != ''"> and review_position = #{reviewPosition}</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
<!--            <if test="startTime != null "> and start_time = #{startTime}</if>-->
<!--            <if test="endTime != null "> and end_time = #{endTime}</if>-->
<!--            <if test="totalRiskCount != null "> and total_risk_count = #{totalRiskCount}</if>-->
<!--            <if test="highRiskCount != null "> and high_risk_count = #{highRiskCount}</if>-->
<!--            <if test="normalRiskCount != null "> and normal_risk_count = #{normalRiskCount}</if>-->
<!--            <if test="errorMessage != ''"> and error_message = #{errorMessage}</if>-->
<!--            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>-->
<!--            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>-->
<!--            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>-->
        </where>
    </select>
    
    <select id="selectContractReviewTaskById" parameterType="Long" resultMap="ContractReviewTaskResult">
        <include refid="selectContractReviewTaskVo"/>
        where t.id = #{id}
    </select>

    <insert id="insertContractReviewTask" parameterType="ContractReviewTask" useGeneratedKeys="true" keyProperty="id">
        insert into contract_review_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskNo != null and taskNo != ''">task_no,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="reviewPosition != null and reviewPosition != ''">review_position,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="totalRiskCount != null">total_risk_count,</if>
            <if test="highRiskCount != null">high_risk_count,</if>
            <if test="normalRiskCount != null">normal_risk_count,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="reviewResult != null">review_result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskNo != null and taskNo != ''">#{taskNo},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="strategyId != null">#{strategyId},</if>
            <if test="reviewPosition != null and reviewPosition != ''">#{reviewPosition},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="totalRiskCount != null">#{totalRiskCount},</if>
            <if test="highRiskCount != null">#{highRiskCount},</if>
            <if test="normalRiskCount != null">#{normalRiskCount},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="reviewResult != null">#{reviewResult},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractReviewTask" parameterType="ContractReviewTask">
        update contract_review_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskNo != null and taskNo != ''">task_no = #{taskNo},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="strategyId != null">strategy_id = #{strategyId},</if>
            <if test="reviewPosition != null and reviewPosition != ''">review_position = #{reviewPosition},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="totalRiskCount != null">total_risk_count = #{totalRiskCount},</if>
            <if test="highRiskCount != null">high_risk_count = #{highRiskCount},</if>
            <if test="normalRiskCount != null">normal_risk_count = #{normalRiskCount},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="reviewResult != null">review_result = #{reviewResult},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractReviewTaskById" parameterType="Long">
        delete from contract_review_task where id = #{id}
    </delete>

    <delete id="deleteContractReviewTaskByIds" parameterType="String">
        delete from contract_review_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>