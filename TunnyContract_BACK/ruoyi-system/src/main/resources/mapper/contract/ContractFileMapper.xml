<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.contract.mapper.ContractFileMapper">
    
    <resultMap type="ContractFile" id="ContractFileResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileSize"    column="file_size"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileMd5"    column="file_md5"    />
        <result property="uploadStatus"    column="upload_status"    />
        <result property="parseStatus"    column="parse_status"    />
        <result property="contentText"    column="content_text"    />
        <result property="pageCount"    column="page_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="attr1"    column="attr1"    />
        <result property="attr2"    column="attr2"    />
        <result property="attr3"    column="attr3"    />
    </resultMap>

    <sql id="selectContractFileVo">
        select id, task_id, file_name, file_path, file_size, file_type, file_md5, upload_status, parse_status, content_text, page_count, create_time, create_by, update_time, update_by, del_flag, attr1, attr2, attr3 from contract_file
    </sql>

    <select id="selectContractFileList" parameterType="ContractFile" resultMap="ContractFileResult">
        <include refid="selectContractFileVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and file_path = #{filePath}</if>
            <if test="fileSize != null "> and file_size = #{fileSize}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileMd5 != null  and fileMd5 != ''"> and file_md5 = #{fileMd5}</if>
            <if test="uploadStatus != null  and uploadStatus != ''"> and upload_status = #{uploadStatus}</if>
            <if test="parseStatus != null  and parseStatus != ''"> and parse_status = #{parseStatus}</if>
            <if test="contentText != null  and contentText != ''"> and content_text = #{contentText}</if>
            <if test="pageCount != null "> and page_count = #{pageCount}</if>
            <if test="attr1 != null  and attr1 != ''"> and attr1 = #{attr1}</if>
            <if test="attr2 != null  and attr2 != ''"> and attr2 = #{attr2}</if>
            <if test="attr3 != null  and attr3 != ''"> and attr3 = #{attr3}</if>
        </where>
    </select>
    
    <select id="selectContractFileById" parameterType="Long" resultMap="ContractFileResult">
        <include refid="selectContractFileVo"/>
        where id = #{id}
    </select>

    <select id="selectContractFileByMd5" parameterType="String" resultMap="ContractFileResult">
        <include refid="selectContractFileVo"/>
        where file_md5 = #{fileMd5} and del_flag = '0'
        limit 1
    </select>

    <insert id="insertContractFile" parameterType="ContractFile" useGeneratedKeys="true" keyProperty="id">
        insert into contract_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="filePath != null and filePath != ''">file_path,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileMd5 != null">file_md5,</if>
            <if test="uploadStatus != null">upload_status,</if>
            <if test="parseStatus != null">parse_status,</if>
            <if test="contentText != null">content_text,</if>
            <if test="pageCount != null">page_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="attr1 != null">attr1,</if>
            <if test="attr2 != null">attr2,</if>
            <if test="attr3 != null">attr3,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="filePath != null and filePath != ''">#{filePath},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileMd5 != null">#{fileMd5},</if>
            <if test="uploadStatus != null">#{uploadStatus},</if>
            <if test="parseStatus != null">#{parseStatus},</if>
            <if test="contentText != null">#{contentText},</if>
            <if test="pageCount != null">#{pageCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="attr1 != null">#{attr1},</if>
            <if test="attr2 != null">#{attr2},</if>
            <if test="attr3 != null">#{attr3},</if>
         </trim>
    </insert>

    <update id="updateContractFile" parameterType="ContractFile">
        update contract_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="filePath != null and filePath != ''">file_path = #{filePath},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileMd5 != null">file_md5 = #{fileMd5},</if>
            <if test="uploadStatus != null">upload_status = #{uploadStatus},</if>
            <if test="parseStatus != null">parse_status = #{parseStatus},</if>
            <if test="contentText != null">content_text = #{contentText},</if>
            <if test="pageCount != null">page_count = #{pageCount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="attr1 != null">attr1 = #{attr1},</if>
            <if test="attr2 != null">attr2 = #{attr2},</if>
            <if test="attr3 != null">attr3 = #{attr3},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteContractFileById" parameterType="Long">
        delete from contract_file where id = #{id}
    </delete>

    <delete id="deleteContractFileByIds" parameterType="String">
        delete from contract_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>