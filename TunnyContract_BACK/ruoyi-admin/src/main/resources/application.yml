# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（2小时）
    expireTime: 120

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Dify AI智能体配置
dify:
  # 是否启用Dify功能
  enabled: true

  # Dify服务基础URL（所有智能体共享）
  base-url: http://**************/v1

  # 连接配置
  connection:
    connect-timeout: 5000
    read-timeout: 60000
    write-timeout: 30000

  # 默认用户标识
  default-user: system

  # 智能体配置 - 动态配置
  agents:
    contract-review:
      api:
        key: app-BaGnkHUVdociqBnguETgKlmm
      description: "专门用于合同条款审查和风险评估"
      type: "workflow"
      enabled: true

    contract-analysis:
      api:
        key: app-example-contract-analysis
      description: "专门处理合同内容分析和风险评估"
      type: "chat"
      enabled: false

# Minio配置
minio:
    url: http://**************:9000
    accessKey: admin
    secretKey: tunny_huazheng
    bucketName: contract-data

# 合同文件上传配置
contract:
  file:
    # 支持的文件格式
    allowed-types: pdf,doc,docx
    # 单个文件最大大小(MB)
    max-file-size: 100
    # 批量上传最大文件数
    max-batch-count: 1
    # 是否启用MD5去重
    enable-md5-check: false
  
  # 文档转换配置
  conversion:
    # LibreOffice可执行文件路径
    libreoffice-path: /Applications/LibreOffice.app/Contents/MacOS/soffice
    # 转换超时时间(秒)
    timeout: 300
    # 临时文件目录
    temp-dir: /tmp/contract-render
    # PDF缓存目录
    pdf-cache-dir: /tmp/contract-pdf-cache