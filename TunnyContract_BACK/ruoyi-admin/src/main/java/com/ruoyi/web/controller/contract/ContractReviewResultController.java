package com.ruoyi.web.controller.contract;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractReviewResult;
import com.ruoyi.contract.service.IContractReviewResultService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同审查结果Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/reviewresult")
public class ContractReviewResultController extends BaseController
{
    @Autowired
    private IContractReviewResultService contractReviewResultService;

    /**
     * 查询合同审查结果列表
     */
    @PreAuthorize("@ss.hasPermi('contract:reviewresult:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractReviewResult contractReviewResult)
    {
        startPage();
        List<ContractReviewResult> list = contractReviewResultService.selectContractReviewResultList(contractReviewResult);
        return getDataTable(list);
    }

    /**
     * 导出合同审查结果列表
     */
    @PreAuthorize("@ss.hasPermi('contract:reviewresult:export')")
    @Log(title = "合同审查结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractReviewResult contractReviewResult)
    {
        List<ContractReviewResult> list = contractReviewResultService.selectContractReviewResultList(contractReviewResult);
        ExcelUtil<ContractReviewResult> util = new ExcelUtil<ContractReviewResult>(ContractReviewResult.class);
        util.exportExcel(response, list, "合同审查结果数据");
    }

    /**
     * 获取合同审查结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:reviewresult:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractReviewResultService.selectContractReviewResultById(id));
    }

    /**
     * 新增合同审查结果
     */
    @PreAuthorize("@ss.hasPermi('contract:reviewresult:add')")
    @Log(title = "合同审查结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractReviewResult contractReviewResult)
    {
        return toAjax(contractReviewResultService.insertContractReviewResult(contractReviewResult));
    }

    /**
     * 修改合同审查结果
     */
    @PreAuthorize("@ss.hasPermi('contract:reviewresult:edit')")
    @Log(title = "合同审查结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractReviewResult contractReviewResult)
    {
        return toAjax(contractReviewResultService.updateContractReviewResult(contractReviewResult));
    }

    /**
     * 删除合同审查结果
     */
    @PreAuthorize("@ss.hasPermi('contract:reviewresult:remove')")
    @Log(title = "合同审查结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractReviewResultService.deleteContractReviewResultByIds(ids));
    }
}
