package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.dify.DifyException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.IDifyService;
import io.github.imfangs.dify.client.model.chat.ChatMessageResponse;
import io.github.imfangs.dify.client.model.completion.CompletionResponse;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import io.github.imfangs.dify.client.model.datasets.RetrieveResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Dify AI智能体控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dify")
public class DifyController extends BaseController
{
    @Autowired
    private IDifyService difyService;

    /**
     * 获取所有启用的智能体信息
     */
    @GetMapping("/agents")
    public AjaxResult getEnabledAgents()
    {
        try
        {
            Map<String, Object> agents = difyService.getEnabledAgents();
            return success(agents);
        }
        catch (DifyException e)
        {
            logger.error("获取智能体信息失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("获取智能体信息异常", e);
            return error("获取智能体信息失败");
        }
    }

    /**
     * 发送对话消息
     */
    @Log(title = "Dify对话", businessType = BusinessType.OTHER)
    @PostMapping("/agents/{agentName}/chat")
    public AjaxResult sendChatMessage(@PathVariable String agentName, @RequestBody Map<String, Object> params)
    {
        try
        {
            String query = (String) params.get("query");
            String conversationId = (String) params.get("conversationId");
            String user = getCurrentUser();

            if (StringUtils.isEmpty(query))
            {
                return error("查询内容不能为空");
            }

            ChatMessageResponse response = difyService.sendChatMessage(agentName, query, user, conversationId);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("发送对话消息失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("发送对话消息异常", e);
            return error("发送对话消息失败");
        }
    }

    /**
     * 发送文本生成请求
     */
    @Log(title = "Dify文本生成", businessType = BusinessType.OTHER)
    @PostMapping("/agents/{agentName}/completion")
    public AjaxResult sendCompletionMessage(@PathVariable String agentName, @RequestBody Map<String, Object> params)
    {
        try
        {
            @SuppressWarnings("unchecked")
            Map<String, Object> inputs = (Map<String, Object>) params.get("inputs");
            String user = getCurrentUser();
            
            if (inputs == null || inputs.isEmpty())
            {
                return error("输入参数不能为空");
            }

            CompletionResponse response = difyService.sendCompletionMessage(agentName, inputs, user);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("发送文本生成请求失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("发送文本生成请求异常", e);
            return error("发送文本生成请求失败");
        }
    }

    /**
     * 执行工作流
     */
    @Log(title = "Dify工作流", businessType = BusinessType.OTHER)
    @PostMapping("/agents/{agentName}/workflow")
    public AjaxResult runWorkflow(@PathVariable String agentName, @RequestBody Map<String, Object> params)
    {
        try
        {
            @SuppressWarnings("unchecked")
            Map<String, Object> inputs = (Map<String, Object>) params.get("inputs");
            String user = getCurrentUser();
            
            if (inputs == null || inputs.isEmpty())
            {
                return error("输入参数不能为空");
            }

            WorkflowRunResponse response = difyService.runWorkflow(agentName, inputs, user);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("执行工作流失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("执行工作流异常", e);
            return error("执行工作流失败");
        }
    }

    /**
     * 检索知识库
     */
    @Log(title = "Dify知识库检索", businessType = BusinessType.OTHER)
    @PostMapping("/agents/{agentName}/datasets/{datasetId}/retrieve")
    public AjaxResult retrieveDataset(@PathVariable String agentName, @PathVariable String datasetId, @RequestBody Map<String, Object> params)
    {
        try
        {
            String query = (String) params.get("query");
            Integer topK = (Integer) params.get("topK");
            Float scoreThreshold = params.get("scoreThreshold") != null ? 
                Float.valueOf(params.get("scoreThreshold").toString()) : null;
            
            if (StringUtils.isEmpty(query))
            {
                return error("查询内容不能为空");
            }

            RetrieveResponse response = difyService.retrieveDataset(agentName, datasetId, query, topK, scoreThreshold);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("检索知识库失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("检索知识库异常", e);
            return error("检索知识库失败");
        }
    }

    /**
     * 获取会话历史消息
     */
    @GetMapping("/agents/{agentName}/conversations/{conversationId}/messages")
    public AjaxResult getMessages(@PathVariable String agentName,
                                  @PathVariable String conversationId,
                                  @RequestParam(required = false) String firstId,
                                  @RequestParam(defaultValue = "10") Integer limit)
    {
        try
        {
            String user = getCurrentUser();
            Object response = difyService.getMessages(agentName, conversationId, user, firstId, limit);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("获取会话历史消息失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("获取会话历史消息异常", e);
            return error("获取会话历史消息失败");
        }
    }

    /**
     * 获取会话列表
     */
    @PreAuthorize("@ss.hasPermi('system:dify:chat')")
    @GetMapping("/agents/{agentName}/conversations")
    public AjaxResult getConversations(@PathVariable String agentName,
                                       @RequestParam(required = false) String lastId,
                                       @RequestParam(defaultValue = "10") Integer limit,
                                       @RequestParam(required = false) String pinned)
    {
        try
        {
            String user = getCurrentUser();
            Object response = difyService.getConversations(agentName, user, lastId, limit, pinned);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("获取会话列表失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("获取会话列表异常", e);
            return error("获取会话列表失败");
        }
    }

    /**
     * 重命名会话
     */
    @PreAuthorize("@ss.hasPermi('system:dify:chat')")
    @Log(title = "重命名会话", businessType = BusinessType.UPDATE)
    @PutMapping("/agents/{agentName}/conversations/{conversationId}/name")
    public AjaxResult renameConversation(@PathVariable String agentName, @PathVariable String conversationId, @RequestBody Map<String, Object> params)
    {
        try
        {
            String name = (String) params.get("name");
            Boolean autoGenerate = (Boolean) params.get("autoGenerate");
            String user = getCurrentUser();

            Object response = difyService.renameConversation(agentName, conversationId, name, autoGenerate, user);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("重命名会话失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("重命名会话异常", e);
            return error("重命名会话失败");
        }
    }

    /**
     * 删除会话
     */
    @PreAuthorize("@ss.hasPermi('system:dify:chat')")
    @Log(title = "删除会话", businessType = BusinessType.DELETE)
    @DeleteMapping("/agents/{agentName}/conversations/{conversationId}")
    public AjaxResult deleteConversation(@PathVariable String agentName, @PathVariable String conversationId)
    {
        try
        {
            String user = getCurrentUser();
            Object response = difyService.deleteConversation(agentName, conversationId, user);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("删除会话失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("删除会话异常", e);
            return error("删除会话失败");
        }
    }

    /**
     * 消息反馈
     */
    @PreAuthorize("@ss.hasPermi('system:dify:chat')")
    @Log(title = "消息反馈", businessType = BusinessType.OTHER)
    @PostMapping("/agents/{agentName}/messages/{messageId}/feedback")
    public AjaxResult feedbackMessage(@PathVariable String agentName, @PathVariable String messageId, @RequestBody Map<String, Object> params)
    {
        try
        {
            String rating = (String) params.get("rating");
            String content = (String) params.get("content");
            String user = getCurrentUser();

            if (StringUtils.isEmpty(rating))
            {
                return error("评分不能为空");
            }

            Object response = difyService.feedbackMessage(agentName, messageId, rating, user, content);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("消息反馈失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("消息反馈异常", e);
            return error("消息反馈失败");
        }
    }

    /**
     * 获取建议问题
     */
    @PreAuthorize("@ss.hasPermi('system:dify:chat')")
    @GetMapping("/agents/{agentName}/messages/{messageId}/suggested")
    public AjaxResult getSuggestedQuestions(@PathVariable String agentName, @PathVariable String messageId)
    {
        try
        {
            String user = getCurrentUser();
            Object response = difyService.getSuggestedQuestions(agentName, messageId, user);
            return success(response);
        }
        catch (DifyException e)
        {
            logger.error("获取建议问题失败", e);
            return error(e.getMessage());
        }
        catch (Exception e)
        {
            logger.error("获取建议问题异常", e);
            return error("获取建议问题失败");
        }
    }

    /**
     * 获取当前用户标识
     */
    private String getCurrentUser()
    {
        try
        {
            return SecurityUtils.getUsername();
        }
        catch (Exception e)
        {
            return "system";
        }
    }
}
