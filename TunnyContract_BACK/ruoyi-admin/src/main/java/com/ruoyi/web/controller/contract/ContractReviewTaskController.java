package com.ruoyi.web.controller.contract;

import java.io.File;
import java.io.InputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.dify.DifyClientUtil;
import io.github.imfangs.dify.client.DifyWorkflowClient;
import io.github.imfangs.dify.client.model.file.FileUploadResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractReviewTask;
import com.ruoyi.contract.domain.ContractReviewStrategy;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractReviewTaskService;
import com.ruoyi.contract.service.IContractFileProcessService;
import com.ruoyi.contract.service.IContractFileService;
import com.ruoyi.contract.service.IContractReviewStrategyService;
import com.ruoyi.contract.service.IContractDocumentRenderService;
import com.ruoyi.contract.service.IContractCoordinateService;
import com.ruoyi.system.service.IDifyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.contract.dto.StrategyClauseRiskDTO;
import io.github.imfangs.dify.client.model.workflow.WorkflowRunResponse;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 合同审查任务Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/task")
public class ContractReviewTaskController extends BaseController
{
    @Autowired
    private IContractReviewTaskService contractReviewTaskService;

    @Autowired
    private IContractFileProcessService contractFileProcessService;

    @Autowired
    private IContractFileService contractFileService;

    @Autowired
    private IContractReviewStrategyService contractReviewStrategyService;

    @Autowired
    private IDifyService difyService;

    @Autowired
    private IContractDocumentRenderService documentRenderService;

    @Autowired
    private IContractCoordinateService coordinateService;

    /**
     * 查询合同审查任务列表
     */
    @PreAuthorize("@ss.hasPermi('contract:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractReviewTask contractReviewTask)
    {
        startPage();
        List<ContractReviewTask> list = contractReviewTaskService.selectContractReviewTaskList(contractReviewTask);
        return getDataTable(list);
    }



    /**
     * 获取合同审查任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractReviewTaskService.selectContractReviewTaskById(id));
    }

    /**
     * 新增合同审查任务
     */
    @PreAuthorize("@ss.hasPermi('contract:task:add')")
    @Log(title = "合同审查任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractReviewTask contractReviewTask)
    {
        return toAjax(contractReviewTaskService.insertContractReviewTask(contractReviewTask));
    }

    /**
     * 新增合同审查任务（支持文件上传）
     */
    @PreAuthorize("@ss.hasPermi('contract:task:add')")
    @Log(title = "合同审查任务", businessType = BusinessType.INSERT)
    @PostMapping("/addWithFiles")
    public AjaxResult addTaskWithFiles(
            @RequestParam("taskData") String taskData,
            @RequestParam(value = "files", required = false) MultipartFile[] files)
    {
        try
        {
            // 1. 解析任务数据
            ContractReviewTask task = JSON.parseObject(taskData, ContractReviewTask.class);

            // 2. 创建任务
            int result = contractReviewTaskService.insertContractReviewTask(task);

            if (result <= 0)
            {
                return error("任务创建失败");
            }

            // 3. 处理文件上传（如果有文件）
            if (files != null && files.length > 0)
            {
                List<ContractFile> uploadResults = contractFileProcessService.processUploadedFiles(task.getId(), files);

                // 统计上传结果
                long successCount = uploadResults.stream()
                    .filter(file -> "1".equals(file.getUploadStatus()))
                    .count();
                long failureCount = uploadResults.size() - successCount;

                logger.info("文件上传完成，任务ID：{}，成功：{}，失败：{}", task.getId(), successCount, failureCount);

                // 如果有文件上传失败，记录警告但不影响任务创建
                if (failureCount > 0)
                {
                    logger.warn("任务ID：{}，有{}个文件上传失败", task.getId(), failureCount);
                }
            }

            return success("任务创建成功");
        }
        catch (Exception e)
        {
            logger.error("创建任务失败：{}", e.getMessage(), e);
            return error("创建任务失败：" + e.getMessage());
        }
    }

    /**
     * 修改合同审查任务
     */
    @PreAuthorize("@ss.hasPermi('contract:task:edit')")
    @Log(title = "合同审查任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractReviewTask contractReviewTask)
    {
        return toAjax(contractReviewTaskService.updateContractReviewTask(contractReviewTask));
    }

    /**
     * 删除合同审查任务
     */
    @PreAuthorize("@ss.hasPermi('contract:task:remove')")
    @Log(title = "合同审查任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractReviewTaskService.deleteContractReviewTaskByIds(ids));
    }

    /**
     * 根据合同分类和审查立场获取可用策略列表
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @GetMapping("/strategies")
    public TableDataInfo getAvailableStrategies(@RequestParam Long categoryId, @RequestParam String reviewPosition)
    {
        List<ContractReviewStrategy> list = contractReviewTaskService.getAvailableStrategies(categoryId, reviewPosition);
        return getDataTable(list);
    }

    /**
     * 启动合同审查任务
     */
    @PreAuthorize("@ss.hasPermi('contract:task:edit')")
    @Log(title = "合同审查任务", businessType = BusinessType.UPDATE)
    @PostMapping("/start/{id}")
    public AjaxResult startReview(@PathVariable("id") Long id)
    {
        try
        {
            // 1. 获取任务信息
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(id);
            if (task == null)
            {
                return error("审查任务不存在");
            }

            // todo检查任务状态
//            if (!"0".equals(task.getTaskStatus()) || !"3".equals(task.getTaskStatus()))
//            {
//                return error("任务状态不允许启动审查");
//            }

            // 2. 获取任务关联的合同文件（只取第一个）
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(id);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty())
            {
                return error("未找到关联的合同文件");
            }

            ContractFile contractFile = contractFiles.get(0); // 只取第一个文件
            if (!"2".equals(contractFile.getParseStatus()))
            {
                return error("合同文件尚未解析完成，无法启动审查");
            }

            // 3. 更新任务状态为处理中
            task.setTaskStatus("1");
            task.setStartTime(new java.util.Date());
            contractReviewTaskService.updateContractReviewTask(task);

            // 4. 异步启动审查流程
            try {
                // 获取策略条款和风险点信息
                List<StrategyClauseRiskDTO> clauseRisks = contractReviewStrategyService.getStrategyClausesWithRiskPoints(task.getStrategyId());

                // 构建Dify工作流参数（只传递四个必要参数）
                Map<String, Object> inputs = new HashMap<>();
                
                // 1. contract_type - 合同类型
                inputs.put("contract_type", task.getCategoryName());
                
                // 2. review_position - 审查立场（数字转中文）
                String reviewPositionText = "1".equals(task.getReviewPosition()) ? "甲方" : "乙方";
                inputs.put("review_position", reviewPositionText);
                
                // 3. contract_file - 合同文件（文件上传形式）
                ContractFile firstFile = contractFiles.get(0);
                // 注意：这里先占位，实际的文件ID将在文件上传成功后更新
                inputs.put("contract_file", ""); // 将在文件上传后更新为文件ID
                
                // 4. strategy_list - 策略条款列表（JSON格式）
                inputs.put("strategy_list", JSON.toJSONString(success(clauseRisks)));

                DifyWorkflowClient workflowClient = DifyClientUtil.getWorkflowClient("contract-review");

                // 从MinIO URL下载文件到临时目录
                String fileUrl = firstFile.getFilePath();
                File tempFile = null;
                FileUploadResponse uploadResponse = null;
                
                try {
                    // 创建临时文件
                    String fileName = firstFile.getFileName();
                    String fileExtension = "";
                    if (fileName != null && fileName.contains(".")) {
                        fileExtension = fileName.substring(fileName.lastIndexOf("."));
                    }
                    tempFile = File.createTempFile("contract_", fileExtension);
                    
                    // 从URL下载文件到临时文件
                    URL url = new URL(fileUrl);
                    URLConnection connection = url.openConnection();
                    try (InputStream inputStream = connection.getInputStream();
                         FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                        
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }
                    
                    // 上传临时文件到Dify
                     uploadResponse = workflowClient.uploadFile(tempFile, "system");
                     System.out.println("文件上传成功，文件ID: " + uploadResponse.getId());
                     
                     // 构建Dify工作流所需的文件参数格式
                     Map<String, Object> contractFileParam = new HashMap<>();
                     contractFileParam.put("type", "document");
                     contractFileParam.put("transfer_method", "local_file");
                     contractFileParam.put("url", "");
                     contractFileParam.put("upload_file_id", uploadResponse.getId());
                     
                     // 更新工作流参数中的文件对象
                     inputs.put("contract_file", contractFileParam);
                    
                } catch (IOException e) {
                    logger.error("下载或上传文件失败: {}", e.getMessage(), e);
                    throw new RuntimeException("文件处理失败: " + e.getMessage(), e);
                } finally {
                    // 清理临时文件
                    if (tempFile != null && tempFile.exists()) {
                        try {
                            Files.deleteIfExists(tempFile.toPath());
                        } catch (IOException e) {
                            logger.warn("删除临时文件失败: {}", e.getMessage());
                        }
                    }
                }

                // 调用Dify工作流
                WorkflowRunResponse response = difyService.runWorkflow("contract-review", inputs, "system");
                
                // 解析和保存审查结果
                if (response != null && response.getData() != null) {
                    try {
                        String outputText = DifyClientUtil.extractOutputText(response.getData());
                        String processedJson = DifyClientUtil.processJsonEscape(outputText);
                        updateTaskSuccess(task, processedJson, id);
                    } catch (Exception ex) {
                        logger.error("解析工作流响应数据失败，任务ID: {}, 错误: {}", id, ex.getMessage(), ex);
                        updateTaskWithWarning(task, response.getData(), ex.getMessage());
                    }
                } else {
                    updateTaskFailure(task, "工作流执行失败，未返回有效结果");
                }
                
            } catch (Exception e) {
                // 异常处理
                logger.error("启动审查任务失败，任务ID: {}, 错误信息: {}", id, e.getMessage(), e);
                task.setTaskStatus("3");
                task.setErrorMessage("审查过程中发生异常: " + e.getMessage());
                task.setEndTime(new java.util.Date());
            } finally {
                // 最终更新任务状态
                contractReviewTaskService.updateContractReviewTask(task);
            }

            return success("审查任务已启动");
        }
        catch (Exception e)
        {
            logger.error("启动审查任务失败", e);
            return error("启动审查任务失败：" + e.getMessage());
        }
    }
    

    
    /**
     * 更新任务为成功状态
     */
    private void updateTaskSuccess(ContractReviewTask task, String result, Long taskId) {
        task.setReviewResult(result);
        task.setTaskStatus("2");
        task.setEndTime(new java.util.Date());
        task.setErrorMessage(null);
        logger.info("成功提取并保存outputs.text数据，任务ID: {}", taskId);
    }
    
    /**
     * 更新任务为警告状态（完成但有问题）
     */
    private void updateTaskWithWarning(ContractReviewTask task, WorkflowRunResponse.WorkflowRunData data, String errorMsg) {
        String resultJson = JSON.toJSONString(data);
        task.setReviewResult(resultJson);
        task.setTaskStatus("2");
        task.setEndTime(new java.util.Date());
        task.setErrorMessage("数据解析警告: " + errorMsg);
    }
    
    /**
     * 更新任务为失败状态
     */
    private void updateTaskFailure(ContractReviewTask task, String errorMsg) {
        task.setTaskStatus("3");
        task.setErrorMessage(errorMsg);
        task.setEndTime(new java.util.Date());
    }

    /**
     * 获取合同审查报告详情
     * @param taskId 任务ID
     * @return 报告详情数据
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @Log(title = "合同审查报告", businessType = BusinessType.OTHER)
    @GetMapping("/report/{taskId}")
    public AjaxResult getTaskReport(@PathVariable Long taskId) {
        try {
            // 1. 获取任务基本信息
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            if (task == null) {
                return error("审查任务不存在");
            }
            
            // 2. 检查任务状态
            if (!"2".equals(task.getTaskStatus())) {
                return error("任务尚未完成，无法查看报告");
            }
            
            // 3. 获取任务关联的合同文件（只取索引0的文件）
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(taskId);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty()) {
                return error("未找到关联的合同文件");
            }
            
            // 只使用第一个合同文件
            ContractFile contractFile = contractFiles.get(0);
            
            // 4. 构建报告数据
            Map<String, Object> reportData = new HashMap<>();
            reportData.put("taskInfo", task);
            reportData.put("contractFile", contractFile); // 单个文件对象
            
            // 5. 解析审查结果
            String reviewResult = task.getReviewResult();
            if (reviewResult != null && !reviewResult.trim().isEmpty()) {
                try {
                    Object parsedResult = JSON.parseObject(reviewResult);
                    reportData.put("reviewResults", parsedResult);
                } catch (Exception e) {
                    logger.warn("解析审查结果失败，任务ID: {}, 错误: {}", taskId, e.getMessage());
                    reportData.put("reviewResults", null);
                }
            } else {
                reportData.put("reviewResults", null);
            }
            
            // 6. 记录报告访问日志
            logReportAccess(taskId, "VIEW_REPORT");
            
            return success(reportData);
            
        } catch (Exception e) {
            logger.error("获取任务报告失败，任务ID: {}", taskId, e);
            return error("获取报告失败：" + e.getMessage());
        }
    }

    /**
     * 获取合同文档访问地址
     * @param taskId 任务ID
     * @return 文档访问地址信息
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @Log(title = "合同文档访问", businessType = BusinessType.OTHER)
    @GetMapping("/document/{taskId}")
    public AjaxResult getDocumentAccess(@PathVariable Long taskId) {
        try {
            // 1. 获取任务信息
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            if (task == null) {
                return error("审查任务不存在");
            }
            
            // 2. 获取任务关联的合同文件（只取第一个）
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(taskId);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty()) {
                return error("未找到关联的合同文件");
            }
            
            ContractFile contractFile = contractFiles.get(0);
            
            // 3. 检查文件是否已解析
            if (!"2".equals(contractFile.getParseStatus())) {
                return error("文件尚未解析完成，无法访问");
            }
            
            // 4. 构建文档访问信息
            Map<String, Object> documentInfo = new HashMap<>();
            documentInfo.put("fileId", contractFile.getId());
            documentInfo.put("fileName", contractFile.getFileName());
            documentInfo.put("originalFileName", contractFile.getFileName());
            documentInfo.put("fileType", contractFile.getFileType());
            documentInfo.put("fileSize", contractFile.getFileSize());
            documentInfo.put("fileMd5", contractFile.getFileMd5());
            
            // 5. 处理文件访问地址
            String filePath = contractFile.getFilePath();
            if (filePath != null && !filePath.trim().isEmpty()) {
                // 如果是MinIO地址，直接返回
                if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
                    documentInfo.put("accessUrl", filePath);
                    documentInfo.put("accessType", "direct");
                } else {
                    // 如果是相对路径，构建完整访问路径
                    documentInfo.put("accessUrl", "/contract/task/download/" + contractFile.getId());
                    documentInfo.put("accessType", "proxy");
                }
            } else {
                return error("文件路径信息缺失");
            }
            
            // 6. 检查是否需要格式转换
            String fileType = contractFile.getFileType();
            boolean needConversion = false;
            if ("doc".equalsIgnoreCase(fileType) || "docx".equalsIgnoreCase(fileType)) {
                needConversion = true;
                // 检查是否已存在PDF转换版本
                String pdfPath = getPdfConvertPath(contractFile);
                if (pdfPath != null) {
                    documentInfo.put("pdfAccessUrl", pdfPath);
                    documentInfo.put("conversionStatus", "completed");
                } else {
                    documentInfo.put("pdfAccessUrl", "/contract/task/convert/" + contractFile.getId());
                    documentInfo.put("conversionStatus", "pending");
                }
            }
            documentInfo.put("needConversion", needConversion);
            
            // 7. 记录访问日志
            logDocumentAccess(taskId, contractFile.getId(), "ACCESS_DOCUMENT");
            
            return success(documentInfo);
            
        } catch (Exception e) {
            logger.error("获取文档访问地址失败，任务ID: {}", taskId, e);
            return error("获取文档访问地址失败：" + e.getMessage());
        }
    }
    
    /**
     * 文件下载代理接口
     * @param fileId 文件ID
     * @param response HTTP响应
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @GetMapping("/download/{fileId}")
    public void downloadFile(@PathVariable Long fileId, HttpServletResponse response) {
        try {
            // 1. 获取文件信息
            ContractFile contractFile = contractFileService.selectContractFileById(fileId);
            if (contractFile == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            // 2. 检查权限（文件所属任务的访问权限）
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(contractFile.getTaskId());
            if (task == null) {
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
            
            // 3. 获取文件流并返回
            String filePath = contractFile.getFilePath();
            if (filePath == null || filePath.trim().isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            // 设置响应头
            String fileName = contractFile.getFileName();
            if (fileName == null) {
                fileName = contractFile.getFileName();
            }
            
            response.setContentType(getContentType(contractFile.getFileType()));
            response.setHeader("Content-Disposition", "inline; filename=\"" + fileName + "\"");
            response.setHeader("Cache-Control", "public, max-age=3600");
            
            // 如果是MinIO URL，重定向
            if (filePath.startsWith("http://") || filePath.startsWith("https://")) {
                response.sendRedirect(filePath);
            } else {
                // 本地文件处理
                Path file = Paths.get(filePath);
                if (Files.exists(file)) {
                    Files.copy(file, response.getOutputStream());
                } else {
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                }
            }
            
            // 记录下载日志
            logDocumentAccess(contractFile.getTaskId(), fileId, "DOWNLOAD_DOCUMENT");
            
        } catch (Exception e) {
            logger.error("文件下载失败，文件ID: {}", fileId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * Word转PDF接口（方案C：转换并上传到MinIO）
     * @param fileId 文件ID
     * @return 转换结果
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @Log(title = "文档格式转换", businessType = BusinessType.OTHER)
    @PostMapping("/convert/{fileId}")
    public AjaxResult convertToPdf(@PathVariable Long fileId) {
        try {
            // 1. 获取文件信息
            ContractFile contractFile = contractFileService.selectContractFileById(fileId);
            if (contractFile == null) {
                return error("文件不存在");
            }
            
            // 2. 检查文件类型
            String fileType = contractFile.getFileType();
            if (!"doc".equalsIgnoreCase(fileType) && !"docx".equalsIgnoreCase(fileType)) {
                return error("只支持Word文档转换");
            }
            
            // 3. 使用文档渲染服务进行转换和上传
            try {
                String pdfMinIOUrl = documentRenderService.getDocumentPreviewUrl(contractFile);
                
                Map<String, Object> result = new HashMap<>();
                result.put("pdfAccessUrl", pdfMinIOUrl);
                result.put("conversionStatus", "completed");
                result.put("message", "转换完成并已上传到MinIO");
                result.put("directAccess", true); // 标识可以直接访问
                
                // 记录转换日志
                logDocumentAccess(contractFile.getTaskId(), fileId, "CONVERT_TO_PDF_MINIO");
                
                return success(result);
                
            } catch (Exception e) {
                logger.error("Word转PDF并上传MinIO失败，文件ID: {}", fileId, e);
                return error("转换过程中出现异常：" + e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("转换请求处理失败，文件ID: {}", fileId, e);
            return error("转换请求处理失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取PDF转换路径
     * @param contractFile 合同文件
     * @return PDF路径，如果不存在返回null
     */
    private String getPdfConvertPath(ContractFile contractFile) {
        // 使用文档渲染服务检查缓存
        return documentRenderService.checkPdfCache(contractFile.getFileMd5());
    }
    
    /**
     * 保存PDF转换路径
     * @param contractFile 原始文件
     * @param pdfPath PDF文件路径
     */
    private void savePdfConvertPath(ContractFile contractFile, String pdfPath) {
        // TODO: 实现PDF路径保存逻辑
        // 可以更新contract_file表的pdf_path字段，或者创建新的转换记录
        logger.info("PDF转换完成，原文件ID: {}, PDF路径: {}", contractFile.getId(), pdfPath);
    }
    
    /**
     * 执行Word到PDF的转换
     * @param contractFile 合同文件
     * @return 转换后的PDF路径
     */
    private String performWordToPdfConversion(ContractFile contractFile) {
        try {
            // 使用文档渲染服务进行转换
            return documentRenderService.convertWordToPdf(contractFile);
        } catch (Exception e) {
            logger.error("Word转PDF转换失败，文件ID: {}", contractFile.getId(), e);
            return null;
        }
    }
    
    /**
     * 根据文件类型获取Content-Type
     * @param fileType 文件类型
     * @return Content-Type
     */
    private String getContentType(String fileType) {
        if (fileType == null) {
            return "application/octet-stream";
        }
        
        switch (fileType.toLowerCase()) {
            case "pdf":
                return "application/pdf";
            case "doc":
                return "application/msword";
            case "docx":
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            default:
                return "application/octet-stream";
        }
    }
    
    /**
     * 访问PDF缓存文件（已完全移除）
     * @deprecated 此接口已完全移除，请使用MinIO直接访问
     */
    
    /**
     * 获取文档预览信息（统一接口）
     * @param taskId 任务ID
     * @return 预览信息
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @Log(title = "合同文档预览", businessType = BusinessType.OTHER)
    @GetMapping("/preview/{taskId}")
    public AjaxResult getDocumentPreview(@PathVariable Long taskId) {
        try {
            // 1. 获取任务信息
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            if (task == null) {
                return error("审查任务不存在");
            }
            
            // 2. 获取任务关联的合同文件（只取第一个）
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(taskId);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty()) {
                return error("未找到关联的合同文件");
            }
            
            ContractFile contractFile = contractFiles.get(0);
            
            // 3. 检查文件是否支持预览
            if (!documentRenderService.isSupportedForPreview(contractFile.getFileType())) {
                return error("文件类型不支持在线预览");
            }
            
            // 4. 获取预览URL
            String previewUrl = documentRenderService.getDocumentPreviewUrl(contractFile);
            
            // 5. 构建预览信息
            Map<String, Object> previewInfo = new HashMap<>();
            previewInfo.put("fileId", contractFile.getId());
            previewInfo.put("fileName", contractFile.getFileName());
            previewInfo.put("fileType", contractFile.getFileType());
            previewInfo.put("previewUrl", previewUrl);
            previewInfo.put("supportPreview", true);
            
            // 6. 记录预览访问日志
            logDocumentAccess(taskId, contractFile.getId(), "PREVIEW_DOCUMENT");
            
            return success(previewInfo);
            
        } catch (Exception e) {
            logger.error("获取文档预览信息失败，任务ID: {}", taskId, e);
            return error("获取预览信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取条款坐标信息（包含风险点信息）
     * @param taskId 任务ID
     * @return 条款坐标数据
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @Log(title = "条款坐标解析", businessType = BusinessType.OTHER)
    @GetMapping("/coordinates/{taskId}")
    public AjaxResult getClauseCoordinates(@PathVariable Long taskId) {
        try {
            // 1. 获取任务信息
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            if (task == null) {
                return error("审查任务不存在");
            }
            
            // 2. 检查任务是否已完成
            if (!"2".equals(task.getTaskStatus())) {
                return error("任务尚未完成，无法获取坐标信息");
            }
            
            // 3. 检查是否有审查结果
            String reviewResult = task.getReviewResult();
            if (reviewResult == null || reviewResult.trim().isEmpty()) {
                return error("审查结果为空，无法解析坐标");
            }
            
            // 4. 获取关联的合同文件（只取第一个）
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(taskId);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty()) {
                return error("未找到关联的合同文件");
            }
            
            ContractFile contractFile = contractFiles.get(0);
            
            // 5. 检查文件是否支持坐标提取
            if (!coordinateService.isSupportedForCoordinateExtraction(contractFile)) {
                return error("文件类型不支持坐标提取，仅支持PDF格式");
            }
            
            // 6. 解析条款坐标（新方法）
            Map<String, Object> coordinateData = coordinateService.parseClauseCoordinatesFromReviewResult(contractFile, reviewResult);
            
            // 7. 记录坐标解析日志
            logCoordinateAccess(taskId, contractFile.getId(), "PARSE_CLAUSE_COORDINATES");
            
            return success(coordinateData);
            
        } catch (Exception e) {
            logger.error("获取条款坐标失败，任务ID: {}", taskId, e);
            return error("获取坐标信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 搜索文档中特定文本的坐标
     * @param taskId 任务ID
     * @param searchText 搜索文本
     * @param matchType 匹配类型：exact, fuzzy, partial, auto
     * @return 文本坐标信息
     */
    @PreAuthorize("@ss.hasPermi('contract:task:query')")
    @Log(title = "文档文本搜索", businessType = BusinessType.OTHER)
    @GetMapping("/search-coordinates/{taskId}")
    public AjaxResult searchTextCoordinates(@PathVariable Long taskId, 
                                          @RequestParam String searchText,
                                          @RequestParam(defaultValue = "auto") String matchType) {
        try {
            // 1. 参数验证
            if (searchText == null || searchText.trim().isEmpty()) {
                return error("搜索文本不能为空");
            }
            
            // 2. 获取任务和文件信息
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            if (task == null) {
                return error("审查任务不存在");
            }
            
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(taskId);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty()) {
                return error("未找到关联的合同文件");
            }
            
            ContractFile contractFile = contractFiles.get(0);
            
            // 3. 检查文件支持
            if (!coordinateService.isSupportedForCoordinateExtraction(contractFile)) {
                return error("文件类型不支持坐标提取");
            }
            
            // 4. 搜索文本坐标
            List<IContractCoordinateService.RiskTextMatch> matches = 
                coordinateService.findTextCoordinates(contractFile, searchText.trim(), matchType);
            
            // 5. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("contractFileId", contractFile.getId());
            result.put("contractFileName", contractFile.getFileName());
            result.put("searchText", searchText.trim());
            result.put("matchType", matchType);
            result.put("totalMatches", matches.size());
            result.put("matches", matches);
            
            // 6. 记录搜索日志
            logCoordinateAccess(taskId, contractFile.getId(), "SEARCH_TEXT");
            
            return success(result);
            
        } catch (Exception e) {
            logger.error("搜索文本坐标失败，任务ID: {}, 搜索文本: {}", taskId, searchText, e);
            return error("搜索失败：" + e.getMessage());
        }
    }
    
    /**
     * 清理坐标缓存
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PreAuthorize("@ss.hasPermi('contract:task:edit')")
    @Log(title = "清理坐标缓存", businessType = BusinessType.DELETE)
    @DeleteMapping("/coordinates-cache/{taskId}")
    public AjaxResult clearCoordinateCache(@PathVariable Long taskId) {
        try {
            // 1. 获取文件信息
            ContractFile queryFile = new ContractFile();
            queryFile.setTaskId(taskId);
            List<ContractFile> contractFiles = contractFileService.selectContractFileList(queryFile);
            if (contractFiles == null || contractFiles.isEmpty()) {
                return error("未找到关联的合同文件");
            }
            
            ContractFile contractFile = contractFiles.get(0);
            
            // 2. 清理缓存
            coordinateService.clearCoordinateCache(contractFile.getFileMd5());
            
            // 3. 记录操作日志
            logCoordinateAccess(taskId, contractFile.getId(), "CLEAR_CACHE");
            
            return success("缓存清理完成");
            
        } catch (Exception e) {
            logger.error("清理坐标缓存失败，任务ID: {}", taskId, e);
            return error("清理缓存失败：" + e.getMessage());
        }
    }
    
    /**
     * 记录坐标解析相关操作日志
     * @param taskId 任务ID
     * @param fileId 文件ID
     * @param action 操作类型
     */
    private void logCoordinateAccess(Long taskId, Long fileId, String action) {
        try {
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            ContractFile file = contractFileService.selectContractFileById(fileId);
            if (task != null && file != null) {
                String logMsg = String.format("坐标解析操作，任务：%s，文件：%s，操作：%s", 
                                            task.getTaskName(), file.getFileName(), action);
                logger.info("审计日志 - {}", logMsg);
            }
        } catch (Exception e) {
            logger.warn("记录坐标解析日志失败，任务ID: {}, 文件ID: {}", taskId, fileId, e);
        }
    }

    /**
     * 记录文档访问日志
     * @param taskId 任务ID
     * @param fileId 文件ID
     * @param action 操作类型
     */
    private void logDocumentAccess(Long taskId, Long fileId, String action) {
        try {
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            ContractFile file = contractFileService.selectContractFileById(fileId);
            if (task != null && file != null) {
                String logMsg = String.format("文档访问操作，任务：%s，文件：%s，操作：%s", 
                                            task.getTaskName(), file.getFileName(), action);
                logger.info("审计日志 - {}", logMsg);
            }
        } catch (Exception e) {
            logger.warn("记录文档访问日志失败，任务ID: {}, 文件ID: {}", taskId, fileId, e);
        }
    }

    /**
     * 记录报告查看日志
     * @param taskId 任务ID
     * @param action 操作类型
     */
    private void logReportAccess(Long taskId, String action) {
        try {
            ContractReviewTask task = contractReviewTaskService.selectContractReviewTaskById(taskId);
            if (task != null) {
                String logMsg = String.format("用户查看合同审查报告，任务：%s，操作：%s", 
                                            task.getTaskName(), action);
                logger.info("审计日志 - {}", logMsg);
            }
        } catch (Exception e) {
            logger.warn("记录审计日志失败，任务ID: {}", taskId, e);
        }
    }
}
