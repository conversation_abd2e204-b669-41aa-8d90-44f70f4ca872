package com.ruoyi.web.controller.contract;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractCategory;
import com.ruoyi.contract.service.IContractCategoryService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同分类Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/category")
public class ContractCategoryController extends BaseController
{
    @Autowired
    private IContractCategoryService contractCategoryService;

    /**
     * 查询合同分类列表
     */
    @PreAuthorize("@ss.hasPermi('contract:contract:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractCategory contractCategory)
    {
        startPage();
        List<ContractCategory> list = contractCategoryService.selectContractCategoryList(contractCategory);
        return getDataTable(list);
    }

    /**
     * 导出合同分类列表
     */
    @PreAuthorize("@ss.hasPermi('contract:contract:export')")
    @Log(title = "合同分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractCategory contractCategory)
    {
        List<ContractCategory> list = contractCategoryService.selectContractCategoryList(contractCategory);
        ExcelUtil<ContractCategory> util = new ExcelUtil<ContractCategory>(ContractCategory.class);
        util.exportExcel(response, list, "合同分类数据");
    }

    /**
     * 获取合同分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:contract:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractCategoryService.selectContractCategoryById(id));
    }

    /**
     * 新增合同分类
     */
    @PreAuthorize("@ss.hasPermi('contract:contract:add')")
    @Log(title = "合同分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractCategory contractCategory)
    {
        return toAjax(contractCategoryService.insertContractCategory(contractCategory));
    }

    /**
     * 修改合同分类
     */
    @PreAuthorize("@ss.hasPermi('contract:contract:edit')")
    @Log(title = "合同分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractCategory contractCategory)
    {
        return toAjax(contractCategoryService.updateContractCategory(contractCategory));
    }

    /**
     * 删除合同分类
     */
    @PreAuthorize("@ss.hasPermi('contract:contract:remove')")
    @Log(title = "合同分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractCategoryService.deleteContractCategoryByIds(ids));
    }
}
