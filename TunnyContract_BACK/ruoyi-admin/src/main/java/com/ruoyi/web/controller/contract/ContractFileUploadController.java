package com.ruoyi.web.controller.contract;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.contract.ContractFileUploadService;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.Map;

/**
 * 合同文件上传Controller
 * 专门处理合同文件的上传、校验、管理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/upload")
public class ContractFileUploadController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ContractFileUploadController.class);

    @Autowired
    private ContractFileUploadService contractFileUploadService;

    @Autowired
    private IContractFileService contractFileService;

    /**
     * 单个合同文件上传
     * 
     * @param file 上传的文件
     * @param taskId 关联的任务ID（可选）
     * @return 上传结果
     */
    @PostMapping("/single")
    @Log(title = "合同文件上传", businessType = BusinessType.INSERT)
    public AjaxResult uploadSingle(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "taskId", required = false) Long taskId)
    {
        try
        {
            // 参数校验
            contractFileUploadService.validateUploadParams(file, taskId);
            
            // 检查文件MD5是否已存在（去重）
            String fileMd5 = com.ruoyi.common.utils.contract.ContractFileValidator.calculateFileMD5(file);
            ContractFile existingFile = contractFileService.selectContractFileByMd5(fileMd5);
            if (existingFile != null)
            {
                log.warn("文件已存在，MD5: {}, 原文件: {}", fileMd5, existingFile.getFileName());
                AjaxResult result = AjaxResult.success("文件已存在，无需重复上传");
                result.put("existing", true);
                result.put("existingFile", existingFile);
                return result;
            }

            // 上传文件
            Map<String, Object> uploadResult = contractFileUploadService.uploadContractFile(file);
            
            // 保存到数据库
            ContractFile contractFile = new ContractFile();
            contractFile.setTaskId(taskId);
            contractFile.setFileName((String) uploadResult.get("originalFileName"));
            contractFile.setFilePath((String) uploadResult.get("filePath"));
            contractFile.setFileSize((Long) uploadResult.get("fileSize"));
            contractFile.setFileType((String) uploadResult.get("fileType"));
            contractFile.setFileMd5((String) uploadResult.get("fileMd5"));
            contractFile.setUploadStatus("1"); // 上传成功
            contractFile.setParseStatus("0"); // 未解析
            contractFile.setPageCount(contractFileUploadService.estimatePageCount(file));
            contractFile.setCreateBy(getUsername());
            contractFile.setCreateTime(new Date());
            
            int result = contractFileService.insertContractFile(contractFile);
            if (result > 0)
            {
                uploadResult.put("fileId", contractFile.getId());
                log.info("合同文件上传并保存成功：ID={}, 文件名={}", contractFile.getId(), contractFile.getFileName());
                return AjaxResult.success("文件上传成功", uploadResult);
            }
            else
            {
                log.error("合同文件数据库保存失败：{}", contractFile.getFileName());
                return AjaxResult.error("文件上传成功但保存记录失败");
            }
        }
        catch (Exception e)
        {
            log.error("合同文件上传失败", e);
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 批量合同文件上传
     * 
     * @param files 文件数组
     * @param taskId 关联的任务ID（可选）
     * @return 批量上传结果
     */
    @PostMapping("/batch")
    @Log(title = "批量合同文件上传", businessType = BusinessType.INSERT)
    public AjaxResult uploadBatch(@RequestParam("files") MultipartFile[] files,
                                  @RequestParam(value = "taskId", required = false) Long taskId)
    {
        try
        {
            if (files == null || files.length == 0)
            {
                return AjaxResult.error("请选择要上传的文件");
            }

            // 批量上传
            Map<String, Object> batchResult = contractFileUploadService.uploadContractFiles(files);
            
            // 保存成功上传的文件到数据库
            @SuppressWarnings("unchecked")
            Map<String, Object> successResults = (Map<String, Object>) batchResult.get("successResults");
            
            int savedCount = 0;
            for (Map.Entry<String, Object> entry : successResults.entrySet())
            {
                try
                {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> fileResult = (Map<String, Object>) entry.getValue();
                    
                    ContractFile contractFile = new ContractFile();
                    contractFile.setTaskId(taskId);
                    contractFile.setFileName((String) fileResult.get("originalFileName"));
                    contractFile.setFilePath((String) fileResult.get("filePath"));
                    contractFile.setFileSize((Long) fileResult.get("fileSize"));
                    contractFile.setFileType((String) fileResult.get("fileType"));
                    contractFile.setFileMd5((String) fileResult.get("fileMd5"));
                    contractFile.setUploadStatus("1"); // 上传成功
                    contractFile.setParseStatus("0"); // 未解析
                    contractFile.setPageCount(Long.valueOf("1")); // 批量上传暂不预估页数
                    contractFile.setCreateBy(getUsername());
                    contractFile.setCreateTime(new Date());
                    
                    contractFileService.insertContractFile(contractFile);
                    fileResult.put("fileId", contractFile.getId());
                    savedCount++;
                }
                catch (Exception e)
                {
                    log.error("保存批量上传文件记录失败", e);
                }
            }
            
            batchResult.put("savedCount", savedCount);
            log.info("批量合同文件上传完成：总数={}, 成功={}, 保存={}", 
                    files.length, batchResult.get("successCount"), savedCount);
                    
            return AjaxResult.success("批量上传完成", batchResult);
        }
        catch (Exception e)
        {
            log.error("批量合同文件上传失败", e);
            return AjaxResult.error("批量上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件信息（预检查，不实际上传）
     * 
     * @param file 文件
     * @return 文件信息
     */
    @PostMapping("/check")
    public AjaxResult checkFile(@RequestParam("file") MultipartFile file)
    {
        try
        {
            Map<String, Object> fileInfo = contractFileUploadService.getFileInfo(file);
            
            // 检查MD5是否已存在
            String fileMd5 = (String) fileInfo.get("fileMd5");
            ContractFile existingFile = contractFileService.selectContractFileByMd5(fileMd5);
            fileInfo.put("exists", existingFile != null);
            if (existingFile != null)
            {
                fileInfo.put("existingFile", existingFile);
            }
            
            return AjaxResult.success("文件检查完成", fileInfo);
        }
        catch (Exception e)
        {
            log.error("文件检查失败", e);
            return AjaxResult.error("文件检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取支持的文件格式信息
     * 
     * @return 格式信息
     */
    @GetMapping("/formats")
    public AjaxResult getSupportedFormats()
    {
        try
        {
            Map<String, Object> formats = contractFileUploadService.getSupportedFormats();
            return AjaxResult.success(formats);
        }
        catch (Exception e)
        {
            log.error("获取支持格式失败", e);
            return AjaxResult.error("获取格式信息失败：" + e.getMessage());
        }
    }

    /**
     * 删除上传的文件（软删除）
     * 
     * @param fileId 文件ID
     * @return 删除结果
     */
    @DeleteMapping("/{fileId}")
    @Log(title = "删除合同文件", businessType = BusinessType.DELETE)
    public AjaxResult deleteUploadedFile(@PathVariable Long fileId)
    {
        try
        {
            ContractFile contractFile = contractFileService.selectContractFileById(fileId);
            if (contractFile == null)
            {
                return AjaxResult.error("文件不存在");
            }
            
            // 检查权限（只能删除自己上传的文件）
            if (!contractFile.getCreateBy().equals(getUsername()))
            {
                return AjaxResult.error("无权限删除此文件");
            }
            
            // 软删除
            int result = contractFileService.deleteContractFileById(fileId);
            if (result > 0)
            {
                log.info("合同文件删除成功：ID={}, 文件名={}", fileId, contractFile.getFileName());
                return AjaxResult.success("文件删除成功");
            }
            else
            {
                return AjaxResult.error("文件删除失败");
            }
        }
        catch (Exception e)
        {
            log.error("删除合同文件失败：fileId={}", fileId, e);
            return AjaxResult.error("文件删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户上传的文件列表
     * 
     * @param taskId 任务ID（可选）
     * @return 文件列表
     */
    @GetMapping("/list")
    public AjaxResult getUploadedFiles(@RequestParam(value = "taskId", required = false) Long taskId)
    {
        try
        {
            ContractFile queryParam = new ContractFile();
            queryParam.setTaskId(taskId);
            queryParam.setCreateBy(getUsername());
            
            return AjaxResult.success(contractFileService.selectContractFileList(queryParam));
        }
        catch (Exception e)
        {
            log.error("获取上传文件列表失败", e);
            return AjaxResult.error("获取文件列表失败：" + e.getMessage());
        }
    }
}