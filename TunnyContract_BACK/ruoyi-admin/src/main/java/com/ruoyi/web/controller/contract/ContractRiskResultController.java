package com.ruoyi.web.controller.contract;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractRiskResult;
import com.ruoyi.contract.service.IContractRiskResultService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同风险识别结果Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/riskresult")
public class ContractRiskResultController extends BaseController
{
    @Autowired
    private IContractRiskResultService contractRiskResultService;

    /**
     * 查询合同风险识别结果列表
     */
    @PreAuthorize("@ss.hasPermi('contract:riskresult:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractRiskResult contractRiskResult)
    {
        startPage();
        List<ContractRiskResult> list = contractRiskResultService.selectContractRiskResultList(contractRiskResult);
        return getDataTable(list);
    }

    /**
     * 导出合同风险识别结果列表
     */
    @PreAuthorize("@ss.hasPermi('contract:riskresult:export')")
    @Log(title = "合同风险识别结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractRiskResult contractRiskResult)
    {
        List<ContractRiskResult> list = contractRiskResultService.selectContractRiskResultList(contractRiskResult);
        ExcelUtil<ContractRiskResult> util = new ExcelUtil<ContractRiskResult>(ContractRiskResult.class);
        util.exportExcel(response, list, "合同风险识别结果数据");
    }

    /**
     * 获取合同风险识别结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:riskresult:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractRiskResultService.selectContractRiskResultById(id));
    }

    /**
     * 新增合同风险识别结果
     */
    @PreAuthorize("@ss.hasPermi('contract:riskresult:add')")
    @Log(title = "合同风险识别结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractRiskResult contractRiskResult)
    {
        return toAjax(contractRiskResultService.insertContractRiskResult(contractRiskResult));
    }

    /**
     * 修改合同风险识别结果
     */
    @PreAuthorize("@ss.hasPermi('contract:riskresult:edit')")
    @Log(title = "合同风险识别结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractRiskResult contractRiskResult)
    {
        return toAjax(contractRiskResultService.updateContractRiskResult(contractRiskResult));
    }

    /**
     * 删除合同风险识别结果
     */
    @PreAuthorize("@ss.hasPermi('contract:riskresult:remove')")
    @Log(title = "合同风险识别结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractRiskResultService.deleteContractRiskResultByIds(ids));
    }
}
