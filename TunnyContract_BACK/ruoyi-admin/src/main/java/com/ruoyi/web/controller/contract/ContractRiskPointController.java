package com.ruoyi.web.controller.contract;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractRiskPoint;
import com.ruoyi.contract.service.IContractRiskPointService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同风险点Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/point")
public class ContractRiskPointController extends BaseController
{
    @Autowired
    private IContractRiskPointService contractRiskPointService;

    /**
     * 查询合同风险点列表
     */
    @PreAuthorize("@ss.hasPermi('contract:point:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractRiskPoint contractRiskPoint)
    {
        startPage();
        List<ContractRiskPoint> list = contractRiskPointService.selectContractRiskPointList(contractRiskPoint);
        return getDataTable(list);
    }

    /**
     * 导出合同风险点列表
     */
    @PreAuthorize("@ss.hasPermi('contract:point:export')")
    @Log(title = "合同风险点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractRiskPoint contractRiskPoint)
    {
        List<ContractRiskPoint> list = contractRiskPointService.selectContractRiskPointList(contractRiskPoint);
        ExcelUtil<ContractRiskPoint> util = new ExcelUtil<ContractRiskPoint>(ContractRiskPoint.class);
        util.exportExcel(response, list, "合同风险点数据");
    }

    /**
     * 获取合同风险点详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:point:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractRiskPointService.selectContractRiskPointById(id));
    }

    /**
     * 新增合同风险点
     */
    @PreAuthorize("@ss.hasPermi('contract:point:add')")
    @Log(title = "合同风险点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractRiskPoint contractRiskPoint)
    {
        return toAjax(contractRiskPointService.insertContractRiskPoint(contractRiskPoint));
    }

    /**
     * 修改合同风险点
     */
    @PreAuthorize("@ss.hasPermi('contract:point:edit')")
    @Log(title = "合同风险点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractRiskPoint contractRiskPoint)
    {
        return toAjax(contractRiskPointService.updateContractRiskPoint(contractRiskPoint));
    }

    /**
     * 删除合同风险点
     */
    @PreAuthorize("@ss.hasPermi('contract:point:remove')")
    @Log(title = "合同风险点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractRiskPointService.deleteContractRiskPointByIds(ids));
    }
}
