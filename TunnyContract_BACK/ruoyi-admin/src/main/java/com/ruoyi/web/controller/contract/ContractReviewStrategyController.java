package com.ruoyi.web.controller.contract;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractReviewStrategy;
import com.ruoyi.contract.dto.StrategyClauseRiskDTO;
import com.ruoyi.contract.service.IContractReviewStrategyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同审查策略Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/strategy")
public class ContractReviewStrategyController extends BaseController
{
    @Autowired
    private IContractReviewStrategyService contractReviewStrategyService;

    /**
     * 查询合同审查策略列表
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractReviewStrategy contractReviewStrategy)
    {
        startPage();
        List<ContractReviewStrategy> list = contractReviewStrategyService.selectContractReviewStrategyList(contractReviewStrategy);
        return getDataTable(list);
    }

    /**
     * 导出合同审查策略列表
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:export')")
    @Log(title = "合同审查策略", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractReviewStrategy contractReviewStrategy)
    {
        List<ContractReviewStrategy> list = contractReviewStrategyService.selectContractReviewStrategyList(contractReviewStrategy);
        ExcelUtil<ContractReviewStrategy> util = new ExcelUtil<ContractReviewStrategy>(ContractReviewStrategy.class);
        util.exportExcel(response, list, "合同审查策略数据");
    }

    /**
     * 获取合同审查策略详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractReviewStrategyService.selectContractReviewStrategyById(id));
    }

    /**
     * 新增合同审查策略
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:add')")
    @Log(title = "合同审查策略", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractReviewStrategy contractReviewStrategy)
    {
        return toAjax(contractReviewStrategyService.insertContractReviewStrategy(contractReviewStrategy));
    }

    /**
     * 修改合同审查策略
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:edit')")
    @Log(title = "合同审查策略", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractReviewStrategy contractReviewStrategy)
    {
        return toAjax(contractReviewStrategyService.updateContractReviewStrategy(contractReviewStrategy));
    }

    /**
     * 删除合同审查策略
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:remove')")
    @Log(title = "合同审查策略", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractReviewStrategyService.deleteContractReviewStrategyByIds(ids));
    }

    /**
     * 发布策略
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:edit')")
    @Log(title = "发布合同审查策略", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{id}")
    public AjaxResult publishStrategy(@PathVariable("id") Long id)
    {
        return toAjax(contractReviewStrategyService.publishStrategy(id));
    }

    /**
     * 取消发布策略（设为草稿）
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:edit')")
    @Log(title = "取消发布合同审查策略", businessType = BusinessType.UPDATE)
    @PutMapping("/unpublish/{id}")
    public AjaxResult unpublishStrategy(@PathVariable("id") Long id)
    {
        return toAjax(contractReviewStrategyService.unpublishStrategy(id));
    }

    /**
     * 复制策略
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:add')")
    @Log(title = "复制合同审查策略", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{id}")
    public AjaxResult copyStrategy(@PathVariable("id") Long id, @RequestBody Map<String, Object> params)
    {
        String strategyName = (String) params.get("strategyName");
        return toAjax(contractReviewStrategyService.copyStrategy(id, strategyName));
    }

    /**
     * 获取策略统计信息
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:list')")
    @GetMapping("/stats")
    public AjaxResult getStrategyStats()
    {
        return success(contractReviewStrategyService.getStrategyStats());
    }

    /**
     * 根据策略ID获取关联的审查条款清单及每个条款对应的风险点列表
     */
    @PreAuthorize("@ss.hasPermi('contract:strategy:query')")
    @GetMapping("/clauses-with-risks/{strategyId}")
    public AjaxResult getStrategyClausesWithRiskPoints(@PathVariable("strategyId") Long strategyId)
    {
        List<StrategyClauseRiskDTO> result = contractReviewStrategyService.getStrategyClausesWithRiskPoints(strategyId);
        return success(result);
    }
}
