package com.ruoyi.web.controller.contract;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.contract.AsyncDocumentParseService;
import com.ruoyi.common.utils.contract.AsyncDocumentParseService.ParseTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 异步文档解析控制器
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/async-parse")
public class AsyncDocumentParseController extends BaseController
{
    @Autowired
    private AsyncDocumentParseService asyncDocumentParseService;

    /**
     * 提交异步解析任务
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:submit')")
    @Log(title = "异步文档解析", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    public AjaxResult submitParseTask(@RequestParam("filePath") String filePath)
    {
        try
        {
            String taskId = asyncDocumentParseService.submitParseTask(filePath);
            return success("任务提交成功", Collections.singletonMap("taskId", taskId));
        }
        catch (Exception e)
        {
            logger.error("提交异步解析任务失败：{}", filePath, e);
            return error("任务提交失败：" + e.getMessage());
        }
    }

    /**
     * 批量提交解析任务
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:submit')")
    @Log(title = "批量异步文档解析", businessType = BusinessType.INSERT)
    @PostMapping("/batch-submit")
    public AjaxResult submitBatchParseTasks(@RequestBody Map<String, Object> params)
    {
        try
        {
            @SuppressWarnings("unchecked")
            List<String> filePaths = (List<String>) params.get("filePaths");
            
            if (filePaths == null || filePaths.isEmpty())
            {
                return error("文件路径列表不能为空");
            }

            List<String> taskIds = asyncDocumentParseService.submitBatchParseTasks(filePaths);
            Map<String, Object> result = new HashMap<>();
            result.put("taskIds", taskIds);
            result.put("taskCount", taskIds.size());
            return success("批量任务提交成功", result);
        }
        catch (Exception e)
        {
            logger.error("批量提交异步解析任务失败", e);
            return error("批量任务提交失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务状态
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @GetMapping("/status/{taskId}")
    public AjaxResult getTaskStatus(@PathVariable String taskId)
    {
        try
        {
            ParseTask task = asyncDocumentParseService.getTaskStatus(taskId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", task.getTaskId());
            result.put("fileName", task.getFileName());
            result.put("fileType", task.getFileType());
            result.put("status", task.getStatus().name());
            result.put("statusDescription", task.getStatus().getDescription());
            result.put("progress", task.getProgress());
            result.put("startTime", task.getStartTime());
            result.put("endTime", task.getEndTime());
            result.put("duration", task.getDuration());
            result.put("errorMessage", task.getErrorMessage());
            
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("获取任务状态失败：{}", taskId, e);
            return error("获取任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有任务状态
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @GetMapping("/status/all")
    public AjaxResult getAllTaskStatus()
    {
        try
        {
            Map<String, ParseTask> taskMap = asyncDocumentParseService.getAllTaskStatus();
            
            // 转换为前端友好的格式
            List<Map<String, Object>> taskList = taskMap.entrySet().stream()
                    .map(entry -> {
                        ParseTask task = entry.getValue();
                        Map<String, Object> totaskMap = new HashMap<>();
                        totaskMap.put("taskId", task.getTaskId());
                        totaskMap.put("fileName", task.getFileName());
                        totaskMap.put("fileType", task.getFileType());
                        totaskMap.put("status", task.getStatus().name());
                        totaskMap.put("statusDescription", task.getStatus().getDescription());
                        totaskMap.put("progress", task.getProgress());
                        totaskMap.put("startTime", task.getStartTime());
                        totaskMap.put("endTime", task.getEndTime());
                        totaskMap.put("duration", task.getDuration());
                        totaskMap.put("errorMessage", task.getErrorMessage());
                        return totaskMap;
                    })
                    .sorted((a, b) -> Long.compare((Long) b.get("startTime"), (Long) a.get("startTime")))
                    .collect(Collectors.toList());
            
            Map<String, Object> result = new HashMap<>();
            result.put("tasks", taskList);
            result.put("totalCount", taskList.size());
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("获取所有任务状态失败", e);
            return error("获取所有任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 等待任务完成并获取结果
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @GetMapping("/wait/{taskId}")
    public AjaxResult waitForTaskCompletion(@PathVariable String taskId,
                                           @RequestParam(value = "timeoutSeconds", defaultValue = "300") long timeoutSeconds)
    {
        try
        {
            Object result = asyncDocumentParseService.waitForTaskCompletion(taskId, timeoutSeconds);
            
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("taskId", taskId);
            responseMap.put("result", result);
            return success("任务执行完成", responseMap);
        }
        catch (Exception e)
        {
            logger.error("等待任务完成失败：{}", taskId, e);
            return error("等待任务完成失败：" + e.getMessage());
        }
    }

    /**
     * 取消解析任务
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:cancel')")
    @Log(title = "取消异步文档解析", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{taskId}")
    public AjaxResult cancelTask(@PathVariable String taskId)
    {
        try
        {
            boolean cancelled = asyncDocumentParseService.cancelTask(taskId);
            
            if (cancelled)
            {
                return success("任务取消成功", Collections.singletonMap("taskId", taskId));
            }
            else
            {
                return error("任务取消失败，可能任务已完成或不存在");
            }
        }
        catch (Exception e)
        {
            logger.error("取消任务失败：{}", taskId, e);
            return error("取消任务失败：" + e.getMessage());
        }
    }

    /**
     * 清理已完成的任务
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:cleanup')")
    @Log(title = "清理异步解析任务", businessType = BusinessType.DELETE)
    @PostMapping("/cleanup")
    public AjaxResult cleanupCompletedTasks(@RequestParam(value = "olderThanMinutes", defaultValue = "60") int olderThanMinutes)
    {
        try
        {
            int cleanedCount = asyncDocumentParseService.cleanupCompletedTasks(olderThanMinutes);
            
            Map<String, Object> result = new HashMap<>();
            result.put("cleanedCount", cleanedCount);
            result.put("olderThanMinutes", olderThanMinutes);
            return success("任务清理完成", result);
        }
        catch (Exception e)
        {
            logger.error("清理任务失败", e);
            return error("清理任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @GetMapping("/statistics")
    public AjaxResult getTaskStatistics()
    {
        try
        {
            Map<String, Object> stats = asyncDocumentParseService.getTaskStatistics();
            return success(stats);
        }
        catch (Exception e)
        {
            logger.error("获取任务统计信息失败", e);
            return error("获取任务统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 系统健康检查
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @GetMapping("/health")
    public AjaxResult healthCheck()
    {
        try
        {
            Map<String, Object> health = asyncDocumentParseService.healthCheck();
            return success(health);
        }
        catch (Exception e)
        {
            logger.error("健康检查失败", e);
            return error("健康检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务进度（轮询接口）
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @GetMapping("/progress/{taskId}")
    public AjaxResult getTaskProgress(@PathVariable String taskId)
    {
        try
        {
            ParseTask task = asyncDocumentParseService.getTaskStatus(taskId);
            
            Map<String, Object> progress = new HashMap<>();
            progress.put("taskId", task.getTaskId());
            progress.put("status", task.getStatus().name());
            progress.put("statusDescription", task.getStatus().getDescription());
            progress.put("progress", task.getProgress());
            progress.put("duration", task.getDuration());
            progress.put("isCompleted", task.getStatus() == AsyncDocumentParseService.ParseStatus.SUCCESS ||
                                   task.getStatus() == AsyncDocumentParseService.ParseStatus.FAILED ||
                                   task.getStatus() == AsyncDocumentParseService.ParseStatus.CANCELLED);
            
            return success(progress);
        }
        catch (Exception e)
        {
            logger.error("获取任务进度失败：{}", taskId, e);
            return error("获取任务进度失败：" + e.getMessage());
        }
    }

    /**
     * 批量获取任务状态
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:query')")
    @PostMapping("/batch-status")
    public AjaxResult getBatchTaskStatus(@RequestBody Map<String, Object> params)
    {
        try
        {
            @SuppressWarnings("unchecked")
            List<String> taskIds = (List<String>) params.get("taskIds");
            
            if (taskIds == null || taskIds.isEmpty())
            {
                return error("任务ID列表不能为空");
            }

            List<Map<String, Object>> results = taskIds.stream()
                    .map(taskId -> {
                        try
                        {
                            ParseTask task = asyncDocumentParseService.getTaskStatus(taskId);
                            Map<String, Object> taskResult = new HashMap<>();
                            taskResult.put("taskId", task.getTaskId());
                            taskResult.put("fileName", task.getFileName());
                            taskResult.put("status", task.getStatus().name());
                            taskResult.put("statusDescription", task.getStatus().getDescription());
                            taskResult.put("progress", task.getProgress());
                            taskResult.put("duration", task.getDuration());
                            taskResult.put("errorMessage", task.getErrorMessage() != null ? task.getErrorMessage() : "");
                            return taskResult;
                        }
                        catch (Exception e)
                        {
                            Map<String, Object> errorResult = new HashMap<>();
                            errorResult.put("taskId", taskId);
                            errorResult.put("status", "NOT_FOUND");
                            errorResult.put("statusDescription", "任务不存在");
                            errorResult.put("errorMessage", e.getMessage());
                            return errorResult;
                        }
                    })
                    .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("results", results);
            result.put("totalCount", results.size());
            return success(result);
        }
        catch (Exception e)
        {
            logger.error("批量获取任务状态失败", e);
            return error("批量获取任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 重试失败的任务
     */
    @PreAuthorize("@ss.hasPermi('contract:parse:submit')")
    @Log(title = "重试异步文档解析", businessType = BusinessType.UPDATE)
    @PostMapping("/retry/{taskId}")
    public AjaxResult retryFailedTask(@PathVariable String taskId)
    {
        try
        {
            ParseTask originalTask = asyncDocumentParseService.getTaskStatus(taskId);
            
            if (originalTask.getStatus() != AsyncDocumentParseService.ParseStatus.FAILED)
            {
                return error("只能重试失败的任务，当前任务状态：" + originalTask.getStatus().getDescription());
            }

            // 重新提交任务
            String newTaskId = asyncDocumentParseService.submitParseTask(originalTask.getFilePath());
            
            Map<String, Object> result = new HashMap<>();
            result.put("originalTaskId", taskId);
            result.put("newTaskId", newTaskId);
            return success("任务重试提交成功", result);
        }
        catch (Exception e)
        {
            logger.error("重试任务失败：{}", taskId, e);
            return error("重试任务失败：" + e.getMessage());
        }
    }
}