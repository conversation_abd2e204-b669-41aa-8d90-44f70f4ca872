package com.ruoyi.web.controller.contract;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.contract.domain.ContractFile;
import com.ruoyi.contract.service.IContractFileService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 合同文件Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/file")
public class ContractFileController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ContractFileController.class);

    @Autowired
    private IContractFileService contractFileService;

    /**
     * 查询合同文件列表
     */
    @PreAuthorize("@ss.hasPermi('contract:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractFile contractFile)
    {
        startPage();
        List<ContractFile> list = contractFileService.selectContractFileList(contractFile);
        return getDataTable(list);
    }

    /**
     * 导出合同文件列表
     */
    @PreAuthorize("@ss.hasPermi('contract:file:export')")
    @Log(title = "合同文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractFile contractFile)
    {
        List<ContractFile> list = contractFileService.selectContractFileList(contractFile);
        ExcelUtil<ContractFile> util = new ExcelUtil<ContractFile>(ContractFile.class);
        util.exportExcel(response, list, "合同文件数据");
    }

    /**
     * 获取合同文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:file:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractFileService.selectContractFileById(id));
    }

    /**
     * 新增合同文件
     */
    @PreAuthorize("@ss.hasPermi('contract:file:add')")
    @Log(title = "合同文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractFile contractFile)
    {
        return toAjax(contractFileService.insertContractFile(contractFile));
    }

    /**
     * 修改合同文件
     */
    @PreAuthorize("@ss.hasPermi('contract:file:edit')")
    @Log(title = "合同文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractFile contractFile)
    {
        return toAjax(contractFileService.updateContractFile(contractFile));
    }

    /**
     * 删除合同文件
     */
    @PreAuthorize("@ss.hasPermi('contract:file:remove')")
    @Log(title = "合同文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractFileService.deleteContractFileByIds(ids));
    }

    /**
     * 根据任务ID查询合同文件列表
     * 
     * @param taskId 任务ID
     * @return 文件列表
     */
    @PreAuthorize("@ss.hasPermi('contract:file:query')")
    @GetMapping("/task/{taskId}")
    public AjaxResult getFilesByTaskId(@PathVariable Long taskId)
    {
        try
        {
            ContractFile queryParam = new ContractFile();
            queryParam.setTaskId(taskId);
            List<ContractFile> list = contractFileService.selectContractFileList(queryParam);
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("total", list.size());
            
            // 统计不同状态的文件数量
            long uploadedCount = list.stream().filter(f -> "1".equals(f.getUploadStatus())).count();
            long parsedCount = list.stream().filter(f -> "2".equals(f.getParseStatus())).count();
            long failedCount = list.stream().filter(f -> "3".equals(f.getParseStatus())).count();
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("uploaded", uploadedCount);
            statistics.put("parsed", parsedCount);
            statistics.put("failed", failedCount);
            statistics.put("pending", list.size() - parsedCount - failedCount);
            result.put("statistics", statistics);
            
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            log.error("根据任务ID查询文件失败：taskId={}", taskId, e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新文件解析状态
     * 
     * @param ids 文件ID数组
     * @param parseStatus 解析状态
     * @return 更新结果
     */
    @PreAuthorize("@ss.hasPermi('contract:file:edit')")
    @Log(title = "批量更新解析状态", businessType = BusinessType.UPDATE)
    @PutMapping("/parseStatus")
    public AjaxResult batchUpdateParseStatus(@RequestParam Long[] ids, 
                                           @RequestParam String parseStatus)
    {
        try
        {
            if (ids == null || ids.length == 0)
            {
                return AjaxResult.error("请选择要更新的文件");
            }
            
            if (StringUtils.isEmpty(parseStatus))
            {
                return AjaxResult.error("解析状态不能为空");
            }
            
            int updateCount = 0;
            for (Long id : ids)
            {
                ContractFile contractFile = new ContractFile();
                contractFile.setId(id);
                contractFile.setParseStatus(parseStatus);
                contractFile.setUpdateBy(getUsername());
                contractFile.setUpdateTime(new Date());
                
                int result = contractFileService.updateContractFile(contractFile);
                if (result > 0)
                {
                    updateCount++;
                }
            }
            
            log.info("批量更新解析状态完成：总数={}, 成功={}, 状态={}", ids.length, updateCount, parseStatus);
            return AjaxResult.success("批量更新成功，共更新" + updateCount + "个文件");
        }
        catch (Exception e)
        {
            log.error("批量更新解析状态失败", e);
            return AjaxResult.error("批量更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件统计信息
     * 
     * @param taskId 任务ID（可选）
     * @return 统计信息
     */
    @PreAuthorize("@ss.hasPermi('contract:file:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(@RequestParam(required = false) Long taskId)
    {
        try
        {
            ContractFile queryParam = new ContractFile();
            if (taskId != null)
            {
                queryParam.setTaskId(taskId);
            }
            
            List<ContractFile> allFiles = contractFileService.selectContractFileList(queryParam);
            
            Map<String, Object> statistics = new HashMap<>();
            
            // 总体统计
            statistics.put("totalFiles", allFiles.size());
            statistics.put("totalSize", allFiles.stream().mapToLong(f -> f.getFileSize() != null ? f.getFileSize() : 0).sum());
            
            // 按文件类型统计
            Map<String, Long> typeStats = allFiles.stream()
                    .collect(HashMap::new,
                            (m, f) -> m.merge(f.getFileType() != null ? f.getFileType() : "unknown", 1L, Long::sum),
                            (m1, m2) -> m1.putAll(m2));
            statistics.put("typeStatistics", typeStats);

            // 按上传状态统计
            Map<String, Long> uploadStats = allFiles.stream()
                    .collect(HashMap::new,
                            (m, f) -> m.merge(f.getUploadStatus() != null ? f.getUploadStatus() : "0", 1L, Long::sum),
                            (m1, m2) -> m1.putAll(m2));
            statistics.put("uploadStatistics", uploadStats);

            // 按解析状态统计
            Map<String, Long> parseStats = allFiles.stream()
                    .collect(HashMap::new,
                            (m, f) -> m.merge(f.getParseStatus() != null ? f.getParseStatus() : "0", 1L, Long::sum),
                            (m1, m2) -> m1.putAll(m2));
            statistics.put("parseStatistics", parseStats);
            
            // 最近文件
            List<ContractFile> recentFiles = allFiles.stream()
                    .filter(f -> f.getCreateTime() != null)
                    .sorted((f1, f2) -> f2.getCreateTime().compareTo(f1.getCreateTime()))
                    .limit(5)
                    .collect(java.util.stream.Collectors.toList());
            statistics.put("recentFiles", recentFiles);
            
            return AjaxResult.success(statistics);
        }
        catch (Exception e)
        {
            log.error("获取文件统计信息失败", e);
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 重置文件解析状态
     * 
     * @param id 文件ID
     * @return 重置结果
     */
    @PreAuthorize("@ss.hasPermi('contract:file:edit')")
    @Log(title = "重置解析状态", businessType = BusinessType.UPDATE)
    @PutMapping("/resetParse/{id}")
    public AjaxResult resetParseStatus(@PathVariable Long id)
    {
        try
        {
            ContractFile contractFile = contractFileService.selectContractFileById(id);
            if (contractFile == null)
            {
                return AjaxResult.error("文件不存在");
            }
            
            // 重置解析相关状态
            contractFile.setParseStatus("0"); // 未解析
            contractFile.setContentText(null);
            contractFile.setPageCount(null);
            contractFile.setUpdateBy(getUsername());
            contractFile.setUpdateTime(new Date());
            
            int result = contractFileService.updateContractFile(contractFile);
            if (result > 0)
            {
                log.info("重置文件解析状态成功：fileId={}, fileName={}", id, contractFile.getFileName());
                return AjaxResult.success("解析状态重置成功");
            }
            else
            {
                return AjaxResult.error("解析状态重置失败");
            }
        }
        catch (Exception e)
        {
            log.error("重置文件解析状态失败：fileId={}", id, e);
            return AjaxResult.error("重置失败：" + e.getMessage());
        }
    }

    /**
     * 检查文件是否可以删除
     * 
     * @param id 文件ID
     * @return 检查结果
     */
    @PreAuthorize("@ss.hasPermi('contract:file:query')")
    @GetMapping("/checkDeletable/{id}")
    public AjaxResult checkDeletable(@PathVariable Long id)
    {
        try
        {
            ContractFile contractFile = contractFileService.selectContractFileById(id);
            if (contractFile == null)
            {
                return AjaxResult.error("文件不存在");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("deletable", true);
            result.put("reason", "");
            
            // 检查是否有关联的审查任务
            if (contractFile.getTaskId() != null)
            {
                // TODO: 检查关联的审查任务状态
                // 如果任务正在进行中，可能不允许删除
                result.put("hasTask", true);
                result.put("taskId", contractFile.getTaskId());
            }
            
            // 检查是否已解析
            if ("2".equals(contractFile.getParseStatus()))
            {
                result.put("parsed", true);
                result.put("warning", "文件已解析，删除后将丢失解析结果");
            }
            
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            log.error("检查文件删除权限失败：fileId={}", id, e);
            return AjaxResult.error("检查失败：" + e.getMessage());
        }
    }

    /**
     * 根据MD5查询重复文件
     * 
     * @param md5 MD5值
     * @return 重复文件信息
     */
    @PreAuthorize("@ss.hasPermi('contract:file:query')")
    @GetMapping("/duplicate/{md5}")
    public AjaxResult checkDuplicate(@PathVariable String md5)
    {
        try
        {
            if (StringUtils.isEmpty(md5))
            {
                return AjaxResult.error("MD5值不能为空");
            }
            
            ContractFile existingFile = contractFileService.selectContractFileByMd5(md5);
            
            Map<String, Object> result = new HashMap<>();
            result.put("duplicate", existingFile != null);
            if (existingFile != null)
            {
                result.put("existingFile", existingFile);
            }
            
            return AjaxResult.success(result);
        }
        catch (Exception e)
        {
            log.error("检查重复文件失败：md5={}", md5, e);
            return AjaxResult.error("检查重复文件失败：" + e.getMessage());
        }
    }

    /**
     * 获取任务的文件解析状态
     *
     * @param taskId 任务ID
     * @return 文件解析状态信息
     */
    @PreAuthorize("@ss.hasPermi('contract:file:query')")
    @GetMapping("/taskStatus/{taskId}")
    public AjaxResult getTaskFileStatus(@PathVariable Long taskId)
    {
        try
        {
            if (taskId == null)
            {
                return AjaxResult.error("任务ID不能为空");
            }

            // 复用现有统计逻辑
            ContractFile queryParam = new ContractFile();
            queryParam.setTaskId(taskId);
            List<ContractFile> files = contractFileService.selectContractFileList(queryParam);

            // 计算解析状态统计
            Map<String, Object> status = new HashMap<>();
            status.put("totalFiles", files.size());

            // 统计各种状态的文件数量
            long parsedFiles = files.stream().filter(f -> "2".equals(f.getParseStatus())).count();
            long failedFiles = files.stream().filter(f -> "3".equals(f.getParseStatus())).count();
            long parsingFiles = files.stream().filter(f -> "1".equals(f.getParseStatus())).count();
            long pendingFiles = files.size() - parsedFiles - failedFiles - parsingFiles;

            status.put("parsedFiles", parsedFiles);
            status.put("failedFiles", failedFiles);
            status.put("parsingFiles", parsingFiles);
            status.put("pendingFiles", pendingFiles);

            // 计算汇总状态
            String fileParseStatus = calculateFileParseStatus(files.size(), parsedFiles, failedFiles, parsingFiles);
            status.put("fileParseStatus", fileParseStatus);

            // 添加详细的文件列表（可选）
            status.put("files", files);

            log.debug("获取任务文件状态：taskId={}, 总数={}, 已解析={}, 失败={}, 解析中={}, 状态={}",
                     taskId, files.size(), parsedFiles, failedFiles, parsingFiles, fileParseStatus);

            return AjaxResult.success(status);
        }
        catch (Exception e)
        {
            log.error("获取任务文件状态失败：taskId={}", taskId, e);
            return AjaxResult.error("获取文件状态失败：" + e.getMessage());
        }
    }

    /**
     * 计算文件解析的汇总状态
     *
     * @param totalFiles 总文件数
     * @param parsedFiles 已解析文件数
     * @param failedFiles 解析失败文件数
     * @param parsingFiles 解析中文件数
     * @return 汇总状态 (0-未开始，1-解析中，2-全部成功，3-部分失败)
     */
    private String calculateFileParseStatus(long totalFiles, long parsedFiles, long failedFiles, long parsingFiles)
    {
        if (totalFiles == 0)
        {
            return "0"; // 无文件
        }

        if (parsedFiles == totalFiles)
        {
            return "2"; // 全部解析成功
        }

        if (failedFiles > 0)
        {
            return "3"; // 有解析失败
        }

        if (parsingFiles > 0)
        {
            return "1"; // 解析中
        }

        return "0"; // 未开始解析
    }
}
