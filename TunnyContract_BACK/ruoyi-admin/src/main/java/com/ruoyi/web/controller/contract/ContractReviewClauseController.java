package com.ruoyi.web.controller.contract;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.contract.domain.ContractReviewClause;
import com.ruoyi.contract.service.IContractReviewClauseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 合同审查条款Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/contract/clause")
public class ContractReviewClauseController extends BaseController
{
    @Autowired
    private IContractReviewClauseService contractReviewClauseService;

    /**
     * 查询合同审查条款列表
     */
    @PreAuthorize("@ss.hasPermi('contract:clause:list')")
    @GetMapping("/list")
    public TableDataInfo list(ContractReviewClause contractReviewClause)
    {
        startPage();
        List<ContractReviewClause> list = contractReviewClauseService.selectContractReviewClauseList(contractReviewClause);
        return getDataTable(list);
    }

    /**
     * 导出合同审查条款列表
     */
    @PreAuthorize("@ss.hasPermi('contract:clause:export')")
    @Log(title = "合同审查条款", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractReviewClause contractReviewClause)
    {
        List<ContractReviewClause> list = contractReviewClauseService.selectContractReviewClauseList(contractReviewClause);
        ExcelUtil<ContractReviewClause> util = new ExcelUtil<ContractReviewClause>(ContractReviewClause.class);
        util.exportExcel(response, list, "合同审查条款数据");
    }

    /**
     * 获取合同审查条款详细信息
     */
    @PreAuthorize("@ss.hasPermi('contract:clause:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(contractReviewClauseService.selectContractReviewClauseById(id));
    }

    /**
     * 新增合同审查条款
     */
    @PreAuthorize("@ss.hasPermi('contract:clause:add')")
    @Log(title = "合同审查条款", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ContractReviewClause contractReviewClause)
    {
        return toAjax(contractReviewClauseService.insertContractReviewClause(contractReviewClause));
    }

    /**
     * 修改合同审查条款
     */
    @PreAuthorize("@ss.hasPermi('contract:clause:edit')")
    @Log(title = "合同审查条款", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ContractReviewClause contractReviewClause)
    {
        return toAjax(contractReviewClauseService.updateContractReviewClause(contractReviewClause));
    }

    /**
     * 删除合同审查条款
     */
    @PreAuthorize("@ss.hasPermi('contract:clause:remove')")
    @Log(title = "合同审查条款", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(contractReviewClauseService.deleteContractReviewClauseByIds(ids));
    }
}
