# Dify 智能体快速开始指南

## 🚀 5分钟快速上手

### 1. 配置智能体

在 `application.yml` 中添加配置：

```yaml
dify:
  enabled: true
  base-url: "https://api.dify.ai"
  default-user: "system"
  connection:
    connect-timeout: 30000
    read-timeout: 60000
    write-timeout: 60000
  agents:
    my-assistant:
      enabled: true
      type: "chat"
      description: "我的智能助手"
      api:
        key: "app-xxxxxxxxxxxxxxxxxxxxxxxx"  # 替换为你的 API Key
```

### 2. 注入服务

```java
@RestController
@RequestMapping("/api/chat")
public class ChatController {
    
    @Autowired
    private IDifyService difyService;
    
    @PostMapping("/send")
    public AjaxResult sendMessage(@RequestBody ChatRequest request) {
        try {
            ChatMessageResponse response = difyService.sendChatMessage(
                "my-assistant",           // 智能体名称
                request.getMessage(),     // 用户消息
                request.getUserId(),      // 用户ID
                request.getConversationId() // 会话ID（可选）
            );
            
            return AjaxResult.success(response);
        } catch (DifyException e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
```

### 3. 启动应用

启动应用后，你会看到类似的日志：

```
=== Dify智能体服务初始化信息 ===
服务状态: 已启用
服务地址: https://api.dify.ai
默认用户: system
智能体配置:
  - 总数量: 1
  - 启用数量: 1
  📱 智能体: my-assistant
    - 类型: chat
    - 描述: 我的智能助手
    - API Key: app-1234***
    - 状态: 已启用
=== Dify智能体服务初始化完成 ===
```

### 4. 测试接口

```bash
curl -X POST http://localhost:8080/api/chat/send \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好，请介绍一下自己",
    "userId": "user123"
  }'
```

## 📝 常用代码片段

### 基础对话
```java
ChatMessageResponse response = difyService.sendChatMessage(
    "agent-name", "用户消息", "user-id", null
);
System.out.println("回复: " + response.getAnswer());
```

### 文本生成
```java
Map<String, Object> inputs = Map.of(
    "topic", "人工智能",
    "style", "专业"
);
CompletionResponse response = difyService.sendCompletionMessage(
    "agent-name", inputs, "user-id"
);
```

### 异常处理
```java
try {
    // Dify 调用
} catch (DifyException e) {
    if ("AGENT_NOT_FOUND".equals(e.getErrorCode())) {
        // 智能体不存在
    } else if ("SERVICE_UNAVAILABLE".equals(e.getErrorCode())) {
        // 服务不可用
    }
}
```

## 🔧 常见问题

### Q: 如何获取 API Key？
A: 登录 Dify 控制台，在应用设置中找到 API Key。

### Q: 支持哪些智能体类型？
A: 支持 chat（对话）、completion（文本生成）、workflow（工作流）、datasets（知识库）。

### Q: 如何配置多个智能体？
A: 在 `agents` 下添加多个配置项，每个智能体使用不同的名称和 API Key。

### Q: 如何禁用某个智能体？
A: 设置 `enabled: false`。

## 📚 更多资源

- [完整文档](./Dify智能体集成说明文档.md)
- [Dify 官方文档](https://docs.dify.ai/)
- [Java Client 文档](https://github.com/imfangs/dify-java-client)

---

*快速开始指南 - 让你在5分钟内上手 Dify 智能体！*
