# Dify工作流流式调用使用指南

## 概述

本文档介绍如何使用扩展后的DifyService进行工作流的流式调用。新增的流式调用功能支持实时接收工作流执行过程中的事件和消息，相比阻塞调用具有更好的用户体验和响应性。

## 功能特性

- **实时事件监听**：支持监听工作流开始、节点执行、工作流完成等事件
- **异步非阻塞**：不会阻塞主线程，适合长时间运行的工作流
- **完整异常处理**：包含详细的错误处理和日志记录
- **灵活回调机制**：支持自定义回调处理器和简化的消息回调
- **参数验证**：严格的输入参数验证，确保调用安全性

## 接口说明

### 1. 完整回调接口

```java
void runWorkflowStream(Map<String, Object> inputs, String user, WorkflowStreamCallback callback)
```

**参数说明：**
- `inputs`: 工作流输入参数（不能为空）
- `user`: 用户标识（不能为空或空字符串）
- `callback`: 工作流流式回调处理器（不能为空）

### 2. 简化回调接口

```java
void runWorkflowStream(Map<String, Object> inputs, String user, Consumer<String> onMessage)
```

**参数说明：**
- `inputs`: 工作流输入参数（不能为空）
- `user`: 用户标识（不能为空或空字符串）
- `onMessage`: 消息回调处理器（不能为空）

## 使用示例

### 示例1：使用完整回调接口

```java
@Autowired
private IDifyService difyService;

public void executeWorkflowWithFullCallback() {
    Map<String, Object> inputs = new HashMap<>();
    inputs.put("query", "请帮我分析这个文档");
    inputs.put("document", "文档内容...");
    
    WorkflowStreamCallback callback = new WorkflowStreamCallback() {
        @Override
        public void onWorkflowStarted(WorkflowStartedEvent event) {
            System.out.println("工作流开始执行: " + event);
        }
        
        @Override
        public void onNodeStarted(NodeStartedEvent event) {
            System.out.println("节点开始执行: " + event);
        }
        
        @Override
        public void onNodeFinished(NodeFinishedEvent event) {
            System.out.println("节点执行完成: " + event);
        }
        
        @Override
        public void onWorkflowFinished(WorkflowFinishedEvent event) {
            System.out.println("工作流执行完成: " + event);
        }
        
        @Override
        public void onError(ErrorEvent event) {
            System.err.println("工作流执行出错: " + event);
        }
        
        @Override
        public void onException(Throwable throwable) {
            System.err.println("工作流执行异常: " + throwable.getMessage());
        }
    };
    
    try {
        difyService.runWorkflowStream(inputs, "user123", callback);
    } catch (Exception e) {
        System.err.println("调用失败: " + e.getMessage());
    }
}
```

### 示例2：使用简化回调接口

```java
public void executeWorkflowWithSimpleCallback() {
    Map<String, Object> inputs = new HashMap<>();
    inputs.put("query", "请帮我生成一份报告");
    inputs.put("data", "数据内容...");
    
    try {
        difyService.runWorkflowStream(inputs, "user456", message -> {
            System.out.println("收到消息: " + message);
            // 这里可以将消息发送到前端或进行其他处理
        });
    } catch (Exception e) {
        System.err.println("调用失败: " + e.getMessage());
    }
}
```

### 示例3：使用默认回调处理器

```java
public void executeWorkflowWithDefaultCallback() {
    Map<String, Object> inputs = new HashMap<>();
    inputs.put("task", "数据分析任务");
    
    // 创建默认回调处理器
    DefaultWorkflowStreamCallback callback = new DefaultWorkflowStreamCallback(
        message -> {
            // 处理实时消息
            System.out.println("实时消息: " + message);
        },
        completionMessage -> {
            // 处理完成消息
            System.out.println("完成消息: " + completionMessage);
        },
        "user789" // 用户ID，用于日志记录
    );
    
    try {
        difyService.runWorkflowStream(inputs, "user789", callback);
    } catch (Exception e) {
        System.err.println("调用失败: " + e.getMessage());
    }
}
```

## 错误处理

### 常见异常类型

1. **IllegalArgumentException**: 参数验证失败
   - 输入参数为空
   - 用户标识为空或空字符串
   - 回调处理器为空

2. **DifyException**: Dify服务调用异常
   - 网络连接问题
   - API认证失败
   - 服务不可用

3. **RuntimeException**: 其他运行时异常
   - 工作流执行失败
   - 回调处理异常

### 异常处理示例

```java
public void executeWorkflowWithErrorHandling() {
    Map<String, Object> inputs = new HashMap<>();
    inputs.put("query", "测试查询");
    
    try {
        difyService.runWorkflowStream(inputs, "user123", message -> {
            System.out.println("消息: " + message);
        });
    } catch (IllegalArgumentException e) {
        System.err.println("参数错误: " + e.getMessage());
    } catch (DifyException e) {
        System.err.println("Dify服务异常: " + e.getMessage());
        // 可以根据错误码进行不同处理
    } catch (Exception e) {
        System.err.println("未知异常: " + e.getMessage());
    }
}
```

## 最佳实践

### 1. 参数验证

```java
// 在调用前进行参数验证
if (inputs == null || inputs.isEmpty()) {
    throw new IllegalArgumentException("输入参数不能为空");
}

if (StringUtils.isBlank(user)) {
    throw new IllegalArgumentException("用户标识不能为空");
}
```

### 2. 异步处理

```java
// 使用线程池进行异步处理
@Async
public CompletableFuture<Void> executeWorkflowAsync(Map<String, Object> inputs, String user) {
    return CompletableFuture.runAsync(() -> {
        difyService.runWorkflowStream(inputs, user, message -> {
            // 异步处理消息
            processMessageAsync(message);
        });
    });
}
```

### 3. 消息缓存

```java
// 缓存消息以便后续查询
private final Map<String, List<String>> messageCache = new ConcurrentHashMap<>();

public void executeWorkflowWithCache(String sessionId, Map<String, Object> inputs, String user) {
    List<String> messages = new ArrayList<>();
    messageCache.put(sessionId, messages);
    
    difyService.runWorkflowStream(inputs, user, message -> {
        messages.add(message);
        // 同时发送到前端
        sendToFrontend(sessionId, message);
    });
}
```

### 4. 超时处理

```java
// 设置超时机制
public void executeWorkflowWithTimeout(Map<String, Object> inputs, String user) {
    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
        difyService.runWorkflowStream(inputs, user, message -> {
            System.out.println("消息: " + message);
        });
    });
    
    try {
        future.get(30, TimeUnit.SECONDS); // 30秒超时
    } catch (TimeoutException e) {
        System.err.println("工作流执行超时");
        future.cancel(true);
    } catch (Exception e) {
        System.err.println("执行异常: " + e.getMessage());
    }
}
```

## 日志配置

为了更好地监控和调试，建议配置适当的日志级别：

```yaml
# application.yml
logging:
  level:
    com.ruoyi.system.service.impl.DifyServiceImpl: INFO
    com.ruoyi.system.service.impl.DefaultWorkflowStreamCallback: DEBUG
    io.github.imfangs.dify: DEBUG
```

## 性能考虑

1. **连接池配置**：确保HTTP连接池配置合理
2. **回调处理**：避免在回调中执行耗时操作
3. **内存管理**：及时清理不需要的消息缓存
4. **并发控制**：合理控制并发调用数量

## 注意事项

1. 流式调用是异步的，不会返回最终结果，需要通过回调获取
2. 确保回调处理器中的异常不会影响主流程
3. 在生产环境中建议使用连接池和重试机制
4. 注意API调用频率限制，避免触发限流
5. 敏感信息不要在日志中输出

## 版本兼容性

- 支持 dify-java-client 1.1.7+
- 兼容 Spring Boot 2.x 和 3.x
- 需要 Java 8+

## 更新日志

- v1.0.0: 初始版本，支持基本的流式调用功能
- v1.1.0: 增加DefaultWorkflowStreamCallback默认回调处理器
- v1.2.0: 完善异常处理和日志记录功能