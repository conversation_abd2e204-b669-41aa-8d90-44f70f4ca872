# 合同审查报告功能技术实现方案

## 1. 功能概述

### 1.1 需求描述
当用户在前端点击某条审查任务的操作栏中的"报告"按钮时，触发报告查看功能并跳转到审查报告页面。

### 1.2 核心流程
1. 点击报告查看按钮
2. 后端获取当前任务的审查结果字段(review_result)
3. 根据审查结果获取风险条款的坐标信息
4. 将合同附件统一渲染成PDF并支持高亮风险条款
5. 前端展示三栏布局的审查报告页面

### 1.3 技术架构
- **后端**: Spring Boot + PDFBox + Apache POI
- **前端**: Vue 3 + TypeScript + PDF.js + Element Plus
- **数据存储**: MySQL + MinIO文件存储

## 2. 数据结构分析

### 2.1 审查结果JSON结构
```json
{
  "data": [
    {
      "clauseName": "合同总价及付款方式",
      "clauseContent": "合同总价：人民币捌拾肆万肆千元整...",
      "riskPoints": [
        {
          "riskName": "预付款比例",
          "riskHits": [
            {
              "text": "30%",
              "matchType": "numeric",
              "pattern": "超过25%",
              "explain": "合同约定预付款比例为30%..."
            }
          ],
          "riskAnalysis": "作为采购方...",
          "suggestModify": "建议将预付款比例调整为..."
        }
      ]
    }
  ]
}
```

### 2.2 坐标定位数据结构
```java
public class TextBlock {
    private String text;        // 文本内容
    private int pageNumber;     // 页码（从1开始）
    private float x;           // X坐标
    private float y;           // Y坐标
    private float width;       // 宽度
    private float height;      // 高度
    private int startIndex;    // 起始索引
    private int endIndex;      // 结束索引
}
```

## 3. 后端API接口设计

### 3.1 获取审查报告数据（优化版）

**接口路径**: `GET /contract/task/report/{taskId}`

**请求参数**:
- `taskId`: 任务ID (路径参数)
- `includeHighlight`: 是否包含高亮数据 (可选，默认true)

**响应数据结构优化**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskInfo": {
      "id": 12345,
      "taskName": "销售合同审查",
      "categoryName": "买卖合同",
      "fileName": "sales_contract.pdf",
      "fileSize": 1024000,
      "createTime": "2024-01-15 10:30:00",
      "taskStatus": "2",
      "taskStatusName": "已完成",
      "totalRiskCount": 15,
      "highRiskCount": 3,
      "normalRiskCount": 12
    },
    "reviewResult": {
      "data": [...] // 审查结果JSON，保持现有结构
    },
    "fileInfo": {
      "originalFileId": "file123",
      "pdfFileId": "pdf456",
      "fileUrl": "/api/files/pdf/pdf456",
      "pageCount": 10,
      "fileExtension": "pdf",
      "conversionStatus": "completed" // 转换状态
    },
    "highlightData": [
      {
        "riskId": "risk001",
        "riskName": "预付款比例",
        "riskLevel": "high",
        "clauseName": "合同总价及付款方式",
        "matchText": "30%",
        "confidence": 0.95, // 匹配置信度
        "positions": [
          {
            "pageNumber": 2,
            "x": 150.5,
            "y": 400.2,
            "width": 25.0,
            "height": 12.0,
            "startIndex": 245,
            "endIndex": 248
          }
        ]
      }
    ],
    "statistics": {
      "totalHighlights": 15,
      "matchedHighlights": 13,
      "matchRate": 0.87
    }
  }
}
```

**错误响应增强**:
```json
{
  "code": 500,
  "msg": "报告生成失败",
  "data": null,
  "errorDetail": {
    "errorType": "CONVERSION_FAILED",
    "errorMessage": "Word文档转换PDF失败",
    "retryable": true,
    "fallbackOptions": ["download_original", "manual_upload_pdf"]
  }
}
```
        "clauseName": "合同总价及付款方式",
        "matchText": "30%",
        "positions": [
          {
            "pageNumber": 2,
            "x": 150.5,
            "y": 400.2,
            "width": 25.0,
            "height": 12.0,
            "startIndex": 245,
            "endIndex": 248
          }
        ]
      }
    ]
  }
}
```

### 3.2 获取PDF文件流

**接口路径**: `GET /contract/files/pdf/{fileId}`

**请求参数**:
- `fileId`: 文件ID (路径参数)

**响应**: PDF文件流 (Content-Type: application/pdf)

### 3.3 文档格式转换

**接口路径**: `POST /contract/files/convert`

**请求参数**:
```json
{
  "fileId": "file123",
  "targetFormat": "pdf"
}
```

**响应数据**:
```json
{
  "code": 200,
  "msg": "转换成功",
  "data": {
    "convertedFileId": "pdf456",
    "fileUrl": "/api/files/download/pdf456",
    "pageCount": 10
  }
}
```

## 4. 核心算法实现

### 4.1 坐标提取算法

```java
@Service
public class ContractReportService {
    
    /**
     * 生成报告高亮数据
     */
    public List<HighlightData> generateHighlightData(Long taskId) {
        // 1. 获取任务和审查结果
        ContractReviewTask task = taskService.getById(taskId);
        String reviewResultJson = task.getReviewResult();
        
        // 2. 解析审查结果
        ReviewResult reviewResult = JSON.parseObject(reviewResultJson, ReviewResult.class);
        
        // 3. 获取PDF文件并解析
        String pdfFileId = convertToPdf(task.getFileId());
        PdfParseResult pdfParseResult = PdfParseUtil.parsePdf(getFilePath(pdfFileId));
        
        // 4. 为每个风险点生成坐标信息
        List<HighlightData> highlightDataList = new ArrayList<>();
        
        for (ReviewClause clause : reviewResult.getData()) {
            for (RiskPoint riskPoint : clause.getRiskPoints()) {
                for (RiskHit riskHit : riskPoint.getRiskHits()) {
                    // 在PDF中搜索风险文本
                    Map<String, List<Map<String, Object>>> searchResults = 
                        PdfParseUtil.searchKeywords(pdfParseResult, Arrays.asList(riskHit.getText()));
                    
                    // 生成高亮数据
                    HighlightData highlightData = createHighlightData(
                        riskPoint, riskHit, searchResults.get(riskHit.getText())
                    );
                    highlightDataList.add(highlightData);
                }
            }
        }
        
        return highlightDataList;
    }
    
    /**
     * 文档转PDF
     */
    private String convertToPdf(String originalFileId) {
        ContractFile contractFile = fileService.getById(originalFileId);
        
        if ("pdf".equalsIgnoreCase(contractFile.getFileExtension())) {
            return originalFileId; // 已是PDF，直接返回
        }
        
        // Word转PDF
        if ("docx".equalsIgnoreCase(contractFile.getFileExtension()) || 
            "doc".equalsIgnoreCase(contractFile.getFileExtension())) {
            return WordToPdfConverter.convert(contractFile.getFilePath());
        }
        
        throw new BusinessException("不支持的文件格式");
    }
}
```

### 4.2 Word转PDF实现

```java
@Component
public class WordToPdfConverter {
    
    public static String convert(String wordFilePath) {
        try {
            // 使用Apache POI + iText实现Word转PDF
            XWPFDocument document = new XWPFDocument(new FileInputStream(wordFilePath));
            
            String pdfPath = wordFilePath.replace(".docx", ".pdf").replace(".doc", ".pdf");
            PdfWriter writer = new PdfWriter(pdfPath);
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document doc = new Document(pdfDoc);
            
            // 转换文档内容
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                doc.add(new Paragraph(paragraph.getText()));
            }
            
            // 转换表格
            for (XWPFTable table : document.getTables()) {
                convertTable(doc, table);
            }
            
            doc.close();
            document.close();
            
            // 保存到文件系统并返回文件ID
            return fileService.saveConvertedFile(pdfPath);
            
        } catch (Exception e) {
            throw new BusinessException("Word转PDF失败: " + e.getMessage());
        }
    }
}
```

### 4.3 精确文本匹配算法

```java
public class TextMatcher {
    
    /**
     * 精确匹配文本位置
     */
    public static List<TextPosition> findTextPositions(
            PdfParseResult pdfResult, String targetText) {
        
        List<TextPosition> positions = new ArrayList<>();
        
        for (PageInfo page : pdfResult.getPages()) {
            // 1. 简单字符串匹配
            List<TextPosition> simpleMatches = findSimpleMatches(page, targetText);
            positions.addAll(simpleMatches);
            
            // 2. 模糊匹配（处理换行、空格等）
            if (simpleMatches.isEmpty()) {
                List<TextPosition> fuzzyMatches = findFuzzyMatches(page, targetText);
                positions.addAll(fuzzyMatches);
            }
        }
        
        return positions;
    }
    
    /**
     * 处理跨行文本匹配
     */
    private static List<TextPosition> findFuzzyMatches(PageInfo page, String targetText) {
        // 移除目标文本中的空白字符
        String normalizedTarget = targetText.replaceAll("\\s+", "");
        String normalizedPageText = page.getText().replaceAll("\\s+", "");
        
        int index = normalizedPageText.indexOf(normalizedTarget);
        if (index >= 0) {
            // 计算原始文本中的位置
            return calculateOriginalPositions(page, index, normalizedTarget.length());
        }
        
        return new ArrayList<>();
    }
}
```

## 5. 前端实现方案

### 5.1 路由配置

```typescript
// src/router/modules/contract.ts
export default {
  path: '/contract',
  component: Layout,
  children: [
    {
      path: 'task/report/:id',
      name: 'ContractTaskReport',
      component: () => import('@/views/contract/task/report/index.vue'),
      meta: {
        title: '审查报告',
        icon: 'document'
      }
    }
  ]
}
```

### 5.2 报告页面组件结构

```vue
<!-- src/views/contract/task/report/index.vue -->
<template>
  <div class="contract-report-container">
    <!-- 顶部工具栏 -->
    <div class="report-toolbar">
      <el-button @click="downloadReport">下载审查报告</el-button>
      <el-button @click="exportResults">导出审查结果</el-button>
    </div>
    
    <!-- 三栏布局 -->
    <div class="report-content">
      <!-- 左侧：任务信息 -->
      <div class="task-info-panel">
        <TaskInfoCard :task-info="taskInfo" />
      </div>
      
      <!-- 中间：PDF查看器 -->
      <div class="pdf-viewer-panel">
        <PdfViewer 
          :pdf-url="pdfUrl"
          :highlight-data="highlightData"
          @text-selected="handleTextSelected"
        />
      </div>
      
      <!-- 右侧：审查结果 -->
      <div class="review-results-panel">
        <ReviewResultsTree 
          :review-data="reviewResult"
          @risk-click="handleRiskClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getTaskReport } from '@/api/contract/task'

const route = useRoute()
const taskId = route.params.id

const taskInfo = ref({})
const reviewResult = ref({})
const highlightData = ref([])
const pdfUrl = ref('')

// 加载报告数据
const loadReportData = async () => {
  try {
    const response = await getTaskReport(taskId)
    taskInfo.value = response.data.taskInfo
    reviewResult.value = response.data.reviewResult
    highlightData.value = response.data.highlightData
    pdfUrl.value = response.data.fileInfo.fileUrl
  } catch (error) {
    console.error('加载报告数据失败:', error)
  }
}

// 处理风险点击事件
const handleRiskClick = (riskData) => {
  // 通知PDF查看器定位到对应位置
  pdfViewerRef.value?.scrollToRisk(riskData.riskId)
}

onMounted(() => {
  loadReportData()
})
</script>
```

### 5.3 PDF查看器组件

```vue
<!-- src/components/PdfViewer/index.vue -->
<template>
  <div class="pdf-viewer">
    <div class="pdf-toolbar">
      <el-button-group>
        <el-button @click="zoomIn">放大</el-button>
        <el-button @click="zoomOut">缩小</el-button>
        <el-button @click="resetZoom">重置</el-button>
      </el-button-group>
      <el-button @click="highlightAll" type="primary">高亮全部</el-button>
      <span class="page-info">第 {{ currentPage }} 页 / 共 {{ totalPages }} 页</span>
    </div>
    
    <div class="pdf-container" ref="pdfContainer">
      <canvas 
        v-for="page in renderedPages" 
        :key="page.pageNumber"
        :ref="'page-' + page.pageNumber"
        @click="handlePageClick"
      ></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as pdfjsLib from 'pdfjs-dist'
import { ref, onMounted, watch } from 'vue'

interface HighlightItem {
  riskId: string
  riskName: string
  riskLevel: 'high' | 'medium' | 'low'
  positions: Array<{
    pageNumber: number
    x: number
    y: number
    width: number
    height: number
  }>
}

const props = defineProps<{
  pdfUrl: string
  highlightData: HighlightItem[]
}>()

const pdfContainer = ref<HTMLElement>()
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1.0)
const pdfDoc = ref(null)
const renderedPages = ref([])

// PDF高亮管理器
class PDFHighlightManager {
  private highlightMap = new Map()
  
  addHighlight(pageNum: number, highlight: HighlightItem) {
    if (!this.highlightMap.has(pageNum)) {
      this.highlightMap.set(pageNum, [])
    }
    this.highlightMap.get(pageNum).push(highlight)
  }
  
  renderHighlights(pageNum: number, canvas: HTMLCanvasElement) {
    const highlights = this.highlightMap.get(pageNum) || []
    const ctx = canvas.getContext('2d')
    
    highlights.forEach(highlight => {
      highlight.positions.forEach(pos => {
        if (pos.pageNumber === pageNum) {
          // 绘制高亮矩形
          ctx.fillStyle = this.getRiskColor(highlight.riskLevel)
          ctx.fillRect(
            pos.x * scale.value,
            pos.y * scale.value,
            pos.width * scale.value,
            pos.height * scale.value
          )
        }
      })
    })
  }
  
  getRiskColor(level: string): string {
    switch (level) {
      case 'high': return 'rgba(255, 0, 0, 0.3)'
      case 'medium': return 'rgba(255, 165, 0, 0.3)'
      case 'low': return 'rgba(255, 255, 0, 0.3)'
      default: return 'rgba(0, 0, 255, 0.3)'
    }
  }
  
  scrollToRisk(riskId: string) {
    const highlight = props.highlightData.find(h => h.riskId === riskId)
    if (highlight && highlight.positions.length > 0) {
      const firstPos = highlight.positions[0]
      this.scrollToPage(firstPos.pageNumber)
      this.flashHighlight(highlight)
    }
  }
  
  scrollToPage(pageNumber: number) {
    const pageElement = document.querySelector(`[data-page="${pageNumber}"]`)
    if (pageElement) {
      pageElement.scrollIntoView({ behavior: 'smooth' })
      currentPage.value = pageNumber
    }
  }
  
  flashHighlight(highlight: HighlightItem) {
    // 实现高亮闪烁效果
    setTimeout(() => {
      // 添加闪烁动画类
    }, 500)
  }
}

const highlightManager = new PDFHighlightManager()

// 加载PDF
const loadPDF = async () => {
  try {
    const loadingTask = pdfjsLib.getDocument(props.pdfUrl)
    pdfDoc.value = await loadingTask.promise
    totalPages.value = pdfDoc.value.numPages
    
    // 渲染所有页面
    await renderAllPages()
    
    // 添加高亮数据
    props.highlightData.forEach(highlight => {
      highlight.positions.forEach(pos => {
        highlightManager.addHighlight(pos.pageNumber, highlight)
      })
    })
    
  } catch (error) {
    console.error('PDF加载失败:', error)
  }
}

// 渲染所有页面
const renderAllPages = async () => {
  for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
    await renderPage(pageNum)
  }
}

// 渲染单个页面
const renderPage = async (pageNum: number) => {
  const page = await pdfDoc.value.getPage(pageNum)
  const viewport = page.getViewport({ scale: scale.value })
  
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  canvas.height = viewport.height
  canvas.width = viewport.width
  canvas.setAttribute('data-page', pageNum.toString())
  
  const renderContext = {
    canvasContext: context,
    viewport: viewport
  }
  
  await page.render(renderContext).promise
  
  // 渲染高亮
  highlightManager.renderHighlights(pageNum, canvas)
  
  // 添加到容器
  pdfContainer.value?.appendChild(canvas)
  
  renderedPages.value.push({
    pageNumber: pageNum,
    canvas: canvas
  })
}

// 缩放控制
const zoomIn = () => {
  scale.value = Math.min(scale.value * 1.2, 3.0)
  rerenderPages()
}

const zoomOut = () => {
  scale.value = Math.max(scale.value / 1.2, 0.5)
  rerenderPages()
}

const resetZoom = () => {
  scale.value = 1.0
  rerenderPages()
}

// 重新渲染页面
const rerenderPages = async () => {
  // 清空容器
  pdfContainer.value.innerHTML = ''
  renderedPages.value = []
  
  // 重新渲染
  await renderAllPages()
}

// 高亮全部
const highlightAll = () => {
  renderedPages.value.forEach(page => {
    highlightManager.renderHighlights(page.pageNumber, page.canvas)
  })
}

// 暴露方法给父组件
defineExpose({
  scrollToRisk: highlightManager.scrollToRisk.bind(highlightManager)
})

watch(() => props.pdfUrl, () => {
  if (props.pdfUrl) {
    loadPDF()
  }
})

onMounted(() => {
  // 配置PDF.js worker
  pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.js'
  
  if (props.pdfUrl) {
    loadPDF()
  }
})
</script>
```

## 6. 分阶段实施方案

### 6.1 阶段一：核心功能实现（2周）

**目标**: 实现基础报告查看功能，确保核心流程可用

#### 后端开发任务
1. **创建报告相关实体类**
   - `HighlightData.java` - 高亮数据实体
   - `TextPosition.java` - 文本位置实体  
   - `ReportResponse.java` - 报告响应实体

2. **实现基础报告API**
   - `ContractReportController.java` - 报告控制器
   - `ContractReportService.java` - 报告业务服务
   - 实现 `/contract/task/report/{taskId}` 接口

3. **基础PDF处理**
   - 扩展现有 `PdfParseUtil.java`
   - 实现简单文本搜索功能
   - 支持单页内精确匹配

#### 前端开发任务
1. **安装核心依赖**
   ```bash
   npm install pdfjs-dist@3.11.174
   npm install @types/pdfjs-dist
   ```

2. **创建基础组件**
   - `PdfViewer.vue` - 可复用PDF查看器组件
   - `TaskInfoCard.vue` - 任务信息展示卡片
   - `RiskPointsList.vue` - 风险点列表组件

3. **实现报告页面路由**
   ```typescript
   // 在 staticRoutes.ts 中添加
   {
     path: 'task/report/:taskId',
     name: 'ContractTaskReport',
     component: () => import('@views/contract/task/report/index.vue'),
     meta: {
       title: '审查报告',
       keepAlive: false
     }
   }
   ```

4. **基础PDF渲染**
   - PDF文档加载和显示
   - 基础缩放功能
   - 简单高亮标记

#### 验收标准
- ✅ 用户可以点击"报告"按钮跳转到报告页面
- ✅ 报告页面正确显示任务信息和审查结果
- ✅ PDF文档可以正常加载和查看
- ✅ 风险文本可以基础高亮显示

### 6.2 阶段二：增强功能实现（2周）

**目标**: 完善文档转换和高级匹配功能

#### 后端开发任务
1. **文档转换服务**
   - `DocumentConvertService.java` - 统一文档转换服务
   - `WordToPdfConverter.java` - Word转PDF转换器
   - 集成LibreOffice转换服务

2. **高级文本匹配**
   - `TextMatcher.java` - 智能文本匹配算法
   - 实现模糊匹配和容错机制
   - 处理换行、空格、特殊字符

3. **性能优化**
   - 添加文件转换缓存
   - 实现异步转换处理
   - 优化大文件处理

#### 前端开发任务
1. **PDF查看器增强**
   - 实现虚拟滚动优化性能
   - 添加页面预加载机制
   - 优化大文件渲染

2. **交互功能完善**
   - 风险点定位跳转
   - 高亮闪烁动画效果
   - 缩放和导航控制

3. **用户体验优化**
   - 加载状态提示
   - 错误处理和重试机制
   - 响应式布局适配

#### 验收标准
- ✅ Word文档可以自动转换为PDF
- ✅ 文本匹配准确率达到90%以上
- ✅ 大文件（20MB+）加载流畅
- ✅ 交互响应时间小于500ms

### 6.3 阶段三：完善功能实现（1周）

**目标**: 实现高级功能和全面优化

#### 开发任务
1. **跨页文本高亮**
   - 实现文本片段拆分算法
   - 支持连续高亮效果
   - 处理复杂布局场景

2. **全面测试优化**
   - 性能压力测试
   - 兼容性测试
   - 用户体验测试

3. **监控和日志**
   - 添加关键操作日志
   - 性能监控指标
   - 错误追踪机制

#### 验收标准
- ✅ 支持跨页文本高亮
- ✅ 系统稳定性达到99%
- ✅ 用户满意度测试通过

### 6.4 测试验证计划

#### 单元测试（贯穿各阶段）
- 文本匹配算法准确性测试
- PDF解析功能测试
- API接口功能测试

#### 集成测试（阶段二）
- 前后端数据交互测试
- 文件上传下载流程测试
- 并发访问压力测试

#### 用户验收测试（阶段三）
- 真实场景业务流程测试
- 不同文档格式兼容性测试
- 性能和稳定性测试
   - 高亮定位准确性测试
   - 交互响应速度测试

## 7. 技术难点与解决方案

### 7.1 文本匹配精度问题 🔴 **高风险**

**问题**: PDF中的文本可能存在换行、空格、特殊字符等，导致精确匹配困难。

**优化解决方案**:
1. **分层匹配策略**（阶段一实现）
   - 精确字符串匹配（优先级最高）
   - 标准化匹配（移除空白字符）
   - 模糊匹配（编辑距离算法）

2. **文本预处理增强**（阶段二实现）
   ```java
   public class TextNormalizer {
       public static String normalize(String text) {
           return text.replaceAll("\\s+", " ")
                     .replaceAll("[\u00A0\u2000-\u200B\u2028\u2029]", " ")
                     .trim();
       }
   }
   ```

3. **容错机制**
   - 设置匹配阈值（相似度>85%）
   - 提供人工校正接口
   - 记录匹配失败案例用于算法优化

**风险控制**: 阶段一先实现60%准确率，阶段二提升到90%

### 7.2 PDF渲染性能优化 🔴 **高风险**

**问题**: 大文件PDF渲染可能导致页面卡顿，影响用户体验。

**强化解决方案**:
1. **虚拟滚动实现**（阶段二必须完成）
   ```typescript
   // 只渲染可视区域的页面
   const visiblePages = computed(() => {
     const startPage = Math.max(1, currentPage.value - 2)
     const endPage = Math.min(totalPages.value, currentPage.value + 2)
     return Array.from({length: endPage - startPage + 1}, (_, i) => startPage + i)
   })
   ```

2. **Web Worker优化**
   - PDF解析移至Worker线程
   - 避免主线程阻塞
   - 实现渐进式加载

3. **缓存策略**
   - 页面Canvas缓存
   - 高亮数据缓存
   - 智能预加载相邻页面

4. **性能监控**
   - 页面渲染时间监控
   - 内存使用量监控
   - 用户操作响应时间统计

**性能目标**: 50MB文件加载<5秒，滚动响应<100ms

### 7.3 Word转PDF质量问题 🟡 **中风险**

**问题**: Apache POI转换可能丢失格式，影响坐标准确性。

**改进方案**:
1. **多引擎支持**
   - 优先使用LibreOffice转换（推荐）
   - Apache POI作为备选方案
   - 在线转换服务作为最后选择

2. **质量检测**
   ```java
   public class ConversionQualityChecker {
       public QualityReport checkConversion(String originalFile, String pdfFile) {
           // 检查页数是否一致
           // 检查文本内容完整性
           // 检查图片是否正常显示
           return qualityReport;
       }
   }
   ```

3. **降级处理**
   - 转换失败时提供原文件下载
   - 支持手动上传PDF版本
   - 提供转换状态实时反馈

### 7.4 跨页文本高亮复杂性 🟡 **中风险**

**问题**: 风险文本可能跨越多个页面，实现复杂。

**分阶段解决**:
1. **阶段一**: 仅支持单页内高亮
2. **阶段三**: 实现跨页高亮
   ```typescript
   interface CrossPageHighlight {
     segments: {
       pageNumber: number
       startPos: TextPosition
       endPos: TextPosition
       isStart: boolean
       isEnd: boolean
     }[]
   }
   ```

3. **视觉连续性**
   - 使用相同颜色标识
   - 添加连接线或箭头指示
   - 实现平滑滚动定位

### 7.5 并发处理能力限制 🟡 **中风险**

**问题**: 文档转换资源消耗大，并发能力有限。

**解决方案**:
1. **队列管理**
   - 实现转换任务队列
   - 设置优先级机制
   - 提供排队状态提示

2. **资源控制**
   - 限制同时转换任务数（建议5个）
   - 实现转换超时机制
   - 添加资源使用监控

3. **用户体验**
   - 实时进度反馈
   - 预估完成时间
   - 支持任务取消功能

## 8. 性能指标与监控

### 8.1 响应时间要求（分阶段目标）

#### 阶段一目标
- 报告页面加载: < 5秒
- PDF基础渲染: < 8秒
- 简单高亮响应: < 1秒

#### 最终目标（阶段三）
- 报告页面加载: < 3秒
- PDF完整渲染: < 5秒
- 高亮定位响应: < 500ms
- 文档转换时间: < 30秒
- 页面滚动响应: < 100ms

### 8.2 并发处理能力
- 同时查看报告: 50个用户
- 同时文档转换: 5个用户（优化后）
- 排队等待转换: 20个任务
- 系统总并发: 100个用户

### 8.3 文件处理限制

#### 文件大小限制
- 单个PDF文件: < 50MB
- 单个Word文件: < 20MB
- 页面数量限制: < 200页
- 单页文本量: < 10000字符

#### 性能分级处理
```typescript
interface FilePerformanceLevel {
  small: { size: '<5MB', pages: '<20', renderTime: '<2s' }
  medium: { size: '5-20MB', pages: '20-100', renderTime: '<5s' }
  large: { size: '20-50MB', pages: '100-200', renderTime: '<10s' }
}
```

### 8.4 性能监控指标

#### 关键性能指标（KPI）
- **可用性**: > 99%
- **响应时间**: P95 < 3秒
- **错误率**: < 1%
- **转换成功率**: > 95%
- **用户满意度**: > 4.0/5.0

#### 监控实现
```java
@Component
public class PerformanceMonitor {
    
    @EventListener
    public void onReportAccess(ReportAccessEvent event) {
        // 记录访问时间、用户、文件大小等
        metricsCollector.recordReportAccess(event);
    }
    
    @EventListener
    public void onPdfRender(PdfRenderEvent event) {
        // 记录渲染时间、页数、文件大小
        metricsCollector.recordRenderTime(event);
    }
}
```

#### 性能告警规则
- 响应时间超过10秒 → 立即告警
- 转换失败率超过10% → 告警
- 并发用户超过80个 → 预警
- 内存使用率超过85% → 告警

## 9. 安全考虑

### 9.1 文件安全
- 文件上传病毒扫描
- 文件类型白名单验证
- 文件大小限制

### 9.2 数据安全
- 用户权限验证
- 数据传输加密
- 敏感信息脱敏

### 9.3 系统安全
- API接口鉴权
- 防止SQL注入
- XSS攻击防护

## 10. 部署与运维

### 10.1 环境要求

#### 基础环境
- **JDK**: 8+ (推荐JDK 11)
- **Node.js**: 16+ (推荐18.x LTS)
- **MySQL**: 8.0+
- **MinIO**: 存储服务
- **Redis**: 缓存服务（新增）

#### 系统资源要求
- **CPU**: 4核心以上
- **内存**: 8GB以上（文档转换需要较多内存）
- **磁盘**: 100GB以上（存储转换后的PDF文件）
- **网络**: 100Mbps以上

### 10.2 配置文件优化

```yaml
# application.yml
contract:
  file:
    upload-path: /data/contract/files
    converted-path: /data/contract/converted  # 转换后文件存储路径
    max-size: 50MB
    allowed-extensions: ["pdf", "doc", "docx"]
  
  pdf:
    worker-path: /static/js/pdf.worker.js
    max-pages: 200
    render-timeout: 30000
  
  convert:
    timeout: 30000
    max-concurrent: 5  # 最大并发转换数
    queue-size: 20     # 队列大小
    retry-times: 3     # 重试次数
    
  performance:
    cache-enabled: true
    cache-ttl: 3600    # 缓存1小时
    monitor-enabled: true
    
# Redis配置
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

### 10.3 静态资源部署

#### PDF.js资源
```bash
# 下载PDF.js
wget https://github.com/mozilla/pdf.js/releases/download/v3.11.174/pdfjs-3.11.174-dist.zip
unzip pdfjs-3.11.174-dist.zip -d /static/js/

# 配置nginx
location /static/js/ {
    alias /data/static/js/;
    expires 30d;
    add_header Cache-Control "public, immutable";
}
```

#### 文件访问配置
```nginx
# PDF文件访问
location /api/files/pdf/ {
    proxy_pass http://backend;
    proxy_set_header Range $http_range;
    proxy_set_header If-Range $http_if_range;
    proxy_cache pdf_cache;
    proxy_cache_valid 200 1h;
}
```

### 10.4 监控部署

#### 应用监控
```yaml
# 添加到application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 关键监控指标
- 报告访问QPS
- PDF渲染耗时
- 文档转换成功率
- 内存和CPU使用率
- 文件存储使用量

### 10.5 部署检查清单

#### 部署前检查
- [ ] 环境依赖安装完成
- [ ] 数据库表结构更新
- [ ] 配置文件正确设置
- [ ] 静态资源部署完成
- [ ] 文件存储目录权限正确

#### 部署后验证
- [ ] 报告页面正常访问
- [ ] PDF文件正常显示
- [ ] 文档转换功能正常
- [ ] 高亮功能正常工作
- [ ] 性能指标符合要求
- [ ] 监控告警正常

### 10.6 故障排查指南

#### 常见问题
1. **PDF无法显示**
   - 检查PDF.js worker路径
   - 验证文件权限和路径
   - 查看浏览器控制台错误

2. **文档转换失败**
   - 检查LibreOffice安装
   - 验证文件格式支持
   - 查看转换日志

3. **性能问题**
   - 检查内存使用情况
   - 验证缓存配置
   - 分析慢查询日志

#### 日志配置
```yaml
logging:
  level:
    com.contract.report: DEBUG
    com.contract.convert: INFO
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

---

## 11. 项目管理

### 11.1 里程碑计划

| 阶段 | 时间 | 主要交付物 | 验收标准 |
|------|------|------------|----------|
| 阶段一 | 第1-2周 | 基础报告功能 | 核心流程可用，基础高亮 |
| 阶段二 | 第3-4周 | 增强功能 | 文档转换，性能优化 |
| 阶段三 | 第5周 | 完善功能 | 跨页高亮，全面测试 |
| 上线准备 | 第6周 | 部署上线 | 生产环境验证 |

### 11.2 风险管控

#### 技术风险
- **高风险**: 文本匹配精度、PDF渲染性能
- **应对**: 分阶段实现，设置最低可接受标准
- **预案**: 准备降级方案和手动处理流程

#### 进度风险
- **风险点**: 复杂算法开发时间不确定
- **应对**: 采用敏捷开发，每周评估进度
- **预案**: 功能优先级调整，核心功能优先

### 11.3 质量保证

#### 代码质量
- 代码审查覆盖率100%
- 单元测试覆盖率>80%
- 集成测试覆盖核心流程

#### 性能质量
- 每个阶段进行性能测试
- 建立性能基线和监控
- 用户体验测试验收

---

**文档版本**: V2.0（优化版）  
**创建日期**: 2024-01-15  
**最后更新**: 2024-01-15  
**负责人**: 全栈架构师团队  
**审核状态**: 已优化，待实施