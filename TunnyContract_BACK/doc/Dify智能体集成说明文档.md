# Dify 智能体集成说明文档

## 📋 目录

- [概述](#概述)
- [架构设计](#架构设计)
- [配置说明](#配置说明)
- [核心组件](#核心组件)
- [使用示例](#使用示例)
- [API 接口](#api-接口)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 🎯 概述

本项目集成了 Dify AI 智能体服务，提供了完整的对话、文本生成、工作流执行和知识库检索功能。通过统一的配置和管理机制，支持多个智能体的并发使用。

### 主要特性

- ✅ **多智能体支持**：支持配置和管理多个不同类型的智能体
- ✅ **统一配置管理**：通过 YAML 配置文件集中管理所有智能体
- ✅ **客户端缓存**：智能缓存机制提升性能
- ✅ **完整的 API 封装**：提供对话、文本生成、工作流、知识库等完整功能
- ✅ **异常处理**：完善的错误处理和日志记录
- ✅ **Spring Boot 集成**：无缝集成到 Spring Boot 应用中

## 🏗️ 架构设计

### 模块结构

```
ruoyi-common/
├── config/
│   ├── DifyProperties.java        # 配置属性类
│   └── DifyConfig.java           # Spring 配置类
├── utils/dify/
│   └── DifyClientUtil.java       # 工具类和客户端管理器
├── constant/
│   └── DifyConstants.java        # 常量定义
└── exception/dify/
    └── DifyException.java         # 异常类

ruoyi-system/
├── service/
│   ├── IDifyService.java         # 服务接口
│   └── impl/
│       └── DifyServiceImpl.java  # 服务实现

ruoyi-admin/
└── controller/
    └── DifyController.java       # REST API 控制器
```

### 依赖关系

```
ruoyi-admin → ruoyi-system → ruoyi-common
                ↑                ↓
                └── 安全依赖 ──────┘
```

## ⚙️ 配置说明

### 1. Maven 依赖

在 `ruoyi-common/pom.xml` 中已包含：

```xml
<dependency>
    <groupId>io.github.imfangs</groupId>
    <artifactId>dify-java-client</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 应用配置

在 `application.yml` 中添加配置：

```yaml
# Dify 智能体配置
dify:
  enabled: true                    # 是否启用 Dify 功能
  base-url: "https://api.dify.ai"  # Dify 服务地址
  default-user: "system"           # 默认用户标识
  
  # 连接配置
  connection:
    connect-timeout: 30000         # 连接超时时间（毫秒）
    read-timeout: 60000           # 读取超时时间（毫秒）
    write-timeout: 60000          # 写入超时时间（毫秒）
  
  # 智能体配置
  agents:
    # 客服助手
    customer-service:
      enabled: true
      type: "chat"
      description: "智能客服助手"
      api:
        key: "app-xxxxxxxxxxxxxxxxxxxxxxxx"
    
    # 文档生成器
    document-generator:
      enabled: true
      type: "completion"
      description: "文档自动生成助手"
      api:
        key: "app-yyyyyyyyyyyyyyyyyyyyyyyy"
    
    # 工作流助手
    workflow-assistant:
      enabled: true
      type: "workflow"
      description: "业务流程自动化助手"
      api:
        key: "app-zzzzzzzzzzzzzzzzzzzzzzzz"
    
    # 知识库助手
    knowledge-base:
      enabled: false               # 可以禁用某个智能体
      type: "datasets"
      description: "企业知识库检索助手"
      api:
        key: "app-wwwwwwwwwwwwwwwwwwwwwwww"
```

### 3. 配置验证

系统启动时会自动验证配置：

- ✅ 检查必需的配置项
- ✅ 验证 API Key 格式
- ✅ 检查连接参数有效性
- ✅ 打印详细的初始化信息

## 🔧 核心组件

### 1. DifyProperties

配置属性管理类，负责：
- 读取和验证配置
- 提供配置访问接口
- 支持动态配置更新

### 2. DifyClientUtil

核心工具类，提供：
- 智能体客户端获取
- 统一的 API 调用接口
- 客户端缓存管理
- 状态监控和诊断

### 3. DifyAgentClientManager

客户端管理器，负责：
- 客户端实例创建和缓存
- 配置管理和验证
- 性能监控和统计

### 4. DifyService

业务服务层，提供：
- 高级业务接口
- 参数验证和处理
- 异常处理和转换
- 日志记录和监控

## 💡 使用示例

### 1. 基础对话

```java
@Autowired
private IDifyService difyService;

// 发送对话消息
public void chatExample() {
    try {
        ChatMessageResponse response = difyService.sendChatMessage(
            "customer-service",    // 智能体名称
            "你好，我需要帮助",      // 查询内容
            "user123",            // 用户标识
            null                  // 会话ID（可选）
        );
        
        System.out.println("回复: " + response.getAnswer());
        System.out.println("会话ID: " + response.getConversationId());
    } catch (DifyException e) {
        log.error("对话失败: {}", e.getMessage());
    }
}
```

### 2. 文本生成

```java
public void completionExample() {
    Map<String, Object> inputs = new HashMap<>();
    inputs.put("topic", "人工智能");
    inputs.put("length", "500字");
    
    try {
        CompletionResponse response = difyService.sendCompletionMessage(
            "document-generator",  // 智能体名称
            inputs,               // 输入参数
            "user123"             // 用户标识
        );
        
        System.out.println("生成内容: " + response.getAnswer());
    } catch (DifyException e) {
        log.error("文本生成失败: {}", e.getMessage());
    }
}
```

### 3. 工作流执行

```java
public void workflowExample() {
    Map<String, Object> inputs = new HashMap<>();
    inputs.put("order_id", "ORD123456");
    inputs.put("action", "process");
    
    try {
        WorkflowRunResponse response = difyService.runWorkflow(
            "workflow-assistant",  // 智能体名称
            inputs,               // 输入参数
            "user123"             // 用户标识
        );
        
        System.out.println("工作流状态: " + response.getStatus());
        System.out.println("执行结果: " + response.getData());
    } catch (DifyException e) {
        log.error("工作流执行失败: {}", e.getMessage());
    }
}
```

### 4. 知识库检索

```java
public void retrieveExample() {
    try {
        RetrieveResponse response = difyService.retrieveDataset(
            "knowledge-base",     // 智能体名称
            "dataset001",         // 数据集ID
            "如何使用API",         // 查询内容
            5,                    // 返回数量
            0.7f                  // 相似度阈值
        );
        
        response.getRecords().forEach(record -> {
            System.out.println("匹配内容: " + record.getContent());
            System.out.println("相似度: " + record.getScore());
        });
    } catch (DifyException e) {
        log.error("知识库检索失败: {}", e.getMessage());
    }
}
```

### 5. 直接使用工具类

```java
// 获取启用的智能体列表
Map<String, Object> agents = DifyClientUtil.getEnabledAgents();

// 检查智能体是否可用
boolean available = DifyClientUtil.isAgentAvailable("customer-service");

// 发送简单对话
ChatMessageResponse response = DifyClientUtil.sendChatMessage(
    "customer-service", 
    "你好", 
    "user123"
);

// 获取管理器状态
Map<String, Object> status = DifyClientUtil.getManagerStatus();

// 打印状态信息
DifyClientUtil.printManagerStatus();
```

## 🌐 API 接口

### REST API 端点

所有 API 都在 `/system/dify` 路径下：

#### 1. 获取智能体列表

```http
GET /system/dify/agents
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "customer-service": {
      "type": "chat",
      "description": "智能客服助手",
      "enabled": true
    },
    "document-generator": {
      "type": "completion",
      "description": "文档自动生成助手",
      "enabled": true
    }
  }
}
```

#### 2. 发送对话消息

```http
POST /system/dify/agents/{agentName}/chat
Content-Type: application/json

{
  "query": "你好，我需要帮助",
  "user": "user123",
  "conversationId": "conv_123" // 可选
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "msg_456",
    "answer": "您好！我是智能客服助手，很高兴为您服务。请问有什么可以帮助您的吗？",
    "conversationId": "conv_123",
    "createdAt": 1703123456
  }
}
```

#### 3. 文本生成

```http
POST /system/dify/agents/{agentName}/completion
Content-Type: application/json

{
  "inputs": {
    "topic": "人工智能",
    "length": "500字"
  },
  "user": "user123"
}
```

#### 4. 执行工作流

```http
POST /system/dify/agents/{agentName}/workflow
Content-Type: application/json

{
  "inputs": {
    "order_id": "ORD123456",
    "action": "process"
  },
  "user": "user123"
}
```

#### 5. 知识库检索

```http
POST /system/dify/agents/{agentName}/datasets/{datasetId}/retrieve
Content-Type: application/json

{
  "query": "如何使用API",
  "topK": 5,
  "scoreThreshold": 0.7
}
```

#### 6. 会话管理

```http
# 获取会话历史
GET /system/dify/agents/{agentName}/conversations/{conversationId}/messages

# 获取会话列表
GET /system/dify/agents/{agentName}/conversations

# 重命名会话
PUT /system/dify/agents/{agentName}/conversations/{conversationId}/name
{
  "name": "新会话名称",
  "autoGenerate": false
}

# 删除会话
DELETE /system/dify/agents/{agentName}/conversations/{conversationId}

# 消息反馈
POST /system/dify/agents/{agentName}/messages/{messageId}/feedback
{
  "rating": "like", // like 或 dislike
  "content": "反馈内容"
}

# 获取建议问题
GET /system/dify/agents/{agentName}/messages/{messageId}/suggested
```

## 🔍 故障排除

### 常见问题

#### 1. 启动时报错：循环依赖

**错误信息：**
```
添加对模块 'ruoyi-framework' 的依赖将在模块 'ruoyi-framework' 和 'ruoyi-system' 之间引入循环依赖关系
```

**解决方案：**
确保 Dify 相关代码都在 `ruoyi-common` 模块中，不要在 `ruoyi-framework` 和 `ruoyi-system` 之间创建循环依赖。

#### 2. 无法解析符号错误

**错误信息：**
```
无法解析符号 'ChatMessageResponse'
无法解析符号 'DifyClientFactory'
```

**解决方案：**
检查导入的包名是否正确：
```java
// 正确的导入
import io.github.imfangs.dify.client.model.chat.ChatMessageResponse;
import io.github.imfangs.dify.client.DifyClientFactory;

// 错误的导入（不要使用）
import io.github.imfangs.dify.model.chat.ChatMessageResponse;
import io.github.imfangs.dify.client.factory.DifyClientFactory;
```

#### 3. 智能体不可用

**错误信息：**
```
智能体不存在或未启用: customer-service
```

**解决方案：**
1. 检查配置文件中智能体是否正确配置
2. 确认 `enabled: true`
3. 验证 API Key 是否正确
4. 检查网络连接

#### 4. API Key 格式错误

**错误信息：**
```
智能体API Key未配置
```

**解决方案：**
确保 API Key 格式正确：
```yaml
agents:
  my-agent:
    api:
      key: "app-xxxxxxxxxxxxxxxxxxxxxxxx"  # 必须以 app- 开头
```

### 调试技巧

#### 1. 启用详细日志

```yaml
logging:
  level:
    com.ruoyi.common.utils.dify: DEBUG
    com.ruoyi.system.service.impl.DifyServiceImpl: DEBUG
```

#### 2. 查看初始化信息

系统启动时会打印详细信息：
```
=== Dify智能体服务初始化信息 ===
服务状态: 已启用
服务地址: https://api.dify.ai
默认用户: system
连接配置:
  - 连接超时: 30000ms
  - 读取超时: 60000ms
  - 写入超时: 60000ms
智能体配置:
  - 总数量: 4
  - 启用数量: 3
  - 禁用数量: 1
启用的智能体详情:
  📱 智能体: customer-service
    - 类型: chat
    - 描述: 智能客服助手
    - API Key: app-1234***
    - 状态: 已启用
=== Dify智能体服务初始化完成 ===
```

#### 3. 运行时状态检查

```java
// 打印当前状态
DifyClientUtil.printManagerStatus();

// 获取状态信息
Map<String, Object> status = DifyClientUtil.getManagerStatus();
```

## 🚀 最佳实践

### 1. 配置管理

#### 环境分离
```yaml
# 开发环境
spring:
  profiles: dev
dify:
  base-url: "https://dev-api.dify.ai"
  agents:
    test-agent:
      api:
        key: "app-dev-xxxxxxxxxxxxxxxx"

---
# 生产环境
spring:
  profiles: prod
dify:
  base-url: "https://api.dify.ai"
  agents:
    prod-agent:
      api:
        key: "${DIFY_API_KEY}"  # 使用环境变量
```

#### 敏感信息保护
```yaml
# 使用环境变量
dify:
  agents:
    customer-service:
      api:
        key: "${DIFY_CUSTOMER_SERVICE_KEY}"

# 或使用 Spring Cloud Config
dify:
  agents:
    customer-service:
      api:
        key: "@dify.customer-service.key@"
```

### 2. 异常处理

```java
@Service
public class BusinessService {

    @Autowired
    private IDifyService difyService;

    public String processUserQuery(String query, String userId) {
        try {
            // 1. 参数验证
            if (StringUtils.isEmpty(query)) {
                throw new IllegalArgumentException("查询内容不能为空");
            }

            // 2. 调用智能体
            ChatMessageResponse response = difyService.sendChatMessage(
                "customer-service", query, userId, null
            );

            // 3. 结果处理
            return response.getAnswer();

        } catch (DifyException e) {
            // 4. 业务异常处理
            log.error("智能体调用失败: {}", e.getMessage(), e);
            return "抱歉，服务暂时不可用，请稍后重试。";

        } catch (Exception e) {
            // 5. 系统异常处理
            log.error("系统异常: {}", e.getMessage(), e);
            return "系统错误，请联系管理员。";
        }
    }
}
```

### 3. 性能优化

#### 客户端缓存
```java
// 系统会自动缓存客户端实例，无需手动管理
// 如需清理缓存：
DifyClientUtil.clearAgentCache("agent-name");  // 清理指定智能体
DifyClientUtil.clearAllCache();                // 清理所有缓存
```

#### 异步处理
```java
@Service
public class AsyncDifyService {

    @Async
    public CompletableFuture<String> processAsync(String query) {
        try {
            ChatMessageResponse response = difyService.sendChatMessage(
                "customer-service", query, "async-user", null
            );
            return CompletableFuture.completedFuture(response.getAnswer());
        } catch (Exception e) {
            return CompletableFuture.failedFuture(e);
        }
    }
}
```

#### 批量处理
```java
public List<String> processBatch(List<String> queries, String userId) {
    return queries.parallelStream()
        .map(query -> {
            try {
                ChatMessageResponse response = difyService.sendChatMessage(
                    "customer-service", query, userId, null
                );
                return response.getAnswer();
            } catch (Exception e) {
                log.error("批量处理失败: {}", query, e);
                return "处理失败";
            }
        })
        .collect(Collectors.toList());
}
```

### 4. 监控和日志

#### 自定义监控
```java
@Component
public class DifyMonitor {

    private final MeterRegistry meterRegistry;
    private final Counter successCounter;
    private final Counter errorCounter;
    private final Timer responseTimer;

    public DifyMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.successCounter = Counter.builder("dify.requests.success").register(meterRegistry);
        this.errorCounter = Counter.builder("dify.requests.error").register(meterRegistry);
        this.responseTimer = Timer.builder("dify.response.time").register(meterRegistry);
    }

    public void recordSuccess() {
        successCounter.increment();
    }

    public void recordError() {
        errorCounter.increment();
    }

    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

#### 结构化日志
```java
@Slf4j
public class DifyServiceImpl implements IDifyService {

    @Override
    public ChatMessageResponse sendChatMessage(String agentName, String query, String user, String conversationId) {
        // 使用结构化日志
        MDC.put("agentName", agentName);
        MDC.put("userId", user);
        MDC.put("conversationId", conversationId);

        try {
            log.info("开始处理对话请求: query={}", query);

            ChatMessageResponse response = // ... 处理逻辑

            log.info("对话请求处理成功: messageId={}, responseLength={}",
                response.getId(), response.getAnswer().length());

            return response;

        } catch (Exception e) {
            log.error("对话请求处理失败: error={}", e.getMessage(), e);
            throw e;
        } finally {
            MDC.clear();
        }
    }
}
```

### 5. 测试策略

#### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class DifyServiceTest {

    @Mock
    private DifyProperties difyProperties;

    @InjectMocks
    private DifyServiceImpl difyService;

    @Test
    void testSendChatMessage_Success() {
        // Given
        when(difyProperties.getDefaultUser()).thenReturn("test-user");

        // When & Then
        assertDoesNotThrow(() -> {
            ChatMessageResponse response = difyService.sendChatMessage(
                "test-agent", "hello", "user123", null
            );
            assertNotNull(response);
        });
    }

    @Test
    void testSendChatMessage_InvalidParams() {
        // When & Then
        assertThrows(DifyException.class, () -> {
            difyService.sendChatMessage("", "hello", "user123", null);
        });
    }
}
```

#### 集成测试
```java
@SpringBootTest
@TestPropertySource(properties = {
    "dify.enabled=true",
    "dify.base-url=https://test-api.dify.ai",
    "dify.agents.test-agent.enabled=true",
    "dify.agents.test-agent.api.key=app-test-key"
})
class DifyIntegrationTest {

    @Autowired
    private IDifyService difyService;

    @Test
    void testFullWorkflow() {
        // 测试完整的工作流程
        ChatMessageResponse response = difyService.sendChatMessage(
            "test-agent", "测试消息", "test-user", null
        );

        assertNotNull(response);
        assertNotNull(response.getAnswer());
        assertNotNull(response.getConversationId());
    }
}
```

## 📚 附录

### 关键类说明

#### DifyConstants
```java
public class DifyConstants {
    public static class ErrorCode {
        public static final String INVALID_PARAMS = "INVALID_PARAMS";
        public static final String AGENT_NOT_FOUND = "AGENT_NOT_FOUND";
        public static final String SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE";
    }

    public static class DefaultValues {
        public static final String DEFAULT_USER = "system";
        public static final int DEFAULT_TOP_K = 5;
        public static final float DEFAULT_SCORE_THRESHOLD = 0.7f;
        public static final int DEFAULT_PAGE_SIZE = 20;
    }
}
```

#### DifyException
```java
public class DifyException extends RuntimeException {
    private final String errorCode;

    public DifyException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public DifyException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
```

### 版本兼容性

| 组件 | 版本要求 |
|------|----------|
| Java | 8+ |
| Spring Boot | 2.x+ |
| dify-java-client | 1.0.0+ |


