# Dify API 参考文档

## 📋 接口概览

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取智能体列表 | GET | `/system/dify/agents` | 获取所有启用的智能体 |
| 发送对话消息 | POST | `/system/dify/agents/{agentName}/chat` | 与智能体对话 |
| 文本生成 | POST | `/system/dify/agents/{agentName}/completion` | 生成文本内容 |
| 执行工作流 | POST | `/system/dify/agents/{agentName}/workflow` | 执行工作流 |
| 知识库检索 | POST | `/system/dify/agents/{agentName}/datasets/{datasetId}/retrieve` | 检索知识库 |
| 获取会话消息 | GET | `/system/dify/agents/{agentName}/conversations/{conversationId}/messages` | 获取会话历史 |
| 获取会话列表 | GET | `/system/dify/agents/{agentName}/conversations` | 获取用户会话列表 |
| 重命名会话 | PUT | `/system/dify/agents/{agentName}/conversations/{conversationId}/name` | 重命名会话 |
| 删除会话 | DELETE | `/system/dify/agents/{agentName}/conversations/{conversationId}` | 删除会话 |
| 消息反馈 | POST | `/system/dify/agents/{agentName}/messages/{messageId}/feedback` | 对消息进行反馈 |
| 获取建议问题 | GET | `/system/dify/agents/{agentName}/messages/{messageId}/suggested` | 获取建议问题 |

## 🔍 接口详情

### 1. 获取智能体列表

```http
GET /system/dify/agents
```

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "customer-service": {
      "type": "chat",
      "description": "智能客服助手",
      "enabled": true
    },
    "document-generator": {
      "type": "completion",
      "description": "文档生成助手",
      "enabled": true
    }
  }
}
```

### 2. 发送对话消息

```http
POST /system/dify/agents/{agentName}/chat
Content-Type: application/json

{
  "query": "你好，我需要帮助",
  "user": "user123",
  "conversationId": "conv_123"
}
```

**参数说明：**
- `query` (string, 必需): 用户查询内容
- `user` (string, 可选): 用户标识，默认使用配置的默认用户
- `conversationId` (string, 可选): 会话ID，不提供则创建新会话

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "msg_456",
    "answer": "您好！我是智能客服助手...",
    "conversationId": "conv_123",
    "createdAt": 1703123456,
    "metadata": {
      "usage": {
        "prompt_tokens": 10,
        "completion_tokens": 20,
        "total_tokens": 30
      }
    }
  }
}
```

### 3. 文本生成

```http
POST /system/dify/agents/{agentName}/completion
Content-Type: application/json

{
  "inputs": {
    "topic": "人工智能",
    "length": "500字",
    "style": "专业"
  },
  "user": "user123"
}
```

**参数说明：**
- `inputs` (object, 必需): 输入参数，根据智能体配置而定
- `user` (string, 可选): 用户标识

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "comp_789",
    "answer": "人工智能（AI）是计算机科学的一个分支...",
    "createdAt": 1703123456,
    "metadata": {
      "usage": {
        "prompt_tokens": 15,
        "completion_tokens": 150,
        "total_tokens": 165
      }
    }
  }
}
```

### 4. 执行工作流

```http
POST /system/dify/agents/{agentName}/workflow
Content-Type: application/json

{
  "inputs": {
    "order_id": "ORD123456",
    "action": "process",
    "priority": "high"
  },
  "user": "user123"
}
```

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "taskId": "task_abc123",
    "status": "completed",
    "data": {
      "result": "订单处理完成",
      "order_status": "processed",
      "next_steps": ["发货", "通知客户"]
    },
    "createdAt": 1703123456
  }
}
```

### 5. 知识库检索

```http
POST /system/dify/agents/{agentName}/datasets/{datasetId}/retrieve
Content-Type: application/json

{
  "query": "如何使用API",
  "topK": 5,
  "scoreThreshold": 0.7
}
```

**参数说明：**
- `query` (string, 必需): 检索查询
- `topK` (integer, 可选): 返回结果数量，默认5
- `scoreThreshold` (float, 可选): 相似度阈值，默认0.7

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "doc_001",
        "content": "API使用指南：首先需要获取API密钥...",
        "score": 0.95,
        "metadata": {
          "source": "用户手册",
          "page": 10
        }
      }
    ]
  }
}
```

### 6. 会话管理

#### 获取会话消息
```http
GET /system/dify/agents/{agentName}/conversations/{conversationId}/messages?limit=20&firstId=msg_123
```

#### 获取会话列表
```http
GET /system/dify/agents/{agentName}/conversations?limit=20&lastId=conv_456&pinned=true
```

#### 重命名会话
```http
PUT /system/dify/agents/{agentName}/conversations/{conversationId}/name
Content-Type: application/json

{
  "name": "新的会话名称",
  "autoGenerate": false
}
```

#### 删除会话
```http
DELETE /system/dify/agents/{agentName}/conversations/{conversationId}
```

#### 消息反馈
```http
POST /system/dify/agents/{agentName}/messages/{messageId}/feedback
Content-Type: application/json

{
  "rating": "like",
  "content": "回答很有帮助"
}
```

**参数说明：**
- `rating` (string, 必需): "like" 或 "dislike"
- `content` (string, 可选): 反馈内容

#### 获取建议问题
```http
GET /system/dify/agents/{agentName}/messages/{messageId}/suggested
```

## 🚨 错误码说明

| 错误码 | HTTP状态码 | 描述 | 解决方案 |
|--------|------------|------|----------|
| 200 | 200 | 成功 | - |
| 400 | 400 | 参数错误 | 检查请求参数 |
| 404 | 404 | 智能体不存在 | 检查智能体名称和配置 |
| 500 | 500 | 服务器错误 | 检查服务状态和日志 |
| 503 | 503 | 服务不可用 | 检查 Dify 服务连接 |

**错误响应格式：**
```json
{
  "code": 400,
  "msg": "智能体名称不能为空",
  "data": null
}
```

## 🔐 认证和权限

所有接口都需要通过系统的认证机制：

```http
Authorization: Bearer <your-token>
```

权限要求：
- `system:dify:chat` - 对话相关接口
- `system:dify:completion` - 文本生成接口
- `system:dify:workflow` - 工作流接口
- `system:dify:datasets` - 知识库接口

## 📊 限流说明

- 每个用户每分钟最多 60 次请求
- 每个智能体每秒最多 10 次请求
- 超出限制返回 429 状态码

## 🔄 SDK 使用

### Java SDK
```java
@Autowired
private IDifyService difyService;

// 发送对话
ChatMessageResponse response = difyService.sendChatMessage(
    "agent-name", "message", "user-id", null
);
```

### 直接 HTTP 调用
```bash
curl -X POST "http://localhost:8080/system/dify/agents/my-agent/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{"query": "Hello", "user": "user123"}'
```

---

*API 参考文档 - 完整的接口说明和使用示例*
