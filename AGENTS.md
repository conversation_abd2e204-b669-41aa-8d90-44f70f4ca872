# AGENTS

面向 TunnyContract 项目的通用智能体协作约定与执行规范。目标是：快速对齐、可审计、最小化变更、结果可验证。

## 角色与职责

- Coding Agent：实现需求，保持变更最小且聚焦；必要时补充文档与脚本。
- Reviewer Agent（可选）：从可读性、回归风险、边界条件与安全性角度给出审查意见。
- Docs Agent：维护 PRD/ 与 README 类文档，确保与实现同步。

## 工作流

1) 需求澄清：明确范围、输入输出与成功标准；标注依赖与风险。
2) 计划编排：当任务非单一步骤时，使用 `update_plan` 建立 3–7 步的短计划，并在推进时更新状态。
3) 实施变更：使用 `apply_patch` 以最小粒度提交文件修改；避免无关改动与格式大动。
4) 验证结果：
   - 优先局部验证：仅运行/检查与本次改动紧密相关的代码路径或测试。
   - 若仓库提供脚本/测试，按既有方式运行；无则提供最小验证方法与说明。
5) 交付说明：在最终回复中以要点形式总结改动、涉及文件、验证方式与后续建议。

## 代码与文档规范

- 最小变更：修复根因，不修无关问题；保持现有风格与命名。
- 文档同步：涉及行为变化时更新 `README`/`PRD/` 或相关注释。
- 不新增版权/许可证头（除非明确要求）。
- 避免单字母变量；不添加与任务无关的重构。
- 中文用于 PRD 与沟通；代码与标识符使用英文。
- 路径引用使用相对路径并在沟通中标注文件与起始行，如：`PRD/合同分段审核方案_1.md:1`。

## 开发规范（通用）

- 语言与版本：在“仓库事实表”中明确运行时版本；新代码遵循该版本特性与最佳实践。
- 代码风格：保持现有风格；若有格式化/静态检查工具（如 Prettier/ESLint、Black/Ruff、gofmt），以其结果为准；无工具时遵循：
  - 缩进统一（2 或 4 空格，按项目既有约定）。
  - 每行不超过 100–120 列；保留文件末尾换行；不留尾随空格。
  - 表达清晰优先于聪明写法；避免过度嵌套与一行多义。
- 命名规范：
  - 文件/目录：全小写，使用 `-` 或 `_` 分隔（依项目约定一致）。
  - 变量/函数：`lowerCamelCase` 或 `snake_case`（与项目一致）。
  - 类型/类：`UpperCamelCase`；常量：`UPPER_SNAKE_CASE`。
  - 禁用难以理解的缩写；避免一字母命名（循环索引除外）。
- 结构与模块化：
  - 单一职责，小模块/小函数；公共逻辑抽到共享模块；避免循环依赖。
  - 保持目录层级扁平化；按功能域分层而非技术分层优先。
- 注释与文档：
  - 公共接口/导出符号需有文档注释；复杂算法给出思路与复杂度说明。
  - TODO/FIXME 需含责任人或追踪链接；避免无时限 TODO。
- 错误处理：
  - 不吞错；携带可定位信息（上下文、关键参数，不含敏感数据）。
  - 区分可重试/不可重试；为调用方提供明确的处理方式；避免用异常做业务分支。
- 日志规范：
  - 结构化日志：固定键（timestamp、level、message、component、trace_id）。
  - 级别：debug/info/warn/error；禁止在 info 级别打印调试细节或敏感信息。
  - 时间使用 UTC；支持关联 ID 贯穿请求链路。
- 依赖管理：
  - 固定版本并使用锁文件；最小依赖原则；定期安全更新（如每月一次）。
  - 引入新依赖需说明动机、权衡与替代；避免过度多样化同类库。
- 配置与密钥：
  - 配置外置（环境变量/配置文件）；支持默认、环境差异与本地覆盖三层。
  - 密钥不入库；通过 `.env`/密钥管理服务注入；提供示例 `*.example` 文件。
- API 设计（如适用）：
  - 合约优先（Schema/IDL）与版本化（SemVer 或 `/v1`）；输入输出显式校验。
  - 幂等与可重试：PUT/DELETE 需幂等；非幂等操作提供幂等键。
  - 错误模型统一（code、message、details）；避免过载的 200。
- 数据与迁移（如适用）：
  - 迁移脚本原子、可回滚；兼容旧版本读写；避免破坏性变更。
  - 索引与约束与查询模式一致；避免 N+1；数据修复脚本可重复执行。
- 性能与资源：
  - 关注复杂度；避免热点循环分配；尽量流式处理与懒加载。
  - 添加合理超时、并发限流与缓存；避免全局可变状态。
- 并发与时序：
  - 优先无共享或不可变；必要时加锁并最小化临界区。
  - 重试使用指数退避与抖动；跨服务操作保持幂等。
- 国际化（如适用）：
  - UI 文案用 i18n key；不硬编码用户可见文本；有默认语言与回退。
- 可观测性：
  - 基础三件套：日志、指标、追踪；关键路径埋点；暴露健康检查。
- 安全编码：
  - 输入校验与转义；严禁命令注入、SQL 注入、路径穿越、模板注入。
  - 外部请求默认超时与允许域白名单；文件/反序列化使用安全模式。
- 测试规范：
  - 金字塔：单元 > 集成 > 端到端；优先覆盖边界与错误路径。
  - 命名与结构：`test_<unit>`/`<Unit>_tests`；Arrange-Act-Assert。
  - 测试应可重现、无网络（除非必要）、无并发共享状态；测试数据使用工厂/夹具。
  - 回归缺陷先写用例再修复；衡量覆盖率但不唯覆盖率。
- 评审与提交：
  - 小步快跑；一 PR 聚焦一主题；描述动机、变更点、验证与影响面。
  - 推荐 Conventional Commits：`feat|fix|docs|refactor|test|chore|build|ci(scope): message`。
  - 分支：`feat/<scope>-<subject>` 等；避免长期大分支与巨型 PR。

## 工具与约束

- Shell：
  - 搜索优先使用 `rg`；阅读文件分块（<= 250 行）。
  - 尊重沙箱与网络限制；需要更高权限或网络访问时应先取得批准。
- `apply_patch`：唯一允许的改动方式；原子化变更，按文件聚合；避免无意义重排。
- `update_plan`：仅在多步骤/长期任务中使用；保持唯一 `in_progress` 步。

## 提交与版本控制

- 默认不执行 `git commit`/创建分支，除非任务明确要求。
- 若需提交，建议信息模板：
  - 标题：<范围>: <简要动机/结果>
  - 正文：动机、主要变更点、验证方式、影响面与回滚策略（如有）。

## 质量与安全清单（PR 前自检）

- 变更是否聚焦且必要？
- 是否影响现有接口/行为？若是，是否已更新文档与调用方？
- 是否提供了最小可复现或验证步骤？
- 异常路径、边界输入与并发/时序是否考虑？
- 是否引入敏感信息或泄露机密？

## 目录约定（当前与后续建议）

- `PRD/`：产品与方案文档（中文）。
- `scripts/`：本地脚本与自动化（如后续引入）。
- `docs/`：开发/运维说明（如后续引入）。

## 与 IDE/代理的协作建议

- 在实现前以一句话说明即将进行的动作；长耗时前先同步意图。
- 变更后提供受影响文件列表与验证指引，便于复核与回滚。

---

如需补充团队特定规范（代码风格、分支策略、发布流程），请在本文件追加相应小节并在评审中达成一致。
# AGENTS

面向 TunnyContract 项目的通用智能体协作约定与执行规范。目标：快速对齐、可审计、最小化变更、结果可验证。

> 适用对象：在本仓库内协作的 Coding/Reviewer/Docs 智能体与工程师。

## 目标与范围

- 对齐协作模式：输入/输出、审批、工具使用、交付格式与验证流程。
- 最小化变更：聚焦任务根因，避免无关重构与样式化改动。
- 可审计：关键动作与决定可追溯，文件与行号引用规范统一。
- 可验证：提供最小验证步骤与回滚建议，降低回归风险。

## 角色与职责

- Coding Agent：实现需求，保持变更最小且聚焦；必要时补充文档与脚本。
- Reviewer Agent（可选）：从可读性、回归风险、边界条件与安全性角度给出审查意见。
- Docs Agent：维护 PRD/ 与 README 类文档，确保与实现同步。

## 工作流

1) 需求澄清：明确范围、输入输出与成功标准；标注依赖与风险。
2) 计划编排：当任务非单一步骤时，使用 `update_plan` 建立 3–7 步的短计划，并在推进时更新状态（保持唯一 in_progress）。
3) 实施变更：使用 `apply_patch` 以最小粒度提交文件修改；避免无关改动与大范围格式化。
4) 验证结果：
   - 优先局部验证：仅运行/检查与本次改动紧密相关的代码路径或测试。
   - 若仓库提供脚本/测试，按既有方式运行；无则提供最小验证方法与说明。
5) 交付说明：在最终回复中以要点形式总结改动、涉及文件、验证方式与后续建议。

成功标准（每次任务交付需满足）：

- 变更聚焦且必要，解决已定义问题或实现明确需求。
- 受影响接口/行为已在文档同步，含验证与回滚指引。
- 提供最小可运行/可验证步骤，优先局部验证。
- 无敏感信息泄露；未引入无关格式化或大规模重排。

## 代码与文档规范

- 最小变更：修复根因，不修无关问题；保持现有风格与命名。
- 文档同步：涉及行为变化时更新 `README`/`PRD/` 或相关注释。
- 不新增版权/许可证头（除非明确要求）。
- 避免单字母变量；不进行与任务无关的重构。
- 中文用于 PRD 与沟通；代码与标识符使用英文。
- 路径引用使用相对路径并在沟通中标注文件与起始行，如：`PRD/合同分段审核方案_1.md:1`。

## 工具与约束

- Shell：
  - 搜索优先使用 `rg`；阅读文件分块（<= 250 行）。
  - 尊重沙箱与网络限制；需要更高权限或网络访问时应先取得批准。
- `apply_patch`：唯一允许的改动方式；原子化变更，按文件聚合；避免无意义重排。
- `update_plan`：用于多步骤/长期任务；保持唯一 `in_progress` 步。

推荐命令模式：

- 搜索：`rg '<keyword>' -n --hidden -g '!**/.git/**'`
- 枚举文件：`rg --files --hidden -g '!**/.git/**'`
- 分块阅读：`sed -n '1,200p' <file>`（最多 250 行一段）
- 打补丁：`apply_patch` 工具（见本仓库代理环境说明）

## 提交与版本控制

- 默认不执行 `git commit`/创建分支，除非任务明确要求。
- 建议信息模板：
  - 标题：`<范围>: <简要动机/结果>`
  - 正文：动机、主要变更点、验证方式、影响面与回滚策略（如有）。

分支与提交（如需使用 Git）：

- 分支命名：`feat/<scope>-<short>`、`fix/<scope>-<short>`、`docs/<scope>-<short>`。
- 提交粒度：与任务子目标一致；避免在同一提交包含无关改动。
- 回滚策略：提供可逆补丁或回退指令，标注受影响文件。

## 环境与审批

- 沙箱：`workspace-write`（仅可写工作空间内文件）；网络：`restricted`。
- 审批模式：`on-request`。需要更高权限或潜在破坏性操作时先说明并征求确认。
- 需要审批的常见场景：
  - 运行会写入受限目录或需要网络的命令（如依赖安装、集成测试）。
  - 潜在破坏性命令（如 `rm`、`git reset`、大规模格式化）。
  - 无法在沙箱内完成且与任务密切相关的验证步骤。

请求审批的写法（示例）：

- 说明目的与预期产出：为何需要、会改变什么、如何回滚。
- 列出将执行的命令与风险点（如可能写入临时目录）。
- 提供替代方案（如只读路径验证）并给出权衡。

## 质量与安全清单（PR 前自检）

- 变更是否聚焦且必要？
- 是否影响现有接口/行为？若是，是否已更新文档与调用方？
- 是否提供了最小可复现或验证步骤？
- 异常路径、边界输入与并发/时序是否考虑？
- 是否引入敏感信息或泄露机密？

安全要点：

- 不输出访问令牌、密钥、个人数据；引用示例使用假值占位。
- 不写入仓库外路径；不在未知目录执行破坏性命令。
- 不擅自更改许可证与版权头（除非明确要求）。

## 交流与进度同步（Preamble & Updates）

- 在执行工具调用前，用 1–2 句说明即将进行的动作，避免逐条噪声。
- 示例：
  - “我将扫描仓库结构并查看 PRD 条目。”
  - “接着更新配置与相关测试用例。”
- 长耗时操作前先同步意图；批量相关命令合并说明。

进度更新（建议 8–10 字内）：

- “已看完 PRD；开始查接口定义。”
- “配置已改；补测试与校验中。”

## 目录约定

- `PRD/`：产品与方案文档（中文）。
- `scripts/`：本地脚本与自动化（如后续引入）。
- `docs/`：开发/运维说明（如后续引入）。

命名约定：

- PRD 文件：`PRD/<主题>_<版本|序号>.md`，如 `PRD/合同分段审核方案_2.md`。
- 脚本：`scripts/<动作>-<对象>.sh`，如 `scripts/gen-report.sh`。
- 代码：遵循语言社区惯例（如 `src/`, `pkg/`, `internal/`）。

## 仓库事实表（填充此区以对齐协作）

以下信息用于将规范落到可执行的命令与路径。若有变更，请同步更新本表与相关小节。

- 语言与运行时：<填入，如 Node 18 / Python 3.11 / Go 1.22>
- 包管理器：<填入，如 pnpm / npm / pip / poetry / cargo>
- 构建命令：`<填入>`
- 测试命令：`<填入>`（仅运行改动相关测试优先）
- Lint/Format：`<填入>`（如 `eslint`, `ruff`, `prettier`, `gofmt`）
- 主要入口：`<填入>`（如服务启动脚本或二进制入口）
- 配置/密钥：`<填入>`（列举 `.env`/`config` 路径与管理方式）
- CI/CD：<填入，如 GitHub Actions / GitLab CI，关键工作流名与触发条件>
- 关键目录：`<填入>`（如 `src/`, `pkg/`, `internal/`, `app/`）

## 与 IDE/代理的协作建议

- 在实现前以一句话说明即将进行的动作；长耗时前先同步意图。
- 变更后提供受影响文件列表与验证指引，便于复核与回滚。

---

## 团队特定规范（参考 .augment/rules）

以下要点依据工作空间的规范约定进行固化，直接内联在本文档中：

- 输出风格：使用简洁要点；必要时用小节标题；避免重格式化与冗长解释。
- 代码与路径引用：用反引号包围命令、路径、标识符；文件引用需含路径与起始行，如 `PRD/合同分段审核方案_1.md:1`；不输出 ANSI 码。
- Shell 使用：优先 `rg` 搜索；读取文件按块（<= 250 行）；尊重沙箱与网络限制。
- 变更工具：仅用 `apply_patch` 修改；原子化、按文件聚合；避免无意义重排。
- 计划工具：非单步任务使用 `update_plan`；保持唯一 `in_progress` 步；适时更新状态。
- 提交策略：默认不 `git commit`/建分支；如需提交，按模板撰写信息（标题与正文）。
- 验证策略：优先局部验证；按仓库既有脚本/测试；无则提供最小验证说明与操作步骤。
- 安全与合规：不引入敏感信息；不新增版权/许可证头（除非明确要求）。

如需与 `.augment/rules/*` 文件逐字对齐，请提供当前规则内容或授权读取该目录，我将把其内容直接内联到本节。

---

## 输出结构规范（面向最终答复）

- 标题：仅在有助于可读性时使用，1–3 个词，`**Title Case**`。
- 列表：以 `- ` 开头，关键词加粗后接冒号与简述，合并相近点。
- 等宽：命令、路径、标识符使用反引号包裹，如 `apply_patch`、`PRD/合同分段审核方案_1.md`。
- 文件引用：必须包含路径与起始行，示例：
  - `AGENTS.md:1`
  - `PRD/合同分段审核方案_1.md:1`
- 语气：协作、简洁、现在时；避免冗长与重复。

## PRD 写作与引用规范

- 语言：中文撰写，术语统一；必要时在首次出现给出英文原名。
- 命名：`PRD/<主题>_<版本/序号>.md`，如 `PRD/合同分段审核方案_1.md`。
- 结构：问题定义 → 目标与非目标 → 方案与权衡 → 风险与验证 → 验收标准。
- 引用：在实现或评审时，使用文件与起始行引用段落，如 `PRD/合同分段审核方案_2.md:1`。
- 变更同步：实现导致 PRD 变更时，同 PR 合并，保持版本号/序号或在文首标注变更历史。

## 工具使用细则

- `shell`：搜索优先 `rg`；读取文件分块（<= 250 行）；输出可能被截断，必要时分段读取。
- `apply_patch`：唯一允许的文件改动方式；按文件聚合改动；禁止无关重排与风格化格式化。
- `update_plan`：仅在多步骤任务中使用；保持唯一 `in_progress`；阶段完成后及时标记。

## 验证与回归控制

- 局部优先：仅运行/检查与改动直接相关的代码路径或测试。
- 既有路径：若仓库有脚本/测试，遵循其既有方式；无则提供最小验证步骤。
- 变更说明：在最终答复中列出改动摘要、文件与行、验证方式与下一步建议。

## 角色补充要点

- Reviewer Agent：聚焦可读性、回归风险、边界条件、安全性；指出可最小化风险的替代实现。
- Docs Agent：保持 `PRD/` 与实现一致；当接口/行为变化时，更新相应文档与引用。

## 快速上手（Playbook）

- 代码小修（单文件）：
  - 澄清输入/输出与成功标准 → `apply_patch` 最小改动 → 局部验证 → 在答复中列改动摘要与验证步骤。
- 新功能（多步骤）：
  - 用 `update_plan` 规划 3–7 步，保持唯一 `in_progress` → 分阶段实现与验证 → 汇总交付说明与后续建议。
- 文档增补：
  - 对齐实现 → 更新 `PRD/*` 或 `README` → 标注引用文件与起始行 → 提供变更历史或版本号。

## Reviewer 清单（精简）

- 可读性：命名、结构、注释是否清晰一致；无多余改动。
- 回归风险：边界输入/异常路径覆盖；是否提供最小验证步骤。
- 安全：无敏感信息；权限与网络使用合规。
- 文档：`PRD/` 与实现一致；交付说明完备。

## 常见场景规范

- 搜索与定位：先 `rg` 定位定义与引用，再分块阅读。
- 大文件修改：仅编辑必要片段，避免整体重排；若需格式化，限定作用域并说明理由。
- 多文件联动：按文件聚合补丁；每个补丁仅聚焦一个逻辑单元。
- 测试不足：优先局部最小验证；必要时添加紧邻变更的最小测试（遵循现有测试风格）。

## 变更日志（维护建议）

- 在此记录对协作规范的重大调整与日期，便于团队对齐与回溯。
