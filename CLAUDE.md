# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

这是 **TunnyContract**，一个基于若依框架的全栈Web应用程序。包含：

- **TunnyContract_UI**: Vue 3 + TypeScript 前端 (onePiece Pro 主题)
- **TunnyContract_BACK**: Spring Boot 后端 (若依 v3.9.0)

项目采用现代技术栈：前端使用 Vue 3 + TypeScript + Vite，后端使用 Spring Boot + Spring Security + MyBatis。

## 开发命令

### 前端 (TunnyContract_UI)

**重要**：前端任务始终在 `TunnyContract_UI` 目录中工作。

```bash
cd TunnyContract_UI

# 开发
npm run dev          # 启动开发服务器（自动打开浏览器）
npm run build        # 生产构建（包含类型检查）
npm run serve        # 预览生产构建

# 代码质量
npm run lint         # 运行 ESLint
npm run fix          # 自动修复 ESLint 问题
npm run lint:prettier      # 使用 Prettier 格式化代码
npm run lint:stylelint     # 检查和修复 CSS/SCSS 样式
npm run lint:lint-staged   # 运行预提交检查

# 工具命令
npm run commit       # 使用 commitizen 进行标准化提交
npm run clean:dev    # 清理开发环境
```

### 后端 (TunnyContract_BACK)

**重要**：后端任务始终在 `TunnyContract_BACK` 目录中工作。

```bash
cd TunnyContract_BACK

# 开发（需要 Java 8+ 和 Maven）
./ry.sh             # 启动应用程序（Linux/macOS）
ry.bat              # 启动应用程序（Windows）

# Maven 命令
mvn clean install   # 构建项目
mvn spring-boot:run # 直接运行应用程序
```

## 架构概览

### 前端架构

**核心框架**: Vue 3.5.12 + TypeScript 5.6.3 + Vite 6.1.0

**关键模式**：

1. **API 层结构**:
```typescript
// 标准 API 类模式
export class [Module]Api {
  static async get[Module]List(params?: QueryParams) {
    return http.get('/system/[module]/list', { params })
  }
  static async add[Module](data: Partial<Entity>) {
    return http.put('/system/[module]', data)
  }
  // ... 其他 CRUD 方法
}
```

2. **组合式函数**:
   - `useTable`: 高级表格数据管理（缓存、防抖）
   - `useTableColumns`: 动态列管理
   - `useDict`: 字典数据管理

3. **组件结构**:
   - `src/components/core/`: 可复用UI组件
   - `src/components/custom/`: 项目特定组件
   - 启用自动导入（无需手动导入）

**路径别名** （在 Vite 和 TypeScript 中配置）:
```typescript
'@/*': ['src/*']
'@views/*': ['src/views/*']
'@utils/*': ['src/utils/*']
'@stores/*': ['src/store/*']
'@styles/*': ['src/assets/styles/*']
// ... 更多别名
```

### 后端架构

**框架**: Spring Boot 2.5.15 + Spring Security + MyBatis

**模块**:
- `ruoyi-admin`: 主应用程序模块
- `ruoyi-common`: 通用工具和常量
- `ruoyi-framework`: 核心框架组件
- `ruoyi-system`: 系统管理模块
- `ruoyi-generator`: 代码生成工具
- `ruoyi-quartz`: 定时任务管理

## 开发指南

### 代码风格

**前端**:
- ESLint 配置强制使用单引号，无分号
- Prettier 进行一致性格式化
- Stylelint 用于 CSS/SCSS 标准
- Commitizen 用于标准化提交消息

**TypeScript 标准**:
- 启用严格类型检查
- 接口定义遵循 PascalCase
- API 响应类型扩展 `RuoyiResponse<T>`

### 文件命名约定

- **组件**: PascalCase（如 `UserCard.vue`）
- **组合式函数**: camelCase，以 `use` 开头（如 `useTable.ts`）
- **类型**: PascalCase（如 `UserInfo.ts`）
- **API 模块**: PascalCase 类（如 `UserApi`）

### 关键开发模式

1. **API 集成**:
   - 所有 API 使用集中的 `http` 工具
   - 响应类型使用 TypeScript 强类型化
   - 错误处理集中化

2. **状态管理**:
   - 使用 Pinia 进行状态管理，包含持久化插件
   - 存储模块遵循领域驱动组织

3. **路由**:
   - Vue Router 4 支持动态路由加载
   - 路由守卫用于身份验证和权限控制

4. **国际化**:
   - Vue i18n 支持多语言
   - 语言文件位于 `src/locales/langs/`

5. **主题**:
   - SCSS 使用全局变量和混合器
   - 支持深色/浅色主题
   - Element Plus 主题自定义

## 测试

项目使用若依测试方法。在创建新测试之前，请检查现有测试文件和后端单元测试以了解测试模式。

## 构建配置

- **前端**: Vite 具有高级优化（压缩、块分割、树摇）
- **TypeScript**: 启用严格模式，进行全面类型检查
- **自动导入**: 组件和 Vue API 自动导入
- **开发**: 启用 HMR 和 Vue DevTools 集成

## 环境设置

1. **先决条件**: Node.js ≥18.12.0、Java 8+、Maven 3.6+
2. **前端设置**: `cd TunnyContract_UI && npm install`
3. **后端设置**: `cd TunnyContract_BACK && mvn clean install`
4. **数据库**: 在后端 application.yml 中配置数据库连接

## 常见开发任务

- **添加新功能**: 遵循前后端模块化结构
- **API 开发**: 按现有模式创建 API 类
- **组件开发**: 使用已建立的组件架构和 TypeScript
- **数据库变更**: 使用 MyBatis 进行 ORM，遵循若依约定
- **权限管理**: 使用若依的 RBAC 系统实现

在提交代码前始终运行代码检查和类型检查。

## 智能合同审查系统架构

### 文档处理引擎

**核心组件位置**: `TunnyContract_BACK/ruoyi-common/src/main/java/com/ruoyi/common/utils/contract/`

#### 关键服务类
- **PdfParseUtil/PdfParseService**: PDF文档解析，支持文本提取、坐标定位、元数据获取
- **WordParseUtil/WordParseService**: Word文档解析，支持DOC/DOCX格式
- **AsyncDocumentParseService**: 异步文档处理，支持任务队列、进度跟踪、批量处理
- **ContractFileValidator**: 文件安全校验，支持类型检测、MD5去重、路径安全
- **ContractFileUploadService**: 文件上传服务，集成MinIO存储

#### 文档解析架构模式
```java
// 同步解析模式
PdfParseResult result = pdfParseService.parsePdfSync(filePath);

// 异步解析模式  
String taskId = asyncParseService.submitParseTask(filePath);
ParseTask task = asyncParseService.getTaskStatus(taskId);

// 批量处理模式
List<String> taskIds = asyncParseService.submitBatchParseTasks(filePaths);
```

### Dify智能审查集成

**依赖**: `io.github.imfangs:dify-java-client:1.1.7`

系统已预留Dify接口集成点，用于智能合同审查：
- 合同内容分析和风险识别
- 条款合规性检查
- 审查报告生成

### 数据库表结构

**合同文件表** (`contract_file`):
- 支持PDF/Word文档元数据存储
- MD5去重机制
- 解析状态跟踪
- MinIO存储路径管理

**必需字段模式**:
```sql
-- 所有合同相关表必须包含
`file_md5` varchar(32) -- 文件MD5校验
`parse_status` char(1) -- 解析状态：0-未解析，1-解析中，2-解析完成，3-解析失败
`upload_status` char(1) -- 上传状态：0-未上传，1-上传成功，2-上传失败
`task_id` bigint(20) -- 关联审查任务ID
```

## 数据库设计规范

### 基础规范

**数据库编码**: UTF8MB4_BIN
**表引擎**: InnoDB
**主键策略**: 自增ID + 业务唯一索引

### 标准字段规范

所有业务表**必须**包含以下标准字段：

#### 审计字段（必选）

```sql
-- 时间审计字段
`create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',

-- 用户审计字段  
`create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
`update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',

-- 软删除标识
`del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
```

#### 扩展字段（推荐）

```sql
-- 预留扩展字段，便于后续业务扩展
`attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
`attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2', 
`attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
```

### 完整表结构模板

```sql
CREATE TABLE `业务表名` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 业务字段
  `业务字段名` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '业务字段说明',
  
  -- 标准审计字段（必选）
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '修改人',
  `del_flag` char(1) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '0-正常，2-删除',
  
  -- 扩展字段（推荐）
  `attr1` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段1',
  `attr2` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段2',
  `attr3` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '保留字段3',
  
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='业务表说明';
```

### 命名规范

#### 表命名
- **格式**: `模块_业务表名`
- **示例**: `sys_user`、`contract_info`、`order_detail`
- **规则**: 全小写，单词间用下划线分隔

#### 字段命名
- **格式**: 全小写，单词间用下划线分隔
- **布尔字段**: 以 `is_`、`has_`、`can_` 开头
- **状态字段**: 以 `status`、`state`、`flag` 结尾
- **时间字段**: 以 `_time` 结尾
- **ID字段**: 以 `_id` 结尾

#### 索引命名
- **主键**: `PRIMARY KEY`
- **唯一索引**: `uk_字段名`
- **普通索引**: `idx_字段名`
- **组合索引**: `idx_字段1_字段2`

### 数据类型规范

#### 数值类型
- **主键ID**: `bigint(20)` - 支持大数据量
- **状态标识**: `char(1)` - 固定长度状态
- **金额字段**: `decimal(10,2)` - 精确计算
- **数量字段**: `int(11)` - 整数数量

#### 字符类型
- **短文本**: `varchar(50/100/255)` - 根据实际需要
- **长文本**: `text` - 大段文本内容
- **JSON数据**: `json` - 结构化数据存储

#### 时间类型
- **时间戳**: `timestamp` - 自动维护时间
- **日期**: `date` - 仅日期不含时间
- **日期时间**: `datetime` - 完整日期时间

### 业务规范

#### 软删除
- 使用 `del_flag` 字段实现软删除
- `0` 表示正常，`2` 表示已删除
- 查询时必须添加 `del_flag = '0'` 条件

#### 审计追踪
- `create_by`/`update_by` 存储用户ID或用户名
- `create_time`/`update_time` 自动维护时间戳
- 重要业务操作应记录操作日志

#### 扩展设计
- 预留 `attr1`、`attr2`、`attr3` 字段用于业务扩展
- 复杂扩展需求可使用JSON字段存储
- 考虑未来数据迁移和版本兼容性

### MyBatis 集成

#### BaseEntity 基类

```java
public class BaseEntity implements Serializable {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    private String createBy;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")  
    private Date updateTime;
    
    private String updateBy;
    
    private String delFlag = "0";
    
    private String attr1;
    private String attr2;
    private String attr3;
}
```

#### Mapper XML 模板

```xml
<!-- 标准查询条件 -->
<select id="selectList" resultMap="BaseResultMap">
    SELECT * FROM 表名 
    WHERE del_flag = '0'
    <if test="createBy != null and createBy != ''">
        AND create_by = #{createBy}
    </if>
    ORDER BY create_time DESC
</select>

<!-- 软删除更新 -->
<update id="delete">
    UPDATE 表名 SET 
        del_flag = '2',
        update_time = NOW(),
        update_by = #{updateBy}
    WHERE id = #{id}
</update>
```

### 开发检查清单

在创建新表或修改表结构时，请确认：

- [ ] 包含所有必需的审计字段
- [ ] 遵循命名规范
- [ ] 设置适当的索引
- [ ] 添加字段注释
- [ ] 考虑软删除需求  
- [ ] 预留扩展字段
- [ ] 对应Entity类继承BaseEntity
- [ ] Mapper支持软删除条件

## REST API设计规范

### 智能合同审查API模式

**控制器层级**:
```java
@RestController
@RequestMapping("/contract/[module]")
public class Contract[Module]Controller extends BaseController {
    // 标准CRUD + 业务特定方法
    // 使用@PreAuthorize进行权限控制
    // 使用@Log记录操作日志
}
```

**异步任务API模式**:
- `/contract/async-parse/submit` - 提交解析任务
- `/contract/async-parse/status/{taskId}` - 查询任务状态
- `/contract/async-parse/progress/{taskId}` - 获取任务进度
- `/contract/async-parse/cancel/{taskId}` - 取消任务

### 文件处理API设计

**上传API**: `/contract/upload/single`
- 支持文件类型: PDF, DOC, DOCX
- 自动MD5去重检查
- MinIO存储集成
- 返回格式: `{success: true, data: {fileId, filePath, fileMd5}}`

**解析API**: 
- 同步: `/contract/parse/sync`
- 异步: `/contract/parse/async`
- 批量: `/contract/parse/batch`

## 重要依赖和配置

### Maven依赖 (ruoyi-common/pom.xml)
```xml
<!-- 文档处理 -->
<dependency>
    <groupId>org.apache.pdfbox</groupId>
    <artifactId>pdfbox</artifactId>
    <version>2.0.30</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-scratchpad</artifactId>
    <version>5.2.5</version>
</dependency>

<!-- 智能审查 -->
<dependency>
    <groupId>io.github.imfangs</groupId>
    <artifactId>dify-java-client</artifactId>
    <version>1.1.7</version>
</dependency>

<!-- 文件存储 -->
<dependency>
    <groupId>io.minio</groupId>
    <artifactId>minio</artifactId>
    <version>8.2.1</version>
</dependency>

<!-- 文件类型检测 -->
<dependency>
    <groupId>org.apache.tika</groupId>
    <artifactId>tika-core</artifactId>
    <version>2.9.1</version>
</dependency>
```

### 关键配置项 (application.yml)
- MinIO存储配置 (bucket, endpoint, credentials)
- 文件上传限制 (大小、类型)
- 异步任务线程池配置
- Dify API配置 (预留)
- 请记住,在后续的JAVA代码的实现中请严格使用JAVA8的语法,不要使用高于这个版本的语法,否则会导致编译报错
- 请记住不要使用npm命令,要使用pnpm命令