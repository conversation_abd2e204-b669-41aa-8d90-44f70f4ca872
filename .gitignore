# ===================================
# TunnyContract Project .gitignore
# 前后端分离项目忽略文件配置
# ===================================

# ===================================
# 操作系统相关文件
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===================================
# IDE 和编辑器配置文件
# ===================================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
.idea_modules/

# Eclipse
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json


# Spec-workflow
.spec-workflow/
.promptx/

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# ===================================
# Java/Maven 相关
# ===================================
# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Java 编译产物
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Java 运行时
hs_err_pid*
replay_pid*

# ===================================
# Node.js/前端相关
# ===================================
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建产物
dist/
build/
.nuxt/
.next/
.vuepress/dist/

# 缓存目录
.npm
.yarn-integrity
.pnpm-store/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 前端工具缓存
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# ===================================
# 日志文件
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ===================================
# 临时文件和缓存
# ===================================
# 通用临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.rar
*.tar
*.zip

# ===================================
# 数据库文件
# ===================================
*.db
*.sqlite
*.sqlite3

# ===================================
# 敏感信息和配置
# ===================================
# 配置文件
config/local.properties
application-local.yml
application-local.properties

# 密钥文件
*.pem
*.key
*.p12
*.jks

# ===================================
# 其他开发工具
# ===================================
# Git
.git/
.gitattributes

# Docker
.dockerignore

# 测试覆盖率
coverage/
*.lcov
.nyc_output/

# 文档生成
docs/_build/
site/

# ===================================
# 项目特定忽略
# ===================================
# 若依框架特定
ruoyi-admin/src/main/resources/static/
ruoyi-admin/src/main/resources/public/

# 上传文件目录
upload/
uploads/
files/

# 自定义配置
custom.properties
local.conf

# ===================================
# AI工具配置文件
# ===================================
.kiro
.data
.conductor