# 🎯 TunnyContract

<p align="center">
  <img alt="TunnyContract Logo" src="https://img.shields.io/badge/TunnyContract-v1.0.0-blue.svg">
</p>

<h4 align="center">基于若依框架的现代化合同管理系统</h4>

<p align="center">
  <a href="#"><img src="https://img.shields.io/badge/Vue-3.5.12-4FC08D?logo=vue.js"></a>
  <a href="#"><img src="https://img.shields.io/badge/Spring%20Boot-2.5.15-6DB33F?logo=spring"></a>
  <a href="#"><img src="https://img.shields.io/badge/TypeScript-5.6.3-3178C6?logo=typescript"></a>
  <a href="#"><img src="https://img.shields.io/badge/Element%20Plus-2.10.2-409EFF?logo=element"></a>
  <a href="#"><img src="https://img.shields.io/badge/License-MIT-green.svg"></a>
</p>

claude-code --permission-mode bypassPermissions

claude-code --permission-mode bypassPermissions --no-open

> 一款专注于合同全生命周期管理的企业级解决方案，采用前后端分离架构，提供现代化的用户体验和强大的业务功能。

## 📖 项目简介

**TunnyContract** 是一个基于若依框架开发的现代化合同管理系统，旨在为企业提供完整的合同管理解决方案。系统采用前后端分离架构，前端基于 Vue 3 + TypeScript + Element Plus 构建，后端采用 Spring Boot + Spring Security + MyBatis 技术栈，提供安全、高效、易用的合同管理平台。

### 🎯 核心价值

- 📋 **合同全生命周期管理** - 从合同创建到归档的完整流程管理
- 🔐 **企业级安全保障** - 基于 RBAC 的权限控制和数据安全
- 🎨 **现代化用户界面** - 基于 onePiece Pro 主题的精美 UI 设计
- 🚀 **高性能架构** - 前后端分离，支持高并发和横向扩展
- 🛠️ **开箱即用** - 完整的业务功能和丰富的管理工具

## 🏗️ 技术架构

### 前端技术栈 (TunnyContract_UI)

| 技术 | 版本 | 说明 |
|------|------|------|
| **Vue** | 3.5.12 | 渐进式 JavaScript 框架 |
| **TypeScript** | 5.6.3 | 类型安全的 JavaScript 超集 |
| **Vite** | 6.1.0 | 下一代前端构建工具 |
| **Element Plus** | 2.10.2 | 基于 Vue 3 的组件库 |
| **Pinia** | 3.0.2 | Vue 官方推荐的状态管理库 |
| **Vue Router** | 4.4.2 | Vue 官方路由管理器 |
| **Axios** | 1.7.5 | HTTP 客户端库 |
| **ECharts** | 5.6.0 | 数据可视化图表库 |

### 后端技术栈 (TunnyContract_BACK)

| 技术 | 版本 | 说明 |
|------|------|------|
| **Spring Boot** | 2.5.15 | 企业级应用开发框架 |
| **Spring Security** | 5.7.12 | 安全认证和授权框架 |
| **MyBatis** | - | 持久层框架 |
| **Redis** | - | 缓存和会话存储 |
| **JWT** | 0.9.1 | 无状态身份验证 |
| **Druid** | 1.2.23 | 数据库连接池 |
| **FastJSON** | 2.0.57 | JSON 处理库 |
| **Maven** | - | 项目构建和依赖管理 |

## 📁 项目结构

```
TunnyContract/
├── 📁 TunnyContract_UI/              # 前端项目
│   ├── 📁 src/                       # 源代码
│   │   ├── 📁 api/                   # API 接口层
│   │   ├── 📁 components/            # 可复用组件
│   │   ├── 📁 views/                 # 页面组件
│   │   ├── 📁 store/                 # 状态管理
│   │   ├── 📁 router/                # 路由配置
│   │   ├── 📁 utils/                 # 工具函数
│   │   └── 📁 types/                 # TypeScript 类型定义
│   ├── 📄 package.json               # 项目依赖配置
│   ├── 📄 vite.config.ts             # Vite 构建配置
│   └── 📄 tsconfig.json              # TypeScript 配置
├── 📁 TunnyContract_BACK/            # 后端项目
│   ├── 📁 ruoyi-admin/               # 主应用模块
│   ├── 📁 ruoyi-common/              # 通用工具模块
│   ├── 📁 ruoyi-framework/           # 核心框架模块
│   ├── 📁 ruoyi-system/              # 系统管理模块
│   ├── 📁 ruoyi-generator/           # 代码生成模块
│   ├── 📁 ruoyi-quartz/              # 定时任务模块
│   ├── 📁 sql/                       # 数据库脚本
│   └── 📄 pom.xml                    # Maven 配置文件
├── 📄 README.md                      # 项目说明文档
├── 📄 .gitignore                     # Git 忽略文件配置
└── 📄 CLAUDE.md                      # 开发指导文档
```

## 🎨 功能特性

### 🎯 核心业务功能

- ✅ **合同管理** - 合同创建、编辑、审批、归档
- ✅ **用户管理** - 用户注册、权限分配、组织架构
- ✅ **角色权限** - 基于 RBAC 的细粒度权限控制
- ✅ **审批流程** - 可配置的合同审批工作流
- ✅ **文档管理** - 合同文档上传、版本控制、在线预览
- ✅ **数据统计** - 合同数据分析和可视化报表

### 🎨 界面特性

- 🎭 **主题切换** - 浅色/暗色主题无缝切换
- 🔍 **全局搜索** - 快速定位合同和功能
- 📑 **多标签页** - 提升操作效率
- 🍞 **面包屑导航** - 清晰的页面层级
- 🌍 **国际化支持** - 多语言切换
- 📱 **响应式设计** - 完美支持移动端

### 🛠️ 系统管理功能

- 🏢 **组织管理** - 部门、岗位、用户组织架构
- 📋 **菜单管理** - 动态菜单配置和权限控制
- 📊 **系统监控** - 在线用户、操作日志、系统性能
- 🔧 **代码生成** - 自动生成前后端 CRUD 代码
- 📚 **数据字典** - 系统配置和枚举值管理
- ⏰ **定时任务** - 可视化任务调度管理

## 🚀 快速开始

### 📋 环境要求

- **Node.js**: ≥ 18.12.0 (推荐使用 LTS 版本)
- **Java**: ≥ 1.8 (推荐 Java 11)
- **Maven**: ≥ 3.6.0
- **MySQL**: ≥ 5.7 或 ≥ 8.0
- **Redis**: ≥ 6.0 (可选，用于缓存)

### 📦 安装部署

#### 1. 克隆项目

```bash
git clone https://github.com/your-username/TunnyContract.git
cd TunnyContract
```

#### 2. 后端部署

```bash
# 进入后端目录
cd TunnyContract_BACK

# 配置数据库
# 1. 创建数据库 tunny_contract
# 2. 导入 sql/ry_20250522.sql 文件
# 3. 修改 ruoyi-admin/src/main/resources/application.yml 中的数据库配置

# 编译项目
mvn clean install

# 启动应用
./ry.sh        # Linux/macOS
# 或
ry.bat         # Windows
```

#### 3. 前端部署

```bash
# 进入前端目录
cd TunnyContract_UI

# 安装依赖
npm install
# 或使用 pnpm (推荐)
pnpm install

# 启动开发服务器
npm run dev
# 或
pnpm dev

# 构建生产版本
npm run build
# 或
pnpm build
```

### 🌐 访问系统

- **前端地址**: http://localhost:3000
- **后端地址**: http://localhost:8080
- **默认账号**: admin / admin123

## 📚 开发指南

### 🔧 开发规范

#### 前端开发规范
- **组件命名**: 使用 PascalCase (UserCard.vue)
- **函数命名**: 使用 camelCase (getUserList)
- **常量命名**: 使用 UPPER_SNAKE_CASE (API_BASE_URL)
- **文件命名**: 使用 kebab-case (user-management.vue)

#### 后端开发规范
- **包命名**: 使用小写字母 (com.ruoyi.system)
- **类命名**: 使用 PascalCase (UserController)
- **方法命名**: 使用 camelCase (getUserList)
- **常量命名**: 使用 UPPER_SNAKE_CASE (DEFAULT_PAGE_SIZE)

### 🛠️ API 设计规范

```typescript
// 前端 API 调用示例
export class ContractApi {
  static async getContractList(params?: ContractQueryParams) {
    return http.get('/system/contract/list', { params })
  }
  
  static async addContract(data: Partial<Contract>) {
    return http.post('/system/contract', data)
  }
  
  static async updateContract(data: Contract) {
    return http.put('/system/contract', data)
  }
  
  static async deleteContract(contractId: number) {
    return http.delete(`/system/contract/${contractId}`)
  }
}
```

### 🎯 组件开发

```vue
<!-- 组件开发示例 -->
<template>
  <div class="contract-card">
    <el-card>
      <template #header>
        <span>{{ contract.title }}</span>
      </template>
      <p>{{ contract.description }}</p>
    </el-card>
  </div>
</template>

<script setup lang="ts">
interface Props {
  contract: Contract
}

defineProps<Props>()
</script>
```

## 🚀 部署说明

### 生产环境部署

#### 前端部署
```bash
# 构建生产版本
npm run build

# 将 dist 目录部署到 Web 服务器
# 推荐使用 Nginx 作为静态文件服务器
```

#### 后端部署
```bash
# 打包应用
mvn clean package

# 运行 JAR 文件
java -jar ruoyi-admin/target/ruoyi-admin.jar

# 或使用 Docker 部署
docker build -t tunny-contract-backend .
docker run -p 8080:8080 tunny-contract-backend
```

### Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # 后端 API 代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🤝 贡献指南

欢迎参与 TunnyContract 项目的开发！请遵循以下步骤：

1. **Fork** 本仓库
2. **创建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'Add some amazing feature'`)
4. **推送分支** (`git push origin feature/amazing-feature`)
5. **创建 Pull Request**

### 📋 开发流程

1. 安装依赖并启动开发环境
2. 编写代码并进行测试
3. 运行代码检查: `npm run lint`
4. 格式化代码: `npm run lint:prettier`
5. 提交代码: `npm run commit`

## 📄 许可证

本项目采用 [MIT](LICENSE) 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢 [若依框架](https://gitee.com/y_project/RuoYi-Vue) 提供的优秀基础框架
- 感谢 [onePiece Pro](https://github.com/your-username/onePiece-pro) 提供的精美前端主题
- 感谢所有为这个项目做出贡献的开发者

## 📞 技术支持

- 📖 **项目文档**: [查看详细文档](./docs/)
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-username/TunnyContract/issues)
- 💬 **技术交流**: 欢迎加入我们的技术交流群

---

⭐ 如果这个项目对你有帮助，请给它一个 Star！
