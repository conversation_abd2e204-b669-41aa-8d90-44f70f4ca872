# 合同智能审核系统技术实现方案

## 一、项目结构与依赖配置

### 1.1 项目结构
```
contract-review-system/
├── src/main/java/com/example/contractreview/
│   ├── controller/          # REST接口层
│   ├── service/             # 业务逻辑层
│   ├── repository/          # 数据访问层
│   ├── entity/              # 实体类
│   ├── dto/                 # 数据传输对象
│   ├── config/              # 配置类
│   ├── utils/               # 工具类
│   └── ContractReviewApplication.java
├── src/main/resources/
│   ├── application.yml      # 配置文件
│   └── db/migration/        # 数据库迁移脚本
└── pom.xml
```

### 1.2 Maven依赖配置 (pom.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
    </parent>

    <groupId>com.example</groupId>
    <artifactId>contract-review-system</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>17</java.version>
        <langchain4j.version>0.24.0</langchain4j.version>
        <poi.version>5.2.4</poi.version>
    </properties>

    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-async</artifactId>
        </dependency>

        <!-- LangChain4j -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-open-ai</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-embeddings</artifactId>
            <version>${langchain4j.version}</version>
        </dependency>

        <!-- Document Processing -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>3.0.0</version>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Utilities -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

## 二、核心实体类设计

### 2.1 合同文档实体

```java
package com.example.contractreview.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "contract_documents")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractDocument {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(nullable = false)
    private String fileName;
    
    @Column(length = 1000000) // 存储文件内容，实际应使用文件系统或对象存储
    private String content;
    
    @Enumerated(EnumType.STRING)
    private ContractType contractType;
    
    @Enumerated(EnumType.STRING)
    private ReviewPosition reviewPosition;
    
    @Enumerated(EnumType.STRING)
    private ReviewStatus status = ReviewStatus.PENDING;
    
    private LocalDateTime uploadTime;
    
    private LocalDateTime reviewTime;
    
    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ContractSegment> segments = new ArrayList<>();
    
    @OneToMany(mappedBy = "document", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ReviewResult> reviewResults = new ArrayList<>();
    
    public enum ContractType {
        PURCHASE("采购合同"),
        SALES("销售合同"),
        SERVICE("服务合同"),
        LEASE("租赁合同"),
        LABOR("劳动合同"),
        NDA("保密协议");
        
        private final String description;
        
        ContractType(String description) {
            this.description = description;
        }
    }
    
    public enum ReviewPosition {
        PARTY_A("甲方"),
        PARTY_B("乙方"),
        NEUTRAL("中立");
        
        private final String description;
        
        ReviewPosition(String description) {
            this.description = description;
        }
    }
    
    public enum ReviewStatus {
        PENDING,
        PROCESSING,
        COMPLETED,
        FAILED
    }
}
```

### 2.2 合同段落实体

```java
package com.example.contractreview.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity
@Table(name = "contract_segments")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractSegment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id")
    @JsonIgnore
    private ContractDocument document;
    
    private Integer sequenceNumber;
    
    @Column(length = 10000)
    private String content;
    
    private String sectionTitle;
    
    @Enumerated(EnumType.STRING)
    private SegmentType segmentType;
    
    private String semanticCategory; // 语义分类：付款条款、违约责任等
    
    private Integer startPosition;
    
    private Integer endPosition;
    
    public enum SegmentType {
        TITLE,
        CLAUSE,
        SUBSECTION,
        PARAGRAPH
    }
}
```

### 2.3 审核规则实体

```java
package com.example.contractreview.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;
import java.util.ArrayList;

@Entity
@Table(name = "review_rules")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReviewRule {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @Column(nullable = false)
    private String ruleName;
    
    @Enumerated(EnumType.STRING)
    private ContractDocument.ContractType contractType;
    
    @Enumerated(EnumType.STRING)
    private ContractDocument.ReviewPosition reviewPosition;
    
    @Column(length = 5000)
    private String checkPoint; // 审核要点
    
    @OneToMany(mappedBy = "rule", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<RiskPoint> riskPoints = new ArrayList<>();
    
    @Column(length = 10000)
    private String promptTemplate; // Prompt模板
    
    private Boolean isActive = true;
    
    private Integer priority = 0;
}
```

### 2.4 风险点实体

```java
package com.example.contractreview.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity
@Table(name = "risk_points")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskPoint {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    private String riskName;
    
    @Column(length = 2000)
    private String riskDescription;
    
    @Enumerated(EnumType.STRING)
    private RiskLevel riskLevel;
    
    @Column(length = 2000)
    private String suggestionTemplate; // 建议模板
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rule_id")
    @JsonIgnore
    private ReviewRule rule;
    
    public enum RiskLevel {
        HIGH("高风险"),
        MEDIUM("中风险"),
        LOW("低风险"),
        INFO("提示");
        
        private final String description;
        
        RiskLevel(String description) {
            this.description = description;
        }
    }
}
```

### 2.5 审核结果实体

```java
package com.example.contractreview.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "review_results")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReviewResult {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id")
    private ContractDocument document;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "segment_id")
    private ContractSegment segment;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rule_id")
    private ReviewRule rule;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "risk_point_id")
    private RiskPoint riskPoint;
    
    @Enumerated(EnumType.STRING)
    private RiskPoint.RiskLevel riskLevel;
    
    @Column(length = 5000)
    private String riskDescription;
    
    @Column(length = 5000)
    private String suggestion;
    
    @Column(length = 2000)
    private String evidence; // 原文证据
    
    private Double confidenceScore;
    
    private LocalDateTime reviewTime;
}
```

## 三、数据访问层

### 3.1 Repository接口

```java
package com.example.contractreview.repository;

import com.example.contractreview.entity.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ContractDocumentRepository extends JpaRepository<ContractDocument, String> {
    List<ContractDocument> findByStatus(ContractDocument.ReviewStatus status);
}

@Repository
public interface ContractSegmentRepository extends JpaRepository<ContractSegment, String> {
    List<ContractSegment> findByDocumentIdOrderBySequenceNumber(String documentId);
}

@Repository
public interface ReviewRuleRepository extends JpaRepository<ReviewRule, String> {
    
    @Query("SELECT r FROM ReviewRule r LEFT JOIN FETCH r.riskPoints " +
           "WHERE r.contractType = :type AND r.reviewPosition = :position AND r.isActive = true")
    List<ReviewRule> findActiveRules(@Param("type") ContractDocument.ContractType type,
                                     @Param("position") ContractDocument.ReviewPosition position);
}

@Repository
public interface ReviewResultRepository extends JpaRepository<ReviewResult, String> {
    List<ReviewResult> findByDocumentId(String documentId);
}
```

## 四、配置类

### 4.1 应用配置 (application.yml)

```yaml
spring:
  application:
    name: contract-review-system
    
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# LangChain4j配置
langchain4j:
  openai:
    api-key: ${OPENAI_API_KEY:sk-your-api-key}
    model-name: gpt-4
    temperature: 0.3
    max-tokens: 2000
    timeout: 60s

# 线程池配置
async:
  executor:
    core-pool-size: 10
    max-pool-size: 20
    queue-capacity: 100
    thread-name-prefix: review-

# 日志配置
logging:
  level:
    com.example.contractreview: DEBUG
    dev.langchain4j: DEBUG
```

### 4.2 异步配置

```java
package com.example.contractreview.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean(name = "reviewExecutor")
    public Executor reviewExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("review-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

### 4.3 LangChain4j配置

```java
package com.example.contractreview.config;

import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.time.Duration;

@Configuration
public class LangChainConfig {
    
    @Value("${langchain4j.openai.api-key}")
    private String apiKey;
    
    @Value("${langchain4j.openai.model-name}")
    private String modelName;
    
    @Value("${langchain4j.openai.temperature}")
    private Double temperature;
    
    @Value("${langchain4j.openai.max-tokens}")
    private Integer maxTokens;
    
    @Bean
    public ChatLanguageModel chatLanguageModel() {
        return OpenAiChatModel.builder()
                .apiKey(apiKey)
                .modelName(modelName)
                .temperature(temperature)
                .maxTokens(maxTokens)
                .timeout(Duration.ofSeconds(60))
                .build();
    }
}
```

## 五、业务逻辑层

### 5.1 文档处理服务

```java
package com.example.contractreview.service;

import com.example.contractreview.entity.ContractDocument;
import com.example.contractreview.entity.ContractSegment;
import com.example.contractreview.repository.ContractDocumentRepository;
import com.example.contractreview.repository.ContractSegmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentProcessingService {
    
    private final ContractDocumentRepository documentRepository;
    private final ContractSegmentRepository segmentRepository;
    
    @Transactional
    public ContractDocument uploadAndProcessDocument(
            MultipartFile file,
            ContractDocument.ContractType contractType,
            ContractDocument.ReviewPosition reviewPosition) throws IOException {
        
        log.info("Processing document: {}, Type: {}, Position: {}", 
                file.getOriginalFilename(), contractType, reviewPosition);
        
        // 创建文档实体
        ContractDocument document = new ContractDocument();
        document.setFileName(file.getOriginalFilename());
        document.setContractType(contractType);
        document.setReviewPosition(reviewPosition);
        document.setUploadTime(LocalDateTime.now());
        document.setStatus(ContractDocument.ReviewStatus.PENDING);
        
        // 提取文档内容
        String content = extractContent(file);
        document.setContent(content);
        
        // 保存文档
        document = documentRepository.save(document);
        
        // 分段处理
        List<ContractSegment> segments = segmentDocument(content, document);
        segmentRepository.saveAll(segments);
        
        document.setSegments(segments);
        
        return document;
    }
    
    private String extractContent(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        String content = "";
        
        if (fileName.endsWith(".pdf")) {
            try (PDDocument document = PDDocument.load(file.getInputStream())) {
                PDFTextStripper stripper = new PDFTextStripper();
                content = stripper.getText(document);
            }
        } else if (fileName.endsWith(".docx")) {
            try (XWPFDocument document = new XWPFDocument(file.getInputStream())) {
                StringBuilder sb = new StringBuilder();
                for (XWPFParagraph paragraph : document.getParagraphs()) {
                    sb.append(paragraph.getText()).append("\n");
                }
                content = sb.toString();
            }
        } else {
            content = new String(file.getBytes());
        }
        
        return content;
    }
    
    private List<ContractSegment> segmentDocument(String content, ContractDocument document) {
        List<ContractSegment> segments = new ArrayList<>();
        
        // 使用正则表达式进行智能分段
        // 识别标题模式：第X条、X.、(X)等
        Pattern titlePattern = Pattern.compile(
            "(第[一二三四五六七八九十\\d]+条|\\d+\\.|\\(\\d+\\)|[一二三四五六七八九十]+、)"
        );
        
        String[] lines = content.split("\n");
        StringBuilder currentSegment = new StringBuilder();
        String currentTitle = "";
        int sequenceNumber = 0;
        int startPosition = 0;
        
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            
            if (line.isEmpty()) {
                continue;
            }
            
            Matcher matcher = titlePattern.matcher(line);
            
            if (matcher.find() && matcher.start() == 0) {
                // 发现新段落标题
                if (currentSegment.length() > 0) {
                    // 保存前一个段落
                    ContractSegment segment = createSegment(
                        document, 
                        sequenceNumber++, 
                        currentTitle, 
                        currentSegment.toString(),
                        startPosition,
                        startPosition + currentSegment.length()
                    );
                    segments.add(segment);
                }
                
                // 开始新段落
                currentTitle = line;
                currentSegment = new StringBuilder();
                startPosition = content.indexOf(line, startPosition);
            } else {
                // 添加到当前段落
                if (currentSegment.length() > 0) {
                    currentSegment.append("\n");
                }
                currentSegment.append(line);
            }
        }
        
        // 保存最后一个段落
        if (currentSegment.length() > 0) {
            ContractSegment segment = createSegment(
                document, 
                sequenceNumber, 
                currentTitle, 
                currentSegment.toString(),
                startPosition,
                content.length()
            );
            segments.add(segment);
        }
        
        log.info("Document segmented into {} segments", segments.size());
        
        return segments;
    }
    
    private ContractSegment createSegment(
            ContractDocument document,
            Integer sequenceNumber,
            String title,
            String content,
            Integer startPosition,
            Integer endPosition) {
        
        ContractSegment segment = new ContractSegment();
        segment.setDocument(document);
        segment.setSequenceNumber(sequenceNumber);
        segment.setSectionTitle(title);
        segment.setContent(content);
        segment.setStartPosition(startPosition);
        segment.setEndPosition(endPosition);
        segment.setSegmentType(determineSegmentType(title, content));
        segment.setSemanticCategory(categorizeSegment(content));
        
        return segment;
    }
    
    private ContractSegment.SegmentType determineSegmentType(String title, String content) {
        if (title.contains("第") && title.contains("条")) {
            return ContractSegment.SegmentType.CLAUSE;
        } else if (title.matches("\\d+\\..*")) {
            return ContractSegment.SegmentType.SUBSECTION;
        } else {
            return ContractSegment.SegmentType.PARAGRAPH;
        }
    }
    
    private String categorizeSegment(String content) {
        // 简单的关键词匹配进行分类
        if (content.contains("付款") || content.contains("支付") || content.contains("价款")) {
            return "付款条款";
        } else if (content.contains("违约") || content.contains("责任") || content.contains("赔偿")) {
            return "违约责任";
        } else if (content.contains("保密") || content.contains("confidential")) {
            return "保密条款";
        } else if (content.contains("交付") || content.contains("交货") || content.contains("验收")) {
            return "交付条款";
        } else if (content.contains("质量") || content.contains("标准") || content.contains("要求")) {
            return "质量标准";
        } else if (content.contains("期限") || content.contains("有效期") || content.contains("时间")) {
            return "期限条款";
        } else {
            return "其他条款";
        }
    }
}
```

### 5.2 动态Prompt管理服务

```java
package com.example.contractreview.service;

import com.example.contractreview.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class PromptTemplateService {
    
    private static final Map<String, String> CONTRACT_TYPE_PROMPTS = new HashMap<>();
    
    static {
        // 初始化不同合同类型的Prompt模板
        CONTRACT_TYPE_PROMPTS.put("PURCHASE_PARTY_A", """
            你是一位专业的法律顾问，正在从采购方（甲方）的角度审核采购合同。
            
            合同段落内容：
            {segment_content}
            
            审核要点：{check_point}
            
            风险点：{risk_point}
            
            请分析上述合同段落，重点关注：
            1. 是否存在{risk_point}相关的风险
            2. 条款表述是否对采购方有利
            3. 是否需要增加保护采购方权益的内容
            
            请以JSON格式输出审核结果：
            {
                "has_risk": true/false,
                "risk_level": "HIGH/MEDIUM/LOW/INFO",
                "risk_description": "具体风险描述",
                "evidence": "原文中的证据",
                "suggestion": "修改建议",
                "confidence": 0.0-1.0
            }
            """);
        
        CONTRACT_TYPE_PROMPTS.put("SALES_PARTY_B", """
            你是一位专业的法律顾问，正在从销售方（乙方）的角度审核销售合同。
            
            合同段落内容：
            {segment_content}
            
            审核要点：{check_point}
            
            风险点：{risk_point}
            
            请分析上述合同段落，重点关注：
            1. 付款条件是否明确且有保障
            2. 是否存在不合理的责任承担
            3. 交付条件是否可行
            
            请以JSON格式输出审核结果：
            {
                "has_risk": true/false,
                "risk_level": "HIGH/MEDIUM/LOW/INFO",
                "risk_description": "具体风险描述",
                "evidence": "原文中的证据",
                "suggestion": "修改建议",
                "confidence": 0.0-1.0
            }
            """);
        
        CONTRACT_TYPE_PROMPTS.put("SERVICE_NEUTRAL", """
            你是一位专业的法律顾问，正在从中立角度审核服务合同。
            
            合同段落内容：
            {segment_content}
            
            审核要点：{check_point}
            
            风险点：{risk_point}
            
            请分析上述合同段落，评估：
            1. 条款是否公平合理
            2. 权利义务是否平衡
            3. 是否符合法律法规要求
            
            请以JSON格式输出审核结果：
            {
                "has_risk": true/false,
                "risk_level": "HIGH/MEDIUM/LOW/INFO",
                "risk_description": "具体风险描述",
                "evidence": "原文中的证据",
                "suggestion": "修改建议",
                "confidence": 0.0-1.0
            }
            """);
    }
    
    public String buildPrompt(
            ContractSegment segment,
            ReviewRule rule,
            RiskPoint riskPoint,
            ContractDocument document) {
        
        // 构建模板键
        String templateKey = document.getContractType() + "_" + document.getReviewPosition();
        
        // 获取基础模板，如果没有特定模板，使用通用模板
        String template = CONTRACT_TYPE_PROMPTS.getOrDefault(